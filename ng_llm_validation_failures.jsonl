{"id": "multistep_42630", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "⇒", "∂", "≤", "⊢", "∧"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "logical_2be43ac0", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "⊥"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "math_739bf286", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "≥"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_3dd51af6", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⟷"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "⟷"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "multistep_79403", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 5}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∨", "≥", "∂", "⊢", "∧"], "total_symbols": 5}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "logical_0ed37759", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊭"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⊭"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "logical_f0508361", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊨"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⊨"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "math_bbf3f9e2", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∆"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∆"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "multistep_23225", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⇒", "∫", "⊢", "∧"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_537e1282", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊤"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "⊤"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "math_289585a6", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∇"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_49714203", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∞"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_5d4029ce", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∂"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_67613478", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "↔"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "structure_⊇_208", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊇", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_4c4b58e7", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∫", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_d4079758", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∂"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "math_ad097fb0", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∮"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∮"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "math_141879aa", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∫", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "logical_9fc80497", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "logical_835cbfa9", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "⊥"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "story_hierarchical_organization_47", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 2}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊂", "⊢"], "total_symbols": 2}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "multistep_50664", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "≠", "⊢", "∧"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "complex_function_theory_2026", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "→", "∃", "∘", "⇒", "∈", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.8888888888888888, "threshold": 0.95}}}
{"id": "math_a74d8958", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≥"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_f4267fe6", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "code_c2496dfb", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "→"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "multistep_38322", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∀", "≠", "≥", "⊢", "∧"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "math_1e28da8c", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∱"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∱"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_48067399", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∑"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_df04f232", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⟷"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "⟷"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "logical_afc4eede", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "∨"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "math_bf584a73", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∮"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∮"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_7f021fee", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≠"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "math_cb236f2f", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∂"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "logical_04ad34f8", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "⇔"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "math_25dbcd9d", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "≥"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_5ba547a1", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⟷"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "⟷"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "logical_ddc50c49", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "≡"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "complex_propositional_logic_88", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "⇔", "¬", "∨", "∴", "⇒", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "logical_ab55ddb0", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊨"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⊨"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "logical_f42e5187", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "≈"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "complex_set_theory_957", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 8}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊂", "∧", "⊆", "⇒", "≠", "∅", "⊢"], "total_symbols": 8}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "structure_⊆_58", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊆", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_ba8d025f", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∆"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∆"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "code_d734a3a2", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️", "⇆"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⇆"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9705882352941176, "threshold": 0.95}}}
{"id": "logical_9e79c70d", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "structure_∪_75", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∪", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_a7cc1e38", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "≤"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "complex_propositional_logic_779", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "⇔", "¬", "∨", "∴", "⇒", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "complex_mathematical_logic_1204", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "∃", "∈", "≠", "⇒", "⊥", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "math_e755800a", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∑"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "complex_mathematical_logic_117", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "∃", "∈", "≠", "⇒", "⊥", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "story_universal_uniqueness_223", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "≠", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.9333333333333333, "threshold": 0.95}}}
{"id": "logical_58a088d2", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "⇔"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "structure_⊆_47", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊆", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "structure_⇔_176", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⇔", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "complex_set_theory_1649", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 8}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊂", "∧", "⊆", "⇒", "≠", "∅", "⊢"], "total_symbols": 8}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_f357e9e7", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "↕"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "math_f40ccb4d", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "≤"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "reverse_logical_reverse_36", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∧", "∨"], "total_symbols": 3}, "code_fidelity": {"passed": true, "score": 0.9622641509433962, "threshold": 0.95}}}
{"id": "code_f2f2c182", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⤴", "🏛️", "⚡", "⟧", "⟦"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "story_universal_uniqueness_56", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "≠", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.9333333333333333, "threshold": 0.95}}}
{"id": "complex_propositional_logic_1059", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "⇔", "¬", "∨", "∴", "⇒", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "math_1e1378d9", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∯"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∯"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_c7729001", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∫", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "math_d4f2bb35", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["√"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "√"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_d5b1e38b", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊤"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "⊤"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "logical_c104c5f2", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "≡"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "logical_4b726fab", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "code_37459a3e", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟨"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "code_8adea19e", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟨"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "logical_4a4c9041", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊨"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⊨"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "reverse_logical_reverse_780", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 2}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∧"], "total_symbols": 2}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "code_750f9a55", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "→"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9705882352941176, "threshold": 0.95}}}
{"id": "structure_∈_101", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∈", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "complex_mathematical_logic_810", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "∃", "∈", "≠", "⇒", "⊥", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "reverse_quantifier_reverse_922", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∃", "∧", "∈"], "total_symbols": 4}, "code_fidelity": {"passed": true, "score": 0.9534883720930233, "threshold": 0.95}}}
{"id": "logical_85fdfdab", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊤"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "⊤"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "logical_ecd21df9", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "↔"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "complex_set_theory_518", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 8}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊂", "∧", "⊆", "⇒", "≠", "∅", "⊢"], "total_symbols": 8}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_5f67eb7d", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≠"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_c94c1520", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "≈"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "math_67634abb", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≠"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_99488f49", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∆"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∆"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "code_e89cfe2b", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟨"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "code_378a7e7c", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟨"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "math_7ad1eb38", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∑"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_69dad114", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "∏"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "structure_⊆_77", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊆", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "code_b796b0a9", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟨"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "math_3fb70bbe", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∯"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∯"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_9e4d7729", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 5}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∀", "∃", "⇒", "⊢"], "total_symbols": 5}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "structure_∩_88", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∩", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_eb48f52a", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⊨"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⊨"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "structure_∈_103", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∈", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_96273484", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "complex_mathematical_logic_52", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "∃", "∈", "≠", "⇒", "⊥", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "math_48a084d8", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∂"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_040130a7", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≠"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "complex_mathematical_logic_1238", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "∃", "∈", "≠", "⇒", "⊥", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "math_24f2970e", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∑"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_3287516f", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∂"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "reverse_function_reverse_869", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∧", "∈"], "total_symbols": 3}, "code_fidelity": {"passed": true, "score": 0.971830985915493, "threshold": 0.95}}}
{"id": "code_8745b97e", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟩"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "code_fd55309a", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["⟦", "⚡", "⤴", "🔄", "⟧", "🏛️", "→"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9705882352941176, "threshold": 0.95}}}
{"id": "complex_propositional_logic_816", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "⇔", "¬", "∨", "∴", "⇒", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "math_f1b2389c", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∆"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∆"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "structure_⊈_7", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊈", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_fb811a06", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∱"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∱"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_58f13928", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∏"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_86e350cf", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∏"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_c8933bc3", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≤"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "code_781cf8c3", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["⟦", "⚡", "⤴", "🔄", "⟧", "🏛️", "→"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9705882352941176, "threshold": 0.95}}}
{"id": "structure_⇔_142", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⇔", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "math_63ff74ea", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∰"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∰"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "logical_44bc20fd", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "↔"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "structure_∴_37", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∴", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_955be3e7", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "≥"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "code_6e2f0f76", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "→"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "logical_244f62a2", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "reverse_function_reverse_873", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∧", "∈"], "total_symbols": 3}, "code_fidelity": {"passed": true, "score": 0.971830985915493, "threshold": 0.95}}}
{"id": "math_2d1d2c7a", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∫", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "structure_∴_45", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∴", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_0a7676e7", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "multistep_40114", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "≥", "⊢", "∧"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_9db68e67", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "¬"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "math_8afb9203", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∞"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "structure_⇔_84", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⇔", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "code_214938c3", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟩"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "structure_⊈_12", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊈", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_aebd8cbf", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∫", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "code_8d54b626", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟩"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "complex_set_theory_1618", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 8}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊂", "∧", "⊆", "⇒", "≠", "∅", "⊢"], "total_symbols": 8}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "complex_function_theory_2200", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "→", "∃", "∘", "⇒", "∈", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.8888888888888888, "threshold": 0.95}}}
{"id": "math_0be06413", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∑"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "structure_∴_183", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∴", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_b230dce7", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⇔"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "complex_set_theory_1513", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 8}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊂", "∧", "⊆", "⇒", "≠", "∅", "⊢"], "total_symbols": 8}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "complex_propositional_logic_1445", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "⇔", "¬", "∨", "∴", "⇒", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "code_401bf96f", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["⟦", "⚡", "⤴", "🔄", "⟧", "🏛️", "→"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "structure_∉_141", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∉", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "code_08f46dd1", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "λ"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "math_b56fec71", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∏"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "code_394f50f8", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "→"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9705882352941176, "threshold": 0.95}}}
{"id": "logical_1d54a2a6", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "↕"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "code_e22f0419", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟩"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "complex_set_theory_1635", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 8}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊂", "∧", "⊆", "⇒", "≠", "∅", "⊢"], "total_symbols": 8}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "structure_⊆_82", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊆", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_2a06a062", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∮"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∮"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_a1621c9a", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∆"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∆"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "structure_⊇_145", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊇", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_42386fed", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 5}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∀", "∃", "⇒", "⊢"], "total_symbols": 5}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "structure_∈_16", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∈", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "code_b0a66bca", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["⟦", "⚡", "⤴", "🔄", "⟧", "🏛️", "→"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9705882352941176, "threshold": 0.95}}}
{"id": "logical_f6cd43e2", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "↕"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "math_8a5adda0", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∱"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∱"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "structure_⊈_84", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊈", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "structure_∅_184", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∅", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_2e4a9875", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "λ"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "logical_d7bd15d0", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "≡"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "math_f5309004", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∞"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "complex_set_theory_1449", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 8}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊂", "∧", "⊆", "⇒", "≠", "∅", "⊢"], "total_symbols": 8}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_6f382c9f", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≤"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "code_f5a436e6", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟩"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9591836734693877, "threshold": 0.95}}}
{"id": "math_5e7640a5", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "∂"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_9d207159", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≤"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "multistep_94777", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["¬", "∂", "⊢", "∧"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "code_1ac15493", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["⟦", "⚡", "⤴", "🔄", "⟧", "🏛️", "→"], "total_symbols": 7}, "code_fidelity": {"passed": true, "score": 0.9705882352941176, "threshold": 0.95}}}
{"id": "structure_∴_62", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∴", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "reverse_logical_reverse_891", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∧", "∨"], "total_symbols": 3}, "code_fidelity": {"passed": true, "score": 0.9622641509433962, "threshold": 0.95}}}
{"id": "complex_propositional_logic_172", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "⇔", "¬", "∨", "∴", "⇒", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "story_function_application_10", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["→", "∧", "⊢"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.9428571428571428, "threshold": 0.95}}}
{"id": "complex_mathematical_logic_2205", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "∃", "∈", "≠", "⇒", "⊥", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "reverse_quantifier_reverse_279", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∃", "∧", "∈"], "total_symbols": 4}, "code_fidelity": {"passed": true, "score": 0.9534883720930233, "threshold": 0.95}}}
{"id": "multistep_95343", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∨", "∫", "⊢", "∧"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "logical_5f192bdd", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "≡"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "logical_6921083f", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["√"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "√"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "structure_⊇_55", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊇", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "math_821f6e9b", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∇"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "code_34c1e756", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "λ"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "math_b40dd8f5", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∫", "∧", "⊢", "≤"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "code_261f8d55", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟩"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "multistep_55068", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 5}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "≥", "∑", "⊢", "∧"], "total_symbols": 5}, "code_fidelity": {"passed": false, "score": 0.9130434782608695, "threshold": 0.95}}}
{"id": "complex_mathematical_logic_2174", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "∃", "∈", "≠", "⇒", "⊥", "⊢"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9354838709677419, "threshold": 0.95}}}
{"id": "multistep_56597", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["¬", "≤", "⊢", "∧"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8181818181818182, "threshold": 0.95}}}
{"id": "code_ccc78b86", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⟨"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "logical_67255cbf", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 5}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∧", "∀", "∃", "⇒", "⊢"], "total_symbols": 5}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "math_9ed6588a", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["∆"], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∆"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "code_b243a7a3", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["🏛️", "⇄"], "total_symbols": 7}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [{"symbol": "🏛️", "token_count": 2}], "missing_symbols": ["🔄", "⚡", "⟧", "⤴", "🏛️", "⟦", "⇄"], "total_symbols": 7}, "code_fidelity": {"passed": false, "score": 0.9090909090909091, "threshold": 0.95}}}
{"id": "logical_b437d1bf", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": false, "invalid_symbols": ["⟷"], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∀", "⊢", "∃", "∧", "⇒", "⟷"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "math_0cbfbbb1", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "≈"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8333333333333334, "threshold": 0.95}}}
{"id": "reverse_function_reverse_143", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∧", "∈"], "total_symbols": 3}, "code_fidelity": {"passed": true, "score": 0.971830985915493, "threshold": 0.95}}}
{"id": "logical_b56a5b26", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⊥"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.8461538461538461, "threshold": 0.95}}}
{"id": "logical_0cdffa9d", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["∃", "∧", "⇒", "⊢", "∀", "⇔"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.92, "threshold": 0.95}}}
{"id": "reverse_function_reverse_263", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∧", "∈"], "total_symbols": 3}, "code_fidelity": {"passed": true, "score": 0.971830985915493, "threshold": 0.95}}}
{"id": "math_ab76eb50", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "∏"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
{"id": "math_a866688f", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 4}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "∫", "∧", "±"], "total_symbols": 4}, "code_fidelity": {"passed": false, "score": 0.8571428571428571, "threshold": 0.95}}}
{"id": "logical_5f807f17", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 6}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊢", "⇒", "∃", "∀", "∧", "∨"], "total_symbols": 6}, "code_fidelity": {"passed": false, "score": 0.7142857142857143, "threshold": 0.95}}}
{"id": "structure_⊆_33", "overall_passed": false, "tests": {"ast_roundtrip": {"passed": true, "similarity": 1.0, "threshold": 0.99}, "registry_compliance": {"passed": true, "invalid_symbols": [], "total_symbols": 3}, "tokenization_integrity": {"passed": false, "zero_splitting_violations": [], "missing_symbols": ["⊆", "⊢", "∧"], "total_symbols": 3}, "code_fidelity": {"passed": false, "score": 0.8947368421052632, "threshold": 0.95}}}
