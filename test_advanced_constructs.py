#!/usr/bin/env python3
"""
Test per <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ni, Edge Cases
Verifica che tutti i pattern avanzati AST-based funzionino perfettamente
"""

from neuroglyph.core.encoder.encoder import NGEncoder

def test_async_with():
    """Test AST-based per async with statement."""
    print("🎯 TEST ADVANCED: Async With Statement")
    
    code = """
async def process_async_file(filename):
    async with aiofiles.open(filename, 'r') as f:
        content = await f.read()
        lines = content.split('\\n')
        return len(lines)
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_type_annotations():
    """Test AST-based per annotazioni di tipo avanzate."""
    print("\n🎯 TEST ADVANCED: Type Annotations")
    
    code = """
from typing import List, Dict, Optional, Union

def process_data(
    items: List[Dict[str, Union[int, str]]], 
    threshold: Optional[float] = None
) -> Dict[str, List[int]]:
    result: Dict[str, List[int]] = {}
    
    for item in items:
        key: str = item.get('name', 'unknown')
        value: Union[int, str] = item.get('value', 0)
        
        if isinstance(value, int) and (threshold is None or value > threshold):
            if key not in result:
                result[key] = []
            result[key].append(value)
    
    return result
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_complex_decorators():
    """Test AST-based per decoratori complessi."""
    print("\n🎯 TEST ADVANCED: Complex Decorators")
    
    code = """
import functools
from dataclasses import dataclass, field

@dataclass
@functools.total_ordering
class Person:
    name: str
    age: int = field(default=0)
    
    @property
    @functools.lru_cache(maxsize=128)
    def display_name(self) -> str:
        return f"{self.name} ({self.age})"
    
    @staticmethod
    @functools.wraps(str.upper)
    def normalize_name(name: str) -> str:
        return name.strip().upper()
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_edge_cases():
    """Test AST-based per edge cases estremi."""
    print("\n🎯 TEST ADVANCED: Edge Cases")
    
    code = """
def extreme_edge_cases():
    # Nested comprehensions
    matrix = [
        [x * y for y in range(3) if y > 0] 
        for x in range(5) 
        if x % 2 == 0
    ]
    
    # Lambda in comprehension in f-string
    result = f"Results: {', '.join(str((lambda x: x**2)(item)) for item in matrix[0])}"
    
    # Generator with multiple conditions
    filtered = (
        item.upper() 
        for sublist in matrix 
        for item in (str(x) for x in sublist if x > 1)
        if len(item) > 0 and item.isdigit()
    )
    
    # Complex try/except with nested constructs
    try:
        with open('test.txt', 'w') as f:
            for line in filtered:
                if line:
                    f.write(f"{line}\\n")
    except (IOError, ValueError) as e:
        print(f"Error: {e}")
        return None
    finally:
        print("Cleanup completed")
    
    return result
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

if __name__ == "__main__":
    print("🎯 ADVANCED CONSTRUCTS TEST - AsyncWith, Annotations, Decorators, Edge Cases")
    print("=" * 100)
    
    # Test 1: Async With
    result1 = test_async_with()
    
    # Test 2: Type Annotations
    result2 = test_type_annotations()
    
    # Test 3: Complex Decorators
    result3 = test_complex_decorators()
    
    # Test 4: Edge Cases
    result4 = test_edge_cases()
    
    print("\n" + "=" * 100)
    print("🎯 RISULTATI FINALI ADVANCED CONSTRUCTS:")
    print(f"   Test 1 (AsyncWith): Fidelity {result1.fidelity_score:.3f}, Compressione {result1.compression_ratio:.1f}%")
    print(f"   Test 2 (Annotations): Fidelity {result2.fidelity_score:.3f}, Compressione {result2.compression_ratio:.1f}%")
    print(f"   Test 3 (Decorators): Fidelity {result3.fidelity_score:.3f}, Compressione {result3.compression_ratio:.1f}%")
    print(f"   Test 4 (Edge Cases): Fidelity {result4.fidelity_score:.3f}, Compressione {result4.compression_ratio:.1f}%")
    
    results = [result1, result2, result3, result4]
    avg_fidelity = sum(r.fidelity_score for r in results) / len(results)
    avg_compression = sum(r.compression_ratio for r in results) / len(results)
    
    print(f"\n🎯 MEDIA ADVANCED CONSTRUCTS:")
    print(f"   - Fidelity media: {avg_fidelity:.3f}")
    print(f"   - Compressione media: {avg_compression:.1f}%")
    
    # Verifica target
    ast_equivalent_count = sum([r.ast_equivalent for r in results])
    syntax_valid_count = sum([r.decoding_result.reconstruction_result.syntax_valid for r in results])
    
    print(f"\n🎯 QUALITÀ ADVANCED CONSTRUCTS:")
    print(f"   - AST equivalenti: {ast_equivalent_count}/{len(results)}")
    print(f"   - Sintassi valida: {syntax_valid_count}/{len(results)}")
    
    if avg_fidelity >= 0.95 and avg_compression >= 80 and syntax_valid_count >= len(results):
        print("✅ ADVANCED CONSTRUCTS SUCCESSO: Target raggiunti!")
        print("   🎯 Fidelity ≥0.95 ✅")
        print("   🎯 Compressione ≥80% ✅")
        print("   🎯 Sintassi valida 100% ✅")
        print("   🎯 AsyncWith, Annotations, Decorators, Edge Cases supportati ✅")
    else:
        print("❌ ADVANCED CONSTRUCTS: Target non raggiunti")
        print(f"   - Fidelity target: ≥0.95 (attuale: {avg_fidelity:.3f})")
        print(f"   - Compressione target: ≥80% (attuale: {avg_compression:.1f}%)")
        print(f"   - Sintassi valida target: 100% (attuale: {syntax_valid_count}/{len(results)})")
    
    print(f"\n🚀 CONCLUSIONE ADVANCED CONSTRUCTS:")
    if avg_fidelity >= 0.95 and syntax_valid_count >= len(results):
        print("   🎉 NEUROGLYPH supporta TUTTI i costrutti Python avanzati!")
        print("   🎯 AsyncWith, Annotations, Decorators, Edge Cases funzionano")
        print("   ✅ Architettura AST-based scalabile confermata")
        print("   🚀 NEUROGLYPH è ora COMPLETO per Python enterprise!")
    else:
        print("   ⚠️ Alcuni costrutti avanzati richiedono ottimizzazioni")
        print("   🔧 Verificare implementazione visit_* methods")
        print("   📊 Analizzare casi specifici di fallimento")
