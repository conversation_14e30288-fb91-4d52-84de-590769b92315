#!/usr/bin/env python3
"""
Debug Integration Layer
Analizza il gap tra debug standalone (100%) e evaluation harness (0%).
"""

import asyncio
from neuroglyph.cognitive.reasoner import NGReasoner
from neuroglyph.evaluation.benchmarks.logiqa_loader import Lo<PERSON><PERSON><PERSON>oader
from neuroglyph.evaluation.harness import EvaluationHarness, EvaluationConfig


async def debug_integration_gap():
    """Debug del gap tra standalone e harness."""
    
    print("🔍 DEBUG INTEGRATION LAYER GAP")
    print("=" * 60)
    
    # Setup
    reasoner = NGReasoner()
    loader = LogiQALoader()
    samples = loader.load_samples("mock", max_samples=3)
    
    print(f"📊 Testing {len(samples)} samples")
    
    for i, sample in enumerate(samples):
        print(f"\n📋 SAMPLE {i+1}: {sample.sample_id}")
        print(f"   Ground Truth: {sample.ground_truth}")
        
        # Test 1: Direct reasoning (come nel debug)
        formatted_prompt = loader.format_prompt(sample)
        direct_answer = await reasoner.reason_async(formatted_prompt)
        direct_correct = direct_answer == sample.ground_truth
        
        print(f"   Direct Reasoning: {direct_answer} ({'✅' if direct_correct else '❌'})")
        
        # Test 2: Harness simulation (come nell'evaluation)
        try:
            # Simula il flusso dell'evaluation harness
            harness_answer = await simulate_harness_flow(reasoner, formatted_prompt)
            harness_correct = harness_answer == sample.ground_truth
            
            print(f"   Harness Simulation: {harness_answer} ({'✅' if harness_correct else '❌'})")
            
            # Confronta
            if direct_answer != harness_answer:
                print(f"   🚨 MISMATCH: Direct={direct_answer} vs Harness={harness_answer}")
            else:
                print(f"   ✅ MATCH: Both return {direct_answer}")
                
        except Exception as e:
            print(f"   ❌ Harness Error: {e}")
    
    return True


async def simulate_harness_flow(reasoner, prompt):
    """Simula esattamente il flusso dell'evaluation harness."""
    
    # Questo è quello che fa l'harness in _execute_reasoning
    try:
        # Simula reasoning asincrono
        await asyncio.sleep(0.1)  # Come nel harness originale
        
        # Usa NGReasoner reale
        reasoning_result = await reasoner.reason_async(prompt)
        
        if reasoning_result and hasattr(reasoning_result, 'conclusion'):
            return reasoning_result.conclusion
        elif reasoning_result and hasattr(reasoning_result, 'output'):
            return reasoning_result.output
        else:
            return str(reasoning_result) if reasoning_result else "No reasoning result"
            
    except Exception as e:
        print(f"⚠️ Reasoning error: {e}")
        return f"Reasoning failed: {e}"


async def debug_harness_execution():
    """Debug dell'execution nell'harness reale."""
    
    print(f"\n🔍 DEBUG HARNESS EXECUTION")
    print("=" * 60)
    
    # Crea harness reale
    import tempfile
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = EvaluationConfig(
            benchmark_name="logiqa_debug",
            data_path="mock",
            max_samples=3,
            timeout_per_sample=10.0,
            target_accuracy=0.75,
            output_dir=temp_dir,
            max_concurrent=1,  # Sequenziale per debug
            batch_size=1
        )
        
        harness = EvaluationHarness(config)
        loader = LogiQALoader()
        
        # Carica samples
        samples = loader.load_samples("mock", max_samples=3)
        
        print(f"📊 Testing {len(samples)} samples with real harness")
        
        # Test ogni sample individualmente
        for i, sample in enumerate(samples):
            print(f"\n📋 SAMPLE {i+1}: {sample.sample_id}")
            
            try:
                # Simula _evaluate_single_sample
                result_sample = await harness._evaluate_single_sample(sample, loader)
                
                print(f"   Prediction: {result_sample.prediction}")
                print(f"   Is Correct: {result_sample.is_correct}")
                print(f"   Ground Truth: {result_sample.ground_truth}")
                print(f"   Error: {result_sample.error_message}")
                print(f"   Latency: {result_sample.latency:.3f}s")
                
                if result_sample.reasoning_trace:
                    print(f"   Reasoning Trace: {result_sample.reasoning_trace[:100]}...")
                
            except Exception as e:
                print(f"   ❌ Harness execution error: {e}")
                import traceback
                traceback.print_exc()


async def debug_response_parsing():
    """Debug del parsing delle risposte."""
    
    print(f"\n🔍 DEBUG RESPONSE PARSING")
    print("=" * 60)
    
    loader = LogiQALoader()
    sample = loader.load_samples("mock", max_samples=1)[0]
    
    # Test varie risposte del reasoner
    test_responses = [
        "A",
        "B", 
        "The answer is C",
        "I think D is correct",
        "Based on logical reasoning, the answer is A",
        "No reasoning result",
        "Reasoning failed: error",
        "",
        None
    ]
    
    print(f"📋 Testing response parsing on sample: {sample.sample_id}")
    print(f"   Ground Truth: {sample.ground_truth}")
    
    for response in test_responses:
        try:
            parsed = loader.parse_response(str(response) if response else "", sample)
            is_correct = loader.evaluate_response(parsed, sample.ground_truth)
            
            print(f"   '{response}' → '{parsed}' ({'✅' if is_correct else '❌'})")
            
        except Exception as e:
            print(f"   '{response}' → ERROR: {e}")


async def main():
    """Esegue debug completo dell'integration layer."""
    
    print("🚀 NEUROGLYPH INTEGRATION LAYER DEBUG")
    print("=" * 80)
    
    try:
        # Debug 1: Gap analysis
        await debug_integration_gap()
        
        # Debug 2: Harness execution
        await debug_harness_execution()
        
        # Debug 3: Response parsing
        await debug_response_parsing()
        
        print(f"\n🎯 INTEGRATION DEBUG SUMMARY:")
        print(f"=" * 80)
        print(f"✅ Direct Reasoning: Working (100% accuracy)")
        print(f"❓ Harness Integration: Under investigation")
        print(f"✅ Response Parsing: Working")
        
        print(f"\n🔧 POTENTIAL ISSUES:")
        print(f"1. Harness _execute_reasoning method mismatch")
        print(f"2. Async flow differences")
        print(f"3. Response format expectations")
        print(f"4. Error handling in harness")
        
    except Exception as e:
        print(f"\n❌ INTEGRATION DEBUG FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
