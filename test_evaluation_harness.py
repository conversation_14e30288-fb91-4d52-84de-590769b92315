#!/usr/bin/env python3
"""
Test per NEUROGLYPH Evaluation Harness
Verifica che l'infrastruttura di evaluation funzioni correttamente.
"""

import asyncio
import tempfile
from pathlib import Path
from typing import List, Any

from neuroglyph.evaluation.harness import (
    EvaluationHarness, 
    EvaluationConfig, 
    EvaluationSample,
    BenchmarkLoader
)
from neuroglyph.evaluation.metrics import BenchmarkResult
from neuroglyph.evaluation.quality_gates import QualityGates


class MockBenchmarkLoader(BenchmarkLoader):
    """Mock loader per test dell'harness."""
    
    def load_samples(self, data_path: str, max_samples: int = None) -> List[EvaluationSample]:
        """Carica samples mock."""
        samples = [
            EvaluationSample(
                sample_id=f"sample_{i}",
                prompt=f"Test prompt {i}: What is 2 + 2?",
                ground_truth="4",
                metadata={"difficulty": "easy"}
            )
            for i in range(10)
        ]
        
        if max_samples:
            samples = samples[:max_samples]
        
        return samples
    
    def format_prompt(self, sample: EvaluationSample) -> str:
        """Formatta prompt per il modello."""
        return f"Question: {sample.prompt}\nAnswer:"
    
    def parse_response(self, response: str, sample: EvaluationSample) -> Any:
        """Parsa risposta del modello."""
        # Estrai risposta dalla response (semplificato)
        if "4" in response:
            return "4"
        else:
            return "unknown"
    
    def evaluate_response(self, prediction: Any, ground_truth: Any) -> bool:
        """Valuta correttezza della risposta."""
        return str(prediction).strip() == str(ground_truth).strip()


async def test_evaluation_harness():
    """Test completo dell'evaluation harness."""
    
    print("🧪 TESTING NEUROGLYPH EVALUATION HARNESS")
    print("=" * 60)
    
    # Setup temporaneo
    with tempfile.TemporaryDirectory() as temp_dir:
        
        # Configurazione test
        config = EvaluationConfig(
            benchmark_name="test_benchmark",
            data_path="mock_data",
            max_samples=5,
            timeout_per_sample=5.0,
            target_accuracy=0.80,
            output_dir=temp_dir,
            max_concurrent=2,
            batch_size=3
        )
        
        print(f"📋 Config: {config.benchmark_name}")
        print(f"   Max samples: {config.max_samples}")
        print(f"   Target accuracy: {config.target_accuracy:.1%}")
        print(f"   Output dir: {config.output_dir}")
        
        # Crea harness
        harness = EvaluationHarness(config)
        
        # Crea mock loader
        loader = MockBenchmarkLoader()
        
        # Esegui evaluation
        print(f"\n🚀 Eseguendo evaluation...")
        result = await harness.evaluate_benchmark(loader, model_name="test_model")
        
        # Verifica risultati
        print(f"\n📊 RISULTATI:")
        print(f"   Benchmark: {result.benchmark_name}")
        print(f"   Model: {result.model_name}")
        print(f"   Total samples: {result.total_samples}")
        print(f"   Valid samples: {result.valid_samples}")
        print(f"   Correct samples: {result.correct_samples}")
        print(f"   Accuracy: {result.accuracy:.1%}")
        print(f"   Avg latency: {result.avg_latency:.3f}s")
        print(f"   P95 latency: {result.p95_latency:.3f}s")
        print(f"   Total time: {result.total_time:.3f}s")
        print(f"   Throughput: {result.throughput:.2f} samples/s")
        
        # Test quality gates
        print(f"\n🎯 TESTING QUALITY GATES:")
        quality_gates = QualityGates()
        gate_report = quality_gates.check_gates(result, config)
        
        print(f"   Overall status: {gate_report.overall_status.value}")
        print(f"   Gates passed: {gate_report.passed_gates}/{gate_report.total_gates}")
        print(f"   Critical failures: {gate_report.critical_failures}")
        print(f"   Major failures: {gate_report.major_failures}")
        print(f"   Minor failures: {gate_report.minor_failures}")
        
        # Mostra dettagli gates
        print(f"\n📋 Gate Details:")
        for gate_result in gate_report.gate_results:
            status_icon = "✅" if gate_result.passed else "❌"
            print(f"   {status_icon} {gate_result.gate.name}: {gate_result.actual_value:.3f} (threshold: {gate_result.threshold})")
        
        # Verifica file output
        output_dir = Path(config.output_dir)
        output_files = list(output_dir.glob("*"))
        
        print(f"\n📄 OUTPUT FILES:")
        for file_path in output_files:
            print(f"   📄 {file_path.name} ({file_path.stat().st_size} bytes)")
        
        # Verifica che i file essenziali esistano
        summary_files = list(output_dir.glob("*summary.json"))
        detailed_files = list(output_dir.glob("*detailed.json"))
        html_files = list(output_dir.glob("*report.html"))
        
        assert len(summary_files) > 0, "Summary JSON non trovato"
        assert len(detailed_files) > 0, "Detailed JSON non trovato"
        assert len(html_files) > 0, "HTML report non trovato"
        
        print(f"\n✅ TEST COMPLETATO CON SUCCESSO!")
        print(f"   - Evaluation harness funzionante")
        print(f"   - Quality gates operativi")
        print(f"   - Output files generati correttamente")
        
        return result, gate_report


def test_quality_gates_standalone():
    """Test standalone dei quality gates."""
    
    print(f"\n🧪 TESTING QUALITY GATES STANDALONE")
    print("=" * 60)
    
    # Crea risultato mock
    from datetime import datetime, timezone
    
    mock_result = BenchmarkResult(
        benchmark_name="logiqa",
        model_name="test_model",
        timestamp=datetime.now(timezone.utc),
        total_samples=100,
        valid_samples=95,
        correct_samples=80,
        accuracy=0.842,  # 84.2% > 75% threshold
        avg_latency=2.5,
        p95_latency=8.0,  # < 15s threshold
        total_time=250.0
    )
    
    # Test quality gates
    quality_gates = QualityGates()
    report = quality_gates.check_gates(mock_result)
    
    print(f"📊 Mock Result:")
    print(f"   Accuracy: {mock_result.accuracy:.1%}")
    print(f"   P95 Latency: {mock_result.p95_latency:.1f}s")
    print(f"   Success Rate: {mock_result.success_rate:.1%}")
    
    print(f"\n🎯 Quality Gates Report:")
    summary = quality_gates.generate_report_summary(report)
    print(summary)
    
    # Verifica che LogiQA gates passino
    assert report.overall_status.value in ['passed', 'warning'], f"Expected passed/warning, got {report.overall_status.value}"
    
    # Verifica accuracy gate specifico
    accuracy_gates = [r for r in report.gate_results if r.gate.metric_name == 'accuracy']
    assert len(accuracy_gates) > 0, "Accuracy gate non trovato"
    assert accuracy_gates[0].passed, f"Accuracy gate dovrebbe passare: {accuracy_gates[0].message}"
    
    print(f"\n✅ QUALITY GATES TEST PASSED!")


async def main():
    """Esegue tutti i test."""
    
    print("🚀 NEUROGLYPH EVALUATION HARNESS - COMPREHENSIVE TEST")
    print("=" * 80)
    
    try:
        # Test 1: Evaluation Harness completo
        result, gate_report = await test_evaluation_harness()
        
        # Test 2: Quality Gates standalone
        test_quality_gates_standalone()
        
        print(f"\n🎉 TUTTI I TEST COMPLETATI CON SUCCESSO!")
        print(f"=" * 80)
        print(f"✅ Evaluation Harness: Funzionante")
        print(f"✅ Quality Gates: Operativi")
        print(f"✅ Logger: Attivo")
        print(f"✅ Metrics: Calcolate correttamente")
        print(f"✅ Output Generation: HTML + JSON")
        
        print(f"\n🚀 READY FOR FASE 5.2.2 - INTEGRAZIONE LOGIQA!")
        
    except Exception as e:
        print(f"\n❌ TEST FALLITO: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
