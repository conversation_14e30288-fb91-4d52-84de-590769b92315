#!/usr/bin/env python3
"""
Test semplice per verificare l'integrazione base di NEUROGLYPH
"""

import sys
from pathlib import Path

# Aggiungi il path del progetto
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test che tutti i moduli si importino correttamente."""
    print("🧪 Test Import Moduli")
    print("-" * 30)
    
    try:
        from neuroglyph.cognitive import NGIntegration
        print("✅ NGIntegration importato")
        
        from neuroglyph.cognitive.integration import PipelineConfig, ExecutionMode
        print("✅ PipelineConfig importato")
        
        from neuroglyph.cognitive.learner_structures import LearnerConfig, LearningMode
        print("✅ LearnerConfig importato")
        
        return True
    except Exception as e:
        print(f"❌ Errore import: {e}")
        return False


def test_parser_standalone():
    """Test del parser standalone."""
    print("\n🧪 Test Parser Standalone")
    print("-" * 30)
    
    try:
        from neuroglyph.core.parser.ng_parser import NGParser
        
        parser = NGParser()
        print("✅ Parser inizializzato")
        
        # Test parsing semplice
        result = parser.parse("def hello(): return 'world'")
        print(f"✅ Parsing completato: {len(result.tokens)} tokens, {len(result.symbols)} simboli")
        
        return True
    except Exception as e:
        print(f"❌ Errore parser: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_context_prioritizer_standalone():
    """Test del context prioritizer standalone."""
    print("\n🧪 Test Context Prioritizer Standalone")
    print("-" * 30)
    
    try:
        from neuroglyph.cognitive import NGContextPrioritizer
        from neuroglyph.cognitive.data_structures import ParsedPrompt
        
        prioritizer = NGContextPrioritizer()
        print("✅ Context Prioritizer inizializzato")
        
        # Crea un ParsedPrompt di test
        test_prompt = ParsedPrompt(
            original_text="def hello(): return 'world'",
            tokens=["def", "hello", "(", ")", ":", "return", "'world'"],
            segments=[],
            intents=["coding"],
            symbols=["<NG_FUNCTION_DEF>", "<NG_RETURN>"],
            metadata={}
        )
        
        result = prioritizer.prioritize(test_prompt)
        print(f"✅ Prioritization completata: urgency={result.urgency:.3f}")
        
        return True
    except Exception as e:
        print(f"❌ Errore prioritizer: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration_basic():
    """Test integrazione base senza esecuzione completa."""
    print("\n🧪 Test Integrazione Base")
    print("-" * 30)
    
    try:
        from neuroglyph.cognitive import NGIntegration
        from neuroglyph.cognitive.integration import PipelineConfig, ExecutionMode
        
        # Configurazione minima
        config = PipelineConfig(
            execution_mode=ExecutionMode.DRY_RUN,
            enable_self_check=False,
            enable_sandbox=False,
            enable_adaptive_patching=False,
            enable_learning=False
        )
        
        pipeline = NGIntegration(config)
        print("✅ Pipeline inizializzata")
        
        # Test solo parsing e prioritization
        source_code = "print('Hello NEUROGLYPH')"
        
        # Inizializza risultato manualmente
        from neuroglyph.cognitive.integration import PipelineResult, ExecutionMetrics
        import uuid
        
        execution_id = f"test_{uuid.uuid4().hex[:8]}"
        result = PipelineResult(
            execution_id=execution_id,
            success=False,
            metrics=ExecutionMetrics(execution_id=execution_id)
        )
        
        # Test parsing
        result = pipeline._execute_parsing(source_code, result, None)
        if result.success:
            print("✅ Parsing riuscito")
            
            # Test prioritization
            result = pipeline._execute_prioritization(result)
            if result.success:
                print("✅ Prioritization riuscita")
                return True
            else:
                print(f"❌ Prioritization fallita: {result.error_message}")
        else:
            print(f"❌ Parsing fallito: {result.error_message}")
        
        return False
    except Exception as e:
        print(f"❌ Errore integrazione: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cli_tool():
    """Test del CLI tool."""
    print("\n🧪 Test CLI Tool")
    print("-" * 30)
    
    try:
        from neuroglyph.cli.ngpipeline import create_pipeline_config
        
        # Simula argomenti CLI
        class MockArgs:
            mode = "dry_run"
            enable_self_check = True
            enable_sandbox = False
            enable_patching = False
            enable_learning = False
            learning_mode = "balanced"
            min_confidence = 0.3
            save_patterns = False
            timeout = 60.0
            verbose = False
            save_trace = True
        
        args = MockArgs()
        config = create_pipeline_config(args)
        print(f"✅ CLI config creata: mode={config.execution_mode.value}")
        
        return True
    except Exception as e:
        print(f"❌ Errore CLI: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Esegue tutti i test semplici."""
    print("🚀 NEUROGLYPH Simple Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Import Moduli", test_imports),
        ("Parser Standalone", test_parser_standalone),
        ("Context Prioritizer Standalone", test_context_prioritizer_standalone),
        ("Integrazione Base", test_integration_basic),
        ("CLI Tool", test_cli_tool)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success, None))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False, str(e)))
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RISULTATI TEST")
    print("=" * 50)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for test_name, success, error in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"      Error: {error}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("\n🎉 Tutti i test base sono passati!")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test falliti.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
