#!/usr/bin/env python3
"""
Test funzionante per NGReasoner con componenti base
"""

def test_reasoning_components():
    print("🧠 TEST REASONING COMPONENTS")
    
    try:
        from neuroglyph.cognitive.reasoning_structures import (
            LogicalFact, ReasoningStep, ReasoningPath, ReasoningGraph,
            LogicalOperator, FactType
        )
        from neuroglyph.cognitive.logical_operators import LogicalOperators
        
        # Test LogicalFact
        fact1 = LogicalFact(
            statement="it is raining",
            predicate="raining",
            fact_type=FactType.PREMISE,
            truth_value=True
        )
        
        fact2 = LogicalFact(
            statement="it is raining ⇒ the ground is wet",
            predicate="implication",
            fact_type=FactType.PREMISE,
            truth_value=True
        )
        
        print(f"✅ Created facts:")
        print(f"   Fact 1: {fact1.statement}")
        print(f"   Fact 2: {fact2.statement}")
        
        # Test LogicalOperators
        operators = LogicalOperators()
        steps = operators.modus_ponens([fact1, fact2])
        
        print(f"✅ Modus ponens result:")
        print(f"   Steps generated: {len(steps)}")
        
        if steps:
            step = steps[0]
            print(f"   Operation: {step.operation}")
            print(f"   Rule: {step.rule_name}")
            print(f"   Conclusion: {step.output_facts[0].statement}")
            print(f"   Confidence: {step.confidence:.3f}")
        
        # Test ReasoningPath
        path = ReasoningPath()
        path.current_facts = [fact1, fact2]
        
        if steps:
            path.add_step(steps[0])
            print(f"✅ ReasoningPath:")
            print(f"   Depth: {path.depth}")
            print(f"   Score: {path.overall_score:.3f}")
            print(f"   Facts: {len(path.current_facts)}")
        
        # Test ReasoningGraph
        graph = ReasoningGraph(max_depth=3, max_paths=2)
        graph.add_initial_facts([fact1, fact2])
        
        print(f"✅ ReasoningGraph:")
        print(f"   Initial facts: {len(graph.initial_facts)}")
        print(f"   Total facts: {graph.total_facts}")
        print(f"   Paths: {len(graph.paths)}")
        print(f"   Active paths: {len(graph.active_paths)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_contradiction_detection():
    print("\n🧠 TEST CONTRADICTION DETECTION")
    
    try:
        from neuroglyph.cognitive.reasoning_structures import LogicalFact, FactType
        from neuroglyph.cognitive.logical_operators import LogicalOperators
        
        # Crea fatti contraddittori
        fact1 = LogicalFact(
            statement="the system is secure",
            predicate="secure",
            fact_type=FactType.PREMISE,
            truth_value=True
        )
        
        fact2 = LogicalFact(
            statement="¬the system is secure",
            predicate="secure", 
            fact_type=FactType.PREMISE,
            truth_value=False
        )
        
        print(f"✅ Created contradictory facts:")
        print(f"   Fact 1: {fact1.statement}")
        print(f"   Fact 2: {fact2.statement}")
        print(f"   Contradicts: {fact1.contradicts(fact2)}")
        
        # Test contradiction detection
        operators = LogicalOperators()
        contradiction_steps = operators.contradiction_detection([fact1, fact2])
        
        print(f"✅ Contradiction detection:")
        print(f"   Steps generated: {len(contradiction_steps)}")
        
        if contradiction_steps:
            step = contradiction_steps[0]
            print(f"   Operation: {step.operation}")
            print(f"   Rule: {step.rule_name}")
            print(f"   Result: {step.output_facts[0].statement}")
            print(f"   Valid: {step.is_valid}")
            print(f"   Errors: {step.validation_errors}")
        
        return len(contradiction_steps) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_conjunction_operations():
    print("\n🧠 TEST CONJUNCTION OPERATIONS")
    
    try:
        from neuroglyph.cognitive.reasoning_structures import LogicalFact, FactType
        from neuroglyph.cognitive.logical_operators import LogicalOperators
        
        # Test conjunction introduction
        fact1 = LogicalFact(
            statement="A",
            predicate="A",
            fact_type=FactType.PREMISE,
            truth_value=True
        )
        
        fact2 = LogicalFact(
            statement="B", 
            predicate="B",
            fact_type=FactType.PREMISE,
            truth_value=True
        )
        
        operators = LogicalOperators()
        
        # Test conjunction introduction (A, B → A∧B)
        intro_steps = operators.conjunction_introduction([fact1, fact2])
        print(f"✅ Conjunction introduction:")
        print(f"   Steps: {len(intro_steps)}")
        
        if intro_steps:
            step = intro_steps[0]
            conjunction_fact = step.output_facts[0]
            print(f"   Result: {conjunction_fact.statement}")
            
            # Test conjunction elimination (A∧B → A, B)
            elim_steps = operators.conjunction_elimination([conjunction_fact])
            print(f"✅ Conjunction elimination:")
            print(f"   Steps: {len(elim_steps)}")
            
            if elim_steps:
                step = elim_steps[0]
                print(f"   Results: {[f.statement for f in step.output_facts]}")
        
        return len(intro_steps) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mini_reasoning_session():
    print("\n🧠 TEST MINI REASONING SESSION")
    
    try:
        from neuroglyph.cognitive.reasoning_structures import (
            LogicalFact, ReasoningGraph, FactType
        )
        from neuroglyph.cognitive.logical_operators import LogicalOperators
        
        # Scenario: Se piove, il terreno è bagnato. Piove. Quindi?
        facts = [
            LogicalFact(
                statement="it is raining",
                predicate="raining",
                fact_type=FactType.PREMISE,
                truth_value=True
            ),
            LogicalFact(
                statement="it is raining ⇒ the ground is wet",
                predicate="implication",
                fact_type=FactType.PREMISE,
                truth_value=True
            ),
            LogicalFact(
                statement="the ground is wet ⇒ we should stay inside",
                predicate="implication",
                fact_type=FactType.PREMISE,
                truth_value=True
            )
        ]
        
        print(f"✅ Initial scenario:")
        for i, fact in enumerate(facts, 1):
            print(f"   {i}. {fact.statement}")
        
        # Crea grafo e applica reasoning
        graph = ReasoningGraph(max_depth=3, max_paths=2)
        graph.add_initial_facts(facts)
        
        operators = LogicalOperators()
        
        # Step 1: Applica modus ponens
        current_facts = graph.initial_facts.copy()
        all_derived_facts = []
        
        for step_num in range(3):  # Max 3 steps
            print(f"\n   🔍 Reasoning step {step_num + 1}:")
            
            # Applica tutte le regole
            new_steps = operators.deduce(current_facts)
            
            if not new_steps:
                print(f"      No new inferences possible")
                break
            
            # Prendi il primo step valido
            step = new_steps[0]
            new_facts = step.output_facts
            
            print(f"      Rule: {step.rule_name}")
            print(f"      New facts: {[f.statement for f in new_facts]}")
            
            # Aggiungi nuovi fatti
            current_facts.extend(new_facts)
            all_derived_facts.extend(new_facts)
        
        print(f"\n✅ Reasoning session completed:")
        print(f"   Initial facts: {len(facts)}")
        print(f"   Derived facts: {len(all_derived_facts)}")
        print(f"   Total facts: {len(current_facts)}")
        
        print(f"\n   📋 All derived facts:")
        for i, fact in enumerate(all_derived_facts, 1):
            print(f"      {i}. {fact.statement} (conf: {fact.confidence:.2f})")
        
        return len(all_derived_facts) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧠 WORKING NG_REASONER TEST")
    print("=" * 80)
    
    # Esegui test
    test_results = []
    
    test_results.append(("Reasoning Components", test_reasoning_components()))
    test_results.append(("Contradiction Detection", test_contradiction_detection()))
    test_results.append(("Conjunction Operations", test_conjunction_operations()))
    test_results.append(("Mini Reasoning Session", test_mini_reasoning_session()))
    
    print("\n" + "=" * 80)
    print("🧠 RISULTATI FINALI:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    success_rate = passed / len(test_results)
    print(f"\n🎯 SUCCESS RATE: {success_rate:.1%} ({passed}/{len(test_results)})")
    
    if success_rate >= 0.75:
        print("✅ NG_REASONER CORE: SUCCESSO!")
        print("   🧠 Componenti reasoning funzionanti")
        print("   🎯 Operatori logici implementati")
        print("   🔍 Contraddizioni rilevate")
        print("   ⚡ Mini-sessioni reasoning funzionanti")
        print("   ✅ Foundation per reasoning simbolico completa!")
    else:
        print("❌ NG_REASONER CORE: Miglioramenti necessari")
        print(f"   - Success rate: {success_rate:.1%} (target: ≥75%)")
    
    print(f"\n🚀 CONCLUSIONE:")
    if success_rate >= 0.75:
        print("   🎉 NGReasoner CORE è FUNZIONANTE!")
        print("   🧠 Reasoning simbolico base implementato")
        print("   🎯 Operatori logici (modus ponens, contraddizioni, congiunzioni)")
        print("   ✅ Foundation solida per Tree-of-Thought completo")
        print("   🚀 TERZO MODULO COGNITIVO completato!")
    else:
        print("   🔧 Debugging necessario per componenti core")
