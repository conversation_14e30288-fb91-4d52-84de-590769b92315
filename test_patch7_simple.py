#!/usr/bin/env python3
"""
Test semplificato per PATCH 7: Smart Body & Chain Split
Testa con body semplici per validare l'approccio
"""

from neuroglyph.core.encoder.encoder import <PERSON>GEncoder

def test_simple_while():
    """Test per while loop semplice."""
    print("🎯 TEST SEMPLICE: While Loop")
    
    code = """
def simple_loop():
    i = 0
    while i < 3:
        i += 1
    return i
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_simple_if_else():
    """Test per if/else semplice."""
    print("\n🎯 TEST SEMPLICE: If/Else")
    
    code = """
def check_positive(x):
    if x > 0:
        return True
    else:
        return False
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

if __name__ == "__main__":
    print("🎯 PATCH 7: Test Semplificato")
    print("=" * 60)
    
    # Test 1: While semplice
    result1 = test_simple_while()
    
    # Test 2: If/Else semplice
    result2 = test_simple_if_else()
    
    print("\n" + "=" * 60)
    print("🎯 RISULTATI FINALI PATCH 7 SEMPLIFICATO:")
    print(f"   Test 1 (While): Fidelity {result1.fidelity_score:.3f}, Compressione {result1.compression_ratio:.1f}%")
    print(f"   Test 2 (If/Else): Fidelity {result2.fidelity_score:.3f}, Compressione {result2.compression_ratio:.1f}%")
    
    avg_fidelity = (result1.fidelity_score + result2.fidelity_score) / 2
    avg_compression = (result1.compression_ratio + result2.compression_ratio) / 2
    
    print(f"\n🎯 MEDIA PATCH 7:")
    print(f"   - Fidelity media: {avg_fidelity:.3f}")
    print(f"   - Compressione media: {avg_compression:.1f}%")
    
    if avg_fidelity >= 0.9 and avg_compression >= 80:
        print("✅ PATCH 7 SUCCESSO: Target raggiunti!")
    else:
        print("❌ PATCH 7 FALLIMENTO: Target non raggiunti")
        print(f"   - Fidelity target: ≥0.9 (attuale: {avg_fidelity:.3f})")
        print(f"   - Compressione target: ≥80% (attuale: {avg_compression:.1f}%)")
