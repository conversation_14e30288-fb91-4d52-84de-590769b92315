#!/usr/bin/env python3
"""
Test per NEUROGLYPH Reasoner
Verifica reasoning simbolico Tree-of-Thought multi-hop
"""

from neuroglyph.cognitive.reasoner import NGReasoner
from neuroglyph.cognitive.reasoning_structures import LogicalFact, FactType
from neuroglyph.cognitive.logical_operators import LogicalOperators
from neuroglyph.cognitive.data_structures import MemoryContext, ParsedPrompt


def create_test_memory_context() -> MemoryContext:
    """Crea contesto di memoria di test."""
    return MemoryContext(
        symbols=[
            {"symbol": "⟨⟩§func_0§", "frequency": 5, "contexts": ["code"]},
            {"symbol": "◊§if_1§", "frequency": 3, "contexts": ["logic"]}
        ],
        episodes=[
            {
                "id": "ep1",
                "prompt": "Fix bug in function",
                "response": "def fix_bug(): pass",
                "relevance": 0.8,
                "priority_score": 0.7,
                "hits": 3
            }
        ],
        errors=[
            {
                "id": "err1", 
                "prompt": "Delete all files",
                "response": "Error: dangerous operation",
                "relevance": 0.9,
                "tags": ["error", "dangerous"]
            }
        ],
        examples=[
            {
                "id": "ex1",
                "prompt": "Create secure function",
                "response": "def secure_func(): validate_input()",
                "relevance": 0.7,
                "domain": "security",
                "symbols": ["⟨⟩§func_0§"]
            }
        ]
    )


def create_test_parsed_prompt(text: str, intents: list = None) -> ParsedPrompt:
    """Crea ParsedPrompt di test."""
    return ParsedPrompt(
        original_text=text,
        tokens=text.split(),
        segments=[],
        intents=intents or ["request"],
        symbols=["⟨⟩§func_0§"],
        metadata={}
    )


def test_logical_operators():
    """Test operatori logici base."""
    print("🧠 TEST LOGICAL OPERATORS")
    
    operators = LogicalOperators()
    
    # Test Modus Ponens
    facts = [
        LogicalFact(
            statement="it is raining",
            predicate="raining",
            fact_type=FactType.PREMISE,
            truth_value=True
        ),
        LogicalFact(
            statement="it is raining ⇒ the ground is wet",
            predicate="implication",
            fact_type=FactType.PREMISE,
            truth_value=True
        )
    ]
    
    steps = operators.modus_ponens(facts)
    
    print(f"   📊 Modus Ponens test:")
    print(f"      Input facts: {len(facts)}")
    print(f"      Generated steps: {len(steps)}")
    
    if steps:
        step = steps[0]
        print(f"      Conclusion: {step.output_facts[0].statement}")
        print(f"      Confidence: {step.confidence:.3f}")
        print(f"      Reasoning: {step.reasoning_text}")
    
    # Test Contradiction Detection
    contradiction_facts = [
        LogicalFact(
            statement="the system is secure",
            predicate="secure",
            fact_type=FactType.PREMISE,
            truth_value=True
        ),
        LogicalFact(
            statement="¬the system is secure",
            predicate="secure",
            fact_type=FactType.PREMISE,
            truth_value=False
        )
    ]
    
    contradiction_steps = operators.contradiction_detection(contradiction_facts)
    
    print(f"   📊 Contradiction Detection test:")
    print(f"      Input facts: {len(contradiction_facts)}")
    print(f"      Contradictions found: {len(contradiction_steps)}")
    
    if contradiction_steps:
        step = contradiction_steps[0]
        print(f"      Contradiction: {step.output_facts[0].statement}")
    
    return len(steps) > 0 and len(contradiction_steps) > 0


def test_basic_reasoning():
    """Test reasoning base."""
    print("\n🧠 TEST BASIC REASONING")
    
    reasoner = NGReasoner(max_depth=3, max_paths=3)
    
    # Crea contesto
    memory_context = create_test_memory_context()
    parsed_prompt = create_test_parsed_prompt(
        "Create a secure function that validates input",
        intents=["create", "security", "validation"]
    )
    
    # Esegui reasoning
    graph = reasoner.reason(memory_context, parsed_prompt)
    
    print(f"   📊 Basic reasoning results:")
    print(f"      Total paths: {len(graph.paths)}")
    print(f"      Total facts: {graph.total_facts}")
    print(f"      Total steps: {graph.total_steps}")
    print(f"      Max depth reached: {graph.max_depth_reached}")
    print(f"      Contradictions: {len(graph.contradictions)}")
    print(f"      Is complete: {graph.is_complete}")
    
    # Verifica best path
    best_path = graph.get_best_path()
    if best_path:
        print(f"      Best path score: {best_path.overall_score:.3f}")
        print(f"      Best path steps: {len(best_path.steps)}")
        print(f"      Best path facts: {len(best_path.current_facts)}")
    
    # Ottieni conclusioni
    conclusions = reasoner.get_conclusions(graph)
    print(f"      Conclusions: {len(conclusions)}")
    
    for i, conclusion in enumerate(conclusions[:3]):  # Mostra prime 3
        print(f"         {i+1}. {conclusion.statement} (conf: {conclusion.confidence:.2f})")
    
    return graph.total_facts > 0 and graph.total_steps > 0


def test_multi_hop_reasoning():
    """Test reasoning multi-hop."""
    print("\n🧠 TEST MULTI-HOP REASONING")
    
    reasoner = NGReasoner(max_depth=5, max_paths=4)
    
    # Crea contesto con chain logico
    memory_context = MemoryContext(
        examples=[
            {
                "id": "ex1",
                "prompt": "if input is invalid then raise error",
                "response": "def validate(x): if not valid(x): raise ValueError()",
                "relevance": 0.9,
                "domain": "validation"
            },
            {
                "id": "ex2", 
                "prompt": "if error is raised then log it",
                "response": "try: func() except Exception as e: log(e)",
                "relevance": 0.8,
                "domain": "error_handling"
            }
        ]
    )
    
    parsed_prompt = create_test_parsed_prompt(
        "Create function that validates input and handles errors properly",
        intents=["create", "validate", "error_handling"]
    )
    
    # Esegui reasoning
    graph = reasoner.reason(memory_context, parsed_prompt)
    
    print(f"   📊 Multi-hop reasoning results:")
    print(f"      Total paths: {len(graph.paths)}")
    print(f"      Max depth reached: {graph.max_depth_reached}")
    print(f"      Total steps: {graph.total_steps}")
    
    # Verifica che abbia fatto reasoning multi-hop
    multi_hop_paths = [path for path in graph.paths if path.depth >= 2]
    print(f"      Multi-hop paths: {len(multi_hop_paths)}")
    
    # Analizza best path
    best_path = graph.get_best_path()
    if best_path:
        print(f"      Best path depth: {best_path.depth}")
        print(f"      Best path score: {best_path.overall_score:.3f}")
        
        # Mostra chain di reasoning
        print(f"      Reasoning chain:")
        for i, step in enumerate(best_path.steps):
            print(f"         {i+1}. {step.rule_name}: {step.reasoning_text[:60]}...")
    
    return len(multi_hop_paths) > 0 and graph.max_depth_reached >= 2


def test_contradiction_handling():
    """Test gestione contraddizioni."""
    print("\n🧠 TEST CONTRADICTION HANDLING")
    
    reasoner = NGReasoner(max_depth=4, max_paths=3)
    
    # Crea contesto con contraddizioni
    memory_context = MemoryContext(
        examples=[
            {
                "id": "ex1",
                "prompt": "the system is secure",
                "response": "Security measures implemented",
                "relevance": 0.8,
                "domain": "security"
            }
        ],
        errors=[
            {
                "id": "err1",
                "prompt": "the system is not secure", 
                "response": "Security vulnerability found",
                "relevance": 0.9,
                "tags": ["security", "error"]
            }
        ]
    )
    
    parsed_prompt = create_test_parsed_prompt(
        "Analyze system security status",
        intents=["analyze", "security"]
    )
    
    # Esegui reasoning
    graph = reasoner.reason(memory_context, parsed_prompt)
    
    print(f"   📊 Contradiction handling results:")
    print(f"      Total contradictions: {len(graph.contradictions)}")
    print(f"      Paths with contradictions: {sum(1 for p in graph.paths if p.has_contradiction)}")
    
    # Verifica che le contraddizioni siano state rilevate
    contradictory_paths = [path for path in graph.paths if path.has_contradiction]
    print(f"      Contradictory paths: {len(contradictory_paths)}")
    
    if contradictory_paths:
        path = contradictory_paths[0]
        print(f"      Example contradictory path score: {path.overall_score:.3f}")
        print(f"      Coherence score (should be low): {path.coherence_score:.3f}")
    
    # Verifica che il best path non abbia contraddizioni
    best_path = graph.get_best_path()
    if best_path:
        print(f"      Best path has contradiction: {best_path.has_contradiction}")
        print(f"      Best path score: {best_path.overall_score:.3f}")
    
    return len(graph.contradictions) > 0


def test_reasoning_explanation():
    """Test spiegazione del reasoning."""
    print("\n🧠 TEST REASONING EXPLANATION")
    
    reasoner = NGReasoner(max_depth=3, max_paths=2)
    
    memory_context = create_test_memory_context()
    parsed_prompt = create_test_parsed_prompt(
        "Fix security vulnerability in authentication system",
        intents=["fix", "security", "authentication"]
    )
    
    # Esegui reasoning
    graph = reasoner.reason(memory_context, parsed_prompt)
    
    # Genera spiegazione
    explanation = reasoner.explain_reasoning(graph)
    
    print(f"   📊 Reasoning explanation:")
    print(f"      Explanation length: {len(explanation)} chars")
    print(f"      Contains steps: {'steps:' in explanation.lower()}")
    print(f"      Contains conclusions: {'conclusions:' in explanation.lower()}")
    
    # Mostra parte della spiegazione
    lines = explanation.split('\n')
    print(f"      First few lines:")
    for line in lines[:5]:
        print(f"         {line}")
    
    if len(lines) > 5:
        print(f"         ... ({len(lines) - 5} more lines)")
    
    return len(explanation) > 100  # Spiegazione ragionevole


def test_performance():
    """Test performance del reasoner."""
    print("\n🧠 TEST PERFORMANCE")
    
    import time
    
    reasoner = NGReasoner(max_depth=4, max_paths=3)
    
    memory_context = create_test_memory_context()
    parsed_prompt = create_test_parsed_prompt(
        "Optimize database query performance with caching",
        intents=["optimize", "database", "performance", "caching"]
    )
    
    # Test performance
    start_time = time.time()
    graph = reasoner.reason(memory_context, parsed_prompt)
    reasoning_time = time.time() - start_time
    
    print(f"   📊 Performance results:")
    print(f"      Reasoning time: {reasoning_time:.3f}s")
    print(f"      Facts processed: {graph.total_facts}")
    print(f"      Steps executed: {graph.total_steps}")
    print(f"      Facts per second: {graph.total_facts / reasoning_time:.1f}")
    print(f"      Steps per second: {graph.total_steps / reasoning_time:.1f}")
    
    # Performance target: <5s per reasoning session
    performance_ok = reasoning_time < 5.0
    print(f"      Performance target (<5s): {'✅' if performance_ok else '❌'}")
    
    return performance_ok


if __name__ == "__main__":
    print("🧠 NEUROGLYPH REASONER TEST")
    print("=" * 80)
    
    # Esegui tutti i test
    test_results = []
    
    test_results.append(("Logical Operators", test_logical_operators()))
    test_results.append(("Basic Reasoning", test_basic_reasoning()))
    test_results.append(("Multi-hop Reasoning", test_multi_hop_reasoning()))
    test_results.append(("Contradiction Handling", test_contradiction_handling()))
    test_results.append(("Reasoning Explanation", test_reasoning_explanation()))
    test_results.append(("Performance", test_performance()))
    
    print("\n" + "=" * 80)
    print("🧠 RISULTATI FINALI NG_REASONER:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    success_rate = passed / len(test_results)
    print(f"\n🎯 SUCCESS RATE: {success_rate:.1%} ({passed}/{len(test_results)})")
    
    if success_rate >= 0.8:
        print("✅ NG_REASONER TEST: SUCCESSO!")
        print("   🧠 Reasoning simbolico funzionante")
        print("   🎯 Tree-of-Thought multi-hop implementato")
        print("   ⚡ Performance <5s per sessione")
        print("   🔍 Contraddizioni rilevate e gestite")
        print("   📝 Spiegazioni reasoning generate")
        print("   ✅ Terzo modulo cognitivo funzionante!")
    else:
        print("❌ NG_REASONER TEST: Miglioramenti necessari")
        print(f"   - Success rate: {success_rate:.1%} (target: ≥80%)")
        print("   - Verificare operatori logici e reasoning")
    
    print(f"\n🚀 CONCLUSIONE NG_REASONER:")
    if success_rate >= 0.8:
        print("   🎉 NGReasoner è PRONTO per la produzione!")
        print("   🧠 Reasoning simbolico Tree-of-Thought funzionante")
        print("   🎯 NEUROGLYPH cognitive pipeline completa")
        print("   ✅ Parser → Prioritizer → Memory → Reasoner")
        print("   🚀 PRIMO LLM PENSANTE sempre più vicino!")
        print("   🎯 Prossimo step: NG_SELF_CHECK per validazione")
    else:
        print("   🔧 Ottimizzazioni necessarie prima di procedere")
        print("   📊 Analizzare operatori logici falliti")
        print("   🧠 Migliorare Tree-of-Thought algorithm")
