{"summary": {"total_tests": 6, "patches_applied": 2, "patches_correct": 0, "compilations_success": 4, "patch_application_rate": 0.3333333333333333, "patch_correctness_rate": 0.0, "compilation_success_rate": 0.6666666666666666, "avg_patch_time": 0.0003862084999997073}, "by_error_type": {"import": {"total": 2, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}, "syntax": {"total": 2, "applied": 1, "correct": 0, "application_rate": 0.5, "correctness_rate": 0.0}, "semantic": {"total": 2, "applied": 1, "correct": 0, "application_rate": 0.5, "correctness_rate": 0.0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_0c0lo0fq/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "import", "original_line": "import calendar", "modified_line": "# REMOVED: import calendar", "line_number": 10, "description": "Removed import at line 10"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_0c0lo0fq/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "syntax", "original_line": "the new headers via `get_new_headers()` and interpreting them appropriately. You", "modified_line": "the new headers via `get_new_headers(` and interpreting them appropriately. You", "line_number": 31, "description": "Removed closing parenthesis at line 31"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_0c0lo0fq/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "semantic", "original_line": "self._r = request", "modified_line": "self._r = request\nundefined_function_call()", "line_number": 36, "description": "Added undefined function call at line 36"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0004405419999997662, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749231844.885588', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 17, 44, 4, 885631, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_0c0lo0fq/requests-main/src/requests/auth.py", "error_injection": {"error_type": "import", "original_line": "import hashlib", "modified_line": "# REMOVED: import hashlib", "line_number": 8, "description": "Removed import at line 8"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_0c0lo0fq/requests-main/src/requests/auth.py", "error_injection": {"error_type": "syntax", "original_line": "def _basic_auth_str(username, password):", "modified_line": "def _basic_auth_str(username, password:", "line_number": 25, "description": "Removed closing parenthesis at line 25"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0003318749999996484, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749231844.885588', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 17, 44, 4, 885631, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_0c0lo0fq/requests-main/src/requests/auth.py", "error_injection": {"error_type": "semantic", "original_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"", "modified_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"\nundefined_function_call()", "line_number": 21, "description": "Added undefined function call at line 21"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}]}