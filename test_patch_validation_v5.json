{"summary": {"total_tests": 9, "patches_applied": 5, "patches_correct": 0, "compilations_success": 4, "patch_application_rate": 0.5555555555555556, "patch_correctness_rate": 0.0, "compilation_success_rate": 0.4444444444444444, "avg_patch_time": 0.00018924160000004519}, "by_error_type": {"import": {"total": 3, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}, "syntax": {"total": 3, "applied": 3, "correct": 0, "application_rate": 1.0, "correctness_rate": 0.0}, "semantic": {"total": 3, "applied": 2, "correct": 0, "application_rate": 0.6666666666666666, "correctness_rate": 0.0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "syntax", "original_line": "class MockRequest:", "modified_line": "class MockRequest", "line_number": 23, "description": "Removed colon at line 23"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0003104170000005624, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749239394.94278', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 19, 49, 54, 942824, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "semantic", "original_line": "self._r = request", "modified_line": "self._r = request\nundefined_function_call()", "line_number": 36, "description": "Added undefined function call at line 36"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00015675000000037187, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749239394.94278', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 19, 49, 54, 942824, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/auth.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/auth.py", "error_injection": {"error_type": "syntax", "original_line": "def _basic_auth_str(username, password):", "modified_line": "def _basic_auth_str(username, password)", "line_number": 25, "description": "Removed colon at line 25"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00013591699999970785, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749239394.94278', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 19, 49, 54, 942824, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/auth.py", "error_injection": {"error_type": "semantic", "original_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"", "modified_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"\nundefined_function_call()", "line_number": 21, "description": "Added undefined function call at line 21"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "syntax", "original_line": "if sys.platform == \"win32\":", "modified_line": "if sys.platform == \"win32\"", "line_number": 55, "description": "Removed colon at line 55"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0001832080000001568, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749239394.94278', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 19, 49, 54, 942824, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_vgwlh85u/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "semantic", "original_line": "if sys.platform == \"win32\":", "modified_line": "if sys.platform == \"win32\":\nundefined_function_call()", "line_number": 55, "description": "Added undefined function call at line 55"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00015991599999942707, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749239394.94278', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 19, 49, 54, 942824, tzinfo=datetime.timezone.utc), metadata={})"}]}