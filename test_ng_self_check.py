#!/usr/bin/env python3
"""
Test per NEUROGLYPH Self Check
Verifica validazione end-to-end del reasoning
"""

def test_validation_structures():
    """Test strutture di validazione."""
    print("🔍 TEST VALIDATION STRUCTURES")
    
    try:
        from neuroglyph.cognitive.validation_structures import (
            ValidationError, ValidationResult, ValidationMetrics, ValidationConfig,
            ValidationErrorType, ValidationSeverity, ValidationLevel
        )
        
        # Test ValidationError
        error = ValidationError(
            error_type=ValidationErrorType.AST_PARSE_ERROR,
            severity=ValidationSeverity.CRITICAL,
            level=ValidationLevel.SYNTAX,
            message="Test error",
            suggestions=["Fix syntax"]
        )
        
        print(f"✅ ValidationError created:")
        print(f"   Type: {error.error_type}")
        print(f"   Severity: {error.severity}")
        print(f"   Level: {error.level}")
        print(f"   Message: {error.message}")
        
        # Test ValidationResult
        result = ValidationResult()
        result.add_error(error)
        result.finalize()
        
        print(f"✅ ValidationResult:")
        print(f"   Is valid: {result.is_valid}")
        print(f"   Total errors: {result.total_errors}")
        print(f"   Duration: {result.validation_duration:.3f}s")
        
        # Test ValidationMetrics
        metrics = ValidationMetrics()
        metrics.coverage_percentage = 75.0
        metrics.avg_confidence = 0.8
        metrics.calculate_overall_score()
        
        print(f"✅ ValidationMetrics:")
        print(f"   Coverage: {metrics.coverage_percentage}%")
        print(f"   Avg confidence: {metrics.avg_confidence}")
        print(f"   Overall score: {metrics.overall_quality_score:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_syntax_validator():
    """Test validatore sintattico."""
    print("\n🔍 TEST SYNTAX VALIDATOR")
    
    try:
        from neuroglyph.cognitive.syntax_validator import SyntaxValidator
        from neuroglyph.cognitive.reasoning_structures import (
            ReasoningGraph, LogicalFact, FactType
        )
        
        # Crea validatore
        validator = SyntaxValidator()
        
        # Crea grafo di test
        graph = ReasoningGraph()
        
        # Aggiungi fatti validi
        valid_fact = LogicalFact(
            statement="it is raining",
            predicate="raining",
            fact_type=FactType.PREMISE,
            confidence=0.9
        )
        
        # Aggiungi fatto con errore
        invalid_fact = LogicalFact(
            statement="",  # Statement vuoto
            predicate="empty",
            fact_type=FactType.PREMISE,
            confidence=1.5  # Confidence fuori range
        )
        
        graph.add_initial_facts([valid_fact, invalid_fact])
        
        # Esegui validazione
        errors = validator.validate_syntax(graph)
        
        print(f"✅ Syntax validation completed:")
        print(f"   Errors found: {len(errors)}")
        
        for i, error in enumerate(errors[:3], 1):
            print(f"   {i}. {error.error_type.value}: {error.message}")
        
        return len(errors) > 0  # Dovrebbe trovare errori
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_semantic_validator():
    """Test validatore semantico."""
    print("\n🔍 TEST SEMANTIC VALIDATOR")
    
    try:
        from neuroglyph.cognitive.semantic_validator import SemanticValidator
        from neuroglyph.cognitive.reasoning_structures import (
            ReasoningGraph, LogicalFact, FactType
        )
        
        # Crea validatore
        validator = SemanticValidator()
        
        # Crea grafo con problemi semantici
        graph = ReasoningGraph()
        
        # Fatto derivato senza parent
        orphan_fact = LogicalFact(
            statement="orphan conclusion",
            predicate="conclusion",
            fact_type=FactType.DERIVED,
            derived_from=["non_existent_id"]  # Parent inesistente
        )
        
        graph.add_initial_facts([orphan_fact])
        
        # Esegui validazione
        errors = validator.validate_semantic(graph)
        
        print(f"✅ Semantic validation completed:")
        print(f"   Errors found: {len(errors)}")
        
        for i, error in enumerate(errors[:3], 1):
            print(f"   {i}. {error.error_type.value}: {error.message}")
        
        return len(errors) > 0  # Dovrebbe trovare errori
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logic_validator():
    """Test validatore logico."""
    print("\n🔍 TEST LOGIC VALIDATOR")
    
    try:
        from neuroglyph.cognitive.logic_validator import LogicValidator
        from neuroglyph.cognitive.reasoning_structures import (
            ReasoningGraph, LogicalFact, ReasoningStep, ReasoningPath,
            FactType, LogicalOperator
        )
        
        # Crea validatore
        validator = LogicValidator()
        
        # Crea grafo con step logico invalido
        graph = ReasoningGraph()
        
        fact1 = LogicalFact(
            statement="A",
            predicate="A",
            fact_type=FactType.PREMISE
        )
        
        fact2 = LogicalFact(
            statement="B",  # Non è A⇒B
            predicate="B",
            fact_type=FactType.PREMISE
        )
        
        # Step Modus Ponens invalido (manca implicazione)
        invalid_step = ReasoningStep(
            operation=LogicalOperator.DEDUCE,
            rule_name="modus_ponens",
            input_facts=[fact1, fact2],
            output_facts=[LogicalFact("C", "C", fact_type=FactType.DERIVED)]
        )
        
        path = ReasoningPath()
        path.current_facts = [fact1, fact2]
        path.add_step(invalid_step)
        
        graph.add_initial_facts([fact1, fact2])
        graph.add_path(path)
        
        # Esegui validazione
        errors = validator.validate_logic(graph)
        
        print(f"✅ Logic validation completed:")
        print(f"   Errors found: {len(errors)}")
        
        for i, error in enumerate(errors[:3], 1):
            print(f"   {i}. {error.error_type.value}: {error.message}")
        
        return len(errors) > 0  # Dovrebbe trovare errori
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ng_self_check():
    """Test NGSelfCheck completo."""
    print("\n🔍 TEST NG_SELF_CHECK")
    
    try:
        from neuroglyph.cognitive.self_check import NGSelfCheck
        from neuroglyph.cognitive.reasoning_structures import (
            ReasoningGraph, LogicalFact, ReasoningStep, ReasoningPath,
            FactType, LogicalOperator
        )
        
        # Crea self-checker
        self_check = NGSelfCheck()
        
        # Crea grafo di test valido
        graph = ReasoningGraph()
        
        # Fatti validi
        fact1 = LogicalFact(
            statement="it is raining",
            predicate="raining",
            fact_type=FactType.PREMISE,
            confidence=0.9
        )
        
        fact2 = LogicalFact(
            statement="it is raining ⇒ the ground is wet",
            predicate="implication",
            fact_type=FactType.PREMISE,
            confidence=0.8
        )
        
        # Fatto derivato valido
        derived_fact = LogicalFact(
            statement="the ground is wet",
            predicate="wet",
            fact_type=FactType.DERIVED,
            confidence=0.7,
            derived_from=[fact1.fact_id, fact2.fact_id],
            derivation_rule="modus_ponens"
        )
        
        # Step valido
        valid_step = ReasoningStep(
            operation=LogicalOperator.DEDUCE,
            rule_name="modus_ponens",
            input_facts=[fact1, fact2],
            output_facts=[derived_fact],
            confidence=0.7
        )
        
        # Path valido
        path = ReasoningPath()
        path.current_facts = [fact1, fact2]
        path.add_step(valid_step)
        
        graph.add_initial_facts([fact1, fact2])
        graph.add_path(path)
        
        # Esegui self-check completo
        result = self_check.self_check(graph)
        
        print(f"✅ Self-check completed:")
        print(f"   Validation passed: {result.validation_passed}")
        print(f"   Total errors: {result.total_errors}")
        print(f"   Quality score: {result.metrics.overall_quality_score:.3f}")
        print(f"   Coverage: {result.metrics.coverage_percentage:.1f}%")
        print(f"   Duration: {result.validation_duration:.3f}s")
        
        # Test validazioni rapide
        syntax_ok = self_check.validate_syntax(graph)
        semantic_ok = self_check.validate_semantic(graph)
        logic_ok = self_check.validate_logic(graph)
        
        print(f"✅ Quick validations:")
        print(f"   Syntax: {'✅' if syntax_ok else '❌'}")
        print(f"   Semantic: {'✅' if semantic_ok else '❌'}")
        print(f"   Logic: {'✅' if logic_ok else '❌'}")
        
        return result.validation_passed
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_report():
    """Test generazione report."""
    print("\n🔍 TEST VALIDATION REPORT")
    
    try:
        from neuroglyph.cognitive.self_check import NGSelfCheck
        from neuroglyph.cognitive.reasoning_structures import (
            ReasoningGraph, LogicalFact, FactType
        )
        
        # Crea self-checker
        self_check = NGSelfCheck()
        
        # Crea grafo con alcuni errori
        graph = ReasoningGraph()
        
        # Fatto con errore
        error_fact = LogicalFact(
            statement="",  # Statement vuoto
            predicate="empty",
            fact_type=FactType.PREMISE,
            confidence=1.2  # Confidence fuori range
        )
        
        graph.add_initial_facts([error_fact])
        
        # Esegui validazione
        result = self_check.self_check(graph)
        
        # Genera report
        report = self_check.generate_report(result)
        
        print(f"✅ Validation report generated:")
        print(f"   Report length: {len(report)} chars")
        print(f"   Contains errors: {'ERROR' in report}")
        print(f"   Contains metrics: {'METRICS' in report}")
        
        # Mostra parte del report
        lines = report.split('\n')
        print(f"   First few lines:")
        for line in lines[:10]:
            print(f"      {line}")
        
        if len(lines) > 10:
            print(f"      ... ({len(lines) - 10} more lines)")
        
        return len(report) > 500  # Report ragionevole
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """Test performance validazione."""
    print("\n🔍 TEST PERFORMANCE")
    
    try:
        import time
        from neuroglyph.cognitive.self_check import NGSelfCheck
        from neuroglyph.cognitive.reasoning_structures import (
            ReasoningGraph, LogicalFact, FactType
        )
        
        # Crea self-checker
        self_check = NGSelfCheck()
        
        # Crea grafo più grande
        graph = ReasoningGraph()
        facts = []
        
        # Genera molti fatti
        for i in range(20):
            fact = LogicalFact(
                statement=f"fact_{i}",
                predicate=f"pred_{i}",
                fact_type=FactType.PREMISE,
                confidence=0.8
            )
            facts.append(fact)
        
        graph.add_initial_facts(facts)
        
        # Test performance
        start_time = time.time()
        result = self_check.self_check(graph)
        validation_time = time.time() - start_time
        
        print(f"✅ Performance test:")
        print(f"   Facts processed: {len(facts)}")
        print(f"   Validation time: {validation_time:.3f}s")
        print(f"   Facts per second: {len(facts) / validation_time:.1f}")
        print(f"   Target (<10s): {'✅' if validation_time < 10.0 else '❌'}")
        
        return validation_time < 10.0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 NEUROGLYPH SELF-CHECK TEST")
    print("=" * 80)
    
    # Esegui tutti i test
    test_results = []
    
    test_results.append(("Validation Structures", test_validation_structures()))
    test_results.append(("Syntax Validator", test_syntax_validator()))
    test_results.append(("Semantic Validator", test_semantic_validator()))
    test_results.append(("Logic Validator", test_logic_validator()))
    test_results.append(("NG Self Check", test_ng_self_check()))
    test_results.append(("Validation Report", test_validation_report()))
    test_results.append(("Performance", test_performance()))
    
    print("\n" + "=" * 80)
    print("🔍 RISULTATI FINALI NG_SELF_CHECK:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    success_rate = passed / len(test_results)
    print(f"\n🎯 SUCCESS RATE: {success_rate:.1%} ({passed}/{len(test_results)})")
    
    if success_rate >= 0.8:
        print("✅ NG_SELF_CHECK TEST: SUCCESSO!")
        print("   🔍 Validazione end-to-end funzionante")
        print("   🎯 Syntax, Semantic, Logic validators operativi")
        print("   📊 Metriche qualità implementate")
        print("   📋 Report dettagliati generati")
        print("   ⚡ Performance <10s per validazione")
        print("   ✅ Quarto modulo cognitivo funzionante!")
    else:
        print("❌ NG_SELF_CHECK TEST: Miglioramenti necessari")
        print(f"   - Success rate: {success_rate:.1%} (target: ≥80%)")
        print("   - Verificare validatori e strutture dati")
    
    print(f"\n🚀 CONCLUSIONE NG_SELF_CHECK:")
    if success_rate >= 0.8:
        print("   🎉 NGSelfCheck è PRONTO per la produzione!")
        print("   🔍 Validazione end-to-end completa")
        print("   🎯 Quality control automatico")
        print("   ✅ Parser → Prioritizer → Memory → Reasoner → SelfCheck")
        print("   🚀 PIPELINE COGNITIVA QUASI COMPLETA!")
        print("   🎯 Prossimo step: NG_SANDBOX per esecuzione sicura")
    else:
        print("   🔧 Debugging necessario per validatori")
        print("   📊 Verificare metriche e soglie qualità")
