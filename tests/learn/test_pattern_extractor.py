"""
Test Pattern Extractor - Fase 3.0
Target: precision ≥ 0.8 su dataset sintetico
"""

import time
import pytest
import sqlite3
import json
import tempfile
import os
from typing import List, Dict, Any

# Import dei moduli NEUROGLYPH
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from neuroglyph.learning.pattern_extractor import PatternExtractor, ExtractedPattern
from neuroglyph.cognitive.performance_storage import PerformanceStorage


class TestPatternExtractor:
    """Test suite per Pattern Extractor."""
    
    @pytest.fixture
    def temp_db(self):
        """Fixture per database temporaneo."""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        try:
            os.unlink(path)
        except:
            pass
    
    @pytest.fixture
    def pattern_extractor(self, temp_db):
        """Fixture per pattern extractor."""
        return PatternExtractor(
            db_path=temp_db,
            min_support=3,
            min_confidence=0.6,
            extraction_interval=3600  # 1 ora per test
        )
    
    @pytest.fixture
    def synthetic_data(self, temp_db):
        """Fixture per dataset sintetico con 100 patch."""
        # Crea PerformanceStorage per inserire dati
        storage = PerformanceStorage(temp_db)
        
        # Pattern sintetici noti
        patterns = [
            ("syntax_error", "syntax_repair", 0.8),      # 80% successo
            ("import_error", "import_addition", 0.9),    # 90% successo  
            ("variable_error", "variable_initialization", 0.7),  # 70% successo
            ("timeout_error", "timeout_adjustment", 0.6),  # 60% successo
            ("memory_error", "memory_optimization", 0.5),  # 50% successo (sotto soglia)
        ]
        
        # Genera 100 patch records
        patch_count = 0
        for error_type, patch_strategy, success_rate in patterns:
            # Genera 20 patch per pattern
            for i in range(20):
                patch_count += 1
                success = (i / 20) < success_rate
                status = "successful" if success else "failed"
                
                metadata = {
                    'patch_id': f'patch_{patch_count}',
                    'error_id': f'error_{patch_count}',
                    'patch_strategy': patch_strategy,
                    'patch_type': 'code_fix',
                    'confidence': 0.8 if success else 0.3,
                    'risk_level': 'low'
                }
                
                storage.append_patch_history(
                    error_type=error_type,
                    status=status,
                    metadata=metadata
                )
        
        # Flush per assicurare persistenza
        storage.force_flush()
        storage.shutdown()
        
        return {
            'total_patches': patch_count,
            'expected_patterns': [p for p in patterns if p[2] >= 0.6],  # Solo sopra soglia confidence
            'patterns': patterns
        }
    
    def test_pattern_extraction_basic(self, pattern_extractor, synthetic_data):
        """Test estrazione pattern base."""
        # Forza estrazione
        stats = pattern_extractor.extract_patterns()
        
        assert stats['status'] == 'success'
        assert stats['records_processed'] == 100
        assert stats['new_patterns'] >= 3  # Almeno 3 pattern sopra soglia
        
        # Verifica pattern estratti
        patterns = pattern_extractor.get_top_patterns(limit=10)
        assert len(patterns) >= 3
        
        # Verifica criteri mining
        for pattern in patterns:
            assert pattern.support >= pattern_extractor.min_support
            assert pattern.confidence >= pattern_extractor.min_confidence
    
    def test_pattern_precision(self, pattern_extractor, synthetic_data):
        """Test precision ≥ 0.8 su pattern noti."""
        # Estrai pattern
        pattern_extractor.extract_patterns()
        patterns = pattern_extractor.get_top_patterns(limit=10)
        
        # Verifica precision
        expected_patterns = synthetic_data['expected_patterns']
        found_patterns = []
        
        for pattern in patterns:
            for error_type, patch_strategy, expected_confidence in expected_patterns:
                if (error_type in pattern.error_types and 
                    patch_strategy in pattern.patch_strategies):
                    found_patterns.append((error_type, patch_strategy))
                    
                    # Verifica confidence vicina a quella attesa (±0.1)
                    assert abs(pattern.confidence - expected_confidence) <= 0.15
        
        # Calcola precision
        precision = len(found_patterns) / len(patterns) if patterns else 0
        print(f"✅ Precision: {precision:.3f}")
        print(f"   - Pattern trovati: {len(found_patterns)}")
        print(f"   - Pattern totali: {len(patterns)}")
        print(f"   - Pattern attesi: {len(expected_patterns)}")
        
        # Target: precision ≥ 0.8
        assert precision >= 0.8, f"Precision troppo bassa: {precision:.3f} < 0.8"
    
    def test_pattern_support_confidence(self, pattern_extractor, synthetic_data):
        """Test criteri support e confidence."""
        pattern_extractor.extract_patterns()
        patterns = pattern_extractor.get_top_patterns(limit=10)
        
        for pattern in patterns:
            # Verifica support ≥ 3
            assert pattern.support >= 3, f"Support troppo basso: {pattern.support}"
            
            # Verifica confidence ≥ 0.6
            assert pattern.confidence >= 0.6, f"Confidence troppo bassa: {pattern.confidence}"
            
            # Verifica lift > 0
            assert pattern.lift > 0, f"Lift invalido: {pattern.lift}"
    
    def test_pattern_versioning(self, pattern_extractor, synthetic_data):
        """Test versioning pattern."""
        # Prima estrazione
        stats1 = pattern_extractor.extract_patterns()
        patterns_before = len(pattern_extractor.patterns)
        
        # Aggiungi più dati per stesso pattern
        storage = PerformanceStorage(pattern_extractor.db_path)
        
        for i in range(10):
            metadata = {
                'patch_strategy': 'syntax_repair',
                'patch_type': 'code_fix'
            }
            storage.append_patch_history(
                error_type='syntax_error',
                status='successful',
                metadata=metadata
            )
        
        storage.force_flush()
        storage.shutdown()
        
        # Seconda estrazione
        stats2 = pattern_extractor.extract_patterns()
        patterns_after = len(pattern_extractor.patterns)
        
        # Verifica aggiornamento invece di duplicazione
        assert patterns_after == patterns_before, "Pattern duplicati invece di aggiornati"
        assert stats2['updated_patterns'] > 0, "Nessun pattern aggiornato"
        
        # Verifica versioning
        syntax_patterns = [p for p in pattern_extractor.patterns.values() 
                          if 'syntax_error' in p.error_types]
        if syntax_patterns:
            pattern = syntax_patterns[0]
            assert pattern.version > 1, f"Version non incrementata: {pattern.version}"
    
    def test_circuit_breaker(self, pattern_extractor):
        """Test circuit breaker su false-positive burst."""
        # Simula 15 fallimenti consecutivi
        for _ in range(15):
            pattern_extractor._update_circuit_breaker(False)
        
        # Verifica attivazione circuit breaker
        assert pattern_extractor._is_circuit_breaker_active()
        
        # Verifica che estrazione sia bloccata
        stats = pattern_extractor.extract_patterns()
        assert stats['status'] == 'circuit_breaker_active'
    
    def test_incremental_extraction(self, pattern_extractor, synthetic_data):
        """Test estrazione incrementale."""
        # Prima estrazione
        stats1 = pattern_extractor.extract_patterns()
        last_ts = pattern_extractor.last_extracted_ts
        
        # Aspetta un po'
        time.sleep(0.1)
        
        # Seconda estrazione (senza nuovi dati)
        stats2 = pattern_extractor.extract_patterns()
        
        assert stats2['status'] == 'no_new_data'
        assert stats2['records'] == 0
        assert pattern_extractor.last_extracted_ts > last_ts
    
    def test_pattern_similarity(self, pattern_extractor):
        """Test rilevamento pattern simili."""
        # Crea pattern manualmente
        pattern1 = ExtractedPattern(
            pattern_id="test_1",
            pattern_name="syntax_error→syntax_repair",
            error_types=["syntax_error"],
            patch_strategies=["syntax_repair"],
            support=5,
            confidence=0.8
        )
        
        pattern_extractor.patterns["test_1"] = pattern1
        
        # Verifica rilevamento similarità
        similar = pattern_extractor._find_similar_pattern("syntax_error", "syntax_repair")
        assert similar is not None
        assert similar.pattern_id == "test_1"
        
        # Verifica non-similarità
        not_similar = pattern_extractor._find_similar_pattern("import_error", "variable_init")
        assert not_similar is None
    
    def test_top_patterns_sorting(self, pattern_extractor, synthetic_data):
        """Test ordinamento top pattern."""
        pattern_extractor.extract_patterns()
        
        # Test ordinamento per confidence
        by_confidence = pattern_extractor.get_top_patterns(limit=5, sort_by="confidence")
        confidences = [p.confidence for p in by_confidence]
        assert confidences == sorted(confidences, reverse=True)
        
        # Test ordinamento per support
        by_support = pattern_extractor.get_top_patterns(limit=5, sort_by="support")
        supports = [p.support for p in by_support]
        assert supports == sorted(supports, reverse=True)
    
    def test_stats_collection(self, pattern_extractor, synthetic_data):
        """Test raccolta statistiche."""
        pattern_extractor.extract_patterns()
        
        stats = pattern_extractor.get_stats()
        
        assert 'total_patterns' in stats
        assert 'extraction_count' in stats
        assert 'avg_confidence' in stats
        assert 'avg_support' in stats
        assert 'circuit_breaker' in stats
        
        assert stats['total_patterns'] > 0
        assert stats['extraction_count'] > 0
        assert 0 <= stats['avg_confidence'] <= 1
        assert stats['avg_support'] >= 0


if __name__ == "__main__":
    # Run tests direttamente
    import tempfile
    
    print("🚀 Running Pattern Extractor tests...")
    
    # Test con database temporaneo
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
        temp_db = f.name
    
    try:
        test = TestPatternExtractor()
        
        # Setup
        pe = PatternExtractor(temp_db, min_support=3, min_confidence=0.6)
        
        # Crea dati sintetici
        storage = PerformanceStorage(temp_db)
        for i in range(50):
            success = i % 4 != 0  # 75% successo
            storage.append_patch_history(
                error_type="test_error",
                status="successful" if success else "failed",
                metadata={'patch_strategy': 'test_strategy'}
            )
        storage.force_flush()
        storage.shutdown()
        
        # Test estrazione
        stats = pe.extract_patterns()
        print(f"✅ Estrazione: {stats}")
        
        patterns = pe.get_top_patterns(5)
        print(f"✅ Pattern trovati: {len(patterns)}")
        
        for pattern in patterns:
            print(f"   - {pattern.pattern_name}: support={pattern.support}, confidence={pattern.confidence:.3f}")
        
        pe.shutdown()
        
    finally:
        try:
            os.unlink(temp_db)
        except:
            pass
    
    print("✅ Test completati!")
