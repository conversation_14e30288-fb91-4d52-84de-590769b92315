{"total_symbols": 1854, "domains": ["self", "causal_reasoning", "geometry", "algebra", "logic", "distributed_systems", "ai", "concurrency_advanced", "memory", "formal_verification", "operator", "advanced_coding", "math", "set_theory", "structure", "type_theory", "machine_learning", "neural_architectures", "security", "mathematical_operators", "code", "geometric_shapes", "meta_programming", "problem_solving", "logical_operators", "cognitive_modeling", "philosophical_concepts", "arrows", "control_flow", "distributed", "performance", "agents", "combinatorics", "cognitive_architectures", "misc_technical", "topology", "quantum_computing", "physics_notation", "creative_thinking", "reasoning", "supplemental_math", "symbolic_ai", "analysis", "extension", "meta", "number_forms", "technical_symbols", "quantum", "temporal", "mathematical_structures", "architecture", "category_theory", "statistics", "cognition", "calculus", "letterlike_symbols", "reserved_expansion"], "symbols_by_domain": {"advanced_coding": ["⦟", "⌮", "⨑", "≅", "✗", "⧹", "✿", "⬤", "⬛", "⬧", "✂", "⬮", "⨸", "⧁", "⧀", "⦹", "⦶", "✪", "⥁", "⫏", "⌲", "∮", "⌵", "⌭", "⦙", "⩎", "⫬", "⤓", "✴", "✵", "✳", "⌫", "⌦", "⧳", "✥", "✢", "⪥", "❤", "✔", "❆", "⭙", "❗", "❚", "∻", "⬣", "⨚", "⨼", "⩄", "⩃", "⦓", "⯨", "⧘", "⤌", "⭅", "❘", "⬳", "✠", "⦛", "⨺", "⨩", "⨫", "⨬", "≂", "⨂", "⯜", "⤤", "⬀", "✲", "✫", "⌥", "∥", "✏", "⯛", "✯", "⨭", "⨹", "⨥", "≟", "✊", "⮐", "⦣", "⮶", "⮷", "〉", "⥇", "⭆", "✞", "⫟", "⧢", "✶", "✺", "⌳", "⩘", "⩡", "⌣", "❄", "⤦", "❇", "⦠", "⫍", "⬒", "✡", "✩", "∴", "⬱", "❅", "⧕", "⯗", "∭", "⩁", "⭡", "⯦", "○", "✧", "⬡", "⬭", "◁", "⬨", "⬯", "⬞", "≀", "⌧", "⤨", "❫", "⭌", "⥐", "⍃", "✾", "⤾", "⤿", "⧍", "⭠", "⍂", "⍣", "❊", "⧃", "⭁", "⌹", "❃", "❜", "⤱", "⩊", "⍀", "➬", "⨣", "⩍", "⭀", "⌤", "⥣", "⦜", "⭄", "❝", "⤕", "⥚", "⩐", "⋚", "⥢", "⮟", "⌼", "⍙", "❐", "⤻", "⥙", "⦴", "⨻", "⬻", "⍒", "⥂", "⦩", "⪒", "⮙", "❑", "⤪", "⦪", "⌿", "⥀", "⥉", "⥘", "⪨", "⭼", "⤳", "⥫", "⦲", "⫉", "⬸", "➞", "⭭", "⨴", "⬵", "⍆", "⍘", "❠", "❡", "⤑", "⤢", "⥛", "⥝", "⥡", "⦐", "⦖", "⦮", "⦺", "⧩", "⧷", "⭈", "⮈", "⯅", "⯮", "✼", "⤰", "⥸", "⦯", "⧂", "⫄", "⤷", "⥟", "⌷", "✷", "⤲", "⤶", "⨰", "⬶"], "agents": ["⢟", "⡨", "ꭐ", "⟷", "𝚳", "𝛘", "𝐡", "𝑜", "𝒪", "⟖", "𝟫", "ꜥ", "𝗀", "ⷸ", "𝛽", "𝓔", "Ꞔ", "𝒍", "𝚢", "𝘉", "𝕃", "𝘓", "𝝭", "𝕚"], "ai": ["⌽", "⍚", "⍠", "⍤", "≐", "≆", "⏟", "⑮", "⑰", "⊕", "∁", "⋺", "⒌", "∕", "⋐", "⊤", "↯", "–", "⋜", "≕", "₣", "≎", "⋛", "≥", "⎀", "⎮", "∩", "₭", "⋋", "⇜", "∡", "∓", "⊭", "⊶", "∂", "⏽", "∶", "⏎", "”", "ⅸ", "⊐", "⏤", "∄", "⌜", "⏠", "≋", "⎌", "⋰", "⋘"], "algebra": ["℻", "⇐", "⇴", "↬", "↾"], "analysis": ["ℙ", "ℽ", "⇤", "↜", "⇶"], "architecture": ["⣲", "⣸", "⠨", "ⷩ", "ⷡ", "Ɡ", "ꞏ", "⸨", "𝒞", "𝒴", "⸬", "𝔙", "𝘥", "ꟃ", "𝔅", "𝕫", "𝟷", "𝖦", "𝑷"], "arrows": ["⇇", "ℎ", "↣", "↑", "ⅅ"], "calculus": ["⅍", "ℨ", "№", "℺", "⇏"], "category_theory": ["⍺", "⧸", "➿", "≷", "➚", "≶", "⩹", "⧶", "⥹", "⯟", "⍷", "⭶", "≸", "❸", "⍶", "❶", "❷", "⥷", "⫲", "❹", "❺", "⥺", "⭹", "⍹", "⩸"], "causal_reasoning": ["🚛", "🚗", "🚼", "🛄", "🛀", "🛁", "🛉", "🚸", "🛈", "🛋", "🛃", "🚚", "🚪", "🛊", "🛅", "🚹", "🚝", "🚞", "🚫", "🚭", "🚘", "🚖", "🛂", "🛇", "🚙", "🚻", "🚿", "🚬", "🚕", "🚽", "🚜", "🛆", "🚩", "🚾", "🚺"], "code": ["②", "⑬", "⇫", "⇻", "⋩", "⍄", "⍋", "⋄", "‡", "ℝ", "∎", "∜", "≑", "―", "⇔", "∑", "⑻", "⑸", "≾", "∷", "⊾", "ⅴ", "⏱", "⁸", "⇳", "⇡", "⅑"], "cognition": ["⍑", "⍱", "⌒", "₳", "ℜ", "⌞", "⠃", "⡕", "⢮", "⊙", "∲", "⊵", "⋽", "⏋", "⊮", "‖", "ℕ", "⋵", "⋕", "≚", "⎽", "⌛", "≓", "∫", "ₛ", "↮", "⁅", "⎡", "↢", "⋦", "𝔪", "𝑍", "⋀", "⊈", "₪", "≉", "≠", "⋬", "≁", "⒔", "⒜", "⎭", "⸊", "⇢", "↠", "Ⅳ", "⊓", "⊏", "≣", "⊊", "≽", "⁵", "⊋", "₮", "⊥", "⋮", "ꝙ", "𝙯", "𝗙", "ꝿ", "𝝟", "ⱻ", "𝜱", "Ɫ", "𝑶", "𝟜"], "cognitive_architectures": ["╜"], "cognitive_modeling": ["⮽", "⯁", "⯀", "⮿", "⮾", "⪼", "➕", "⎼", "⩧", "⮲", "⪽", "⪿", "⫀", "➼", "⎿", "⏀", "⏁", "⫁", "⧨", "➜", "⎾", "➫", "➾", "⏂", "⏄", "⫂", "⫃"], "combinatorics": ["ℿ", "↞", "ℛ", "⇂", "⇸"], "concurrency_advanced": ["⯥", "⎊", "⎄", "⎃", "⪆", "⎈", "⭾", "⪉", "⪅", "⫝", "⊄", "⫨", "⊂", "⧾", "⯺", "⥾", "⍿", "⯎", "❾", "➃", "⪃", "⎅", "➄", "⩨", "⮄", "❿", "⩾", "⎉", "➅", "➆", "➇", "➈", "➉", "➊", "⪇", "⪈", "⪊", "⫯", "⭷", "⮅", "⮆", "⮇", "⮡", "➀", "⎂", "➂", "⮁", "⮂", "➁"], "control_flow": ["✈", "⨿", "⌶", "◄", "◀", "⬥", "∙", "⦿", "◌", "⌄", "≙", "⌂", "⨛", "◘", "⦅", "⬅", "⩒", "◡", "✎", "⨁", "⌆", "⨧", "⦰", "⌐", "⌉", "❍", "✰", "∍", "⬋", "✨", "∢", "⊃", "⌎", "⨇", "⦽", "⌃", "◦", "❀", "✅", "▱", "⦇", "⨡", "⤁", "⩉", "⦳", "◕", "◭", "⬎", "⤅", "◛", "◳", "⥖", "⦕", "⥈", "◔", "⤼", "◪", "◃", "⤮", "⨱", "⥃", "⬼", "⋳", "⊉"], "creative_thinking": ["🝛", "🞷", "🞆", "🞉", "🟊", "🟉", "🞯", "🞵", "🟋", "🞶", "🟍", "🟂", "🞈", "🝚", "🝜", "🝝", "🞊", "🟀", "🟁", "🟇", "🟏", "🟐", "🟑", "🟓"], "distributed": ["₡", "℃", "‗", "⏸", "⋞", "↚", "₤", "⁎", "⋯", "≪", "⊎", "⋃", "≱", "⊅", "≯", "⋢", "⑵", "⒃", "⅌", "⊰", "⁏", "∽", "Ⅻ", "⊒", "⋥", "⋆", "≿", "∃", "ⅎ", "℣"], "distributed_systems": ["❉", "◾", "⭑", "⧒", "⦷", "⦾", "✣", "⪧", "⭘", "⭕", "✮", "⭖", "⨏", "⦑", "⬄", "⤎", "∧", "∨", "✃", "❙", "⬁", "⦧", "⫚", "⨤", "⦥", "⤚", "⮕", "⤜", "⧴", "⩗", "⪾", "✻", "⯊", "⏜", "✹", "⩖", "⩂", "✁", "⧖", "⥎", "⥬", "◈", "❂", "⤬", "⦨", "⩈", "⩞", "⫵", "⬕", "⬺", "⭚", "⮢", "⍓", "⧎", "⤇", "⩜", "⭣", "⤆", "⭢", "⦏", "⤫", "⍗", "⤠", "⤯", "⭛", "⍔", "❕", "⥓", "⮊"], "extension": ["🜁", "🝪", "🝅", "🝗", "🝂", "🝃", "🝙", "🝠", "🜃", "🜂", "🝉", "🝋", "🜲", "🝭", "🜔", "🜛", "🝔", "🝇", "🜍", "🜿", "🝕", "🜊", "🜄", "🝊", "🞟", "🞗", "🞌", "🞘", "🞞", "🞱", "🞤", "🞫", "⊘", "⊖", "⊗", "⫽", "🠿", "🠷", "🡇", "🢓", "🞧", "🞮", "🟆", "🞾", "🞲", "🞬", "🟌", "🞇", "🟫", "🟢", "🡘", "🠴", "🟄", "🞎", "🞼", "🞰", "🞏", "🟖", "🡔", "⯽", "⏾", "🠾", "⯻", "🞡", "🞨", "⫸", "⊨", "🠽", "🡅", "🞥", "🞒", "🢣", "🝐", "⯶", "🞛", "🡨", "🜵", "🢀", "🜷", "🟈", "⯴", "⯸", "🠚", "🢁", "🜳", "🞕", "🞠", "🢚", "🜝", "🠦", "🢘", "🢥", "🟎", "🠃", "🢙", "🜼", "🝘", "🡴", "🝓", "🢔", "⫻", "🢛", "🜱", "🢱", "🞴", "🠝", "⯷", "🜘", "🠉", "🟔", "🝎", "🠢", "🝩", "🠪", "🟃", "⯵", "🞂", "🜉", "⋻", "🠘", "🟰", "🜢", "🠇", "🝏", "🟒", "🠛", "🡼", "🝵", "🢕", "🠫", "🠳", "🞳", "🝢", "🢧", "🠭", "🜭", "🞹", "🜎", "🢂", "⯳", "🜈", "🜗", "🠆", "🡩", "🡠", "🞚", "🜏", "🠲", "🜾", "🝁", "🞀", "🢃", "🠂", "🠈", "🝲", "🡵", "🞿", "🟅", "🠣", "🝼"], "formal_verification": ["⍳", "⍵", "⍴", "⩴", "⩱", "⩮", "≮", "⩲", "⧵", "⩵", "⍲", "❲", "≴", "❳", "❵", "⥪", "⩳", "⭳", "⍯", "❯", "⩯", "≰", "⍰", "⪁", "⥱", "⍮", "⭮"], "geometric_shapes": ["⇘", "⇪", "↴"], "geometry": ["⇓", "⇥", "↝", "ℬ", "℈", "⇷", "⇁"], "letterlike_symbols": ["ⅇ", "⇉", "ℴ", "↷", "⇭"], "logic": ["⦞", "≃", "∵", "⍾", "✬", "▰", "⌍", "⌟", "⌌", "⏡", "◎", "◐", "◓", "⊳", "≘", "⌀", "⬖", "⬗", "∣", "⋇", "⋅", "⩔", "„", "ℂ", "⋱", "⤋", "↡", "⌁", "⋝", "≍", "⨍", "⊩", "≳", "✚", "⧜", "⨎", "⌨", "⬲", "⌊", "〈", "←", "≲", "ₒ", "↹", "⇍", "⇎", "⇞", "≒", "⊯", "⋭", "⌾", "⍈", "⍊", "⍐", "⍖", "⍛", "⍫", "⎋", "⎓", "⏆", "⩛", "◊", "≫", "₦", "⊼", "⎘", "‑", "≄", "≭", "⋪", "℥", "✙", "⑽", "⋔", "⌘", "℞", "⌋", "◗", "⊢", "⦄", "≗", "ℐ", "⌓", "∿", "⋾", "∊", "⌑", "⁺", "∯", "⦀", "⩕", "✐", "◹", "⤒", "⤊", "⌚", "⌇", "◇", "◅", "◽", "◻", "▦", "⋉", "⬽", "❏", "⦈", "⨒", "⋨", "◰", "⤧", "⬑", "⤹", "◂", "⭃", "⨔", "◲", "⤈", "⦋", "⬏", "⭞", "▩"], "logical_operators": ["↻", "ℸ", "℔", "↩", "↗", "⇱", "Ω", "⅋", "⇟"], "machine_learning": ["⎕", "⊜", "⩭", "⎖", "⪢", "⪡", "➗", "➖", "➙", "➘", "⎎", "⎜", "⎝", "⎛", "⎍", "⊌", "⮒", "⮓", "⎒", "⯕", "⎗", "⎙", "⮑", "⮳", "⥽", "⎟", "⎠", "⪞", "⪝", "⪕", "⎔", "⊔", "⊑", "⊡", "⊟", "⮗", "➍", "➎", "➏", "⪌", "⪍", "⪎", "⮍", "➒", "➓", "➔", "➝", "➟", "➢", "➽", "⥵", "⪄", "⪋", "⪓", "⪔", "⪗", "⪛", "⫮", "⭰", "⭲", "⮚", "⮛", "⮝", "⮠", "⮪", "⎐", "➐", "➑", "⩰", "⪏", "⪐", "⪑", "➌", "⮋", "➋"], "math": ["⎇", "⊦", "ℭ", "⌡", "•", "⑭", "≔", "⌴", "≜", "⊀", "⊁", "⋸", "∅", "℮", "ℇ", "∹", "‒", "⊹", "⎻", "‐", "∾", "⇿", "ₐ", "ₓ", "⇀", "⌻", "⍉", "⎏", "⏅", "⊧", "⇗", "⒘", "⏻", "⎞", "Ⅶ", "Ⅹ", "⍽", "ⅰ", "↘", "↙", "⊠", "₀", "⊱", "∼", "⌠", "‴", "↥", "⇈", "∰"], "mathematical_operators": ["ℌ", "⏿", "↳", "⇅"], "mathematical_structures": ["⎪", "⪮", "⪭", "⎩", "⊣", "⮰", "⪬", "➧", "⫶", "⏢", "⎣", "⊫", "⎫", "➩", "➯", "⥲", "⮌", "⮩", "⮫", "⮮", "⎤", "➣", "⎦", "❮", "➥", "➦", "⮥", "⮦", "⎨", "❰", "➨", "⮧", "⭽", "⧼", "⩩", "⮤"], "memory": ["◢", "◼", "▶", "■", "◥", "◒", "⦻", "⨐", "∱", "❌", "⋓", "⬇", "✉", "⨙", "◙", "⩚", "⨀", "✟", "⦆", "⨽", "⬃", "◧", "▥", "⫗", "⩅", "◸", "✑", "❔", "□", "✍", "⨟", "⦁", "⤀", "⭝", "⨮", "≹", "▨", "⭊", "▴", "⭂", "⤄", "◟", "◝", "◞", "◮", "▵"], "meta": ["⍍", "⍟", "⋈", "⠳", "⢅", "⡩", "⢉", "⣀", "∋", "⒐", "—", "⋟", "⋧", "∞", "Ꜳ", "ₔ", "𝛂", "𝐺", "𝑎", "⒙", "⒦", "⸩", "⋊", "⊿", "ↇ", "Ⅴ", "Ⅾ", "Ⅰ", "℠", "ⅼ", "⋤", "⊞", "≛", "⌯", "⅝", "⋿", "𝝘", "ꭖ", "ⅽ", "𝕒", "𝜁", "𝞽", "ꬾ", "𝛺", "ꭞ", "𝗗", "𝗰", "𝚎", "ꟳ", "𝞤", "𝟈", "𝜬", "𝘆", "𝒒", "𝙺", "𝟆"], "meta_programming": ["≌", "❖", "⬬", "⧑", "⩌", "⬘", "⬚", "⩏", "❈", "⯚", "⩟", "❎", "⤣", "⧛", "⭏", "⭎", "⧈", "⧋", "✌", "⥋", "⥒", "⨷", "⩣", "⭍", "✽", "⥅", "⥌", "⦭", "⧣", "⩋", "⩙", "⍏", "⤔", "⧏", "⍢", "❒", "❢", "❥", "⤡", "⧥", "⩝", "⍎", "❁", "⮘", "⮜"], "misc_technical": ["ℷ", "↖", "ℓ", "↨", "↺", "⇌"], "neural_architectures": ["⏦", "⍬", "⏣", "⧭", "⫪", "⫫", "⯰", "⯱", "⏥", "⏳", "⯣", "⮺", "⯧", "⯩", "⯲", "⫧", "⩬", "⯪", "⯫", "⏲", "⫷", "⫣", "⫤", "⯬", "⯭", "⯹", "⫹", "❭", "⥭", "⩫", "⭫", "⏶", "⫥", "⫭", "⏯", "⫰", "⍭", "⭪", "⭬", "⏭"], "number_forms": ["ℵ", "⅀", "↦", "⇮"], "operator": ["∗", "●", "✒", "✀", "▮", "∌", "≝", "∀", "✖", "⨜", "◯", "⌈", "⤛", "◺", "◿", "≞", "⦝", "⋂", "⨉", "⨃", "⬉", "⌖", "⨖", "✋", "⧙", "⤍", "⤏", "⨳", "⬓", "▤", "◨", "⧇", "⌕", "⤺", "⧊", "⬍", "◠", "▽", "▭", "▻", "△", "⦂", "⨄", "◴", "⌸", "⋲", "⨾", "⤉", "⤗", "⭇", "⦬", "◜", "✸", "▢", "◫", "◱", "⤂", "⨅", "⦊", "⤸", "▹", "◶", "⦔", "⨵", "❋", "⬔", "◬", "⥄"], "performance": ["℁", "⏰", "≊", "Å", "⍕", "⍡", "③", "⊬", "↲", "⇊", "₠", "⌢", "‧", "∆", "↫", "⊍", "∐", "≵", "↸", "⒤", "₱", "≺", "⁆", "→", "Ⅺ", "∖", "⋼", "⋷", "ⅿ", "⊆", "₸", "⅐", "⅜", "⊻"], "philosophical_concepts": ["⎵", "➰", "⪻", "⎺", "⊸", "⪳", "⎷", "⮱", "⮴", "⮵", "⎹", "⪶", "⎳", "⎲", "⎴", "⪰", "➸", "⪷", "⎰", "⪯", "⊴", "➱", "⪙", "➹", "➺", "➻", "⪹", "⪺", "⮻", "⎱", "➲", "➳", "⪱", "⎶", "➶", "⮉"], "physics_notation": ["⇖", "⇨", "ℯ", "⇺"], "problem_solving": ["🟗", "🟕", "🟠", "🟣", "🟡", "🟘", "🟙", "🠀", "🠁", "🠄", "🠠", "🠡", "🠤", "🠥", "🠧"], "quantum": ["⌺", "⍅", "⍌", "⍪", "⊛", "⊝", "⋏", "≏", "⌱", "⋫", "∔", "⋶", "⎆", "∺", "≡", "⊷", "⊺", "‘", "⇦", "₼", "∇", "⊽", "⍻", "⑶", "℟", "‷", "’", "ℱ", "⋴", "ⅷ", "ⅻ", "⌰", "™", "⊪", "∪", "⋙", "⅙"], "quantum_computing": ["⫱", "⫝̸", "≤", "⩤", "⍥", "⥥", "⥰", "⭤", "⭸", "⥤"], "reasoning": ["⪪", "⎑"], "reserved_expansion": ["⯋", "⫑", "⫐", "⫒", "⋒", "⋑", "⏏", "⋗", "⯒", "⋖", "⯌", "⏑", "⏒", "⏔", "⏖", "⏕", "⯔", "⋍", "⋌", "⏍", "⫓", "⫋", "⫖", "⫆", "⯑", "⫾", "⯢", "⏊", "➮", "⫊", "➭", "⥨", "⫎", "⮏", "⯍", "⯏", "⏌", "⮣", "⯆", "⏇", "⏈", "⏉", "⫇", "⭯", "⯇", "⯈"], "security": ["∠", "⍁", "≬", "⏷", "₵", "⑧", "⑲", "⊚", "⋎", "∤", "⋹", "−", "⋁", "≇", "⇄", "ↀ", "ⁿ", "⅘", "⅛", "⅖"], "self": ["⣔", "⡀", "𝐋", "𝒹", "𝓏", "⹁", "𝖸", "𝕱", "𝗇", "Ꝗ", "𝞗", "⟢", "𝘶", "𝒀", "𝔻", "𝟬", "𝓝", "⸪", "𝞝"], "set_theory": ["⇠", "↪", "⇲", "↼"], "statistics": ["⇕", "↟", "⇧", "⇹", "↱"], "structure": ["◆", "⬢", "▲", "◑", "◍", "⬌", "⦃", "⤙", "⩑", "⨆", "⌅", "⬂", "⦡", "⬆", "▫", "⥆", "▧", "▿", "◚", "◵", "⩠", "◩", "⥗", "⦉", "⦼", "⧞"], "supplemental_math": ["℀", "ℶ", "ℤ", "ⅉ", "↧", "⇝", "↕", "⇋", "⇯"], "symbolic_ai": ["⧪", "❦", "⧦", "≩", "≧", "≨", "≦", "❧", "⧧", "⩪", "⭥", "❪", "⭩", "⍧", "⥧", "⥶", "⍦", "⩥", "⍩", "➪", "⍨", "❨", "⭧", "⭨", "⥦", "⫴"], "technical_symbols": ["ⅆ", "↤", "⇚", "ℏ", "⇾", "ℳ", "℡", "↶"], "temporal": ["⡃", "⡮", "⢈", "⠐", "ⷲ", "ꬵ", "𝟏", "𝞚", "𝚶", "𝝚", "𝒊", "ⱴ", "𝞂", "𝞼", "𝓹", "𝑨"], "topology": ["℆", "⇣", "↛", "⅏", "⇑", "⇵"], "type_theory": ["⩽", "⯓", "≼", "≻", "⩻", "⭻", "⩼", "⍼", "❼", "❻", "⥻", "⩺", "⭺", "❽"]}, "god_mode_symbols": ["⦟", "⌮", "⨑", "≅", "✗", "⧹", "✿", "⬤", "⬛", "⬧", "✂", "⬮", "⨸", "⧁", "⧀", "⦹", "⦶", "✪", "⥁", "⫏", "⌲", "∮", "⌵", "⌭", "⦙", "⩎", "⫬", "⤓", "✴", "✵", "✳", "⌫", "⌦", "⧳", "✥", "✢", "⪥", "❤", "✔", "❆", "⭙", "❗", "❚", "∻", "⬣", "⨚", "⨼", "⩄", "⩃", "⦓", "⯨", "⧘", "⤌", "⭅", "❘", "⬳", "✠", "⦛", "⨺", "⨩", "⨫", "⨬", "≂", "⨂", "⯜", "⤤", "⬀", "✲", "✫", "⌥", "∥", "✏", "⯛", "✯", "⨭", "⨹", "⨥", "≟", "✊", "⮐", "⦣", "⮶", "⮷", "〉", "⥇", "⭆", "✞", "⫟", "⧢", "✶", "✺", "⌳", "⩘", "⩡", "⌣", "❄", "⤦", "❇", "⦠", "⫍", "⬒", "✡", "✩", "∴", "⬱", "❅", "⧕", "⯗", "∭", "⩁", "⭡", "⯦", "○", "✧", "⬡", "⬭", "◁", "⬨", "⬯", "⬞", "≀", "⌧", "⤨", "❫", "⭌", "⥐", "⍃", "✾", "⤾", "⤿", "⧍", "⭠", "⍂", "⍣", "❊", "⧃", "⭁", "⌹", "❃", "❜", "⤱", "⩊", "⍀", "➬", "⨣", "⩍", "⭀", "⌤", "⥣", "⦜", "⭄", "❝", "⤕", "⥚", "⩐", "⋚", "⥢", "⮟", "⌼", "⍙", "❐", "⤻", "⥙", "⦴", "⨻", "⬻", "⍒", "⥂", "⦩", "⪒", "⮙", "❑", "⤪", "⦪", "⌿", "⥀", "⥉", "⥘", "⪨", "⭼", "⤳", "⥫", "⦲", "⫉", "⬸", "➞", "⭭", "⨴", "⬵", "⍆", "⍘", "❠", "❡", "⤑", "⤢", "⥛", "⥝", "⥡", "⦐", "⦖", "⦮", "⦺", "⧩", "⧷", "⭈", "⮈", "⯅", "⯮", "✼", "⤰", "⥸", "⦯", "⧂", "⫄", "⤷", "⥟", "⌷", "✷", "⤲", "⤶", "⨰", "⬶", "⢟", "⡨", "ꭐ", "⟷", "𝚳", "𝛘", "𝐡", "𝑜", "𝒪", "⟖", "𝟫", "ꜥ", "𝗀", "ⷸ", "𝛽", "𝓔", "Ꞔ", "𝒍", "𝚢", "𝘉", "𝕃", "𝘓", "𝝭", "𝕚", "℻", "⇐", "⇴", "↬", "↾", "ℙ", "ℽ", "⇤", "↜", "⇶", "⣲", "⣸", "⠨", "ⷩ", "ⷡ", "Ɡ", "ꞏ", "⸨", "𝒞", "𝒴", "⸬", "𝔙", "𝘥", "ꟃ", "𝔅", "𝕫", "𝟷", "𝖦", "𝑷", "⇇", "ℎ", "↣", "↑", "ⅅ", "⅍", "ℨ", "№", "℺", "⇏", "⍺", "⧸", "➿", "≷", "➚", "≶", "⩹", "⧶", "⥹", "⯟", "⍷", "⭶", "≸", "❸", "⍶", "❶", "❷", "⥷", "⫲", "❹", "❺", "⥺", "⭹", "⍹", "⩸", "🚛", "🚗", "🚼", "🛄", "🛀", "🛁", "🛉", "🚸", "🛈", "🛋", "🛃", "🚚", "🚪", "🛊", "🛅", "🚹", "🚝", "🚞", "🚫", "🚭", "🚘", "🚖", "🛂", "🛇", "🚙", "🚻", "🚿", "🚬", "🚕", "🚽", "🚜", "🛆", "🚩", "🚾", "🚺", "⠃", "⡕", "⢮", "𝔪", "𝑍", "⸊", "ꝙ", "𝙯", "𝗙", "ꝿ", "𝝟", "ⱻ", "𝜱", "Ɫ", "𝑶", "𝟜", "╜", "⮽", "⯁", "⯀", "⮿", "⮾", "⪼", "➕", "⎼", "⩧", "⮲", "⪽", "⪿", "⫀", "➼", "⎿", "⏀", "⏁", "⫁", "⧨", "➜", "⎾", "➫", "➾", "⏂", "⏄", "⫂", "⫃", "ℿ", "↞", "ℛ", "⇂", "⇸", "⯥", "⎊", "⎄", "⎃", "⪆", "⎈", "⭾", "⪉", "⪅", "⫝", "⊄", "⫨", "⊂", "⧾", "⯺", "⥾", "⍿", "⯎", "❾", "➃", "⪃", "⎅", "➄", "⩨", "⮄", "❿", "⩾", "⎉", "➅", "➆", "➇", "➈", "➉", "➊", "⪇", "⪈", "⪊", "⫯", "⭷", "⮅", "⮆", "⮇", "⮡", "➀", "⎂", "➂", "⮁", "⮂", "➁", "✈", "⨿", "⌶", "◄", "◀", "⬥", "∙", "⦿", "◌", "⌄", "≙", "⌂", "⨛", "◘", "⦅", "⬅", "⩒", "◡", "✎", "⨁", "⌆", "⨧", "⦰", "⌐", "⌉", "❍", "✰", "∍", "⬋", "✨", "∢", "⊃", "⌎", "⨇", "⦽", "⌃", "◦", "❀", "✅", "▱", "⦇", "⨡", "⤁", "⩉", "⦳", "◕", "◭", "⬎", "⤅", "◛", "◳", "⥖", "⦕", "⥈", "◔", "⤼", "◪", "◃", "⤮", "⨱", "⥃", "⬼", "⋳", "⊉", "🝛", "🞷", "🞆", "🞉", "🟊", "🟉", "🞯", "🞵", "🟋", "🞶", "🟍", "🟂", "🞈", "🝚", "🝜", "🝝", "🞊", "🟀", "🟁", "🟇", "🟏", "🟐", "🟑", "🟓", "❉", "◾", "⭑", "⧒", "⦷", "⦾", "✣", "⪧", "⭘", "⭕", "✮", "⭖", "⨏", "⦑", "⬄", "⤎", "∧", "∨", "✃", "❙", "⬁", "⦧", "⫚", "⨤", "⦥", "⤚", "⮕", "⤜", "⧴", "⩗", "⪾", "✻", "⯊", "⏜", "✹", "⩖", "⩂", "✁", "⧖", "⥎", "⥬", "◈", "❂", "⤬", "⦨", "⩈", "⩞", "⫵", "⬕", "⬺", "⭚", "⮢", "⍓", "⧎", "⤇", "⩜", "⭣", "⤆", "⭢", "⦏", "⤫", "⍗", "⤠", "⤯", "⭛", "⍔", "❕", "⥓", "⮊", "🜁", "🝪", "🝅", "🝗", "🝂", "🝃", "🝙", "🝠", "🜃", "🜂", "🝉", "🝋", "🜲", "🝭", "🜔", "🜛", "🝔", "🝇", "🜍", "🜿", "🝕", "🜊", "🜄", "🝊", "🞟", "🞗", "🞌", "🞘", "🞞", "🞱", "🞤", "🞫", "⊘", "⊖", "⊗", "⫽", "🠿", "🠷", "🡇", "🢓", "🞧", "🞮", "🟆", "🞾", "🞲", "🞬", "🟌", "🞇", "🟫", "🟢", "🡘", "🠴", "🟄", "🞎", "🞼", "🞰", "🞏", "🟖", "🡔", "⯽", "⏾", "🠾", "⯻", "🞡", "🞨", "⫸", "⊨", "🠽", "🡅", "🞥", "🞒", "🢣", "🝐", "⯶", "🞛", "🡨", "🜵", "🢀", "🜷", "🟈", "⯴", "⯸", "🠚", "🢁", "🜳", "🞕", "🞠", "🢚", "🜝", "🠦", "🢘", "🢥", "🟎", "🠃", "🢙", "🜼", "🝘", "🡴", "🝓", "🢔", "⫻", "🢛", "🜱", "🢱", "🞴", "🠝", "⯷", "🜘", "🠉", "🟔", "🝎", "🠢", "🝩", "🠪", "🟃", "⯵", "🞂", "🜉", "⋻", "🠘", "🟰", "🜢", "🠇", "🝏", "🟒", "🠛", "🡼", "🝵", "🢕", "🠫", "🠳", "🞳", "🝢", "🢧", "🠭", "🜭", "🞹", "🜎", "🢂", "⯳", "🜈", "🜗", "🠆", "🡩", "🡠", "🞚", "🜏", "🠲", "🜾", "🝁", "🞀", "🢃", "🠂", "🠈", "🝲", "🡵", "🞿", "🟅", "🠣", "🝼", "⍳", "⍵", "⍴", "⩴", "⩱", "⩮", "≮", "⩲", "⧵", "⩵", "⍲", "❲", "≴", "❳", "❵", "⥪", "⩳", "⭳", "⍯", "❯", "⩯", "≰", "⍰", "⪁", "⥱", "⍮", "⭮", "⇘", "⇪", "↴", "⇓", "⇥", "↝", "ℬ", "℈", "⇷", "⇁", "ⅇ", "⇉", "ℴ", "↷", "⇭", "⦞", "≃", "✬", "▰", "⌍", "⌟", "◎", "◐", "◓", "⬖", "⬗", "⩔", "⤋", "⌁", "⋝", "⨍", "✚", "⧜", "⨎", "⬲", "⌊", "≲", "⩛", "◊", "✙", "⌋", "◗", "⦄", "⌑", "∯", "⦀", "⩕", "✐", "◹", "⤒", "⤊", "⌇", "◇", "◅", "◽", "◻", "▦", "⋉", "⬽", "❏", "⦈", "⨒", "⋨", "◰", "⤧", "⬑", "⤹", "◂", "⭃", "⨔", "◲", "⤈", "⦋", "⬏", "⭞", "▩", "↻", "ℸ", "℔", "↩", "↗", "⇱", "Ω", "⅋", "⇟", "⎕", "⊜", "⩭", "⎖", "⪢", "⪡", "➗", "➖", "➙", "➘", "⎎", "⎜", "⎝", "⎛", "⎍", "⊌", "⮒", "⮓", "⎒", "⯕", "⎗", "⎙", "⮑", "⮳", "⥽", "⎟", "⎠", "⪞", "⪝", "⪕", "⎔", "⊔", "⊑", "⊡", "⊟", "⮗", "➍", "➎", "➏", "⪌", "⪍", "⪎", "⮍", "➒", "➓", "➔", "➝", "➟", "➢", "➽", "⥵", "⪄", "⪋", "⪓", "⪔", "⪗", "⪛", "⫮", "⭰", "⭲", "⮚", "⮛", "⮝", "⮠", "⮪", "⎐", "➐", "➑", "⩰", "⪏", "⪐", "⪑", "➌", "⮋", "➋", "ℌ", "⏿", "↳", "⇅", "⎪", "⪮", "⪭", "⎩", "⊣", "⮰", "⪬", "➧", "⫶", "⏢", "⎣", "⊫", "⎫", "➩", "➯", "⥲", "⮌", "⮩", "⮫", "⮮", "⎤", "➣", "⎦", "❮", "➥", "➦", "⮥", "⮦", "⎨", "❰", "➨", "⮧", "⭽", "⧼", "⩩", "⮤", "◢", "◼", "▶", "■", "◥", "◒", "⦻", "⨐", "∱", "❌", "⋓", "⬇", "✉", "⨙", "◙", "⩚", "⨀", "✟", "⦆", "⨽", "⬃", "◧", "▥", "⫗", "⩅", "◸", "✑", "❔", "□", "✍", "⨟", "⦁", "⤀", "⭝", "⨮", "≹", "▨", "⭊", "▴", "⭂", "⤄", "◟", "◝", "◞", "◮", "▵", "⠳", "⢅", "⡩", "⢉", "⣀", "Ꜳ", "𝛂", "𝐺", "𝑎", "⸩", "Ⅰ", "ⅼ", "𝝘", "ꭖ", "ⅽ", "𝕒", "𝜁", "𝞽", "ꬾ", "𝛺", "ꭞ", "𝗗", "𝗰", "𝚎", "ꟳ", "𝞤", "𝟈", "𝜬", "𝘆", "𝒒", "𝙺", "𝟆", "≌", "❖", "⬬", "⧑", "⩌", "⬘", "⬚", "⩏", "❈", "⯚", "⩟", "❎", "⤣", "⧛", "⭏", "⭎", "⧈", "⧋", "✌", "⥋", "⥒", "⨷", "⩣", "⭍", "✽", "⥅", "⥌", "⦭", "⧣", "⩋", "⩙", "⍏", "⤔", "⧏", "⍢", "❒", "❢", "❥", "⤡", "⧥", "⩝", "⍎", "❁", "⮘", "⮜", "ℷ", "↖", "ℓ", "↨", "↺", "⇌", "⏦", "⍬", "⏣", "⧭", "⫪", "⫫", "⯰", "⯱", "⏥", "⏳", "⯣", "⮺", "⯧", "⯩", "⯲", "⫧", "⩬", "⯪", "⯫", "⏲", "⫷", "⫣", "⫤", "⯬", "⯭", "⯹", "⫹", "❭", "⥭", "⩫", "⭫", "⏶", "⫥", "⫭", "⏯", "⫰", "⍭", "⭪", "⭬", "⏭", "ℵ", "⅀", "↦", "⇮", "∗", "●", "✒", "✀", "▮", "∌", "≝", "∀", "✖", "⨜", "◯", "⌈", "⤛", "◺", "◿", "≞", "⦝", "⋂", "⨉", "⨃", "⬉", "⌖", "⨖", "✋", "⧙", "⤍", "⤏", "⨳", "⬓", "▤", "◨", "⧇", "⌕", "⤺", "⧊", "⬍", "◠", "▽", "▭", "▻", "△", "⦂", "⨄", "◴", "⌸", "⋲", "⨾", "⤉", "⤗", "⭇", "⦬", "◜", "✸", "▢", "◫", "◱", "⤂", "⨅", "⦊", "⤸", "▹", "◶", "⦔", "⨵", "❋", "⬔", "◬", "⥄", "⎵", "➰", "⪻", "⎺", "⊸", "⪳", "⎷", "⮱", "⮴", "⮵", "⎹", "⪶", "⎳", "⎲", "⎴", "⪰", "➸", "⪷", "⎰", "⪯", "⊴", "➱", "⪙", "➹", "➺", "➻", "⪹", "⪺", "⮻", "⎱", "➲", "➳", "⪱", "⎶", "➶", "⮉", "⇖", "⇨", "ℯ", "⇺", "🟗", "🟕", "🟠", "🟣", "🟡", "🟘", "🟙", "🠀", "🠁", "🠄", "🠠", "🠡", "🠤", "🠥", "🠧", "⫱", "⫝̸", "≤", "⩤", "⍥", "⥥", "⥰", "⭤", "⭸", "⥤", "⪪", "⎑", "⯋", "⫑", "⫐", "⫒", "⋒", "⋑", "⏏", "⋗", "⯒", "⋖", "⯌", "⏑", "⏒", "⏔", "⏖", "⏕", "⯔", "⋍", "⋌", "⏍", "⫓", "⫋", "⫖", "⫆", "⯑", "⫾", "⯢", "⏊", "➮", "⫊", "➭", "⥨", "⫎", "⮏", "⯍", "⯏", "⏌", "⮣", "⯆", "⏇", "⏈", "⏉", "⫇", "⭯", "⯇", "⯈", "⣔", "⡀", "𝐋", "𝒹", "𝓏", "⹁", "𝖸", "𝕱", "𝗇", "Ꝗ", "𝞗", "⟢", "𝘶", "𝒀", "𝔻", "𝟬", "𝓝", "⸪", "𝞝", "⇠", "↪", "⇲", "↼", "⇕", "↟", "⇧", "⇹", "↱", "◆", "⬢", "▲", "◑", "◍", "⬌", "⦃", "⤙", "⩑", "⨆", "⌅", "⬂", "⦡", "⬆", "▫", "⥆", "▧", "▿", "◚", "◵", "⩠", "◩", "⥗", "⦉", "⦼", "⧞", "℀", "ℶ", "ℤ", "ⅉ", "↧", "⇝", "↕", "⇋", "⇯", "⧪", "❦", "⧦", "≩", "≧", "≨", "≦", "❧", "⧧", "⩪", "⭥", "❪", "⭩", "⍧", "⥧", "⥶", "⍦", "⩥", "⍩", "➪", "⍨", "❨", "⭧", "⭨", "⥦", "⫴", "ⅆ", "↤", "⇚", "ℏ", "⇾", "ℳ", "℡", "↶", "⡃", "⡮", "⢈", "⠐", "ⷲ", "ꬵ", "𝟏", "𝞚", "𝚶", "𝝚", "𝒊", "ⱴ", "𝞂", "𝞼", "𝓹", "𝑨", "℆", "⇣", "↛", "⅏", "⇑", "⇵", "⩽", "⯓", "≼", "≻", "⩻", "⭻", "⩼", "⍼", "❼", "❻", "⥻", "⩺", "⭺", "❽"], "test_files": ["roundtrip_cases.json", "ast_fidelity_cases.json"]}