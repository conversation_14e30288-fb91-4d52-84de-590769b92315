"""
NEUROGLYPH Symbolic Coverage Tests
Test coverage aggregato per tutti i 1,854 simboli Unicode
"""

import pytest
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


class TestSymbolicCoverage:
    """Test coverage aggregato per simboli Unicode."""
    
    @pytest.fixture(scope="class")
    def fixtures_info(self):
        """Carica info fixture."""
        fixtures_path = Path(__file__).parent.parent / "fixtures" / "fixtures_list.json"
        
        if not fixtures_path.exists():
            pytest.skip(f"Fixtures list non trovata: {fixtures_path}")
        
        with open(fixtures_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @pytest.fixture(scope="class")
    def semantic_templates(self):
        """Carica template semantici."""
        templates_path = Path(__file__).parent.parent.parent / "data" / "symbols" / "semantic_templates.json"
        
        if templates_path.exists():
            with open(templates_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    @pytest.fixture(scope="class")
    def prompt_contexts(self):
        """Carica contesti prompt."""
        contexts_path = Path(__file__).parent.parent.parent / "data" / "symbols" / "prompt_contexts.json"
        
        if contexts_path.exists():
            with open(contexts_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def test_overall_symbol_coverage(self, fixtures_info, semantic_templates, prompt_contexts):
        """Test coverage complessivo simboli."""
        total_symbols = fixtures_info['total_symbols']
        all_symbols = set()
        
        # Raccogli tutti i simboli
        for domain, symbols in fixtures_info['symbols_by_domain'].items():
            all_symbols.update(symbols)
        
        # Verifica coerenza
        assert len(all_symbols) == total_symbols, f"Inconsistenza simboli: {len(all_symbols)} vs {total_symbols}"
        
        # Coverage per componente
        template_coverage = len(set(semantic_templates.keys()) & all_symbols) / total_symbols
        context_coverage = len(set(prompt_contexts.keys()) & all_symbols) / total_symbols
        
        print(f"📊 Coverage Complessivo NEUROGLYPH:")
        print(f"   - Simboli totali: {total_symbols}")
        print(f"   - Domini: {len(fixtures_info['domains'])}")
        print(f"   - GOD mode certified: {len(fixtures_info['god_mode_symbols'])}")
        print(f"   - Template semantici: {template_coverage:.2%}")
        print(f"   - Contesti prompt: {context_coverage:.2%}")
        
        # Target coverage minimo
        assert total_symbols >= 1800, f"Troppo pochi simboli: {total_symbols}"
        assert len(fixtures_info['domains']) >= 50, f"Troppo pochi domini: {len(fixtures_info['domains'])}"
        assert len(fixtures_info['god_mode_symbols']) >= 1000, f"Troppo pochi simboli GOD mode: {len(fixtures_info['god_mode_symbols'])}"
    
    def test_critical_domains_coverage(self, fixtures_info):
        """Test coverage domini critici."""
        critical_domains = ['logic', 'math', 'advanced_coding', 'cognition', 'ai', 'code']
        domain_stats = {}
        
        for domain in critical_domains:
            if domain in fixtures_info['symbols_by_domain']:
                symbols = fixtures_info['symbols_by_domain'][domain]
                domain_stats[domain] = {
                    'count': len(symbols),
                    'symbols': symbols
                }
            else:
                domain_stats[domain] = {
                    'count': 0,
                    'symbols': []
                }
        
        print(f"📊 Coverage Domini Critici:")
        for domain, stats in domain_stats.items():
            print(f"   - {domain}: {stats['count']} simboli")
            
            # Domini critici devono avere simboli
            if domain in ['logic', 'math']:
                assert stats['count'] >= 10, f"Dominio critico '{domain}' ha troppo pochi simboli: {stats['count']}"
            elif domain in ['advanced_coding', 'ai']:
                assert stats['count'] >= 5, f"Dominio importante '{domain}' ha troppo pochi simboli: {stats['count']}"
    
    def test_god_mode_distribution(self, fixtures_info):
        """Test distribuzione simboli GOD mode."""
        god_mode_symbols = set(fixtures_info['god_mode_symbols'])
        total_symbols = fixtures_info['total_symbols']
        
        god_mode_rate = len(god_mode_symbols) / total_symbols
        
        # Distribuzione per dominio
        god_mode_by_domain = {}
        for domain, symbols in fixtures_info['symbols_by_domain'].items():
            god_mode_in_domain = [s for s in symbols if s in god_mode_symbols]
            god_mode_by_domain[domain] = {
                'total': len(symbols),
                'god_mode': len(god_mode_in_domain),
                'rate': len(god_mode_in_domain) / len(symbols) if symbols else 0
            }
        
        print(f"📊 Distribuzione GOD Mode:")
        print(f"   - Tasso globale: {god_mode_rate:.2%}")
        
        # Top domini per GOD mode
        sorted_domains = sorted(god_mode_by_domain.items(), 
                               key=lambda x: x[1]['god_mode'], reverse=True)
        
        print(f"   - Top domini GOD mode:")
        for domain, stats in sorted_domains[:10]:
            if stats['god_mode'] > 0:
                print(f"     - {domain}: {stats['god_mode']}/{stats['total']} ({stats['rate']:.1%})")
        
        # Verifica qualità GOD mode
        assert god_mode_rate >= 0.70, f"Tasso GOD mode troppo basso: {god_mode_rate:.2%}"
        
        # Domini critici devono avere buona percentuale GOD mode
        critical_domains = ['logic', 'math', 'advanced_coding']
        for domain in critical_domains:
            if domain in god_mode_by_domain and god_mode_by_domain[domain]['total'] > 0:
                rate = god_mode_by_domain[domain]['rate']
                assert rate >= 0.50, f"Dominio critico '{domain}' ha troppo pochi GOD mode: {rate:.2%}"
    
    def test_component_integration_coverage(self, fixtures_info, semantic_templates, prompt_contexts):
        """Test coverage integrazione componenti."""
        all_symbols = set()
        for symbols in fixtures_info['symbols_by_domain'].values():
            all_symbols.update(symbols)
        
        # Simboli con copertura completa (template + contesto)
        symbols_with_templates = set(semantic_templates.keys())
        symbols_with_contexts = set(prompt_contexts.keys())
        
        # Intersezioni
        symbols_with_both = symbols_with_templates & symbols_with_contexts & all_symbols
        symbols_with_template_only = (symbols_with_templates & all_symbols) - symbols_with_contexts
        symbols_with_context_only = (symbols_with_contexts & all_symbols) - symbols_with_templates
        symbols_with_neither = all_symbols - symbols_with_templates - symbols_with_contexts
        
        # Statistiche integrazione
        total = len(all_symbols)
        both_rate = len(symbols_with_both) / total
        template_only_rate = len(symbols_with_template_only) / total
        context_only_rate = len(symbols_with_context_only) / total
        neither_rate = len(symbols_with_neither) / total
        
        print(f"📊 Coverage Integrazione Componenti:")
        print(f"   - Template + Contesto: {both_rate:.2%} ({len(symbols_with_both)})")
        print(f"   - Solo Template: {template_only_rate:.2%} ({len(symbols_with_template_only)})")
        print(f"   - Solo Contesto: {context_only_rate:.2%} ({len(symbols_with_context_only)})")
        print(f"   - Nessuno: {neither_rate:.2%} ({len(symbols_with_neither)})")
        
        # Target integrazione
        assert both_rate >= 0.30, f"Troppo pochi simboli con copertura completa: {both_rate:.2%}"
        assert neither_rate <= 0.30, f"Troppi simboli senza copertura: {neither_rate:.2%}"
        
        # Almeno uno dei due componenti per la maggior parte dei simboli
        covered_rate = (len(symbols_with_both) + len(symbols_with_template_only) + len(symbols_with_context_only)) / total
        assert covered_rate >= 0.70, f"Coverage componenti troppo bassa: {covered_rate:.2%}"
    
    def test_quality_metrics_aggregation(self, fixtures_info, semantic_templates, prompt_contexts):
        """Test aggregazione metriche qualità."""
        total_symbols = fixtures_info['total_symbols']
        god_mode_symbols = set(fixtures_info['god_mode_symbols'])
        
        # Metriche qualità
        quality_metrics = {
            'total_symbols': total_symbols,
            'domains': len(fixtures_info['domains']),
            'god_mode_rate': len(god_mode_symbols) / total_symbols,
            'template_coverage': len(set(semantic_templates.keys()) & set().union(*fixtures_info['symbols_by_domain'].values())) / total_symbols,
            'context_coverage': len(set(prompt_contexts.keys()) & set().union(*fixtures_info['symbols_by_domain'].values())) / total_symbols,
            'avg_context_length': 0,
            'template_validity_rate': 0
        }
        
        # Calcola lunghezza media contesti
        if prompt_contexts:
            total_length = sum(len(context) for context in prompt_contexts.values())
            quality_metrics['avg_context_length'] = total_length / len(prompt_contexts)
        
        # Calcola validità template (semplificata)
        valid_templates = 0
        for template in semantic_templates.values():
            if '(' in template and ')' in template and len(template) > 5:
                valid_templates += 1
        
        if semantic_templates:
            quality_metrics['template_validity_rate'] = valid_templates / len(semantic_templates)
        
        print(f"📊 Metriche Qualità Aggregate:")
        print(f"   - Simboli totali: {quality_metrics['total_symbols']}")
        print(f"   - Domini: {quality_metrics['domains']}")
        print(f"   - GOD mode rate: {quality_metrics['god_mode_rate']:.2%}")
        print(f"   - Template coverage: {quality_metrics['template_coverage']:.2%}")
        print(f"   - Context coverage: {quality_metrics['context_coverage']:.2%}")
        print(f"   - Lunghezza media contesti: {quality_metrics['avg_context_length']:.0f} caratteri")
        print(f"   - Template validity rate: {quality_metrics['template_validity_rate']:.2%}")
        
        # Target qualità complessiva
        assert quality_metrics['total_symbols'] >= 1800, "Troppo pochi simboli totali"
        assert quality_metrics['domains'] >= 50, "Troppo pochi domini"
        assert quality_metrics['god_mode_rate'] >= 0.70, "GOD mode rate troppo basso"
        assert quality_metrics['avg_context_length'] >= 30, "Contesti troppo corti in media"
        assert quality_metrics['template_validity_rate'] >= 0.90, "Troppi template invalidi"
    
    def test_symbolic_full_coverage_certification(self, fixtures_info, semantic_templates, prompt_contexts):
        """Test certificazione Symbolic Full-Coverage."""
        # Criteri per certificazione Fase 4.5
        certification_criteria = {
            'min_symbols': 1800,
            'min_domains': 50,
            'min_god_mode_rate': 0.70,
            'min_template_coverage': 0.30,
            'min_context_coverage': 0.30,
            'min_integration_coverage': 0.50  # Template OR Context
        }
        
        # Calcola metriche attuali
        total_symbols = fixtures_info['total_symbols']
        all_symbols = set().union(*fixtures_info['symbols_by_domain'].values())
        god_mode_rate = len(fixtures_info['god_mode_symbols']) / total_symbols
        template_coverage = len(set(semantic_templates.keys()) & all_symbols) / total_symbols
        context_coverage = len(set(prompt_contexts.keys()) & all_symbols) / total_symbols
        
        # Coverage integrazione (almeno uno dei due)
        symbols_with_template = set(semantic_templates.keys()) & all_symbols
        symbols_with_context = set(prompt_contexts.keys()) & all_symbols
        symbols_with_either = symbols_with_template | symbols_with_context
        integration_coverage = len(symbols_with_either) / total_symbols
        
        # Verifica criteri
        results = {
            'symbols': total_symbols >= certification_criteria['min_symbols'],
            'domains': len(fixtures_info['domains']) >= certification_criteria['min_domains'],
            'god_mode_rate': god_mode_rate >= certification_criteria['min_god_mode_rate'],
            'template_coverage': template_coverage >= certification_criteria['min_template_coverage'],
            'context_coverage': context_coverage >= certification_criteria['min_context_coverage'],
            'integration_coverage': integration_coverage >= certification_criteria['min_integration_coverage']
        }
        
        passed_criteria = sum(results.values())
        total_criteria = len(results)
        certification_rate = passed_criteria / total_criteria
        
        print(f"🏆 CERTIFICAZIONE SYMBOLIC FULL-COVERAGE:")
        print(f"   - Simboli: {'✅' if results['symbols'] else '❌'} {total_symbols} >= {certification_criteria['min_symbols']}")
        print(f"   - Domini: {'✅' if results['domains'] else '❌'} {len(fixtures_info['domains'])} >= {certification_criteria['min_domains']}")
        print(f"   - GOD mode: {'✅' if results['god_mode_rate'] else '❌'} {god_mode_rate:.2%} >= {certification_criteria['min_god_mode_rate']:.2%}")
        print(f"   - Template: {'✅' if results['template_coverage'] else '❌'} {template_coverage:.2%} >= {certification_criteria['min_template_coverage']:.2%}")
        print(f"   - Context: {'✅' if results['context_coverage'] else '❌'} {context_coverage:.2%} >= {certification_criteria['min_context_coverage']:.2%}")
        print(f"   - Integration: {'✅' if results['integration_coverage'] else '❌'} {integration_coverage:.2%} >= {certification_criteria['min_integration_coverage']:.2%}")
        print(f"")
        print(f"🎯 RISULTATO: {passed_criteria}/{total_criteria} criteri passati ({certification_rate:.2%})")
        
        if certification_rate >= 0.80:
            print(f"🎉 CERTIFICAZIONE SYMBOLIC FULL-COVERAGE: SUPERATA!")
        else:
            print(f"⚠️ CERTIFICAZIONE SYMBOLIC FULL-COVERAGE: DA MIGLIORARE")
        
        # Per passare il test, almeno 80% dei criteri devono essere soddisfatti
        assert certification_rate >= 0.80, f"Certificazione fallita: {certification_rate:.2%} < 80%"
