{"proof_id": "euclid_gcd_proof", "description": "Proof tree per algoritmo di Euclide (GCD)", "steps": [{"rule": "universal_instantiation", "premises": [], "conclusion": {"type": "predicate", "name": "greatest_common_divisor", "args": ["a", "b"]}, "justification": "Euclid algorithm for GCD computation"}, {"rule": "modus_ponens", "premises": [{"type": "predicate", "name": "divides", "args": ["gcd(a,b)", "a"]}], "conclusion": {"type": "predicate", "name": "modulo_operation", "args": ["a", "b", "remainder"]}, "justification": "Modulo operation in Euclidean algorithm"}, {"rule": "recursive_application", "premises": [], "conclusion": {"type": "predicate", "name": "gcd_recursive", "args": ["b", "a % b"]}, "justification": "Recursive step: gcd(a,b) = gcd(b, a mod b)"}], "metadata": {"algorithm_type": "gcd", "complexity": "O(log min(a,b))", "mathematical_domain": "number_theory", "code_template_hint": "euclid_gcd"}}