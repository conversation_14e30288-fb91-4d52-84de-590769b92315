{"proof_id": "fibonacci_recursive_proof", "description": "Proof tree per sequenza di Fibonacci ricorsiva", "steps": [{"rule": "base_case", "premises": [], "conclusion": {"type": "predicate", "name": "<PERSON><PERSON><PERSON><PERSON>", "args": ["0"]}, "justification": "Base case: F(0) = 0"}, {"rule": "base_case", "premises": [], "conclusion": {"type": "predicate", "name": "<PERSON><PERSON><PERSON><PERSON>", "args": ["1"]}, "justification": "Base case: F(1) = 1"}, {"rule": "inductive_step", "premises": [{"type": "predicate", "name": "<PERSON><PERSON><PERSON><PERSON>", "args": ["n-1"]}, {"type": "predicate", "name": "<PERSON><PERSON><PERSON><PERSON>", "args": ["n-2"]}], "conclusion": {"type": "predicate", "name": "<PERSON><PERSON><PERSON><PERSON>", "args": ["n"]}, "justification": "Recursive case: F(n) = F(n-1) + F(n-2)"}], "metadata": {"algorithm_type": "<PERSON><PERSON><PERSON><PERSON>", "complexity": "O(2^n)", "mathematical_domain": "sequences", "code_template_hint": "fibonacci_recursive", "optimization_hint": "Consider iterative version for better performance"}}