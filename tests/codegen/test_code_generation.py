"""
Test Suite NEUROGLYPH Code Generation

Test completi per validare la generazione di codice da proof trees
e l'integrazione con il Template Engine.
"""

import pytest
import json
import tempfile
from pathlib import Path
from typing import Dict, Any

# Import moduli NEUROGLYPH
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from neuroglyph.cognitive.ng_decoder import NGDecoder, CodeGenerationResult, DecodingContext
from neuroglyph.code_templates.template_engine import TemplateEngine
from neuroglyph.logic.proof_tree import ProofTree, ProofStep
from neuroglyph.logic.formula import Formula, Predicate, Variable


class TestTemplateEngine:
    """Test per Template Engine."""
    
    def test_template_engine_initialization(self):
        """Test inizializzazione Template Engine."""
        engine = TemplateEngine()
        
        assert len(engine.templates) > 0
        assert "euclid_gcd" in engine.templates
        assert "fibonacci_recursive" in engine.templates
        assert "binary_search" in engine.templates
    
    def test_template_rendering(self):
        """Test rendering template base."""
        engine = TemplateEngine()
        
        # Test template senza parametri
        code = engine.render("euclid_gcd")
        assert "def gcd(a, b):" in code
        assert "while b:" in code
        assert "return a" in code
    
    def test_template_categories(self):
        """Test categorizzazione template."""
        engine = TemplateEngine()
        
        categories = engine.get_categories()
        assert "mathematics" in categories
        assert "algorithms" in categories
        
        math_templates = engine.list_by_category("mathematics")
        assert "euclid_gcd" in math_templates
        assert "fibonacci_recursive" in math_templates
    
    def test_template_complexities(self):
        """Test livelli di complessità."""
        engine = TemplateEngine()
        
        complexities = engine.get_complexities()
        assert "basic" in complexities
        assert "intermediate" in complexities
        assert "advanced" in complexities
        
        basic_templates = engine.list_by_complexity("basic")
        assert len(basic_templates) > 0


class TestNGDecoder:
    """Test per NGDecoder cognitivo."""
    
    @pytest.fixture
    def decoder(self):
        """Fixture per NGDecoder."""
        return NGDecoder()
    
    @pytest.fixture
    def mock_proof_gcd(self):
        """Fixture per proof tree GCD."""
        # Crea un proof tree mock per algoritmo di Euclide
        steps = [
            ProofStep(
                rule="modus_ponens",
                premises=[],
                conclusion=Predicate("greatest_common_divisor", [Variable("a"), Variable("b")]),
                justification="Euclid algorithm application"
            )
        ]
        return ProofTree(steps=steps)
    
    @pytest.fixture
    def mock_proof_fibonacci(self):
        """Fixture per proof tree Fibonacci."""
        steps = [
            ProofStep(
                rule="induction",
                premises=[],
                conclusion=Predicate("fibonacci", [Variable("n")]),
                justification="Recursive fibonacci sequence"
            )
        ]
        return ProofTree(steps=steps)
    
    def test_decoder_initialization(self, decoder):
        """Test inizializzazione NGDecoder."""
        assert decoder.template_engine is not None
        assert len(decoder.template_engine.templates) > 0
        assert decoder.generations_count == 0
        assert decoder.successful_generations == 0
    
    def test_proof_analysis_gcd(self, decoder, mock_proof_gcd):
        """Test analisi proof tree per GCD."""
        analysis = decoder.analyze_proof_tree(mock_proof_gcd)
        
        assert analysis["steps_count"] == 1
        assert "gcd" in analysis["algorithm_hints"]
        assert "euclid_gcd" in analysis["code_template_candidates"]
    
    def test_proof_analysis_fibonacci(self, decoder, mock_proof_fibonacci):
        """Test analisi proof tree per Fibonacci."""
        analysis = decoder.analyze_proof_tree(mock_proof_fibonacci)
        
        assert analysis["steps_count"] == 1
        assert "fibonacci" in analysis["algorithm_hints"]
        assert "fibonacci_recursive" in analysis["code_template_candidates"]
    
    def test_code_generation_gcd(self, decoder, mock_proof_gcd):
        """Test generazione codice per GCD."""
        result = decoder.generate_code_from_proof(mock_proof_gcd)
        
        assert result.success is True
        assert result.generated_code is not None
        assert "def gcd(a, b):" in result.generated_code
        assert result.template_used == "euclid_gcd"
        
        # Verifica che il codice compili
        namespace = {}
        exec(result.generated_code, namespace)
        assert "gcd" in namespace
        
        # Test funzionalità
        gcd_func = namespace["gcd"]
        assert gcd_func(48, 18) == 6
        assert gcd_func(17, 13) == 1
    
    def test_code_generation_fibonacci(self, decoder, mock_proof_fibonacci):
        """Test generazione codice per Fibonacci."""
        result = decoder.generate_code_from_proof(mock_proof_fibonacci)
        
        assert result.success is True
        assert result.generated_code is not None
        assert "def fib(n):" in result.generated_code
        assert result.template_used == "fibonacci_recursive"
        
        # Verifica che il codice compili
        namespace = {}
        exec(result.generated_code, namespace)
        assert "fib" in namespace
        
        # Test funzionalità
        fib_func = namespace["fib"]
        assert fib_func(0) == 0
        assert fib_func(1) == 1
        assert fib_func(5) == 5  # 0,1,1,2,3,5
    
    def test_code_generation_no_template(self, decoder):
        """Test generazione codice senza template appropriato."""
        # Proof tree vuoto
        empty_proof = ProofTree(steps=[])
        result = decoder.generate_code_from_proof(empty_proof)
        
        assert result.success is False
        assert result.error_message is not None
        assert "Nessun template appropriato" in result.error_message
    
    def test_preferred_template_override(self, decoder, mock_proof_gcd):
        """Test override con template preferito."""
        result = decoder.generate_code_from_proof(
            mock_proof_gcd, 
            preferred_template="fibonacci_recursive"
        )
        
        assert result.success is True
        assert result.template_used == "fibonacci_recursive"
        assert "def fib(n):" in result.generated_code
    
    def test_decoding_context(self, decoder, mock_proof_gcd):
        """Test con contesto di decodifica."""
        context = DecodingContext(
            reasoning_depth=3,
            symbolic_complexity=0.7,
            domain_hints=["mathematics", "algorithms"]
        )
        
        result = decoder.generate_code_from_proof(mock_proof_gcd, context=context)
        
        assert result.success is True
        assert result.generated_code is not None
    
    def test_statistics(self, decoder, mock_proof_gcd):
        """Test statistiche decoder."""
        # Genera alcuni codici
        decoder.generate_code_from_proof(mock_proof_gcd)
        decoder.generate_code_from_proof(mock_proof_gcd)
        
        stats = decoder.get_statistics()
        
        assert stats["generations_count"] == 2
        assert stats["successful_generations"] == 2
        assert stats["success_rate"] == 1.0
        assert stats["available_templates"] > 0


class TestCodeGenerationIntegration:
    """Test di integrazione per code generation."""
    
    def test_end_to_end_code_generation(self):
        """Test end-to-end completo."""
        decoder = NGDecoder()
        
        # Simula un proof tree complesso
        steps = [
            ProofStep(
                rule="universal_instantiation",
                premises=[],
                conclusion=Predicate("prime", [Variable("n")]),
                justification="Prime number verification"
            )
        ]
        proof = ProofTree(steps=steps)
        
        result = decoder.generate_code_from_proof(proof)
        
        if result.success:
            # Verifica che il codice sia eseguibile
            namespace = {}
            exec(result.generated_code, namespace)
            
            # Dovrebbe contenere una funzione
            functions = [name for name, obj in namespace.items() 
                        if callable(obj) and not name.startswith('_')]
            assert len(functions) > 0
    
    def test_multiple_algorithm_detection(self):
        """Test rilevamento algoritmi multipli."""
        decoder = NGDecoder()
        
        # Proof con hint multipli
        steps = [
            ProofStep(
                rule="induction",
                premises=[],
                conclusion=Predicate("fibonacci_gcd", [Variable("a"), Variable("b")]),
                justification="Combined fibonacci and gcd algorithm"
            )
        ]
        proof = ProofTree(steps=steps)
        
        analysis = decoder.analyze_proof_tree(proof)
        
        # Dovrebbe rilevare entrambi gli algoritmi
        assert len(analysis["algorithm_hints"]) >= 1
        assert len(analysis["code_template_candidates"]) >= 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
