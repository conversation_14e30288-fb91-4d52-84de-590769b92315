"""
NEUROGLYPH Symbol Fallback Tests
Test fallback semantico per tutti i 1,854 simboli Unicode
"""

import pytest
import json
import ast
import sys
from pathlib import Path
from typing import Dict, List, Any, Set

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


class TestSymbolFallback:
    """Test fallback semantico per simboli Unicode."""
    
    @pytest.fixture(scope="class")
    def all_symbols(self):
        """Carica tutti i simboli dal registry."""
        fixtures_path = Path(__file__).parent.parent / "fixtures" / "fixtures_list.json"
        
        if not fixtures_path.exists():
            pytest.skip(f"Fixtures list non trovata: {fixtures_path}")
        
        with open(fixtures_path, 'r', encoding='utf-8') as f:
            fixtures = json.load(f)
        
        # Estrai tutti i simboli
        all_symbols = []
        for domain, symbols in fixtures['symbols_by_domain'].items():
            for symbol in symbols:
                all_symbols.append({
                    'symbol': symbol,
                    'domain': domain
                })
        
        return all_symbols
    
    @pytest.fixture(scope="class")
    def semantic_templates(self):
        """Carica template semantici."""
        templates_path = Path(__file__).parent.parent.parent / "data" / "symbols" / "semantic_templates.json"
        
        if not templates_path.exists():
            pytest.skip(f"Template semantici non trovati: {templates_path}")
        
        with open(templates_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def test_semantic_templates_loaded(self, semantic_templates):
        """Test che i template semantici siano caricati."""
        assert len(semantic_templates) > 0, "Nessun template semantico caricato"
        
        # Verifica formato template
        for symbol, template in list(semantic_templates.items())[:5]:
            assert isinstance(symbol, str), f"Simbolo non stringa: {type(symbol)}"
            assert isinstance(template, str), f"Template non stringa: {type(template)}"
            assert len(template) > 0, f"Template vuoto per simbolo: {symbol}"
            assert '(' in template and ')' in template, f"Template non funzione per simbolo: {symbol}"
        
        print(f"✅ Caricati {len(semantic_templates)} template semantici")
    
    def test_template_coverage(self, all_symbols, semantic_templates):
        """Test copertura template per simboli."""
        symbols_with_templates = set(semantic_templates.keys())
        all_symbol_chars = set(s['symbol'] for s in all_symbols)
        
        # Simboli senza template
        missing_templates = all_symbol_chars - symbols_with_templates
        
        # Calcola copertura
        coverage = len(symbols_with_templates & all_symbol_chars) / len(all_symbol_chars)
        
        print(f"📊 Copertura template semantici:")
        print(f"   - Simboli totali: {len(all_symbol_chars)}")
        print(f"   - Con template: {len(symbols_with_templates & all_symbol_chars)}")
        print(f"   - Senza template: {len(missing_templates)}")
        print(f"   - Copertura: {coverage:.2%}")
        
        if missing_templates:
            print(f"❌ Primi simboli senza template: {list(missing_templates)[:10]}")
        
        # Per ora accettiamo copertura parziale, ma target è 100%
        # assert coverage >= 0.50, f"Copertura template troppo bassa: {coverage:.2%}"
        
        # Almeno i simboli critici devono avere template
        critical_symbols = ['⦟', '⌮', '⟨', '⟩', '⊢', '⊨', '⊥', '⊤', '∀', '∃']
        missing_critical = [s for s in critical_symbols if s not in symbols_with_templates]
        assert len(missing_critical) == 0, f"Simboli critici senza template: {missing_critical}"
    
    @pytest.mark.parametrize("symbol_data", 
                           [{'symbol': '⦟', 'domain': 'logic'}, 
                            {'symbol': '⌮', 'domain': 'logic'},
                            {'symbol': '∀', 'domain': 'logic'},
                            {'symbol': '∃', 'domain': 'logic'},
                            {'symbol': '∑', 'domain': 'math'},
                            {'symbol': 'λ', 'domain': 'code'}])
    def test_critical_symbol_fallback(self, symbol_data, semantic_templates):
        """Test fallback per simboli critici."""
        symbol = symbol_data['symbol']
        domain = symbol_data['domain']
        
        # Verifica presenza template
        assert symbol in semantic_templates, f"Template mancante per simbolo critico: {symbol}"
        
        template = semantic_templates[symbol]
        
        # Verifica formato template
        assert isinstance(template, str), f"Template non stringa per {symbol}"
        assert len(template) > 5, f"Template troppo corto per {symbol}: {template}"
        assert '(' in template and ')' in template, f"Template non funzione per {symbol}: {template}"
        
        # Verifica che il template sia parsabile come Python
        try:
            # Prova a parsare come espressione Python
            parsed = ast.parse(template, mode='eval')
            assert parsed is not None, f"Template non parsabile per {symbol}: {template}"
        except SyntaxError as e:
            pytest.fail(f"Errore sintassi template per {symbol}: {template} - {e}")
    
    def test_template_syntax_validation(self, semantic_templates):
        """Test validazione sintassi template."""
        valid_templates = 0
        invalid_templates = []
        
        for symbol, template in semantic_templates.items():
            try:
                # Prova parsing come espressione Python
                parsed = ast.parse(template, mode='eval')
                
                # Verifica che sia una chiamata di funzione
                if isinstance(parsed.body, ast.Call):
                    valid_templates += 1
                else:
                    invalid_templates.append((symbol, template, "Non è una chiamata di funzione"))
                
            except SyntaxError as e:
                invalid_templates.append((symbol, template, f"Errore sintassi: {e}"))
            except Exception as e:
                invalid_templates.append((symbol, template, f"Errore generico: {e}"))
        
        total_templates = len(semantic_templates)
        validity_rate = valid_templates / total_templates if total_templates > 0 else 0
        
        print(f"📊 Validazione sintassi template:")
        print(f"   - Template validi: {valid_templates}")
        print(f"   - Template invalidi: {len(invalid_templates)}")
        print(f"   - Tasso validità: {validity_rate:.2%}")
        
        if invalid_templates:
            print(f"❌ Primi template invalidi:")
            for symbol, template, error in invalid_templates[:5]:
                print(f"   - {symbol}: {template} ({error})")
        
        # Target: almeno 95% template validi
        assert validity_rate >= 0.95, f"Troppi template invalidi: {validity_rate:.2%}"
    
    def test_domain_specific_templates(self, all_symbols, semantic_templates):
        """Test template specifici per dominio."""
        domain_template_stats = {}
        
        for symbol_data in all_symbols:
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            if domain not in domain_template_stats:
                domain_template_stats[domain] = {
                    'total': 0,
                    'with_template': 0,
                    'symbols': []
                }
            
            domain_template_stats[domain]['total'] += 1
            domain_template_stats[domain]['symbols'].append(symbol)
            
            if symbol in semantic_templates:
                domain_template_stats[domain]['with_template'] += 1
        
        # Analizza copertura per dominio
        critical_domains = ['logic', 'math', 'code', 'advanced_coding']
        
        print(f"📊 Copertura template per dominio:")
        for domain in critical_domains:
            if domain in domain_template_stats:
                stats = domain_template_stats[domain]
                coverage = stats['with_template'] / stats['total'] if stats['total'] > 0 else 0
                print(f"   - {domain}: {coverage:.2%} ({stats['with_template']}/{stats['total']})")
                
                # Domini critici dovrebbero avere almeno qualche template
                if stats['total'] > 10:  # Solo se ci sono abbastanza simboli
                    assert coverage > 0, f"Dominio critico '{domain}' senza template"
    
    def test_template_function_names(self, semantic_templates):
        """Test nomi funzioni nei template."""
        function_names = {}
        
        for symbol, template in semantic_templates.items():
            try:
                parsed = ast.parse(template, mode='eval')
                
                if isinstance(parsed.body, ast.Call):
                    if isinstance(parsed.body.func, ast.Name):
                        func_name = parsed.body.func.id
                        
                        if func_name not in function_names:
                            function_names[func_name] = []
                        function_names[func_name].append(symbol)
                
            except Exception:
                continue  # Skip errori per questo test
        
        print(f"📊 Funzioni nei template:")
        print(f"   - Funzioni uniche: {len(function_names)}")
        
        # Mostra funzioni più comuni
        sorted_functions = sorted(function_names.items(), key=lambda x: len(x[1]), reverse=True)
        for func_name, symbols in sorted_functions[:10]:
            print(f"   - {func_name}: {len(symbols)} simboli")
        
        # Verifica diversità funzioni
        assert len(function_names) >= 10, f"Troppo poche funzioni diverse: {len(function_names)}"
        
        # Verifica nomi funzioni sensati
        expected_functions = ['logical_', 'mathematical_', 'semantic_', 'operator_', 'bracket_']
        found_expected = 0
        for func_name in function_names:
            if any(expected in func_name for expected in expected_functions):
                found_expected += 1
        
        # Almeno alcune funzioni dovrebbero seguire convenzioni
        assert found_expected >= 5, f"Troppo poche funzioni con nomi convenzionali: {found_expected}"
    
    def test_generate_missing_templates(self, all_symbols, semantic_templates):
        """Test generazione template mancanti (helper)."""
        symbols_without_templates = []
        
        for symbol_data in all_symbols:
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            if symbol not in semantic_templates:
                symbols_without_templates.append({
                    'symbol': symbol,
                    'domain': domain,
                    'suggested_template': self._generate_template_suggestion(symbol, domain)
                })
        
        if symbols_without_templates:
            print(f"\n📝 Suggerimenti template per {len(symbols_without_templates)} simboli:")
            for item in symbols_without_templates[:20]:  # Primi 20
                print(f"   \"{item['symbol']}\": \"{item['suggested_template']}\",")
        
        # Questo test non fallisce, ma fornisce suggerimenti
        print(f"💡 Simboli senza template: {len(symbols_without_templates)}")
    
    def _generate_template_suggestion(self, symbol: str, domain: str) -> str:
        """Genera suggerimento template per simbolo."""
        # Template base per dominio
        domain_templates = {
            'logic': f'logical_operator_{ord(symbol)}(left, right)',
            'math': f'mathematical_function_{ord(symbol)}(input)',
            'code': f'code_construct_{ord(symbol)}()',
            'advanced_coding': f'advanced_operation_{ord(symbol)}(args)',
            'ai': f'ai_concept_{ord(symbol)}(context)',
            'cognition': f'cognitive_process_{ord(symbol)}(state)',
            'meta': f'meta_operation_{ord(symbol)}(object)',
            'operator': f'operator_{ord(symbol)}(operands)',
            'structure': f'structural_element_{ord(symbol)}(components)'
        }
        
        return domain_templates.get(domain, f'symbol_function_{ord(symbol)}()')
