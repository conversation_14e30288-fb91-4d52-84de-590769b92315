"""
NEUROGLYPH Symbol Prompt Memory Tests
Test contesto prompt per tutti i 1,854 simboli Unicode
"""

import pytest
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


class TestSymbolPromptMemory:
    """Test contesto prompt per simboli Unicode."""
    
    @pytest.fixture(scope="class")
    def all_symbols(self):
        """Carica tutti i simboli dal registry."""
        fixtures_path = Path(__file__).parent.parent / "fixtures" / "fixtures_list.json"
        
        if not fixtures_path.exists():
            pytest.skip(f"Fixtures list non trovata: {fixtures_path}")
        
        with open(fixtures_path, 'r', encoding='utf-8') as f:
            fixtures = json.load(f)
        
        # Estrai tutti i simboli
        all_symbols = []
        for domain, symbols in fixtures['symbols_by_domain'].items():
            for symbol in symbols:
                all_symbols.append({
                    'symbol': symbol,
                    'domain': domain
                })
        
        return all_symbols
    
    @pytest.fixture(scope="class")
    def prompt_contexts(self):
        """Carica contesti prompt."""
        contexts_path = Path(__file__).parent.parent.parent / "data" / "symbols" / "prompt_contexts.json"
        
        if not contexts_path.exists():
            pytest.skip(f"Contesti prompt non trovati: {contexts_path}")
        
        with open(contexts_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def test_prompt_contexts_loaded(self, prompt_contexts):
        """Test che i contesti prompt siano caricati."""
        assert len(prompt_contexts) > 0, "Nessun contesto prompt caricato"
        
        # Verifica formato contesti
        for symbol, context in list(prompt_contexts.items())[:5]:
            assert isinstance(symbol, str), f"Simbolo non stringa: {type(symbol)}"
            assert isinstance(context, str), f"Contesto non stringa: {type(context)}"
            assert len(context) >= 10, f"Contesto troppo corto per simbolo: {symbol}"
        
        print(f"✅ Caricati {len(prompt_contexts)} contesti prompt")
    
    def test_context_coverage(self, all_symbols, prompt_contexts):
        """Test copertura contesti per simboli."""
        symbols_with_contexts = set(prompt_contexts.keys())
        all_symbol_chars = set(s['symbol'] for s in all_symbols)
        
        # Simboli senza contesto
        missing_contexts = all_symbol_chars - symbols_with_contexts
        
        # Calcola copertura
        coverage = len(symbols_with_contexts & all_symbol_chars) / len(all_symbol_chars)
        
        print(f"📊 Copertura contesti prompt:")
        print(f"   - Simboli totali: {len(all_symbol_chars)}")
        print(f"   - Con contesto: {len(symbols_with_contexts & all_symbol_chars)}")
        print(f"   - Senza contesto: {len(missing_contexts)}")
        print(f"   - Copertura: {coverage:.2%}")
        
        if missing_contexts:
            print(f"❌ Primi simboli senza contesto: {list(missing_contexts)[:10]}")
        
        # Per ora accettiamo copertura parziale, ma target è 100%
        # assert coverage >= 0.50, f"Copertura contesti troppo bassa: {coverage:.2%}"
        
        # Almeno i simboli critici devono avere contesto
        critical_symbols = ['⦟', '⌮', '⟨', '⟩', '⊢', '⊨', '⊥', '⊤', '∀', '∃']
        missing_critical = [s for s in critical_symbols if s not in symbols_with_contexts]
        assert len(missing_critical) == 0, f"Simboli critici senza contesto: {missing_critical}"
    
    @pytest.mark.parametrize("symbol_data", 
                           [{'symbol': '⦟', 'domain': 'logic'}, 
                            {'symbol': '⌮', 'domain': 'logic'},
                            {'symbol': '∀', 'domain': 'logic'},
                            {'symbol': '∃', 'domain': 'logic'},
                            {'symbol': '∑', 'domain': 'math'},
                            {'symbol': 'λ', 'domain': 'code'}])
    def test_critical_symbol_contexts(self, symbol_data, prompt_contexts):
        """Test contesti per simboli critici."""
        symbol = symbol_data['symbol']
        domain = symbol_data['domain']
        
        # Verifica presenza contesto
        assert symbol in prompt_contexts, f"Contesto mancante per simbolo critico: {symbol}"
        
        context = prompt_contexts[symbol]
        
        # Verifica qualità contesto
        assert isinstance(context, str), f"Contesto non stringa per {symbol}"
        assert len(context) >= 20, f"Contesto troppo corto per {symbol}: {context}"
        assert len(context) <= 500, f"Contesto troppo lungo per {symbol}: {len(context)} caratteri"
        
        # Verifica contenuto semantico
        context_lower = context.lower()
        
        # Deve contenere parole chiave semantiche
        semantic_keywords = ['operatore', 'simbolo', 'rappresenta', 'indica', 'esprime', 'utilizzato', 'per']
        has_semantic_keyword = any(keyword in context_lower for keyword in semantic_keywords)
        assert has_semantic_keyword, f"Contesto senza parole chiave semantiche per {symbol}: {context}"
        
        # Verifica dominio-specifica
        domain_keywords = {
            'logic': ['logica', 'proposizione', 'verità', 'falso', 'vero', 'condizione'],
            'math': ['matematica', 'calcolo', 'funzione', 'numero', 'operazione'],
            'code': ['programmazione', 'codice', 'funzione', 'lambda', 'algoritmo']
        }
        
        if domain in domain_keywords:
            domain_words = domain_keywords[domain]
            has_domain_keyword = any(keyword in context_lower for keyword in domain_words)
            assert has_domain_keyword, f"Contesto senza parole chiave di dominio per {symbol} ({domain}): {context}"
    
    def test_context_quality_metrics(self, prompt_contexts):
        """Test metriche qualità contesti."""
        quality_stats = {
            'too_short': 0,
            'too_long': 0,
            'good_length': 0,
            'has_semantic_words': 0,
            'has_technical_words': 0,
            'total': len(prompt_contexts)
        }
        
        semantic_words = ['operatore', 'simbolo', 'rappresenta', 'indica', 'esprime', 'utilizzato', 'per', 'che']
        technical_words = ['logica', 'matematica', 'funzione', 'calcolo', 'algoritmo', 'struttura', 'relazione']
        
        for symbol, context in prompt_contexts.items():
            context_len = len(context)
            context_lower = context.lower()
            
            # Lunghezza
            if context_len < 20:
                quality_stats['too_short'] += 1
            elif context_len > 300:
                quality_stats['too_long'] += 1
            else:
                quality_stats['good_length'] += 1
            
            # Parole semantiche
            if any(word in context_lower for word in semantic_words):
                quality_stats['has_semantic_words'] += 1
            
            # Parole tecniche
            if any(word in context_lower for word in technical_words):
                quality_stats['has_technical_words'] += 1
        
        total = quality_stats['total']
        
        print(f"📊 Metriche qualità contesti:")
        print(f"   - Lunghezza buona: {quality_stats['good_length']}/{total} ({quality_stats['good_length']/total:.2%})")
        print(f"   - Troppo corti: {quality_stats['too_short']}")
        print(f"   - Troppo lunghi: {quality_stats['too_long']}")
        print(f"   - Con parole semantiche: {quality_stats['has_semantic_words']}/{total} ({quality_stats['has_semantic_words']/total:.2%})")
        print(f"   - Con parole tecniche: {quality_stats['has_technical_words']}/{total} ({quality_stats['has_technical_words']/total:.2%})")
        
        # Target qualità
        good_length_rate = quality_stats['good_length'] / total
        semantic_rate = quality_stats['has_semantic_words'] / total
        technical_rate = quality_stats['has_technical_words'] / total
        
        assert good_length_rate >= 0.80, f"Troppi contesti con lunghezza sbagliata: {good_length_rate:.2%}"
        assert semantic_rate >= 0.90, f"Troppi contesti senza parole semantiche: {semantic_rate:.2%}"
        assert technical_rate >= 0.70, f"Troppi contesti senza parole tecniche: {technical_rate:.2%}"
    
    def test_domain_specific_contexts(self, all_symbols, prompt_contexts):
        """Test contesti specifici per dominio."""
        domain_context_stats = {}
        
        for symbol_data in all_symbols:
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            if domain not in domain_context_stats:
                domain_context_stats[domain] = {
                    'total': 0,
                    'with_context': 0,
                    'avg_length': 0,
                    'total_length': 0
                }
            
            domain_context_stats[domain]['total'] += 1
            
            if symbol in prompt_contexts:
                domain_context_stats[domain]['with_context'] += 1
                domain_context_stats[domain]['total_length'] += len(prompt_contexts[symbol])
        
        # Calcola medie
        for domain, stats in domain_context_stats.items():
            if stats['with_context'] > 0:
                stats['avg_length'] = stats['total_length'] / stats['with_context']
        
        # Analizza domini critici
        critical_domains = ['logic', 'math', 'code', 'advanced_coding', 'ai']
        
        print(f"📊 Contesti per dominio:")
        for domain in critical_domains:
            if domain in domain_context_stats:
                stats = domain_context_stats[domain]
                coverage = stats['with_context'] / stats['total'] if stats['total'] > 0 else 0
                avg_len = stats['avg_length']
                print(f"   - {domain}: {coverage:.2%} copertura, {avg_len:.0f} caratteri medi")
                
                # Domini critici dovrebbero avere contesti di qualità
                if stats['total'] > 5:  # Solo se ci sono abbastanza simboli
                    assert coverage > 0, f"Dominio critico '{domain}' senza contesti"
                    if coverage > 0:
                        assert avg_len >= 30, f"Contesti troppo corti per dominio '{domain}': {avg_len:.0f} caratteri"
    
    def test_context_uniqueness(self, prompt_contexts):
        """Test unicità contesti."""
        context_counts = {}
        duplicate_contexts = []
        
        for symbol, context in prompt_contexts.items():
            if context in context_counts:
                context_counts[context].append(symbol)
                if len(context_counts[context]) == 2:  # Prima duplicazione
                    duplicate_contexts.append(context)
            else:
                context_counts[context] = [symbol]
        
        duplicate_rate = len(duplicate_contexts) / len(prompt_contexts)
        
        print(f"📊 Unicità contesti:")
        print(f"   - Contesti unici: {len(prompt_contexts) - len(duplicate_contexts)}")
        print(f"   - Contesti duplicati: {len(duplicate_contexts)}")
        print(f"   - Tasso duplicazione: {duplicate_rate:.2%}")
        
        if duplicate_contexts:
            print(f"❌ Primi contesti duplicati:")
            for context in duplicate_contexts[:3]:
                symbols = context_counts[context]
                print(f"   - '{context[:50]}...' usato per: {symbols}")
        
        # Target: meno del 5% di duplicati
        assert duplicate_rate <= 0.05, f"Troppi contesti duplicati: {duplicate_rate:.2%}"
    
    def test_generate_missing_contexts(self, all_symbols, prompt_contexts):
        """Test generazione contesti mancanti (helper)."""
        symbols_without_contexts = []
        
        for symbol_data in all_symbols:
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            if symbol not in prompt_contexts:
                symbols_without_contexts.append({
                    'symbol': symbol,
                    'domain': domain,
                    'suggested_context': self._generate_context_suggestion(symbol, domain)
                })
        
        if symbols_without_contexts:
            print(f"\n📝 Suggerimenti contesti per {len(symbols_without_contexts)} simboli:")
            for item in symbols_without_contexts[:10]:  # Primi 10
                print(f"   \"{item['symbol']}\": \"{item['suggested_context']}\",")
        
        # Questo test non fallisce, ma fornisce suggerimenti
        print(f"💡 Simboli senza contesto: {len(symbols_without_contexts)}")
    
    def _generate_context_suggestion(self, symbol: str, domain: str) -> str:
        """Genera suggerimento contesto per simbolo."""
        # Contesti base per dominio
        domain_contexts = {
            'logic': f'Operatore logico utilizzato per esprimere relazioni formali tra proposizioni nel dominio {domain}',
            'math': f'Simbolo matematico utilizzato per rappresentare operazioni o concetti nel calcolo e nell\'analisi',
            'code': f'Simbolo di programmazione utilizzato per costrutti avanzati e operazioni su codice',
            'advanced_coding': f'Operatore avanzato per programmazione di alto livello e meta-programmazione',
            'ai': f'Simbolo per concetti di intelligenza artificiale e ragionamento automatico',
            'cognition': f'Rappresentazione di processi cognitivi e modelli mentali',
            'meta': f'Operatore meta-linguistico per riferimenti di livello superiore',
            'operator': f'Operatore generico per trasformazioni e manipolazioni di dati',
            'structure': f'Elemento strutturale per organizzazione e composizione di sistemi'
        }
        
        base_context = domain_contexts.get(domain, f'Simbolo specializzato per il dominio {domain}')
        return f"{base_context} (Unicode: U+{ord(symbol):04X})"
