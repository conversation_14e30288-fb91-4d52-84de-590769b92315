"""
NEUROGLYPH Symbolic Reasoning Tests
Test reasoning simbolico per simboli logic/math
"""

import pytest
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import *
    LOGIC_AVAILABLE = True
except ImportError:
    LOGIC_AVAILABLE = False


class TestSymbolicReasoning:
    """Test reasoning simbolico per simboli Unicode."""
    
    @pytest.fixture(scope="class")
    def logic_symbols(self):
        """Carica simboli logici e matematici."""
        fixtures_path = Path(__file__).parent.parent / "fixtures" / "fixtures_list.json"
        
        if not fixtures_path.exists():
            pytest.skip(f"Fixtures list non trovata: {fixtures_path}")
        
        with open(fixtures_path, 'r', encoding='utf-8') as f:
            fixtures = json.load(f)
        
        # Estrai simboli logici e matematici
        logic_symbols = []
        target_domains = ['logic', 'math', 'logical_operators', 'mathematical_operators']
        
        for domain, symbols in fixtures['symbols_by_domain'].items():
            if domain in target_domains:
                for symbol in symbols:
                    logic_symbols.append({
                        'symbol': symbol,
                        'domain': domain
                    })
        
        return logic_symbols
    
    @pytest.fixture(scope="class")
    def logic_engine(self):
        """Inizializza Logic Engine."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        return FormalLogicEngine(max_depth=5, max_steps=20)
    
    def test_logic_symbols_loaded(self, logic_symbols):
        """Test che i simboli logici siano caricati."""
        assert len(logic_symbols) > 0, "Nessun simbolo logico caricato"
        
        # Verifica presenza simboli critici
        symbols_set = set(s['symbol'] for s in logic_symbols)
        critical_logic_symbols = ['⊢', '⊨', '⊥', '⊤', '∀', '∃', '¬', '∧', '∨', '→', '↔']
        
        found_critical = [s for s in critical_logic_symbols if s in symbols_set]
        
        print(f"✅ Caricati {len(logic_symbols)} simboli logici/matematici")
        print(f"🔥 Simboli critici trovati: {len(found_critical)}/{len(critical_logic_symbols)}")
        print(f"   Simboli critici: {found_critical}")
        
        # Almeno alcuni simboli critici devono essere presenti
        assert len(found_critical) >= 5, f"Troppo pochi simboli logici critici: {found_critical}"
    
    @pytest.mark.parametrize("symbol_data", 
                           [{'symbol': '⊢', 'domain': 'logic', 'test_type': 'entailment'}, 
                            {'symbol': '∀', 'domain': 'logic', 'test_type': 'universal'},
                            {'symbol': '∃', 'domain': 'logic', 'test_type': 'existential'},
                            {'symbol': '→', 'domain': 'logic', 'test_type': 'implication'},
                            {'symbol': '∧', 'domain': 'logic', 'test_type': 'conjunction'},
                            {'symbol': '∨', 'domain': 'logic', 'test_type': 'disjunction'}])
    def test_critical_logic_symbol_reasoning(self, symbol_data, logic_engine):
        """Test reasoning per simboli logici critici."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        symbol = symbol_data['symbol']
        test_type = symbol_data['test_type']
        
        try:
            if test_type == 'entailment':
                # Test ⊢ (entailment)
                premises = [P("A")]
                goal = P("A")
                result = logic_engine.deduce(premises, goal)
                assert result.success, f"Entailment semplice fallito per {symbol}"
                
            elif test_type == 'universal':
                # Test ∀ (universal quantifier)
                x = Variable("x")
                premises = [Universal(x, Implication(P("Man", x), P("Mortal", x))), P("Man", Constant("Socrates"))]
                goal = P("Mortal", Constant("Socrates"))
                result = logic_engine.deduce(premises, goal)
                assert result.success, f"Universal quantification fallita per {symbol}"
                
            elif test_type == 'existential':
                # Test ∃ (existential quantifier) - caso semplificato
                premises = [P("Man", Constant("Socrates"))]
                goal = P("Man", Constant("Socrates"))
                result = logic_engine.deduce(premises, goal)
                assert result.success, f"Existential test fallito per {symbol}"
                
            elif test_type == 'implication':
                # Test → (implication)
                premises = [Implication(P("A"), P("B")), P("A")]
                goal = P("B")
                result = logic_engine.deduce(premises, goal)
                assert result.success, f"Modus ponens fallito per {symbol}"
                
            elif test_type == 'conjunction':
                # Test ∧ (conjunction)
                premises = [P("A"), P("B")]
                goal = Conjunction(P("A"), P("B"))
                result = logic_engine.deduce(premises, goal)
                assert result.success, f"Conjunction introduction fallita per {symbol}"
                
            elif test_type == 'disjunction':
                # Test ∨ (disjunction)
                premises = [P("A")]
                goal = Disjunction(P("A"), P("B"))
                result = logic_engine.deduce(premises, goal)
                assert result.success, f"Disjunction introduction fallita per {symbol}"
            
            print(f"✅ Reasoning test passato per simbolo {symbol} ({test_type})")
            
        except Exception as e:
            pytest.fail(f"Errore reasoning per simbolo '{symbol}' ({test_type}): {e}")
    
    def test_logic_engine_basic_functionality(self, logic_engine):
        """Test funzionalità base Logic Engine."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Test 1: Modus ponens semplice
        premises = [Implication(P("A"), P("B")), P("A")]
        goal = P("B")
        result = logic_engine.deduce(premises, goal)
        
        assert result.success, "Modus ponens base fallito"
        assert result.proof_tree is not None, "Proof tree mancante"
        assert result.steps_count > 0, "Nessun step nel proof"
        
        print(f"✅ Logic Engine funzionante: {result.steps_count} steps, {result.depth} depth")
    
    def test_symbolic_formula_parsing(self):
        """Test parsing formule simboliche."""
        test_formulas = [
            ("P(x)", "Predicato semplice"),
            ("P(x) -> Q(x)", "Implicazione"),
            ("P(x) & Q(x)", "Congiunzione"),
            ("forall x: P(x)", "Quantificatore universale"),
            ("forall x: P(x) -> Q(x)", "Quantificatore con implicazione")
        ]
        
        for formula_str, description in test_formulas:
            try:
                parsed = parse_formula(formula_str)
                assert parsed is not None, f"Parsing fallito per: {formula_str}"
                print(f"✅ Parsed {description}: {formula_str}")
                
            except Exception as e:
                pytest.fail(f"Errore parsing formula '{formula_str}' ({description}): {e}")
    
    def test_mathematical_symbol_interpretation(self, logic_symbols):
        """Test interpretazione simboli matematici."""
        math_symbols = [s for s in logic_symbols if s['domain'] in ['math', 'mathematical_operators']]
        
        if not math_symbols:
            pytest.skip("Nessun simbolo matematico trovato")
        
        # Test interpretazione simboli matematici comuni
        math_interpretations = {
            '∑': 'summation',
            '∏': 'product',
            '∫': 'integral',
            '∂': 'partial_derivative',
            '∇': 'gradient',
            '∞': 'infinity',
            '≈': 'approximately_equal',
            '≡': 'equivalent',
            '≠': 'not_equal',
            '≤': 'less_than_or_equal',
            '≥': 'greater_than_or_equal'
        }
        
        interpreted_count = 0
        for symbol_data in math_symbols:
            symbol = symbol_data['symbol']
            
            if symbol in math_interpretations:
                interpretation = math_interpretations[symbol]
                
                # Test che il simbolo abbia un'interpretazione sensata
                assert len(interpretation) > 0, f"Interpretazione vuota per {symbol}"
                assert '_' in interpretation or interpretation.isalpha(), f"Interpretazione non valida per {symbol}: {interpretation}"
                
                interpreted_count += 1
        
        interpretation_rate = interpreted_count / len(math_symbols) if math_symbols else 0
        
        print(f"📊 Interpretazione simboli matematici:")
        print(f"   - Simboli matematici: {len(math_symbols)}")
        print(f"   - Interpretati: {interpreted_count}")
        print(f"   - Tasso interpretazione: {interpretation_rate:.2%}")
        
        # Almeno alcuni simboli matematici devono essere interpretabili
        assert interpreted_count >= 3, f"Troppo pochi simboli matematici interpretati: {interpreted_count}"
    
    def test_reasoning_performance(self, logic_engine):
        """Test performance reasoning."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Test performance su reasoning semplici
        test_cases = [
            # Caso 1: Modus ponens
            {
                'name': 'modus_ponens',
                'premises': [Implication(P("A"), P("B")), P("A")],
                'goal': P("B")
            },
            # Caso 2: Congiunzione
            {
                'name': 'conjunction',
                'premises': [P("A"), P("B")],
                'goal': Conjunction(P("A"), P("B"))
            },
            # Caso 3: Sillogismo semplice
            {
                'name': 'syllogism',
                'premises': [Implication(P("A"), P("B")), Implication(P("B"), P("C")), P("A")],
                'goal': P("C")
            }
        ]
        
        performance_results = []
        
        for test_case in test_cases:
            try:
                result = logic_engine.deduce(test_case['premises'], test_case['goal'])
                
                performance_results.append({
                    'name': test_case['name'],
                    'success': result.success,
                    'duration': result.duration,
                    'steps': result.steps_count,
                    'depth': result.depth
                })
                
            except Exception as e:
                performance_results.append({
                    'name': test_case['name'],
                    'success': False,
                    'error': str(e)
                })
        
        # Analizza risultati
        successful_cases = [r for r in performance_results if r['success']]
        success_rate = len(successful_cases) / len(test_cases)
        
        if successful_cases:
            avg_duration = sum(r['duration'] for r in successful_cases) / len(successful_cases)
            avg_steps = sum(r['steps'] for r in successful_cases) / len(successful_cases)
            
            print(f"📊 Performance reasoning:")
            print(f"   - Success rate: {success_rate:.2%}")
            print(f"   - Durata media: {avg_duration:.3f}s")
            print(f"   - Steps medi: {avg_steps:.1f}")
            
            # Target performance
            assert success_rate >= 0.80, f"Success rate troppo basso: {success_rate:.2%}"
            assert avg_duration <= 1.0, f"Reasoning troppo lento: {avg_duration:.3f}s"
        
        else:
            pytest.fail("Nessun test case di performance riuscito")
    
    @pytest.mark.slow
    def test_batch_symbolic_reasoning(self, logic_symbols, logic_engine):
        """Test reasoning batch su simboli logici (lento)."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Filtra solo simboli logici principali
        main_logic_symbols = [s for s in logic_symbols if s['symbol'] in ['⊢', '∀', '∃', '→', '∧', '∨', '¬', '↔']]
        
        if not main_logic_symbols:
            pytest.skip("Nessun simbolo logico principale trovato")
        
        reasoning_results = []
        
        for symbol_data in main_logic_symbols:
            symbol = symbol_data['symbol']
            
            try:
                # Test reasoning semplice per ogni simbolo
                if symbol == '→':
                    premises = [Implication(P("A"), P("B")), P("A")]
                    goal = P("B")
                elif symbol == '∧':
                    premises = [P("A"), P("B")]
                    goal = Conjunction(P("A"), P("B"))
                elif symbol == '∨':
                    premises = [P("A")]
                    goal = Disjunction(P("A"), P("B"))
                else:
                    # Test generico
                    premises = [P("A")]
                    goal = P("A")
                
                result = logic_engine.deduce(premises, goal)
                
                reasoning_results.append({
                    'symbol': symbol,
                    'success': result.success,
                    'steps': result.steps_count,
                    'duration': result.duration
                })
                
            except Exception as e:
                reasoning_results.append({
                    'symbol': symbol,
                    'success': False,
                    'error': str(e)
                })
        
        # Analizza risultati batch
        successful = [r for r in reasoning_results if r['success']]
        success_rate = len(successful) / len(reasoning_results)
        
        print(f"📊 Batch symbolic reasoning:")
        print(f"   - Simboli testati: {len(reasoning_results)}")
        print(f"   - Successi: {len(successful)}")
        print(f"   - Success rate: {success_rate:.2%}")
        
        if successful:
            avg_steps = sum(r['steps'] for r in successful) / len(successful)
            print(f"   - Steps medi: {avg_steps:.1f}")
        
        # Target: almeno 70% success rate per simboli logici
        assert success_rate >= 0.70, f"Batch reasoning success rate troppo basso: {success_rate:.2%}"
