"""
NEUROGLYPH Symbol AST Fidelity Tests
Test AST fidelity per tutti i 1,854 simboli Unicode
"""

import pytest
import json
import ast
import sys
from pathlib import Path
from typing import Dict, List, Any, Set

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from neuroglyph.parser.ng_parser import NGParser
    from neuroglyph.encoder.ng_encoder import NGEncoder
    from neuroglyph.decoder.ng_decoder import NGDecoder
    NEUROGLYPH_AVAILABLE = True
except ImportError:
    NEUROGLYPH_AVAILABLE = False


class TestSymbolASTFidelity:
    """Test AST fidelity per simboli Unicode."""
    
    @pytest.fixture(scope="class")
    def fidelity_cases(self):
        """Carica test cases AST fidelity."""
        fixtures_path = Path(__file__).parent.parent / "fixtures" / "ast_fidelity_cases.json"
        
        if not fixtures_path.exists():
            pytest.skip(f"Fixture AST fidelity non trovata: {fixtures_path}")
        
        with open(fixtures_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @pytest.fixture(scope="class")
    def ng_components(self):
        """Inizializza componenti NEUROGLYPH."""
        if not NEUROGLYPH_AVAILABLE:
            pytest.skip("NEUROGLYPH components non disponibili")
        
        return {
            'parser': NGParser(),
            'encoder': NGEncoder(),
            'decoder': NGDecoder()
        }
    
    def test_fidelity_cases_loaded(self, fidelity_cases):
        """Test che i test cases fidelity siano caricati."""
        assert len(fidelity_cases) == 1854, f"Attesi 1854 test cases, trovati: {len(fidelity_cases)}"
        
        # Verifica struttura test cases
        for case in fidelity_cases[:5]:  # Primi 5
            assert 'symbol' in case, "Campo 'symbol' mancante"
            assert 'domain' in case, "Campo 'domain' mancante"
            assert 'code' in case, "Campo 'code' mancante"
            assert 'expected_symbols' in case, "Campo 'expected_symbols' mancante"
        
        print(f"✅ Caricati {len(fidelity_cases)} test cases AST fidelity")
    
    @pytest.mark.parametrize("case", 
                           json.load(open(Path(__file__).parent.parent / "fixtures" / "ast_fidelity_cases.json", 'r', encoding='utf-8'))[:50])  # Primi 50
    def test_ast_structure_preservation(self, case):
        """Test preservazione struttura AST."""
        symbol = case['symbol']
        code = case['code']
        expected_symbols = case['expected_symbols']
        
        try:
            # Parse codice complesso
            parsed_ast = ast.parse(code)
            
            # Verifica struttura AST
            assert parsed_ast is not None, f"Parsing fallito per simbolo {symbol}"
            
            # Verifica nodi AST attesi
            ast_dump = ast.dump(parsed_ast)
            
            # Deve contenere nodi di base
            expected_nodes = ['FunctionDef', 'ClassDef', 'Assign', 'If']
            found_nodes = [node for node in expected_nodes if node in ast_dump]
            assert len(found_nodes) >= 2, f"Struttura AST troppo semplice per {symbol}: {found_nodes}"
            
            # Verifica presenza simboli
            for expected_symbol in expected_symbols:
                assert expected_symbol in code, f"Simbolo atteso '{expected_symbol}' non trovato nel codice"
            
        except SyntaxError as e:
            pytest.fail(f"Errore sintassi per simbolo '{symbol}': {e}")
        except Exception as e:
            pytest.fail(f"Errore AST fidelity per simbolo '{symbol}': {e}")
    
    def test_symbol_extraction_from_ast(self, fidelity_cases):
        """Test estrazione simboli da AST."""
        symbols_found = set()
        extraction_failures = []
        
        for case in fidelity_cases[:100]:  # Primi 100 per velocità
            symbol = case['symbol']
            code = case['code']
            
            try:
                # Parse e estrai simboli
                parsed_ast = ast.parse(code)
                
                # Estrai tutti i literal string dall'AST
                class SymbolExtractor(ast.NodeVisitor):
                    def __init__(self):
                        self.symbols = set()
                    
                    def visit_Constant(self, node):
                        if isinstance(node.value, str):
                            self.symbols.add(node.value)
                        self.generic_visit(node)
                    
                    def visit_Str(self, node):  # Python < 3.8 compatibility
                        self.symbols.add(node.s)
                        self.generic_visit(node)
                
                extractor = SymbolExtractor()
                extractor.visit(parsed_ast)
                
                # Verifica che il simbolo sia estratto
                if symbol in extractor.symbols:
                    symbols_found.add(symbol)
                else:
                    extraction_failures.append(symbol)
                
            except Exception as e:
                extraction_failures.append(f"{symbol} (error: {e})")
        
        extraction_rate = len(symbols_found) / min(100, len(fidelity_cases))
        
        print(f"📊 Estrazione simboli da AST:")
        print(f"   - Simboli estratti: {len(symbols_found)}")
        print(f"   - Fallimenti: {len(extraction_failures)}")
        print(f"   - Tasso estrazione: {extraction_rate:.2%}")
        
        if extraction_failures:
            print(f"❌ Primi fallimenti estrazione: {extraction_failures[:5]}")
        
        # Target: almeno 90% estrazione
        assert extraction_rate >= 0.90, f"Tasso estrazione troppo basso: {extraction_rate:.2%}"
    
    def test_complex_ast_nodes(self, fidelity_cases):
        """Test nodi AST complessi."""
        complex_node_counts = {
            'FunctionDef': 0,
            'ClassDef': 0,
            'If': 0,
            'For': 0,
            'While': 0,
            'Try': 0,
            'With': 0,
            'Lambda': 0
        }
        
        for case in fidelity_cases[:200]:  # Primi 200
            symbol = case['symbol']
            code = case['code']
            
            try:
                parsed_ast = ast.parse(code)
                ast_dump = ast.dump(parsed_ast)
                
                # Conta nodi complessi
                for node_type in complex_node_counts:
                    if node_type in ast_dump:
                        complex_node_counts[node_type] += 1
                
            except Exception:
                continue  # Skip errori per questo test
        
        print(f"📊 Distribuzione nodi AST complessi:")
        for node_type, count in complex_node_counts.items():
            print(f"   - {node_type}: {count}")
        
        # Verifica che abbiamo diversità di nodi
        nodes_with_cases = sum(1 for count in complex_node_counts.values() if count > 0)
        assert nodes_with_cases >= 4, f"Troppo pochi tipi di nodi AST: {nodes_with_cases}/8"
    
    def test_domain_specific_fidelity(self, fidelity_cases):
        """Test fidelity specifica per dominio."""
        domain_stats = {}
        
        for case in fidelity_cases:
            domain = case['domain']
            symbol = case['symbol']
            
            if domain not in domain_stats:
                domain_stats[domain] = {
                    'total': 0,
                    'parsed_ok': 0,
                    'symbols': []
                }
            
            domain_stats[domain]['total'] += 1
            domain_stats[domain]['symbols'].append(symbol)
            
            try:
                # Test parsing per dominio
                code = case['code']
                parsed_ast = ast.parse(code)
                
                if parsed_ast is not None:
                    domain_stats[domain]['parsed_ok'] += 1
                
            except Exception:
                continue
        
        # Analizza risultati per dominio
        critical_domains = ['logic', 'math', 'advanced_coding', 'cognition', 'ai']
        
        print(f"📊 Fidelity per dominio:")
        for domain in critical_domains:
            if domain in domain_stats:
                stats = domain_stats[domain]
                success_rate = stats['parsed_ok'] / stats['total'] if stats['total'] > 0 else 0
                print(f"   - {domain}: {success_rate:.2%} ({stats['parsed_ok']}/{stats['total']})")
                
                # Domini critici devono avere almeno 95% success
                assert success_rate >= 0.95, f"Dominio critico '{domain}' ha success rate troppo basso: {success_rate:.2%}"
    
    @pytest.mark.slow
    def test_full_ast_fidelity_batch(self, fidelity_cases):
        """Test AST fidelity completo (lento)."""
        success_count = 0
        syntax_errors = 0
        other_errors = 0
        
        for i, case in enumerate(fidelity_cases):
            if i % 200 == 0:
                print(f"🔄 Progresso AST fidelity: {i}/{len(fidelity_cases)} ({i/len(fidelity_cases)*100:.1f}%)")
            
            symbol = case['symbol']
            code = case['code']
            
            try:
                # Test parsing completo
                parsed_ast = ast.parse(code)
                
                # Verifica struttura minima
                ast_dump = ast.dump(parsed_ast)
                assert len(ast_dump) > 50, "AST troppo semplice"
                
                # Verifica presenza simbolo
                assert symbol in code, f"Simbolo {symbol} non nel codice"
                
                success_count += 1
                
            except SyntaxError:
                syntax_errors += 1
            except Exception:
                other_errors += 1
        
        total = len(fidelity_cases)
        success_rate = success_count / total
        syntax_error_rate = syntax_errors / total
        other_error_rate = other_errors / total
        
        print(f"\n📊 Risultati AST fidelity completi:")
        print(f"   - Successi: {success_count} ({success_rate:.2%})")
        print(f"   - Errori sintassi: {syntax_errors} ({syntax_error_rate:.2%})")
        print(f"   - Altri errori: {other_errors} ({other_error_rate:.2%})")
        
        # Target: almeno 98% success rate
        assert success_rate >= 0.98, f"AST fidelity success rate troppo basso: {success_rate:.2%}"
        
        # Errori sintassi devono essere < 1%
        assert syntax_error_rate <= 0.01, f"Troppi errori sintassi: {syntax_error_rate:.2%}"
