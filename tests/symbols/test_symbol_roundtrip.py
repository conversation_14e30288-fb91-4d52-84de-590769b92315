"""
NEUROGLYPH Symbol Round-trip Tests
Test round-trip AST per tutti i 1,854 simboli Unicode
"""

import pytest
import json
import ast
import sys
from pathlib import Path
from typing import Dict, List, Any

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from neuroglyph.parser.ng_parser import NGParser
    from neuroglyph.encoder.ng_encoder import NGEncoder
    from neuroglyph.decoder.ng_decoder import NGDecoder
    NEUROGLYPH_AVAILABLE = True
except ImportError:
    NEUROGLYPH_AVAILABLE = False


class TestSymbolRoundtrip:
    """Test round-trip AST per simboli Unicode."""
    
    @pytest.fixture(scope="class")
    def test_cases(self):
        """Carica test cases da fixture."""
        fixtures_path = Path(__file__).parent.parent / "fixtures" / "roundtrip_cases.json"
        
        if not fixtures_path.exists():
            pytest.skip(f"Fixture non trovata: {fixtures_path}")
        
        with open(fixtures_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @pytest.fixture(scope="class")
    def ng_components(self):
        """Inizializza componenti NEUROGLYPH."""
        if not NEUROGLYPH_AVAILABLE:
            pytest.skip("NEUROGLYPH components non disponibili")
        
        return {
            'parser': NGParser(),
            'encoder': NGEncoder(),
            'decoder': NGDecoder()
        }
    
    @pytest.fixture(scope="class")
    def fixtures_info(self):
        """Carica info fixture."""
        fixtures_path = Path(__file__).parent.parent / "fixtures" / "fixtures_list.json"
        
        if not fixtures_path.exists():
            pytest.skip(f"Fixtures list non trovata: {fixtures_path}")
        
        with open(fixtures_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def test_fixtures_loaded(self, test_cases, fixtures_info):
        """Test che le fixture siano caricate correttamente."""
        assert len(test_cases) > 0, "Nessun test case caricato"
        assert fixtures_info['total_symbols'] == 1854, f"Simboli attesi: 1854, trovati: {fixtures_info['total_symbols']}"
        
        print(f"✅ Caricati {len(test_cases)} test cases per {fixtures_info['total_symbols']} simboli")
    
    @pytest.mark.parametrize("test_case", 
                           json.load(open(Path(__file__).parent.parent / "fixtures" / "roundtrip_cases.json", 'r', encoding='utf-8'))[:100])  # Primi 100 per velocità
    def test_symbol_roundtrip_basic(self, test_case, ng_components):
        """Test round-trip base per simbolo."""
        if not NEUROGLYPH_AVAILABLE:
            pytest.skip("NEUROGLYPH non disponibile")
        
        symbol = test_case['symbol']
        code = test_case['code']
        case_id = test_case['case_id']
        
        parser = ng_components['parser']
        encoder = ng_components['encoder']
        decoder = ng_components['decoder']
        
        try:
            # Step 1: Parse codice originale
            original_ast = ast.parse(code)
            original_dump = ast.dump(original_ast, indent=2)
            
            # Step 2: Parse con NGParser (se disponibile)
            try:
                ng_parsed = parser.parse(code)
                assert ng_parsed is not None, f"NGParser fallito per {symbol}"
            except Exception as e:
                # Fallback a AST standard se NGParser non implementato
                ng_parsed = original_ast
            
            # Step 3: Encode con NGEncoder (se disponibile)
            try:
                encoded = encoder.encode(ng_parsed)
                assert encoded is not None, f"NGEncoder fallito per {symbol}"
            except Exception as e:
                # Fallback: usa rappresentazione stringa
                encoded = ast.dump(ng_parsed)
            
            # Step 4: Decode con NGDecoder (se disponibile)
            try:
                decoded_ast = decoder.decode(encoded)
                assert decoded_ast is not None, f"NGDecoder fallito per {symbol}"
            except Exception as e:
                # Fallback: parse encoded string
                if isinstance(encoded, str) and encoded.startswith('Module'):
                    # Se encoded è un dump AST, non possiamo fare round-trip
                    pytest.skip(f"Round-trip non supportato per {symbol} (encoder/decoder non implementati)")
                else:
                    decoded_ast = ast.parse(str(encoded))
            
            # Step 5: Confronta AST
            decoded_dump = ast.dump(decoded_ast, indent=2)
            
            # Verifica equivalenza strutturale
            assert original_dump == decoded_dump, f"""
Round-trip fallito per simbolo '{symbol}' (case: {case_id})
Original AST:
{original_dump[:500]}...
Decoded AST:
{decoded_dump[:500]}...
"""
            
        except Exception as e:
            pytest.fail(f"Errore round-trip per simbolo '{symbol}' (case: {case_id}): {e}")
    
    def test_god_mode_symbols_priority(self, test_cases, fixtures_info):
        """Test prioritario per simboli GOD mode certified."""
        god_mode_symbols = set(fixtures_info['god_mode_symbols'])
        god_mode_cases = [tc for tc in test_cases if tc['symbol'] in god_mode_symbols]
        
        assert len(god_mode_cases) > 0, "Nessun test case per simboli GOD mode"
        
        print(f"🔥 {len(god_mode_cases)} test cases per {len(god_mode_symbols)} simboli GOD mode")
        
        # Test almeno i primi 10 simboli GOD mode
        for test_case in god_mode_cases[:10]:
            symbol = test_case['symbol']
            code = test_case['code']
            
            # Test parsing base
            try:
                parsed = ast.parse(code)
                assert parsed is not None, f"Parsing fallito per simbolo GOD mode: {symbol}"
                
                # Verifica che il simbolo sia presente nel codice
                code_contains_symbol = symbol in code
                assert code_contains_symbol, f"Simbolo {symbol} non trovato nel test case"
                
            except Exception as e:
                pytest.fail(f"Errore test GOD mode per simbolo '{symbol}': {e}")
    
    def test_domain_coverage(self, test_cases, fixtures_info):
        """Test copertura domini."""
        domains_in_cases = set(tc['domain'] for tc in test_cases)
        expected_domains = set(fixtures_info['domains'])
        
        missing_domains = expected_domains - domains_in_cases
        assert len(missing_domains) == 0, f"Domini mancanti nei test cases: {missing_domains}"
        
        print(f"✅ Copertura domini: {len(domains_in_cases)}/{len(expected_domains)}")
        
        # Verifica distribuzione per dominio
        domain_counts = {}
        for tc in test_cases:
            domain = tc['domain']
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
        
        # Domini critici devono avere almeno 10 test cases
        critical_domains = ['logic', 'math', 'advanced_coding', 'cognition']
        for domain in critical_domains:
            if domain in domain_counts:
                count = domain_counts[domain]
                assert count >= 10, f"Dominio critico '{domain}' ha solo {count} test cases"
    
    def test_symbol_uniqueness(self, test_cases):
        """Test unicità simboli nei test cases."""
        symbols_seen = set()
        duplicate_symbols = []
        
        for tc in test_cases:
            symbol = tc['symbol']
            if symbol in symbols_seen:
                duplicate_symbols.append(symbol)
            symbols_seen.add(symbol)
        
        assert len(duplicate_symbols) == 0, f"Simboli duplicati: {duplicate_symbols[:10]}"
        print(f"✅ Simboli unici: {len(symbols_seen)}")
    
    @pytest.mark.slow
    def test_full_roundtrip_batch(self, test_cases, ng_components):
        """Test round-trip completo su tutti i simboli (lento)."""
        if not NEUROGLYPH_AVAILABLE:
            pytest.skip("NEUROGLYPH non disponibile")
        
        success_count = 0
        failure_count = 0
        failures = []
        
        for i, test_case in enumerate(test_cases):
            if i % 100 == 0:
                print(f"🔄 Progresso: {i}/{len(test_cases)} ({i/len(test_cases)*100:.1f}%)")
            
            symbol = test_case['symbol']
            code = test_case['code']
            
            try:
                # Test round-trip semplificato
                original_ast = ast.parse(code)
                
                # Per ora, test solo parsing
                assert original_ast is not None
                assert symbol in code
                
                success_count += 1
                
            except Exception as e:
                failure_count += 1
                failures.append({
                    'symbol': symbol,
                    'case_id': test_case['case_id'],
                    'error': str(e)
                })
        
        success_rate = success_count / len(test_cases)
        
        print(f"\n📊 Risultati batch round-trip:")
        print(f"   - Successi: {success_count}")
        print(f"   - Fallimenti: {failure_count}")
        print(f"   - Success rate: {success_rate:.2%}")
        
        # Mostra primi fallimenti
        if failures:
            print(f"\n❌ Primi fallimenti:")
            for failure in failures[:5]:
                print(f"   - {failure['symbol']} ({failure['case_id']}): {failure['error']}")
        
        # Target: almeno 95% success rate
        assert success_rate >= 0.95, f"Success rate troppo basso: {success_rate:.2%} < 95%"
