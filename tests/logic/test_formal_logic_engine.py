"""
Test suite per NEUROGLYPH Formal Logic Engine
Target: accuracy ≥ 90% su sillogismi classici
"""

import pytest
import sys
from pathlib import Path

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from neuroglyph.logic.formula import *
from neuroglyph.logic.logic_engine import FormalLogicEngine, prove
from neuroglyph.logic.proof_tree import ProofTree, ProofRule


class TestFormalLogicEngine:
    """Test suite per Formal Logic Engine."""
    
    def setup_method(self):
        """Setup per ogni test."""
        self.engine = FormalLogicEngine(max_depth=5, max_steps=20)
    
    def test_modus_ponens_basic(self):
        """Test modus ponens base: P → Q, P ⊢ Q."""
        # P → Q
        p = P("P")
        q = P("Q")
        implication = Implies(p, q)
        
        # Premises: P → Q, P
        premises = [implication, p]
        goal = q
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None
        assert result.proof_tree.is_valid
        assert len(result.proof_tree.steps) >= 3  # 2 premises + 1 modus ponens
        
        # Verifica che ci sia un step modus ponens
        modus_ponens_steps = [s for s in result.proof_tree.steps if s.rule == ProofRule.MODUS_PONENS]
        assert len(modus_ponens_steps) == 1
        assert modus_ponens_steps[0].formula == goal
    
    def test_modus_ponens_chain(self):
        """Test catena modus ponens: P → Q, Q → R, P ⊢ R."""
        p = P("P")
        q = P("Q")
        r = P("R")
        
        # Premises: P → Q, Q → R, P
        premises = [
            Implies(p, q),
            Implies(q, r),
            p
        ]
        goal = r
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None
        assert result.depth >= 2  # Almeno 2 livelli di reasoning
    
    def test_universal_instantiation(self):
        """Test istanziazione universale: ∀x P(x), P(a)."""
        x = Variable("x")
        a = Constant("A")
        
        # ∀x P(x)
        universal = ForAll(x, P("P", x))
        
        # Goal: P(A)
        goal = P("P", a)
        
        premises = [universal]
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None
        
        # Verifica step universal instantiation
        univ_inst_steps = [s for s in result.proof_tree.steps if s.rule == ProofRule.UNIVERSAL_INST]
        assert len(univ_inst_steps) == 1
        assert univ_inst_steps[0].formula == goal
    
    def test_conjunction_elimination(self):
        """Test eliminazione congiunzione: P ∧ Q ⊢ P."""
        p = P("P")
        q = P("Q")
        conjunction = And(p, q)
        
        premises = [conjunction]
        goal = p
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None
        
        # Verifica step conjunction elimination
        conj_elim_steps = [s for s in result.proof_tree.steps if s.rule == ProofRule.CONJUNCTION_ELIM]
        assert len(conj_elim_steps) == 1
    
    def test_conjunction_introduction(self):
        """Test introduzione congiunzione: P, Q ⊢ P ∧ Q."""
        p = P("P")
        q = P("Q")
        
        premises = [p, q]
        goal = And(p, q)
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None
        
        # Verifica step conjunction introduction
        conj_intro_steps = [s for s in result.proof_tree.steps if s.rule == ProofRule.CONJUNCTION_INTRO]
        assert len(conj_intro_steps) == 1
    
    def test_syllogism_barbara(self):
        """Test sillogismo Barbara: ∀x (M(x) → P(x)), ∀x (S(x) → M(x)), S(a) ⊢ P(a)."""
        x = Variable("x")
        a = Constant("A")
        
        # ∀x (M(x) → P(x))
        major = ForAll(x, Implies(P("M", x), P("P", x)))
        
        # ∀x (S(x) → M(x))
        minor = ForAll(x, Implies(P("S", x), P("M", x)))
        
        # S(a)
        particular = P("S", a)
        
        premises = [major, minor, particular]
        goal = P("P", a)
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None
        assert result.depth >= 3  # Multi-step reasoning
    
    def test_impossible_goal(self):
        """Test goal impossibile: P ⊢ Q (senza connessione)."""
        p = P("P")
        q = P("Q")
        
        premises = [p]
        goal = q
        
        result = self.engine.deduce(premises, goal)
        
        assert not result.success
        assert "not derivable" in result.error_message.lower()
    
    def test_contradiction_detection(self):
        """Test rilevamento contraddizione: P, ¬P ⊢ Q."""
        p = P("P")
        not_p = Not(p)
        q = P("Q")
        
        premises = [p, not_p]
        goal = q
        
        # Per ora, engine non gestisce contraddizioni
        # Dovrebbe fallire gracefully
        result = self.engine.deduce(premises, goal)
        
        # Non dovrebbe crashare
        assert isinstance(result.success, bool)
    
    def test_complex_predicate_unification(self):
        """Test unificazione predicati complessi."""
        x = Variable("x")
        y = Variable("y")
        a = Constant("A")
        b = Constant("B")
        
        # ∀x ∀y (Loves(x, y) → Happy(x))
        universal = ForAll(x, ForAll(y, Implies(P("Loves", x, y), P("Happy", x))))
        
        # Loves(A, B)
        fact = P("Loves", a, b)
        
        premises = [universal, fact]
        goal = P("Happy", a)
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None
    
    def test_engine_statistics(self):
        """Test statistiche engine."""
        p = P("P")
        q = P("Q")
        
        # Test successo
        result1 = self.engine.deduce([Implies(p, q), p], q)
        
        # Test fallimento
        result2 = self.engine.deduce([p], q)
        
        stats = self.engine.get_statistics()
        
        assert stats['proofs_attempted'] == 2
        assert stats['proofs_successful'] == 1
        assert stats['success_rate'] == 0.5
    
    def test_proof_tree_validation(self):
        """Test validazione proof tree."""
        p = P("P")
        q = P("Q")
        
        premises = [Implies(p, q), p]
        goal = q
        
        result = self.engine.deduce(premises, goal)
        
        assert result.success
        assert result.proof_tree.validate()
        
        # Test natural language output
        nl_proof = result.proof_tree.to_natural_language()
        assert "Proof" in nl_proof
        assert "Therefore" in nl_proof
    
    def test_max_depth_limit(self):
        """Test limite profondità massima."""
        engine = FormalLogicEngine(max_depth=1, max_steps=10)
        
        # Crea catena lunga che richiede depth > 1
        p = P("P")
        q = P("Q")
        r = P("R")
        s = P("S")
        
        premises = [
            Implies(p, q),
            Implies(q, r),
            Implies(r, s),
            p
        ]
        goal = s
        
        result = engine.deduce(premises, goal)
        
        # Dovrebbe fallire per max_depth
        assert not result.success
    
    def test_prove_factory_function(self):
        """Test factory function prove()."""
        p = P("P")
        q = P("Q")
        
        premises = [Implies(p, q), p]
        goal = q
        
        result = prove(premises, goal)
        
        assert result.success
        assert result.proof_tree is not None


class TestClassicalSyllogisms:
    """Test suite per sillogismi classici - Target: ≥90% accuracy."""
    
    def setup_method(self):
        """Setup per ogni test."""
        self.engine = FormalLogicEngine(max_depth=8, max_steps=50)
        self.successful_proofs = 0
        self.total_proofs = 0
    
    def _test_syllogism(self, premises, goal, name):
        """Helper per testare sillogismo."""
        self.total_proofs += 1
        result = self.engine.deduce(premises, goal)
        
        if result.success:
            self.successful_proofs += 1
            print(f"✅ {name}: SUCCESS")
        else:
            print(f"❌ {name}: FAILED - {result.error_message}")
        
        return result.success
    
    def test_barbara_syllogism(self):
        """Barbara: All M are P, All S are M, therefore All S are P."""
        x = Variable("x")
        
        # All M are P: ∀x (M(x) → P(x))
        major = ForAll(x, Implies(P("M", x), P("P", x)))
        
        # All S are M: ∀x (S(x) → M(x))
        minor = ForAll(x, Implies(P("S", x), P("M", x)))
        
        # Particular case: S(a)
        a = Constant("A")
        particular = P("S", a)
        
        premises = [major, minor, particular]
        goal = P("P", a)
        
        assert self._test_syllogism(premises, goal, "Barbara")
    
    def test_celarent_syllogism(self):
        """Celarent: No M are P, All S are M, therefore No S are P."""
        x = Variable("x")
        a = Constant("A")
        
        # No M are P: ∀x (M(x) → ¬P(x))
        major = ForAll(x, Implies(P("M", x), Not(P("P", x))))
        
        # All S are M: ∀x (S(x) → M(x))
        minor = ForAll(x, Implies(P("S", x), P("M", x)))
        
        # S(a)
        particular = P("S", a)
        
        premises = [major, minor, particular]
        goal = Not(P("P", a))
        
        assert self._test_syllogism(premises, goal, "Celarent")
    
    def test_darii_syllogism(self):
        """Darii: All M are P, Some S are M, therefore Some S are P."""
        x = Variable("x")
        a = Constant("A")
        
        # All M are P: ∀x (M(x) → P(x))
        major = ForAll(x, Implies(P("M", x), P("P", x)))
        
        # Some S are M: S(a) ∧ M(a)
        minor = And(P("S", a), P("M", a))
        
        premises = [major, minor]
        goal = And(P("S", a), P("P", a))
        
        assert self._test_syllogism(premises, goal, "Darii")
    
    def test_ferio_syllogism(self):
        """Ferio: No M are P, Some S are M, therefore Some S are not P."""
        x = Variable("x")
        a = Constant("A")
        
        # No M are P: ∀x (M(x) → ¬P(x))
        major = ForAll(x, Implies(P("M", x), Not(P("P", x))))
        
        # Some S are M: S(a) ∧ M(a)
        minor = And(P("S", a), P("M", a))
        
        premises = [major, minor]
        goal = And(P("S", a), Not(P("P", a)))
        
        assert self._test_syllogism(premises, goal, "Ferio")
    
    def test_accuracy_target(self):
        """Verifica target accuracy ≥ 90%."""
        # Esegui tutti i test
        self.test_barbara_syllogism()
        self.test_celarent_syllogism()
        self.test_darii_syllogism()
        self.test_ferio_syllogism()
        
        accuracy = self.successful_proofs / self.total_proofs if self.total_proofs > 0 else 0.0
        
        print(f"\n📊 Syllogism Accuracy: {accuracy:.2%} ({self.successful_proofs}/{self.total_proofs})")
        
        # Target: ≥ 90% accuracy
        assert accuracy >= 0.90, f"Accuracy {accuracy:.2%} below target 90%"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
