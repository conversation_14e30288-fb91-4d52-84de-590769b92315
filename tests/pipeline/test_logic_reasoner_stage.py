"""
Test LogicReasonerStage
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from neuroglyph.pipeline.stages.logic_reasoner import LogicReasonerStage
from neuroglyph.pipeline.pipeline_engine import PipelinePayload


class TestLogicReasonerStage:
    """Test LogicReasonerStage."""
    
    @pytest.fixture
    def stage(self):
        """Crea stage per test."""
        config = {
            'max_depth': 5,
            'max_steps': 20,
            'enable_fallback': True,
            'proof_required': False
        }
        return LogicReasonerStage("logic_reasoner", config)
    
    @pytest.fixture
    def payload_no_proof(self):
        """Payload che non richiede proof."""
        return PipelinePayload(
            stage_id="test_stage",
            trace_id="test-123",
            data={
                'statement': 'Simple statement without proof requirement',
                'requires_proof': False
            }
        )

    @pytest.fixture
    def payload_with_proof(self):
        """Payload che richiede proof."""
        return PipelinePayload(
            stage_id="test_stage",
            trace_id="test-456",
            data={
                'statement': 'If all men are mortal and <PERSON><PERSON> is a man, then <PERSON><PERSON> is mortal',
                'requires_proof': True,
                'premises': [
                    'forall x: Man(x) -> Mortal(x)',
                    'Man(Socrates)'
                ],
                'goal': 'Mortal(Socrates)'
            }
        )

    @pytest.fixture
    def payload_fallback(self):
        """Payload per fallback reasoning."""
        return PipelinePayload(
            stage_id="test_stage",
            trace_id="test-789",
            data={
                'statement': 'If it rains then the ground is wet',
                'requires_proof': True
            }
        )
    
    @pytest.mark.asyncio
    async def test_no_proof_required(self, stage, payload_no_proof):
        """Test processing senza proof requirement."""
        result = await stage.process(payload_no_proof)
        
        assert result.trace_id == "test-123"
        assert result.data['statement'] == 'Simple statement without proof requirement'
        assert 'reasoning_result' not in result.data
    
    @pytest.mark.asyncio
    async def test_formal_reasoning_success(self, stage, payload_with_proof):
        """Test formal reasoning con successo."""
        # Mock successful logic engine
        with patch.object(stage, 'logic_engine') as mock_engine:
            mock_result = Mock()
            mock_result.success = True
            mock_result.proof_tree = Mock()
            mock_result.proof_tree.proof_id = "proof-123"
            mock_result.proof_tree.is_valid = True
            mock_result.proof_tree.steps = [Mock(), Mock()]
            mock_result.proof_tree.to_natural_language.return_value = "Proof: Step 1, Step 2"
            mock_result.proof_tree.to_dict.return_value = {'steps': []}
            mock_result.steps_count = 2
            mock_result.depth = 3
            mock_result.duration = 0.05
            mock_result.error_message = None
            
            mock_engine.deduce.return_value = mock_result
            
            result = await stage.process(payload_with_proof)
            
            assert result.trace_id == "test-456"
            assert 'reasoning_result' in result.data
            
            reasoning = result.data['reasoning_result']
            assert reasoning['success'] is True
            assert reasoning['method'] == 'formal_logic'
            assert reasoning['proof_tree_id'] == "proof-123"
            assert reasoning['steps_count'] == 2
            assert reasoning['depth'] == 3
            assert reasoning['proof_valid'] is True
            
            assert 'proof_tree' in result.data
            assert result.data['reasoning_depth'] == 3
    
    @pytest.mark.asyncio
    async def test_formal_reasoning_failure(self, stage, payload_with_proof):
        """Test formal reasoning con fallimento e fallback."""
        # Mock failed logic engine
        with patch.object(stage, 'logic_engine') as mock_engine:
            mock_result = Mock()
            mock_result.success = False
            mock_result.proof_tree = None
            mock_result.steps_count = 0
            mock_result.depth = 0
            mock_result.duration = 0.01
            mock_result.error_message = "Goal not derivable"

            mock_engine.deduce.return_value = mock_result

            result = await stage.process(payload_with_proof)

            assert result.trace_id == "test-456"
            assert 'reasoning_result' in result.data

            reasoning = result.data['reasoning_result']
            assert reasoning['success'] is True  # Fallback succeeds
            assert reasoning['method'] == 'pattern_matching'  # Fallback
            assert reasoning['fallback_used'] is True
    
    @pytest.mark.asyncio
    async def test_fallback_reasoning(self, stage, payload_fallback):
        """Test fallback pattern matching."""
        # Disable logic engine
        stage.logic_engine = None
        
        result = await stage.process(payload_fallback)
        
        assert result.trace_id == "test-789"
        assert 'reasoning_result' in result.data
        
        reasoning = result.data['reasoning_result']
        assert reasoning['success'] is True
        assert reasoning['method'] == 'pattern_matching'
        assert reasoning['fallback_used'] is True
        assert 'implication' in reasoning['patterns_found']
        assert reasoning['confidence'] > 0.5
        assert result.data['reasoning_depth'] == 1
    
    @pytest.mark.asyncio
    async def test_no_fallback_failure(self, stage, payload_with_proof):
        """Test failure senza fallback."""
        # Disable fallback
        stage.enable_fallback = False
        stage.logic_engine = None
        
        result = await stage.process(payload_with_proof)
        
        assert result.trace_id == "test-456"
        assert 'reasoning_result' in result.data
        
        reasoning = result.data['reasoning_result']
        assert reasoning['success'] is False
        assert reasoning['error'] == 'Logic reasoning not available'
        assert reasoning['fallback_used'] is False
    
    def test_logic_engine_stats(self, stage):
        """Test statistiche logic engine."""
        # Mock logic engine
        with patch.object(stage, 'logic_engine') as mock_engine:
            mock_engine.get_statistics.return_value = {
                'proofs_attempted': 10,
                'proofs_successful': 8,
                'success_rate': 0.8
            }
            
            stats = stage.get_logic_engine_stats()
            
            assert stats['proofs_attempted'] == 10
            assert stats['proofs_successful'] == 8
            assert stats['success_rate'] == 0.8
    
    def test_logic_engine_stats_unavailable(self, stage):
        """Test statistiche con logic engine non disponibile."""
        stage.logic_engine = None
        
        stats = stage.get_logic_engine_stats()
        
        assert stats['logic_engine_available'] is False
        assert stats['proofs_attempted'] == 0
        assert stats['success_rate'] == 0.0
