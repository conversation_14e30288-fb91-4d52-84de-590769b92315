"""
Test Suite NEUROGLYPH Autonomous Evolution

Test completi per validare il sistema di evoluzione autonoma:
- Pattern Learning Engine
- Symbol Evolution System
- Cold Start Learning
- Autonomous Evolution Controller
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from typing import List

# Import moduli NEUROGLYPH
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from neuroglyph.learning.pattern_learning_engine import PatternLearningEngine, PatternCandidate
from neuroglyph.learning.symbol_evolution_system import SymbolEvolutionSystem, SymbolUsageStats
from neuroglyph.learning.cold_start_learning import ColdStartLearningSystem, DomainBootstrap
from neuroglyph.learning.autonomous_evolution_controller import AutonomousEvolutionController, EvolutionConfig
from neuroglyph.logic.proof_tree import ProofTree, ProofStep
from neuroglyph.logic.formula import Predicate, Variable


class TestPatternLearningEngine:
    """Test per Pattern Learning Engine."""
    
    @pytest.fixture
    def temp_db(self):
        """Fixture per database temporaneo."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            yield f.name
        os.unlink(f.name)
    
    @pytest.fixture
    def pattern_engine(self, temp_db):
        """Fixture per Pattern Learning Engine."""
        return PatternLearningEngine(db_path=temp_db)
    
    @pytest.fixture
    def sample_proof_trees(self):
        """Fixture per proof trees di esempio."""
        proofs = []
        
        # Proof 1: GCD pattern
        steps1 = [
            ProofStep(
                formula=Predicate('greatest_common_divisor', [Variable('a'), Variable('b')]),
                justification='GCD algorithm'
            )
        ]
        proofs.append(ProofTree(steps=steps1))
        
        # Proof 2: Fibonacci pattern
        steps2 = [
            ProofStep(
                formula=Predicate('fibonacci', [Variable('n')]),
                justification='Fibonacci sequence'
            )
        ]
        proofs.append(ProofTree(steps=steps2))
        
        # Proof 3: Pattern ripetuto
        steps3 = [
            ProofStep(
                formula=Predicate('greatest_common_divisor', [Variable('x'), Variable('y')]),
                justification='GCD algorithm repeated'
            )
        ]
        proofs.append(ProofTree(steps=steps3))
        
        return proofs
    
    def test_pattern_engine_initialization(self, pattern_engine):
        """Test inizializzazione Pattern Learning Engine."""
        assert pattern_engine is not None
        assert pattern_engine.db is not None
        assert pattern_engine.patterns_analyzed == 0
        assert pattern_engine.symbols_proposed == 0
    
    def test_analyze_proof_patterns(self, pattern_engine, sample_proof_trees):
        """Test analisi pattern da proof trees."""
        candidates = pattern_engine.analyze_proof_patterns(sample_proof_trees)
        
        assert len(candidates) > 0
        assert pattern_engine.patterns_analyzed == len(sample_proof_trees)
        
        # Verifica che i candidati abbiano struttura corretta
        for candidate in candidates:
            assert isinstance(candidate, PatternCandidate)
            assert candidate.frequency > 0
            assert 0 <= candidate.confidence <= 1.0
            assert candidate.description is not None
    
    def test_analyze_ast_patterns(self, pattern_engine):
        """Test analisi pattern AST."""
        code_samples = [
            "def gcd(a, b):\n    while b:\n        a, b = b, a % b\n    return a",
            "def fib(n):\n    if n <= 1:\n        return n\n    return fib(n-1) + fib(n-2)",
            "for i in range(10):\n    print(i)"
        ]
        
        candidates = pattern_engine.analyze_ast_patterns(code_samples)
        
        assert isinstance(candidates, list)
        # Potrebbe essere vuoto se tutti i pattern sono già coperti
        for candidate in candidates:
            assert isinstance(candidate, PatternCandidate)
            assert candidate.pattern_type == "ast"
    
    def test_identify_symbol_gaps(self, pattern_engine):
        """Test identificazione gap simbolici."""
        gaps = pattern_engine.identify_symbol_gaps()
        
        assert isinstance(gaps, list)
        for gap in gaps:
            assert gap.domain is not None
            assert gap.priority >= 0.0
            assert gap.priority <= 1.0
    
    def test_propose_new_symbols(self, pattern_engine, sample_proof_trees):
        """Test proposta nuovi simboli."""
        # Prima analizza pattern
        candidates = pattern_engine.analyze_proof_patterns(sample_proof_trees)
        
        # Poi proponi simboli
        proposals = pattern_engine.propose_new_symbols(candidates)
        
        assert isinstance(proposals, list)
        assert pattern_engine.symbols_proposed >= 0
        
        for proposal in proposals:
            assert 'pattern_id' in proposal
            assert 'suggested_symbol' in proposal
            assert 'confidence' in proposal


class TestSymbolEvolutionSystem:
    """Test per Symbol Evolution System."""
    
    @pytest.fixture
    def temp_db(self):
        """Fixture per database temporaneo."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            yield f.name
        os.unlink(f.name)
    
    @pytest.fixture
    def evolution_system(self, temp_db):
        """Fixture per Symbol Evolution System."""
        return SymbolEvolutionSystem(db_path=temp_db)
    
    def test_evolution_system_initialization(self, evolution_system):
        """Test inizializzazione Symbol Evolution System."""
        assert evolution_system is not None
        assert evolution_system.db is not None
        assert evolution_system.symbols_tracked >= 0
    
    def test_track_symbol_usage(self, evolution_system):
        """Test tracking utilizzo simboli."""
        # Traccia alcuni utilizzi
        evolution_system.track_symbol_usage('⟨⟩', 'parsing', 'logic', 0.8)
        evolution_system.track_symbol_usage('◊', 'encoding', 'math', 0.9)
        evolution_system.track_symbol_usage('⟨⟩', 'reasoning', 'logic', 0.7)
        
        # Verifica che siano stati tracciati
        assert len(evolution_system.usage_stats) >= 1
        
        # Verifica statistiche per simbolo specifico
        if '⟨⟩' in evolution_system.usage_stats:
            stats = evolution_system.usage_stats['⟨⟩']
            assert stats.total_usage >= 2
            assert 'logic' in stats.domains_used
    
    def test_analyze_symbol_trends(self, evolution_system):
        """Test analisi trend simboli."""
        # Aggiungi alcuni utilizzi
        evolution_system.track_symbol_usage('⟨⟩', 'parsing', 'logic', 0.8)
        evolution_system.track_symbol_usage('◊', 'encoding', 'math', 0.9)
        
        trends = evolution_system.analyze_symbol_trends()
        
        assert isinstance(trends, dict)
        assert 'growing' in trends
        assert 'stable' in trends
        assert 'declining' in trends
        assert 'unused' in trends
    
    def test_identify_evolution_opportunities(self, evolution_system):
        """Test identificazione opportunità evoluzione."""
        # Aggiungi alcuni utilizzi per creare dati
        evolution_system.track_symbol_usage('⟨⟩', 'parsing', 'logic', 0.8)
        
        proposals = evolution_system.identify_evolution_opportunities()
        
        assert isinstance(proposals, list)
        assert evolution_system.proposals_generated >= 0


class TestColdStartLearningSystem:
    """Test per Cold Start Learning System."""
    
    @pytest.fixture
    def temp_db(self):
        """Fixture per database temporaneo."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            yield f.name
        os.unlink(f.name)
    
    @pytest.fixture
    def cold_start_system(self, temp_db):
        """Fixture per Cold Start Learning System."""
        return ColdStartLearningSystem(db_path=temp_db)
    
    @pytest.fixture
    def sample_domain_config(self):
        """Fixture per configurazione dominio."""
        return DomainBootstrap(
            domain_name="test_domain",
            seed_patterns=["x + y", "f(x) = y", "if x then y"],
            expected_symbols=3,
            confidence_threshold=0.6,
            validation_rules=["no_empty", "has_structure"]
        )
    
    def test_cold_start_initialization(self, cold_start_system):
        """Test inizializzazione Cold Start Learning."""
        assert cold_start_system is not None
        assert cold_start_system.db is not None
        assert cold_start_system.domains_learned == 0
    
    def test_bootstrap_new_domain(self, cold_start_system, sample_domain_config):
        """Test bootstrap nuovo dominio."""
        session_id = cold_start_system.bootstrap_new_domain(sample_domain_config)
        
        assert session_id is not None
        assert session_id.startswith("session_test_domain_")
        assert session_id in cold_start_system.active_sessions
        
        session = cold_start_system.active_sessions[session_id]
        assert session.domain_name == "test_domain"
        assert session.status in ["active", "completed", "failed"]


class TestAutonomousEvolutionController:
    """Test per Autonomous Evolution Controller."""
    
    @pytest.fixture
    def evolution_config(self):
        """Fixture per configurazione evoluzione."""
        return EvolutionConfig(
            enable_pattern_learning=True,
            enable_symbol_evolution=True,
            enable_cold_start_learning=True,
            learning_interval_seconds=10,  # Breve per test
            evolution_interval_seconds=20,  # Breve per test
            auto_apply_high_confidence=False  # Sicurezza per test
        )
    
    @pytest.fixture
    def controller(self, evolution_config):
        """Fixture per Autonomous Evolution Controller."""
        return AutonomousEvolutionController(config=evolution_config)
    
    def test_controller_initialization(self, controller):
        """Test inizializzazione controller."""
        assert controller is not None
        assert controller.pattern_engine is not None
        assert controller.evolution_system is not None
        assert controller.cold_start_system is not None
        assert not controller.is_running
    
    def test_add_proof_for_analysis(self, controller):
        """Test aggiunta proof per analisi."""
        # Crea proof di test
        steps = [
            ProofStep(
                formula=Predicate('test_predicate', [Variable('x')]),
                justification='Test proof'
            )
        ]
        proof = ProofTree(steps=steps)
        
        initial_queue_size = len(controller.proof_queue)
        controller.add_proof_for_analysis(proof)
        
        assert len(controller.proof_queue) == initial_queue_size + 1
    
    def test_add_code_for_analysis(self, controller):
        """Test aggiunta codice per analisi."""
        code = "def test_function(x):\n    return x * 2"
        
        initial_queue_size = len(controller.code_samples_queue)
        controller.add_code_for_analysis(code)
        
        assert len(controller.code_samples_queue) == initial_queue_size + 1
    
    def test_bootstrap_domain(self, controller):
        """Test bootstrap dominio manuale."""
        session_id = controller.bootstrap_domain(
            domain_name="manual_test_domain",
            seed_patterns=["pattern1", "pattern2"],
            expected_symbols=2
        )
        
        assert session_id is not None
        assert controller.total_domains_bootstrapped >= 1
    
    def test_get_evolution_status(self, controller):
        """Test status evoluzione."""
        status = controller.get_evolution_status()
        
        assert isinstance(status, dict)
        assert 'is_running' in status
        assert 'config' in status
        assert 'metrics' in status
        assert 'last_runs' in status
        
        # Verifica struttura config
        config = status['config']
        assert 'pattern_learning_enabled' in config
        assert 'symbol_evolution_enabled' in config
        assert 'cold_start_learning_enabled' in config
        
        # Verifica struttura metrics
        metrics = status['metrics']
        assert 'total_patterns_learned' in metrics
        assert 'total_symbols_evolved' in metrics
        assert 'total_domains_bootstrapped' in metrics


class TestIntegrationAutonomousEvolution:
    """Test di integrazione per Autonomous Evolution."""
    
    def test_end_to_end_pattern_discovery(self):
        """Test end-to-end scoperta pattern."""
        # Crea controller
        config = EvolutionConfig(
            enable_pattern_learning=True,
            enable_symbol_evolution=False,
            enable_cold_start_learning=False
        )
        controller = AutonomousEvolutionController(config=config)
        
        # Aggiungi proof trees
        for i in range(5):
            steps = [
                ProofStep(
                    formula=Predicate(f'test_pattern_{i % 2}', [Variable('x')]),
                    justification=f'Test pattern {i}'
                )
            ]
            proof = ProofTree(steps=steps)
            controller.add_proof_for_analysis(proof)
        
        # Verifica che i proof siano in queue
        assert len(controller.proof_queue) == 5
        
        # Simula processing (senza async per semplicità)
        if controller.pattern_engine:
            patterns = controller.pattern_engine.analyze_proof_patterns(controller.proof_queue)
            assert len(patterns) >= 0  # Potrebbe essere 0 se pattern non soddisfano soglie
    
    def test_symbol_usage_tracking(self):
        """Test tracking utilizzo simboli."""
        config = EvolutionConfig(
            enable_pattern_learning=False,
            enable_symbol_evolution=True,
            enable_cold_start_learning=False
        )
        controller = AutonomousEvolutionController(config=config)
        
        # Simula utilizzo simboli
        if controller.evolution_system:
            controller.evolution_system.track_symbol_usage('⟨⟩', 'test', 'logic', 0.8)
            controller.evolution_system.track_symbol_usage('◊', 'test', 'math', 0.9)
            
            # Verifica tracking
            stats = controller.evolution_system.get_evolution_statistics()
            assert stats['usage_stats_count'] >= 2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
