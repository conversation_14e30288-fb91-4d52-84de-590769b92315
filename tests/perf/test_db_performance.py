"""
Test Suite Performance per NEUROGLYPH Fase 2.3
Verifica target: ≤1ms append, ≤5ms retrieve @ 10k rows
"""

import pytest
import time
import tempfile
import os
from pathlib import Path
import statistics
from typing import List, Dict, Any

# Aggiungi path per import neuroglyph
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from neuroglyph.cognitive.performance_storage import PerformanceStorage


class TestPerformanceStorage:
    """Test performance per PerformanceStorage."""
    
    @pytest.fixture
    def temp_storage(self):
        """Fixture per storage temporaneo."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        storage = PerformanceStorage(db_path, batch_size=64, flush_interval=0.1)
        yield storage
        
        storage.shutdown()
        try:
            os.unlink(db_path)
        except:
            pass
    
    def test_append_performance_target(self, temp_storage):
        """
        Test append performance: TARGET ≤1ms per record @ 10k records.
        """
        storage = temp_storage
        num_records = 10000
        
        print(f"\n🧪 Test append performance con {num_records} record...")
        
        # Misura append performance
        times = []
        
        for i in range(num_records):
            start = time.perf_counter()
            
            storage.append_patch_history(
                error_type=f"test_error_{i % 20}",
                status="successful" if i % 3 == 0 else "failed",
                metadata={
                    'test_index': i,
                    'batch': 'performance_test',
                    'complexity': 'medium'
                }
            )
            
            elapsed = time.perf_counter() - start
            times.append(elapsed * 1000)  # Convert to ms
        
        # Flush finale per assicurare persistenza
        storage.force_flush()
        
        # Calcola statistiche
        avg_time_ms = statistics.mean(times)
        median_time_ms = statistics.median(times)
        p95_time_ms = statistics.quantiles(times, n=20)[18]  # 95th percentile
        max_time_ms = max(times)
        
        print(f"✅ Append Performance Results:")
        print(f"   - Records: {num_records}")
        print(f"   - Average: {avg_time_ms:.3f}ms")
        print(f"   - Median: {median_time_ms:.3f}ms")
        print(f"   - 95th percentile: {p95_time_ms:.3f}ms")
        print(f"   - Max: {max_time_ms:.3f}ms")
        
        # Verifica target
        target_ms = 1.0
        assert avg_time_ms <= target_ms, f"Average append time {avg_time_ms:.3f}ms > target {target_ms}ms"
        assert p95_time_ms <= target_ms * 2, f"95th percentile {p95_time_ms:.3f}ms > target {target_ms * 2}ms"
        
        print(f"🎯 TARGET RAGGIUNTO: {avg_time_ms:.3f}ms ≤ {target_ms}ms")
    
    def test_retrieve_performance_target(self, temp_storage):
        """
        Test retrieve performance: TARGET ≤5ms per query @ 10k records.
        """
        storage = temp_storage
        num_records = 10000
        num_queries = 1000
        
        print(f"\n🧪 Test retrieve performance con {num_records} record, {num_queries} query...")
        
        # Popola database
        for i in range(num_records):
            storage.append_patch_history(
                error_type=f"error_type_{i % 50}",
                status="successful" if i % 2 == 0 else "failed",
                metadata={'index': i}
            )
        
        storage.force_flush()
        
        # Misura retrieve performance
        times = []
        
        for i in range(num_queries):
            error_type = f"error_type_{i % 50}"
            
            start = time.perf_counter()
            results = storage.get_patch_history(error_type=error_type, limit=100)
            elapsed = time.perf_counter() - start
            
            times.append(elapsed * 1000)  # Convert to ms
            
            # Verifica che abbia restituito risultati
            assert len(results) > 0, f"No results for error_type {error_type}"
        
        # Calcola statistiche
        avg_time_ms = statistics.mean(times)
        median_time_ms = statistics.median(times)
        p95_time_ms = statistics.quantiles(times, n=20)[18]  # 95th percentile
        max_time_ms = max(times)
        
        print(f"✅ Retrieve Performance Results:")
        print(f"   - Queries: {num_queries}")
        print(f"   - Average: {avg_time_ms:.3f}ms")
        print(f"   - Median: {median_time_ms:.3f}ms")
        print(f"   - 95th percentile: {p95_time_ms:.3f}ms")
        print(f"   - Max: {max_time_ms:.3f}ms")
        
        # Verifica target
        target_ms = 5.0
        assert avg_time_ms <= target_ms, f"Average retrieve time {avg_time_ms:.3f}ms > target {target_ms}ms"
        assert p95_time_ms <= target_ms * 2, f"95th percentile {p95_time_ms:.3f}ms > target {target_ms * 2}ms"
        
        print(f"🎯 TARGET RAGGIUNTO: {avg_time_ms:.3f}ms ≤ {target_ms}ms")
    
    def test_statistics_hotpath_performance(self, temp_storage):
        """
        Test statistics hot-path: TARGET ≤0.1ms per call.
        """
        storage = temp_storage
        num_calls = 10000
        
        print(f"\n🧪 Test statistics hot-path con {num_calls} chiamate...")
        
        # Popola un po' di dati
        for i in range(100):
            storage.append_patch_history(
                error_type=f"test_{i}",
                status="successful",
                metadata={'test': True}
            )
        
        # Misura statistics performance
        times = []
        
        for i in range(num_calls):
            start = time.perf_counter()
            stats = storage.get_statistics()
            elapsed = time.perf_counter() - start
            
            times.append(elapsed * 1000)  # Convert to ms
            
            # Verifica che restituisca dati validi
            assert isinstance(stats, dict)
            assert 'total_patches' in stats
        
        # Calcola statistiche
        avg_time_ms = statistics.mean(times)
        median_time_ms = statistics.median(times)
        p95_time_ms = statistics.quantiles(times, n=20)[18]  # 95th percentile
        
        print(f"✅ Statistics Performance Results:")
        print(f"   - Calls: {num_calls}")
        print(f"   - Average: {avg_time_ms:.4f}ms")
        print(f"   - Median: {median_time_ms:.4f}ms")
        print(f"   - 95th percentile: {p95_time_ms:.4f}ms")
        
        # Verifica target
        target_ms = 0.1
        assert avg_time_ms <= target_ms, f"Average stats time {avg_time_ms:.4f}ms > target {target_ms}ms"
        
        print(f"🎯 TARGET RAGGIUNTO: {avg_time_ms:.4f}ms ≤ {target_ms}ms")
    
    def test_concurrent_access(self, temp_storage):
        """
        Test accesso concorrente (WAL mode).
        """
        storage = temp_storage
        
        print(f"\n🧪 Test accesso concorrente...")
        
        import threading
        import queue
        
        results_queue = queue.Queue()
        num_threads = 4
        operations_per_thread = 100
        
        def worker(thread_id):
            times = []
            for i in range(operations_per_thread):
                # Append
                start = time.perf_counter()
                storage.append_patch_history(
                    error_type=f"thread_{thread_id}_error_{i}",
                    status="successful",
                    metadata={'thread_id': thread_id, 'op': i}
                )
                append_time = time.perf_counter() - start
                
                # Retrieve
                start = time.perf_counter()
                results = storage.get_patch_history(limit=10)
                retrieve_time = time.perf_counter() - start
                
                times.append({
                    'append_ms': append_time * 1000,
                    'retrieve_ms': retrieve_time * 1000
                })
            
            results_queue.put((thread_id, times))
        
        # Avvia thread
        threads = []
        for i in range(num_threads):
            t = threading.Thread(target=worker, args=(i,))
            threads.append(t)
            t.start()
        
        # Aspetta completamento
        for t in threads:
            t.join()
        
        # Raccoglie risultati
        all_append_times = []
        all_retrieve_times = []
        
        while not results_queue.empty():
            thread_id, times = results_queue.get()
            for time_data in times:
                all_append_times.append(time_data['append_ms'])
                all_retrieve_times.append(time_data['retrieve_ms'])
        
        # Verifica performance sotto concorrenza
        avg_append_ms = statistics.mean(all_append_times)
        avg_retrieve_ms = statistics.mean(all_retrieve_times)
        
        print(f"✅ Concurrent Performance Results:")
        print(f"   - Threads: {num_threads}")
        print(f"   - Operations per thread: {operations_per_thread}")
        print(f"   - Average append: {avg_append_ms:.3f}ms")
        print(f"   - Average retrieve: {avg_retrieve_ms:.3f}ms")
        
        # Target più rilassati per concorrenza
        assert avg_append_ms <= 2.0, f"Concurrent append {avg_append_ms:.3f}ms > 2.0ms"
        assert avg_retrieve_ms <= 10.0, f"Concurrent retrieve {avg_retrieve_ms:.3f}ms > 10.0ms"
        
        print(f"🎯 CONCURRENCY TARGET RAGGIUNTO")
    
    def test_database_size_and_cleanup(self, temp_storage):
        """
        Test dimensione database e cleanup.
        """
        storage = temp_storage
        
        print(f"\n🧪 Test database size e cleanup...")
        
        # Popola database
        num_records = 5000
        for i in range(num_records):
            storage.append_patch_history(
                error_type=f"size_test_{i % 10}",
                status="successful",
                metadata={'large_data': 'x' * 100}  # Dati più grandi
            )
        
        storage.force_flush()
        
        # Controlla dimensione
        db_info = storage.get_database_info()
        
        print(f"✅ Database Size Results:")
        print(f"   - Records: {num_records}")
        print(f"   - DB size: {db_info['db_size_mb']}MB")
        print(f"   - Page count: {db_info['page_count']}")
        print(f"   - Page size: {db_info['page_size']} bytes")
        
        # Verifica dimensione ragionevole (< 50MB per 5k record)
        assert db_info['db_size_mb'] < 50, f"Database too large: {db_info['db_size_mb']}MB"
        
        print(f"🎯 DATABASE SIZE OK: {db_info['db_size_mb']}MB")


if __name__ == "__main__":
    # Esegui test performance direttamente
    pytest.main([__file__, "-v", "-s"])
