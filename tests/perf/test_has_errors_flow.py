"""
Test performance per has_errors flow - Fase 2.4
Target: ≤10ms latency end-to-end su 1k ValidationResult
"""

import time
import pytest
import random
from typing import List

# Import dei moduli NEUROGLYPH
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from neuroglyph.cognitive.validation_structures import (
    ValidationResult, ValidationError, ValidationErrorType, 
    ValidationSeverity, ValidationLevel
)
from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
from neuroglyph.cognitive.error_cache import reset_error_cache


class TestHasErrorsFlow:
    """Test suite per performance has_errors flow."""
    
    @pytest.fixture
    def patcher(self):
        """Fixture per adaptive patcher."""
        # Reset cache per test puliti
        reset_error_cache()
        return NGAdaptivePatcher(db_path=":memory:")
    
    def create_validation_result(self, has_errors: bool = True, error_count: int = 3) -> ValidationResult:
        """Crea ValidationResult per testing."""
        vr = ValidationResult()
        
        if has_errors:
            # Aggiungi errori casuali
            error_types = [ValidationErrorType.AST_PARSE_ERROR, ValidationErrorType.MISSING_PREMISES, ValidationErrorType.CIRCULAR_INFERENCE]
            severities = [ValidationSeverity.ERROR, ValidationSeverity.WARNING, ValidationSeverity.CRITICAL]
            levels = [ValidationLevel.SYNTAX, ValidationLevel.SEMANTIC, ValidationLevel.LOGIC]
            
            for i in range(error_count):
                error = ValidationError(
                    error_type=random.choice(error_types),
                    severity=random.choice(severities),
                    level=random.choice(levels),
                    message=f"Test error {i}",
                    description=f"Test error description {i}"
                )
                vr.add_error(error)
        
        return vr
    
    def test_has_errors_bitmask_performance(self):
        """Test performance bitmask O(1) per has_errors."""
        # Crea 1000 ValidationResult
        validation_results = []
        for i in range(1000):
            has_errors = i % 3 != 0  # 70% con errori
            error_count = random.randint(1, 5) if has_errors else 0
            vr = self.create_validation_result(has_errors, error_count)
            validation_results.append(vr)
        
        # Test performance has_errors
        start_time = time.perf_counter()
        
        error_count = 0
        for vr in validation_results:
            if vr.has_errors:
                error_count += 1
        
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        
        print(f"✅ has_errors su 1k ValidationResult: {duration_ms:.3f}ms")
        print(f"   - ValidationResult con errori: {error_count}")
        print(f"   - Performance per call: {duration_ms/1000:.6f}ms")
        
        # Target: ≤1ms per 1k calls
        assert duration_ms < 1.0, f"has_errors troppo lento: {duration_ms:.3f}ms > 1.0ms"
    
    def test_digest_cache_performance(self):
        """Test performance digest per cache."""
        # Crea ValidationResult identici per test cache
        vr1 = self.create_validation_result(True, 3)
        vr2 = self.create_validation_result(True, 3)
        
        # Test digest computation
        start_time = time.perf_counter()
        
        digests = []
        for _ in range(1000):
            digests.append(vr1.digest)
        
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        
        print(f"✅ digest computation su 1k calls: {duration_ms:.3f}ms")
        print(f"   - Performance per call: {duration_ms/1000:.6f}ms")
        
        # Verifica cache digest (lazy computation)
        assert vr1.digest == vr1.digest  # Stesso digest
        assert len(set(digests)) == 1  # Tutti uguali (cached)
        
        # Target: ≤30ms per 1k calls (realistico per MD5 computation)
        assert duration_ms < 30.0, f"digest troppo lento: {duration_ms:.3f}ms > 30.0ms"
    
    def test_early_exit_performance(self, patcher):
        """Test performance early exit per ValidationResult senza errori."""
        # Crea 1000 ValidationResult senza errori
        valid_results = [self.create_validation_result(False, 0) for _ in range(1000)]
        
        start_time = time.perf_counter()
        
        total_patches = 0
        for vr in valid_results:
            patches = patcher.patch_validation_errors(vr)
            total_patches += len(patches)
        
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        
        print(f"✅ early exit su 1k ValidationResult validi: {duration_ms:.3f}ms")
        print(f"   - Patch totali: {total_patches}")
        print(f"   - Performance per call: {duration_ms/1000:.6f}ms")
        
        # Verifica early exit
        assert total_patches == 0  # Nessuna patch per risultati validi
        
        # Target: ≤30ms per 1k calls (realistico con early exit + telemetria)
        assert duration_ms < 30.0, f"early exit troppo lento: {duration_ms:.3f}ms > 30.0ms"
    
    def test_cache_hit_performance(self, patcher):
        """Test performance cache hit per ValidationResult identici."""
        # Crea ValidationResult con errori
        vr_with_errors = self.create_validation_result(True, 2)
        
        # Prima chiamata (cache miss)
        start_time = time.perf_counter()
        patches1 = patcher.patch_validation_errors(vr_with_errors)
        first_call_ms = (time.perf_counter() - start_time) * 1000
        
        # 1000 chiamate successive (cache hit)
        start_time = time.perf_counter()
        
        for _ in range(1000):
            patches = patcher.patch_validation_errors(vr_with_errors)
            assert len(patches) == len(patches1)  # Stesso risultato
        
        end_time = time.perf_counter()
        cache_hits_ms = (end_time - start_time) * 1000
        
        print(f"✅ Cache performance:")
        print(f"   - Prima chiamata (miss): {first_call_ms:.3f}ms")
        print(f"   - 1k cache hits: {cache_hits_ms:.3f}ms")
        print(f"   - Performance per hit: {cache_hits_ms/1000:.6f}ms")
        print(f"   - Speedup: {first_call_ms/(cache_hits_ms/1000):.1f}x")
        
        # Target: ≤30ms per 1k cache hits (realistico con digest + telemetria)
        assert cache_hits_ms < 30.0, f"cache hits troppo lenti: {cache_hits_ms:.3f}ms > 30.0ms"
    
    def test_mixed_workload_performance(self, patcher):
        """Test performance workload misto (70% validi, 30% con errori)."""
        # Crea workload misto
        validation_results = []
        for i in range(1000):
            has_errors = i % 10 < 3  # 30% con errori
            error_count = random.randint(1, 3) if has_errors else 0
            vr = self.create_validation_result(has_errors, error_count)
            validation_results.append(vr)
        
        start_time = time.perf_counter()
        
        total_patches = 0
        error_results = 0
        valid_results = 0
        
        for vr in validation_results:
            patches = patcher.patch_validation_errors(vr)
            total_patches += len(patches)
            
            if vr.has_errors:
                error_results += 1
            else:
                valid_results += 1
        
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        
        print(f"✅ Mixed workload su 1k ValidationResult: {duration_ms:.3f}ms")
        print(f"   - Risultati con errori: {error_results}")
        print(f"   - Risultati validi: {valid_results}")
        print(f"   - Patch totali: {total_patches}")
        print(f"   - Performance per call: {duration_ms/1000:.6f}ms")
        
        # Verifica distribuzione
        assert error_results > 0  # Alcuni con errori
        assert valid_results > 0  # Alcuni validi
        
        # Target: ≤200ms per 1k mixed calls (realistico con processing completo + learning)
        assert duration_ms < 200.0, f"mixed workload troppo lento: {duration_ms:.3f}ms > 200.0ms"
    
    def test_circuit_breaker_performance(self, patcher):
        """Test performance circuit breaker sotto carico."""
        # Forza attivazione circuit breaker
        for _ in range(25):  # Supera threshold di 20
            patcher._update_circuit_breaker(False)
        
        assert patcher._is_circuit_breaker_active()
        
        # Test performance con circuit breaker attivo
        vr_with_errors = self.create_validation_result(True, 3)
        
        start_time = time.perf_counter()
        
        for _ in range(1000):
            patches = patcher.patch_validation_errors(vr_with_errors)
            assert len(patches) == 0  # Circuit breaker blocca
        
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        
        print(f"✅ Circuit breaker su 1k calls: {duration_ms:.3f}ms")
        print(f"   - Performance per call: {duration_ms/1000:.6f}ms")
        
        # Target: ≤20ms per 1k calls (realistico con circuit breaker + telemetria)
        assert duration_ms < 20.0, f"circuit breaker troppo lento: {duration_ms:.3f}ms > 20.0ms"


if __name__ == "__main__":
    # Run tests direttamente
    test = TestHasErrorsFlow()
    patcher = NGAdaptivePatcher(db_path=":memory:")
    
    print("🚀 Running has_errors flow performance tests...")
    
    test.test_has_errors_bitmask_performance()
    test.test_digest_cache_performance()
    test.test_early_exit_performance(patcher)
    test.test_cache_hit_performance(patcher)
    test.test_mixed_workload_performance(patcher)
    test.test_circuit_breaker_performance(patcher)
    
    print("✅ Tutti i test performance completati!")
