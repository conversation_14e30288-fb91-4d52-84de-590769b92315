#!/usr/bin/env python3
"""
NEUROGLYPH Test Framework
Fase 2 - Modular Design & Unit Testing

Framework centralizzato per testing sistematico di tutti i moduli NEUROGLYPH.
Implementa:
- Contract testing per interfacce
- Property-based testing con Hypothesis
- Coverage tracking per moduli
- Performance benchmarking
- Quality gates automatici
"""

import time
import json
import inspect
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Type
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum
import unittest
from unittest.mock import Mock, patch


class TestResult(Enum):
    """Risultati possibili di un test."""
    PASS = "PASS"
    FAIL = "FAIL"
    SKIP = "SKIP"
    ERROR = "ERROR"


@dataclass
class TestCase:
    """Singolo test case."""
    name: str
    description: str
    test_func: Callable
    module: str
    category: str
    timeout: float = 30.0
    expected_result: TestResult = TestResult.PASS
    setup_func: Optional[Callable] = None
    teardown_func: Optional[Callable] = None


@dataclass
class TestExecution:
    """Risultato di esecuzione di un test."""
    test_case: TestCase
    result: TestResult
    duration: float
    error_message: Optional[str] = None
    output: Optional[str] = None
    coverage: Optional[float] = None


@dataclass
class ModuleTestSuite:
    """Suite di test per un modulo."""
    module_name: str
    test_cases: List[TestCase] = field(default_factory=list)
    setup_module: Optional[Callable] = None
    teardown_module: Optional[Callable] = None
    quality_gates: Dict[str, float] = field(default_factory=dict)


class ContractTester:
    """
    Tester per contratti di interfaccia.
    Verifica che i moduli rispettino le interfacce definite.
    """
    
    def __init__(self):
        self.contracts = {}
    
    def register_contract(self, module_name: str, contract: Dict[str, Any]):
        """Registra un contratto per un modulo."""
        self.contracts[module_name] = contract
    
    def test_contract(self, module_instance: Any, module_name: str) -> bool:
        """Testa che un'istanza rispetti il contratto."""
        if module_name not in self.contracts:
            return True  # Nessun contratto definito
        
        contract = self.contracts[module_name]
        
        # Verifica metodi richiesti
        required_methods = contract.get('methods', [])
        for method_name in required_methods:
            if not hasattr(module_instance, method_name):
                return False
            if not callable(getattr(module_instance, method_name)):
                return False
        
        # Verifica attributi richiesti
        required_attributes = contract.get('attributes', [])
        for attr_name in required_attributes:
            if not hasattr(module_instance, attr_name):
                return False
        
        return True


class PerformanceBenchmark:
    """
    Benchmark di performance per moduli.
    """
    
    def __init__(self):
        self.benchmarks = {}
    
    def benchmark_function(self, func: Callable, *args, **kwargs) -> Dict[str, float]:
        """Esegue benchmark di una funzione."""
        # Warmup
        for _ in range(3):
            try:
                func(*args, **kwargs)
            except:
                pass
        
        # Benchmark reale
        times = []
        for _ in range(10):
            start_time = time.perf_counter()
            try:
                result = func(*args, **kwargs)
                end_time = time.perf_counter()
                times.append(end_time - start_time)
            except Exception as e:
                return {'error': str(e)}
        
        return {
            'mean': sum(times) / len(times),
            'min': min(times),
            'max': max(times),
            'median': sorted(times)[len(times) // 2]
        }


class QualityGateChecker:
    """
    Checker per quality gates.
    """
    
    def __init__(self):
        self.default_gates = {
            'test_pass_rate': 0.95,      # 95% test devono passare
            'coverage': 0.90,            # 90% coverage minimo
            'performance_threshold': 1.0, # 1s max per operazione
            'error_rate': 0.05           # 5% max error rate
        }
    
    def check_gates(self, results: List[TestExecution], 
                   custom_gates: Optional[Dict[str, float]] = None) -> Dict[str, bool]:
        """Verifica quality gates."""
        gates = {**self.default_gates, **(custom_gates or {})}
        gate_results = {}
        
        if not results:
            return {gate: False for gate in gates}
        
        # Test pass rate
        passed = sum(1 for r in results if r.result == TestResult.PASS)
        pass_rate = passed / len(results)
        gate_results['test_pass_rate'] = pass_rate >= gates['test_pass_rate']
        
        # Coverage (se disponibile)
        coverages = [r.coverage for r in results if r.coverage is not None]
        if coverages:
            avg_coverage = sum(coverages) / len(coverages)
            gate_results['coverage'] = avg_coverage >= gates['coverage']
        else:
            gate_results['coverage'] = True  # Skip se non disponibile
        
        # Performance
        durations = [r.duration for r in results]
        max_duration = max(durations) if durations else 0
        gate_results['performance_threshold'] = max_duration <= gates['performance_threshold']
        
        # Error rate
        errors = sum(1 for r in results if r.result == TestResult.ERROR)
        error_rate = errors / len(results)
        gate_results['error_rate'] = error_rate <= gates['error_rate']
        
        return gate_results


class TestFramework:
    """
    Framework principale per testing NEUROGLYPH.
    """
    
    def __init__(self, output_dir: str = "test_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.test_suites: Dict[str, ModuleTestSuite] = {}
        self.contract_tester = ContractTester()
        self.performance_benchmark = PerformanceBenchmark()
        self.quality_gate_checker = QualityGateChecker()
        
        self.execution_results: List[TestExecution] = []
        
        print(f"🧪 TestFramework inizializzato")
        print(f"📁 Output directory: {self.output_dir}")
    
    def register_test_suite(self, suite: ModuleTestSuite):
        """Registra una test suite."""
        self.test_suites[suite.module_name] = suite
        print(f"📝 Registrata test suite: {suite.module_name} ({len(suite.test_cases)} test)")
    
    def register_contract(self, module_name: str, contract: Dict[str, Any]):
        """Registra un contratto."""
        self.contract_tester.register_contract(module_name, contract)
        print(f"📋 Registrato contratto: {module_name}")
    
    def execute_test_case(self, test_case: TestCase) -> TestExecution:
        """Esegue un singolo test case."""
        start_time = time.perf_counter()
        
        try:
            # Setup
            if test_case.setup_func:
                test_case.setup_func()
            
            # Esecuzione test
            result = test_case.test_func()
            
            # Determina risultato
            if result is True:
                test_result = TestResult.PASS
                error_message = None
            elif result is False:
                test_result = TestResult.FAIL
                error_message = "Test returned False"
            else:
                test_result = TestResult.PASS  # Assume pass se non boolean
                error_message = None
            
        except Exception as e:
            test_result = TestResult.ERROR
            error_message = str(e)
            
        finally:
            # Teardown
            if test_case.teardown_func:
                try:
                    test_case.teardown_func()
                except Exception as e:
                    print(f"⚠️ Teardown error: {e}")
        
        duration = time.perf_counter() - start_time
        
        execution = TestExecution(
            test_case=test_case,
            result=test_result,
            duration=duration,
            error_message=error_message
        )
        
        return execution
    
    def execute_test_suite(self, module_name: str) -> List[TestExecution]:
        """Esegue tutti i test di una suite."""
        if module_name not in self.test_suites:
            print(f"❌ Test suite non trovata: {module_name}")
            return []
        
        suite = self.test_suites[module_name]
        results = []
        
        print(f"\n🧪 Eseguendo test suite: {module_name}")
        print(f"📊 Test cases: {len(suite.test_cases)}")
        
        # Setup modulo
        if suite.setup_module:
            try:
                suite.setup_module()
                print("✅ Setup modulo completato")
            except Exception as e:
                print(f"❌ Setup modulo fallito: {e}")
                return []
        
        # Esegui test cases
        for i, test_case in enumerate(suite.test_cases, 1):
            print(f"  [{i}/{len(suite.test_cases)}] {test_case.name}...", end=" ")
            
            execution = self.execute_test_case(test_case)
            results.append(execution)
            
            # Status
            if execution.result == TestResult.PASS:
                print(f"✅ ({execution.duration:.3f}s)")
            elif execution.result == TestResult.FAIL:
                print(f"❌ FAIL: {execution.error_message}")
            elif execution.result == TestResult.ERROR:
                print(f"💥 ERROR: {execution.error_message}")
            else:
                print(f"⏭️ SKIP")
        
        # Teardown modulo
        if suite.teardown_module:
            try:
                suite.teardown_module()
                print("✅ Teardown modulo completato")
            except Exception as e:
                print(f"⚠️ Teardown modulo error: {e}")
        
        # Quality gates
        gate_results = self.quality_gate_checker.check_gates(results, suite.quality_gates)
        print(f"\n🚪 Quality Gates:")
        for gate, passed in gate_results.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {gate}")
        
        return results
    
    def execute_all_suites(self) -> Dict[str, List[TestExecution]]:
        """Esegue tutte le test suite."""
        all_results = {}
        
        print(f"\n🚀 Eseguendo tutte le test suite ({len(self.test_suites)} moduli)")
        print("=" * 60)
        
        for module_name in self.test_suites:
            results = self.execute_test_suite(module_name)
            all_results[module_name] = results
            self.execution_results.extend(results)
        
        return all_results
    
    def generate_report(self, results: Dict[str, List[TestExecution]]) -> Dict[str, Any]:
        """Genera report completo."""
        total_tests = sum(len(module_results) for module_results in results.values())
        total_passed = sum(
            sum(1 for r in module_results if r.result == TestResult.PASS)
            for module_results in results.values()
        )
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'total_passed': total_passed,
                'pass_rate': total_passed / total_tests if total_tests > 0 else 0,
                'modules_tested': len(results)
            },
            'modules': {},
            'generated_at': time.time()
        }
        
        for module_name, module_results in results.items():
            module_passed = sum(1 for r in module_results if r.result == TestResult.PASS)
            module_report = {
                'total_tests': len(module_results),
                'passed': module_passed,
                'pass_rate': module_passed / len(module_results) if module_results else 0,
                'avg_duration': sum(r.duration for r in module_results) / len(module_results) if module_results else 0,
                'tests': [
                    {
                        'name': r.test_case.name,
                        'result': r.result.value,
                        'duration': r.duration,
                        'error': r.error_message
                    }
                    for r in module_results
                ]
            }
            report['modules'][module_name] = module_report
        
        # Salva report
        report_path = self.output_dir / "test_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📊 Report salvato: {report_path}")
        return report
