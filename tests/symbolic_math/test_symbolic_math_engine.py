"""
Test Suite per SymbolicMathEngine - NEUROGLYPH Fase 6.0

Test completi per il motore di calcolo simbolico con SymPy.
Target: ≥90% success rate su operazioni matematiche avanzate.
"""

import pytest
import json
from neuroglyph.symbolic_math import <PERSON>ym<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MathResult, compute


class TestSymbolicMathEngine:
    """Test suite per SymbolicMathEngine base."""
    
    def setup_method(self):
        """Setup per ogni test."""
        self.engine = SymbolicMathEngine(enable_cache=True)
        self.successful_operations = 0
        self.total_operations = 0
    
    def _test_operation(self, operation_name: str, expected_success: bool = True):
        """Helper per tracciare successi/fallimenti."""
        self.total_operations += 1
        if expected_success:
            self.successful_operations += 1
        return expected_success
    
    def test_simplify_polynomial(self):
        """Test semplificazione polinomiale."""
        result = self.engine.simplify("(x + y)**2")
        
        assert result.success
        assert "x**2" in result.result and "2*x*y" in result.result and "y**2" in result.result
        assert result.operation == "simplify"
        assert "x" in result.variables_used and "y" in result.variables_used
        assert result.computation_time > 0
        
        self._test_operation("simplify_polynomial")
    
    def test_simplify_trigonometric(self):
        """Test semplificazione trigonometrica."""
        result = self.engine.simplify("sin(x)**2 + cos(x)**2")
        
        assert result.success
        assert result.result == "1"  # Identità trigonometrica fondamentale
        assert "x" in result.variables_used
        
        self._test_operation("simplify_trigonometric")
    
    def test_differentiate_polynomial(self):
        """Test derivazione polinomiale."""
        result = self.engine.differentiate("x**3 + 2*x**2 + x + 1", "x")
        
        assert result.success
        assert result.result == "3*x**2 + 4*x + 1"
        assert result.operation == "differentiate_order_1"
        assert "x" in result.variables_used
        
        self._test_operation("differentiate_polynomial")
    
    def test_differentiate_trigonometric(self):
        """Test derivazione trigonometrica."""
        result = self.engine.differentiate("sin(x)*cos(x)", "x")
        
        assert result.success
        # sin(x)*cos(x) = (1/2)*sin(2x), derivata = cos(2x)
        # Oppure: d/dx[sin(x)*cos(x)] = cos(x)**2 - sin(x)**2 = cos(2x)
        assert "cos" in result.result and "sin" in result.result
        assert "x" in result.variables_used
        
        self._test_operation("differentiate_trigonometric")
    
    def test_differentiate_higher_order(self):
        """Test derivazione di ordine superiore."""
        result = self.engine.differentiate("x**4", "x", order=2)
        
        assert result.success
        assert result.result == "12*x**2"
        assert result.operation == "differentiate_order_2"
        
        self._test_operation("differentiate_higher_order")
    
    def test_integrate_polynomial(self):
        """Test integrazione polinomiale."""
        result = self.engine.integrate("3*x**2 + 4*x + 1", "x")
        
        assert result.success
        assert "x**3" in result.result and "2*x**2" in result.result and "x" in result.result
        assert result.operation == "indefinite_integral"
        
        self._test_operation("integrate_polynomial")
    
    def test_integrate_definite(self):
        """Test integrazione definita."""
        result = self.engine.integrate("x**2", "x", lower_limit="0", upper_limit="2")
        
        assert result.success
        assert result.result == "8/3"  # ∫₀² x² dx = [x³/3]₀² = 8/3
        assert "definite_integral" in result.operation
        
        self._test_operation("integrate_definite")
    
    def test_solve_quadratic(self):
        """Test risoluzione equazione quadratica."""
        result = self.engine.solve_equation("x**2 - 5*x + 6 = 0", "x")
        
        assert result.success
        solutions = json.loads(result.result)
        assert set(solutions) == {"2", "3"}  # (x-2)(x-3) = 0
        assert result.operation == "solve_equation"
        
        self._test_operation("solve_quadratic")
    
    def test_solve_linear_system(self):
        """Test risoluzione equazione lineare."""
        result = self.engine.solve_equation("2*x + 3 = 7", "x")
        
        assert result.success
        solutions = json.loads(result.result)
        assert solutions == ["2"]  # x = 2
        
        self._test_operation("solve_linear")
    
    def test_expand_expression(self):
        """Test espansione espressione."""
        result = self.engine.expand("(x + 1)*(x + 2)")
        
        assert result.success
        assert result.result == "x**2 + 3*x + 2"
        assert result.operation == "expand"
        
        self._test_operation("expand_expression")
    
    def test_factor_expression(self):
        """Test fattorizzazione."""
        result = self.engine.factor("x**2 + 3*x + 2")
        
        assert result.success
        assert result.result == "(x + 1)*(x + 2)"
        assert result.operation == "factor"
        
        self._test_operation("factor_expression")
    
    def test_invalid_expression(self):
        """Test gestione espressioni invalide."""
        result = self.engine.simplify("invalid_expression_@#$")
        
        assert not result.success
        assert result.error_message is not None
        assert result.computation_time > 0
        
        # Non conta come successo
        self.total_operations += 1
    
    def test_cache_functionality(self):
        """Test funzionalità cache."""
        # Prima chiamata
        result1 = self.engine.simplify("x**2 + 2*x + 1")
        assert result1.success
        
        # Seconda chiamata (dovrebbe usare cache)
        result2 = self.engine.simplify("x**2 + 2*x + 1")
        assert result2.success
        assert result2.result == result1.result
        
        # Verifica cache hit
        stats = self.engine.get_statistics()
        assert stats["cache_hits"] > 0
        
        self._test_operation("cache_functionality")
    
    def test_statistics(self):
        """Test raccolta statistiche."""
        # Esegui alcune operazioni
        self.engine.simplify("x + 1")
        self.engine.differentiate("x**2", "x")
        
        stats = self.engine.get_statistics()
        
        assert stats["operations_count"] >= 2
        assert stats["total_computation_time"] > 0
        assert stats["average_computation_time"] > 0
        assert stats["sympy_available"] is True
        
        self._test_operation("statistics")
    
    def test_reset_statistics(self):
        """Test reset statistiche."""
        # Esegui operazioni
        self.engine.simplify("x + 1")
        
        # Reset
        self.engine.reset_statistics()
        
        stats = self.engine.get_statistics()
        assert stats["operations_count"] == 0
        assert stats["total_computation_time"] == 0
        assert stats["cache_size"] == 0
        
        self._test_operation("reset_statistics")


class TestFactoryFunction:
    """Test per funzione factory compute()."""
    
    def test_compute_simplify(self):
        """Test compute con simplify."""
        result = compute("(x + 1)**2", "simplify")
        
        assert result.success
        assert "x**2" in result.result
    
    def test_compute_differentiate(self):
        """Test compute con differentiate."""
        result = compute("x**3", "differentiate", variable="x")
        
        assert result.success
        assert result.result == "3*x**2"
    
    def test_compute_integrate(self):
        """Test compute con integrate."""
        result = compute("x**2", "integrate", variable="x")
        
        assert result.success
        assert "x**3" in result.result
    
    def test_compute_solve(self):
        """Test compute con solve."""
        result = compute("x**2 - 4 = 0", "solve", variable="x")
        
        assert result.success
        solutions = json.loads(result.result)
        assert set(solutions) == {"-2", "2"}
    
    def test_compute_invalid_operation(self):
        """Test compute con operazione invalida."""
        with pytest.raises(ValueError, match="Operazione non supportata"):
            compute("x + 1", "invalid_operation")


class TestAdvancedMathematics:
    """Test suite per matematica avanzata - Target ≥90% success rate."""
    
    def setup_method(self):
        """Setup per test avanzati."""
        self.engine = SymbolicMathEngine()
        self.successful_tests = 0
        self.total_tests = 0
    
    def _test_advanced(self, name: str, success: bool):
        """Helper per tracciare test avanzati."""
        self.total_tests += 1
        if success:
            self.successful_tests += 1
            print(f"✅ {name}: SUCCESS")
        else:
            print(f"❌ {name}: FAILED")
        return success
    
    def test_complex_trigonometry(self):
        """Test trigonometria complessa."""
        result = self.engine.simplify("sin(2*x)")
        success = result.success and "sin" in result.result
        self._test_advanced("complex_trigonometry", success)
    
    def test_logarithmic_differentiation(self):
        """Test derivazione logaritmica."""
        result = self.engine.differentiate("log(x**2 + 1)", "x")
        success = result.success and "2*x" in result.result
        self._test_advanced("logarithmic_differentiation", success)
    
    def test_integration_by_parts(self):
        """Test integrazione per parti (automatica in SymPy)."""
        result = self.engine.integrate("x*exp(x)", "x")
        success = result.success and "exp" in result.result
        self._test_advanced("integration_by_parts", success)
    
    def test_partial_fractions(self):
        """Test frazioni parziali."""
        result = self.engine.factor("x**2 - 1")
        success = result.success and result.result == "(x - 1)*(x + 1)"
        self._test_advanced("partial_fractions", success)
    
    def test_cubic_equation(self):
        """Test equazione cubica."""
        result = self.engine.solve_equation("x**3 - 6*x**2 + 11*x - 6 = 0", "x")
        success = result.success and len(json.loads(result.result)) == 3
        self._test_advanced("cubic_equation", success)
    
    def test_success_rate_target(self):
        """Verifica target success rate ≥ 90%."""
        # Esegui tutti i test avanzati
        self.test_complex_trigonometry()
        self.test_logarithmic_differentiation()
        self.test_integration_by_parts()
        self.test_partial_fractions()
        self.test_cubic_equation()
        
        success_rate = self.successful_tests / self.total_tests if self.total_tests > 0 else 0.0
        
        print(f"\n📊 Advanced Math Success Rate: {success_rate:.2%} ({self.successful_tests}/{self.total_tests})")
        
        # Target: ≥ 90% success rate
        assert success_rate >= 0.90, f"Success rate {success_rate:.2%} below target 90%"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
