"""
NEUROGLYPH Knowledge Graph Integration Tests - Fase 6.0
Test integrazione knowledge graph con reasoning engine
Target: KG hit rate ≥70%, Reasoning accuracy ≥90%
"""

import pytest
import sys
import time
import tempfile
from pathlib import Path
from typing import List, Dict, Any

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import (
        P, Variable, Constant, Implication, Negation, 
        Conjunction, Disjunction, Universal as ForAll, Existential as Exists
    )
    from neuroglyph.knowledge.knowledge_graph import KnowledgeGraph, KGNode, KGEdge, KGFact
    COMPONENTS_AVAILABLE = True
except ImportError:
    COMPONENTS_AVAILABLE = False


class TestKnowledgeGraphIntegration:
    """Test integrazione Knowledge Graph con reasoning engine."""
    
    @pytest.fixture(scope="class")
    def temp_kg_db(self):
        """Crea database temporaneo per test."""
        temp_dir = tempfile.mkdtemp()
        db_path = Path(temp_dir) / "test_kg.db"
        yield str(db_path)
        # Cleanup automatico con tempfile
    
    @pytest.fixture(scope="class")
    def knowledge_graph(self, temp_kg_db):
        """Inizializza Knowledge Graph per test."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")
        return KnowledgeGraph(temp_kg_db)
    
    @pytest.fixture(scope="class")
    def logic_engine(self):
        """Inizializza Logic Engine."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")
        return FormalLogicEngine(max_depth=10, max_steps=100)
    
    def test_kg_basic_operations(self, knowledge_graph):
        """Test operazioni base del Knowledge Graph."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")
        
        # Test aggiunta nodi
        human_node = KGNode("human", "concept", "Human", {"domain": "philosophy"})
        mortal_node = KGNode("mortal", "concept", "Mortal", {"domain": "philosophy"})
        
        assert knowledge_graph.add_node(human_node), "Aggiunta nodo human fallita"
        assert knowledge_graph.add_node(mortal_node), "Aggiunta nodo mortal fallita"
        
        # Test aggiunta fatti
        human_mortal_fact = KGFact(
            "human_mortal", 
            "human", 
            "implies", 
            "mortal", 
            1.0, 
            "classical_logic"
        )
        
        assert knowledge_graph.add_fact(human_mortal_fact), "Aggiunta fatto fallita"
        
        # Test lookup
        facts = knowledge_graph.lookup_facts(subject="human", predicate="implies")
        assert len(facts) == 1, f"Expected 1 fact, got {len(facts)}"
        assert facts[0].object == "mortal", f"Expected 'mortal', got {facts[0].object}"
        
        # Test statistiche
        stats = knowledge_graph.get_statistics()
        assert stats['nodes_count'] >= 2, f"Expected ≥2 nodes, got {stats['nodes_count']}"
        assert stats['facts_count'] >= 1, f"Expected ≥1 facts, got {stats['facts_count']}"
        assert stats['lookup_hit_rate'] > 0.0, f"Expected hit rate >0, got {stats['lookup_hit_rate']}"
        
        print(f"✅ KG Basic Operations: {stats['nodes_count']} nodes, {stats['facts_count']} facts")
    
    def test_kg_philosophical_reasoning(self, knowledge_graph, logic_engine):
        """Test reasoning filosofico con supporto KG."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")
        
        # Popola KG con fatti filosofici
        philosophical_facts = [
            KGFact("human_mortal", "Human", "implies", "Mortal", 1.0, "aristotle"),
            KGFact("philosopher_human", "Philosopher", "implies", "Human", 1.0, "classical"),
            KGFact("socrates_philosopher", "Socrates", "instance_of", "Philosopher", 1.0, "historical"),
            KGFact("greek_wise", "Greek", "implies", "Wise", 0.8, "cultural"),
            KGFact("wise_philosopher", "Wise", "implies", "Philosopher", 0.7, "cultural")
        ]
        
        for fact in philosophical_facts:
            knowledge_graph.add_fact(fact)
        
        # Test lookup di implicazioni
        human_implications = knowledge_graph.get_implications("Human")
        philosopher_implications = knowledge_graph.get_implications("Philosopher")
        
        print(f"🧠 KG Philosophical Reasoning:")
        print(f"   - Human implications: {len(human_implications)}")
        print(f"   - Philosopher implications: {len(philosopher_implications)}")
        
        # Verifica che il KG contenga le implicazioni attese
        assert len(human_implications) >= 1, "Nessuna implicazione per Human trovata"
        assert any(fact.object == "Mortal" for fact in human_implications), "Human→Mortal non trovato"
        
        # Test reasoning con supporto KG (simulato)
        # In una implementazione completa, il logic engine userebbe il KG
        x = Variable("x")
        socrates = Constant("Socrates")
        
        premises = [
            ForAll(x, Implication(P("Human", x), P("Mortal", x))),
            ForAll(x, Implication(P("Philosopher", x), P("Human", x))),
            P("Philosopher", socrates)
        ]
        goal = P("Mortal", socrates)
        
        result = logic_engine.deduce(premises, goal)
        
        assert result.success, "Reasoning filosofico fallito"
        
        # Statistiche KG
        kg_stats = knowledge_graph.get_statistics()
        
        print(f"   - KG facts: {kg_stats['facts_count']}")
        print(f"   - KG hit rate: {kg_stats['lookup_hit_rate']:.2%}")
        print(f"   - Reasoning success: {result.success}")
        
        print(f"✅ KG PHILOSOPHICAL REASONING SUCCESS")
    
    def test_kg_mathematical_facts(self, knowledge_graph):
        """Test fatti matematici nel KG."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")
        
        # Popola KG con fatti matematici
        math_facts = [
            KGFact("even_divisible2", "Even", "implies", "DivisibleBy2", 1.0, "number_theory"),
            KGFact("divisible2_integer", "DivisibleBy2", "implies", "Integer", 1.0, "number_theory"),
            KGFact("integer_rational", "Integer", "implies", "Rational", 1.0, "number_theory"),
            KGFact("rational_real", "Rational", "implies", "Real", 1.0, "number_theory"),
            KGFact("real_complex", "Real", "implies", "Complex", 1.0, "number_theory"),
            KGFact("four_even", "4", "instance_of", "Even", 1.0, "arithmetic"),
            KGFact("addition_commutative", "Addition", "property", "Commutative", 1.0, "algebra"),
            KGFact("multiplication_associative", "Multiplication", "property", "Associative", 1.0, "algebra")
        ]
        
        for fact in math_facts:
            knowledge_graph.add_fact(fact)
        
        # Test lookup catena matematica
        even_chain = []
        current = "Even"
        
        for _ in range(5):  # Max 5 passi nella catena
            implications = knowledge_graph.get_implications(current)
            if implications:
                next_concept = implications[0].object
                even_chain.append((current, next_concept))
                current = next_concept
            else:
                break
        
        print(f"🧠 KG Mathematical Facts:")
        print(f"   - Math facts added: {len(math_facts)}")
        print(f"   - Even number chain: {len(even_chain)} steps")
        
        for step, (from_concept, to_concept) in enumerate(even_chain):
            print(f"     Step {step+1}: {from_concept} → {to_concept}")
        
        # Verifica catena matematica
        assert len(even_chain) >= 3, f"Catena troppo corta: {len(even_chain)} < 3"
        
        # Test lookup proprietà
        properties = knowledge_graph.lookup_facts(predicate="property")
        print(f"   - Mathematical properties: {len(properties)}")
        
        assert len(properties) >= 2, f"Poche proprietà trovate: {len(properties)} < 2"
        
        # Statistiche finali
        kg_stats = knowledge_graph.get_statistics()
        print(f"   - Total KG facts: {kg_stats['facts_count']}")
        print(f"   - KG hit rate: {kg_stats['lookup_hit_rate']:.2%}")
        
        print(f"✅ KG MATHEMATICAL FACTS SUCCESS")
    
    def test_kg_performance_lookup(self, knowledge_graph):
        """Test performance lookup KG."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")
        
        # Popola KG con molti fatti per test performance
        bulk_facts = []
        for i in range(100):
            fact = KGFact(
                f"fact_{i}",
                f"concept_{i}",
                "relates_to",
                f"concept_{(i+1) % 100}",
                0.5,
                "synthetic"
            )
            bulk_facts.append(fact)
        
        # Aggiungi fatti in batch
        start_time = time.perf_counter()
        for fact in bulk_facts:
            knowledge_graph.add_fact(fact)
        add_duration = time.perf_counter() - start_time
        
        # Test lookup performance
        start_time = time.perf_counter()
        
        lookup_results = []
        for i in range(0, 100, 10):  # Test 10 lookup
            facts = knowledge_graph.lookup_facts(subject=f"concept_{i}")
            lookup_results.append(len(facts))
        
        lookup_duration = time.perf_counter() - start_time
        avg_lookup_time = lookup_duration / 10
        
        # Test lookup con pattern diversi
        start_time = time.perf_counter()
        predicate_facts = knowledge_graph.lookup_facts(predicate="relates_to")
        predicate_lookup_duration = time.perf_counter() - start_time
        
        print(f"🧠 KG Performance Lookup:")
        print(f"   - Bulk add (100 facts): {add_duration:.3f}s")
        print(f"   - Average lookup time: {avg_lookup_time:.3f}s")
        print(f"   - Predicate lookup (100 facts): {predicate_lookup_duration:.3f}s")
        print(f"   - Facts found per lookup: {sum(lookup_results)/len(lookup_results):.1f}")
        
        # Performance targets
        assert add_duration <= 1.0, f"Bulk add too slow: {add_duration:.3f}s > 1.0s"
        assert avg_lookup_time <= 0.1, f"Lookup too slow: {avg_lookup_time:.3f}s > 0.1s"
        assert predicate_lookup_duration <= 0.5, f"Predicate lookup too slow: {predicate_lookup_duration:.3f}s > 0.5s"
        
        # Statistiche finali
        kg_stats = knowledge_graph.get_statistics()
        print(f"   - Total facts: {kg_stats['facts_count']}")
        print(f"   - Hit rate: {kg_stats['lookup_hit_rate']:.2%}")
        
        print(f"✅ KG PERFORMANCE SUCCESS: avg_lookup={avg_lookup_time:.3f}s")

    def test_kg_logic_engine_integration(self, knowledge_graph):
        """Test integrazione reale KG + FormalLogicEngine (Fase 6.0)."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")

        # Setup KG con fatti per reasoning
        kg_facts = [
            KGFact("socrates_human", "Socrates", "Human", "True", 1.0, "classical"),
            KGFact("plato_human", "Plato", "Human", "True", 1.0, "classical"),
            KGFact("aristotle_human", "Aristotle", "Human", "True", 1.0, "classical"),
            KGFact("human_mortal", "Human", "implies", "Mortal", 1.0, "philosophy"),
        ]

        for fact in kg_facts:
            knowledge_graph.add_fact(fact)

        # Crea Logic Engine con KG integrato
        from neuroglyph.logic import FormalLogicEngine
        logic_engine = FormalLogicEngine(knowledge_graph=knowledge_graph)

        # Test 1: Goal direttamente nel KG
        from neuroglyph.logic.formula import Predicate, Constant
        socrates = Constant("Socrates")
        goal = Predicate("Human", [socrates])

        result = logic_engine.deduce([], goal)

        print(f"🧠 KG + Logic Engine Integration:")
        print(f"   - Direct KG lookup success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Duration: {result.duration:.3f}s")

        # Statistiche KG
        stats = logic_engine.get_statistics()
        print(f"   - KG lookups: {stats['kg_lookups']}")
        print(f"   - KG hits: {stats['kg_hits']}")
        print(f"   - KG hit rate: {stats['kg_hit_rate']:.2%}")

        # Assertions per test 1
        assert result.success, "Direct KG lookup failed"
        assert stats['kg_lookups'] > 0, "No KG lookups performed"
        assert stats['kg_hits'] > 0, "No KG hits"
        assert stats['kg_hit_rate'] > 0, "Zero KG hit rate"

        # Verifica proof tree con step KG
        if result.proof_tree:
            kg_steps = [s for s in result.proof_tree.steps if hasattr(s, 'fact_id')]
            assert len(kg_steps) > 0, "No KG steps in proof tree"

            kg_step = kg_steps[0]
            assert hasattr(kg_step, 'fact_id'), "KG step missing fact_id"
            assert hasattr(kg_step, 'confidence'), "KG step missing confidence"
            print(f"   - KG step fact_id: {kg_step.fact_id}")
            print(f"   - KG step confidence: {kg_step.confidence}")

        # Test 2: Goal non nel KG (fallback a reasoning normale)
        unknown_person = Constant("Unknown")
        unknown_goal = Predicate("Human", [unknown_person])

        logic_engine.reset_statistics()  # Reset per test pulito
        result2 = logic_engine.deduce([], unknown_goal)

        stats2 = logic_engine.get_statistics()
        print(f"   - Unknown goal success: {result2.success}")
        print(f"   - KG lookups (unknown): {stats2['kg_lookups']}")
        print(f"   - KG misses (unknown): {stats2['kg_misses']}")

        # Assertions per test 2
        assert not result2.success, "Unknown goal should fail"
        assert stats2['kg_lookups'] > 0, "No KG lookups for unknown goal"
        assert stats2['kg_misses'] > 0, "No KG misses for unknown goal"

        print(f"✅ KG + LOGIC ENGINE INTEGRATION SUCCESS")

    def test_kg_reasoning_integration_simulation(self, knowledge_graph, logic_engine):
        """Test simulazione integrazione KG con reasoning."""
        if not COMPONENTS_AVAILABLE:
            pytest.skip("Componenti non disponibili")
        
        # Scenario: Il reasoning engine "consulta" il KG per fatti mancanti
        
        # Popola KG con fatti di supporto
        support_facts = [
            KGFact("bird_fly", "Bird", "implies", "CanFly", 0.9, "biology"),
            KGFact("penguin_bird", "Penguin", "implies", "Bird", 1.0, "taxonomy"),
            KGFact("penguin_swim", "Penguin", "implies", "CanSwim", 1.0, "biology"),
            KGFact("mammal_warmblood", "Mammal", "implies", "WarmBlooded", 1.0, "biology"),
            KGFact("dog_mammal", "Dog", "implies", "Mammal", 1.0, "taxonomy")
        ]
        
        for fact in support_facts:
            knowledge_graph.add_fact(fact)
        
        # Test 1: Reasoning con fatti dal KG (simulato)
        # In implementazione reale, logic engine consulterebbe KG per premises mancanti
        
        # Simula lookup di fatti rilevanti
        bird_facts = knowledge_graph.lookup_facts(subject="Bird")
        penguin_facts = knowledge_graph.lookup_facts(subject="Penguin")
        
        print(f"🧠 KG Reasoning Integration Simulation:")
        print(f"   - Bird facts in KG: {len(bird_facts)}")
        print(f"   - Penguin facts in KG: {len(penguin_facts)}")
        
        # Simula reasoning con fatti dal KG
        x = Variable("x")
        penguin = Constant("Penguin")
        
        # Premises dal KG + reasoning locale
        premises = [
            ForAll(x, Implication(P("Bird", x), P("CanFly", x))),  # Dal KG
            ForAll(x, Implication(P("Penguin", x), P("Bird", x))),  # Dal KG
            P("Penguin", penguin)  # Fatto locale
        ]
        goal = P("CanFly", penguin)
        
        result = logic_engine.deduce(premises, goal)
        
        # Test 2: Verifica che KG abbia fornito fatti utili
        kg_provided_facts = len(bird_facts) + len(penguin_facts)
        
        print(f"   - KG provided facts: {kg_provided_facts}")
        print(f"   - Reasoning result: {result.success}")
        print(f"   - Reasoning steps: {result.steps_count}")
        
        # Test 3: Statistiche integrazione
        kg_stats = knowledge_graph.get_statistics()
        logic_stats = logic_engine.get_statistics()
        
        integration_score = kg_stats['lookup_hit_rate'] * logic_stats['success_rate']
        
        print(f"   - KG hit rate: {kg_stats['lookup_hit_rate']:.2%}")
        print(f"   - Logic success rate: {logic_stats['success_rate']:.2%}")
        print(f"   - Integration score: {integration_score:.2%}")
        
        # Assertions
        assert kg_provided_facts >= 2, f"KG provided too few facts: {kg_provided_facts} < 2"
        assert result.success, "Reasoning with KG support failed"
        assert kg_stats['lookup_hit_rate'] >= 0.5, f"KG hit rate too low: {kg_stats['lookup_hit_rate']:.2%} < 50%"
        
        print(f"✅ KG REASONING INTEGRATION SUCCESS: score={integration_score:.2%}")


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
