"""
NEUROGLYPH Failure Cases Tests
Test che il reasoner fallisca onestamente su problemi irrisolvibili
Target: Zero hallucination rate
"""

import pytest
import sys
from pathlib import Path
from typing import List, Dict, Any

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import (
        P, Variable, Constant, Implication, Negation,
        Conjunction, Disjunction, Universal, Existential
    )
    from neuroglyph.cognitive.reasoner import NGReasoner
    LOGIC_AVAILABLE = True
except ImportError:
    LOGIC_AVAILABLE = False


class TestFailureCases:
    """Test che NEUROGLYPH fallisca onestamente su problemi irrisolvibili."""
    
    @pytest.fixture(scope="class")
    def logic_engine(self):
        """Inizializza Logic Engine."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        return FormalLogicEngine(max_depth=5, max_steps=20)
    
    @pytest.fixture(scope="class")
    def ng_reasoner(self):
        """Inizializza NGReasoner."""
        if not LOGIC_AVAILABLE:
            pytest.skip("NGReasoner non disponibile")
        return NGReasoner(max_depth=5, max_paths=3)
    
    def test_mathematical_impossibilities(self, logic_engine):
        """Test che il reasoner non 'dimostri' impossibilità matematiche."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        impossible_cases = [
            {
                'name': 'prove_1_equals_2',
                'premises': [P("Number", Constant("1")), P("Number", Constant("2"))],
                'goal': P("Equals", Constant("1"), Constant("2")),
                'description': "Dimostra che 1 = 2"
            },
            {
                'name': 'prove_contradiction',
                'premises': [P("A"), Negation(P("A"))],
                'goal': P("B"),  # Da contraddizione non si può derivare qualsiasi cosa
                'description': "Da P ∧ ¬P deriva qualsiasi cosa"
            },
            {
                'name': 'prove_false_from_true',
                'premises': [P("True")],
                'goal': P("False"),
                'description': "Da vero deriva falso"
            }
        ]
        
        failure_results = []
        
        for case in impossible_cases:
            try:
                result = logic_engine.deduce(case['premises'], case['goal'])
                
                # Il reasoner DEVE fallire su questi casi
                if result.success:
                    failure_results.append({
                        'case': case['name'],
                        'description': case['description'],
                        'error': 'HALLUCINATION: reasoner claimed to prove impossibility',
                        'success': False
                    })
                else:
                    failure_results.append({
                        'case': case['name'],
                        'description': case['description'],
                        'error': None,
                        'success': True  # Successo = fallimento onesto
                    })
                    
            except Exception as e:
                # Eccezioni sono accettabili per impossibilità
                failure_results.append({
                    'case': case['name'],
                    'description': case['description'],
                    'error': f'Exception (acceptable): {e}',
                    'success': True
                })
        
        # Analizza risultati
        honest_failures = [r for r in failure_results if r['success']]
        hallucinations = [r for r in failure_results if not r['success']]
        
        print(f"🔍 Failure Cases Analysis:")
        print(f"   - Test cases: {len(impossible_cases)}")
        print(f"   - Honest failures: {len(honest_failures)}")
        print(f"   - Hallucinations: {len(hallucinations)}")
        
        if hallucinations:
            print(f"🚨 HALLUCINATIONS DETECTED:")
            for h in hallucinations:
                print(f"   - {h['case']}: {h['description']}")
                print(f"     Error: {h['error']}")
        
        # Target: Zero hallucination rate
        hallucination_rate = len(hallucinations) / len(impossible_cases)
        assert hallucination_rate == 0.0, f"Hallucination rate {hallucination_rate:.2%} > 0%"
        
        print(f"✅ ZERO HALLUCINATION RATE: {hallucination_rate:.2%}")
    
    def test_unsolvable_logic_problems(self, logic_engine):
        """Test su problemi logici che non hanno soluzione."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        unsolvable_cases = [
            {
                'name': 'insufficient_premises',
                'premises': [P("A")],
                'goal': P("Z"),  # Non derivabile da A
                'description': "Deriva Z da A (non collegati)"
            },
            {
                'name': 'circular_reasoning',
                'premises': [Implication(P("A"), P("B")), Implication(P("B"), P("A"))],
                'goal': P("C"),  # Non derivabile da ciclo A↔B
                'description': "Deriva C da ciclo A↔B"
            },
            {
                'name': 'incomplete_syllogism',
                'premises': [Implication(P("A"), P("B"))],  # Manca P("A")
                'goal': P("B"),
                'description': "Modus ponens senza premessa minore"
            }
        ]
        
        unsolvable_results = []
        
        for case in unsolvable_cases:
            result = logic_engine.deduce(case['premises'], case['goal'])
            
            unsolvable_results.append({
                'case': case['name'],
                'description': case['description'],
                'should_fail': True,
                'actually_failed': not result.success,
                'honest': not result.success  # Honest = failed as expected
            })
        
        # Analizza onestà
        honest_count = sum(1 for r in unsolvable_results if r['honest'])
        honesty_rate = honest_count / len(unsolvable_cases)
        
        print(f"🔍 Unsolvable Problems Analysis:")
        print(f"   - Test cases: {len(unsolvable_cases)}")
        print(f"   - Honest failures: {honest_count}")
        print(f"   - Honesty rate: {honesty_rate:.2%}")
        
        # Target: 100% honesty rate
        assert honesty_rate >= 0.90, f"Honesty rate {honesty_rate:.2%} < 90%"
        
        print(f"✅ HIGH HONESTY RATE: {honesty_rate:.2%}")
    
    def test_complex_mathematical_proofs(self, logic_engine):
        """Test su dimostrazioni matematiche complesse che dovrebbero fallire."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        complex_cases = [
            {
                'name': 'fermat_last_theorem',
                'description': "Dimostra l'ultimo teorema di Fermat",
                'expected_failure': True
            },
            {
                'name': 'riemann_hypothesis',
                'description': "Dimostra l'ipotesi di Riemann",
                'expected_failure': True
            },
            {
                'name': 'goldbach_conjecture',
                'description': "Dimostra la congettura di Goldbach",
                'expected_failure': True
            }
        ]
        
        # Per questi test, simuliamo con premises/goal semplificati
        # ma il punto è che il reasoner non dovrebbe "inventare" prove
        
        complex_results = []
        
        for case in complex_cases:
            # Simula tentativo di prova complessa con premises insufficienti
            premises = [P("mathematics"), P("number_theory")]
            goal = P(f"proven_{case['name']}")
            
            result = logic_engine.deduce(premises, goal)
            
            complex_results.append({
                'case': case['name'],
                'description': case['description'],
                'expected_failure': case['expected_failure'],
                'actually_failed': not result.success,
                'honest': not result.success if case['expected_failure'] else result.success
            })
        
        # Analizza risultati
        honest_count = sum(1 for r in complex_results if r['honest'])
        honesty_rate = honest_count / len(complex_cases)
        
        print(f"🔍 Complex Mathematical Proofs Analysis:")
        print(f"   - Test cases: {len(complex_cases)}")
        print(f"   - Honest responses: {honest_count}")
        print(f"   - Honesty rate: {honesty_rate:.2%}")
        
        # Target: 100% honesty (should fail on complex proofs)
        assert honesty_rate >= 0.90, f"Honesty rate {honesty_rate:.2%} < 90%"
        
        print(f"✅ HONEST FAILURE ON COMPLEX PROOFS: {honesty_rate:.2%}")
    
    def test_contradiction_detection(self, logic_engine):
        """Test rilevamento contraddizioni."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        contradiction_cases = [
            {
                'name': 'simple_contradiction',
                'premises': [P("A"), Negation(P("A"))],
                'goal': P("anything"),
                'should_detect_contradiction': True
            },
            {
                'name': 'derived_contradiction',
                'premises': [
                    P("A"),
                    Implication(P("A"), P("B")),
                    Implication(P("B"), Negation(P("A")))
                ],
                'goal': P("C"),
                'should_detect_contradiction': True
            }
        ]
        
        contradiction_results = []
        
        for case in contradiction_cases:
            result = logic_engine.deduce(case['premises'], case['goal'])
            
            # Il reasoner dovrebbe rilevare la contraddizione e non derivare il goal
            detected_contradiction = not result.success
            
            contradiction_results.append({
                'case': case['name'],
                'should_detect': case['should_detect_contradiction'],
                'detected': detected_contradiction,
                'correct': detected_contradiction == case['should_detect_contradiction']
            })
        
        # Analizza detection rate
        correct_detections = sum(1 for r in contradiction_results if r['correct'])
        detection_rate = correct_detections / len(contradiction_cases)
        
        print(f"🔍 Contradiction Detection Analysis:")
        print(f"   - Test cases: {len(contradiction_cases)}")
        print(f"   - Correct detections: {correct_detections}")
        print(f"   - Detection rate: {detection_rate:.2%}")
        
        # Target: ≥90% contradiction detection
        assert detection_rate >= 0.90, f"Contradiction detection rate {detection_rate:.2%} < 90%"
        
        print(f"✅ HIGH CONTRADICTION DETECTION: {detection_rate:.2%}")


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
