# NEUROGLYPH Default Pipeline Configuration
# Parser → <PERSON><PERSON><PERSON> → <PERSON><PERSON> → Learner

name: "neuroglyph-default"
description: "Pipeline seriale per debug facile"

# Configurazione globale
config:
  db_path: "pipeline_learning.db"
  log_level: "INFO"
  max_retries: 3
  timeout_seconds: 30

# Stages della pipeline
stages:
  - id: "validator"
    type: "VRValidatorStage"
    description: "Parser + Validator per codice sorgente"
    config:
      enable_ast_parsing: true
      validation_level: "strict"
      
  - id: "analyzer"
    type: "ErrorAnalysisStage"
    description: "Analisi errori e pattern detection"
    config:
      analysis_depth: "deep"
      pattern_matching: true
      
  - id: "patcher"
    type: "PatchStage"
    description: "Adaptive patching con learning"
    config:
      db_path: "pipeline_learning.db"
      max_patches_per_error: 5
      enable_learning: true
      
  - id: "learner"
    type: "LearnerStage"
    description: "Pattern extraction e knowledge graph"
    config:
      db_path: "pipeline_learning.db"
      min_support: 3
      min_confidence: 0.6

# Dipendenze DAG (per ora seriale)
dependencies:
  analyzer: ["validator"]
  patcher: ["analyzer"]
  learner: ["patcher"]

# Event topics per monitoring
events:
  - "validation.completed"
  - "analysis.completed"
  - "patching.completed"
  - "learning.completed"
  - "pipeline.started"
  - "pipeline.completed"
  - "pipeline.failed"

# Metriche da raccogliere
metrics:
  - name: "stage_duration"
    type: "histogram"
    labels: ["stage_id", "status"]
    
  - name: "pipeline_throughput"
    type: "counter"
    labels: ["pipeline_name"]
    
  - name: "error_count"
    type: "gauge"
    labels: ["error_type"]
    
  - name: "patch_success_rate"
    type: "gauge"
    labels: ["stage_id"]
