#!/usr/bin/env python3
"""
Test per NEUROGLYPH Adaptive Patcher
Verifica auto-correzione intelligente e apprendimento adattivo
"""

def test_patcher_structures():
    """Test strutture del patcher."""
    print("🔧 TEST PATCHER STRUCTURES")
    
    try:
        from neuroglyph.cognitive.patcher_structures import (
            ErrorAnalysis, PatchCandidate, PatchResult, AdaptiveLearningState,
            ErrorType, PatchType, PatchStatus, LearningMode
        )
        
        # Test ErrorAnalysis
        error = ErrorAnalysis(
            error_type=ErrorType.RUNTIME_ERROR,
            error_message="name 'x' is not defined",
            severity="high",
            impact_score=0.8
        )
        
        print(f"✅ ErrorAnalysis created:")
        print(f"   Type: {error.error_type}")
        print(f"   Message: {error.error_message}")
        print(f"   Severity: {error.severity}")
        print(f"   Impact: {error.impact_score}")
        
        # Test PatchCandidate
        patch = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error.error_id,
            patch_description="Initialize variable x",
            confidence=0.8,
            risk_level="medium"
        )
        
        print(f"✅ PatchCandidate created:")
        print(f"   Type: {patch.patch_type}")
        print(f"   Description: {patch.patch_description}")
        print(f"   Confidence: {patch.confidence}")
        print(f"   Risk: {patch.risk_level}")
        
        # Test AdaptiveLearningState
        learning = AdaptiveLearningState(
            learning_mode=LearningMode.BALANCED,
            learning_rate=0.1
        )
        
        print(f"✅ AdaptiveLearningState:")
        print(f"   Mode: {learning.learning_mode}")
        print(f"   Rate: {learning.learning_rate}")
        print(f"   Success rate: {learning.overall_success_rate}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_analyzer():
    """Test analizzatore errori."""
    print("\n🔍 TEST ERROR ANALYZER")
    
    try:
        from neuroglyph.cognitive.error_analyzer import ErrorAnalyzer
        from neuroglyph.cognitive.sandbox_structures import ExecutionResult, ExecutionStatus
        
        # Crea analyzer
        analyzer = ErrorAnalyzer()
        
        # Crea ExecutionResult con errore
        execution_result = ExecutionResult()
        execution_result.success = False
        execution_result.status = ExecutionStatus.FAILED
        execution_result.error_type = "NameError"
        execution_result.error_message = "name 'undefined_var' is not defined"
        execution_result.traceback = "Traceback (most recent call last):\n  File \"<sandbox>\", line 1, in <module>\nNameError: name 'undefined_var' is not defined"
        execution_result.finalize()
        
        # Analizza errore
        analysis = analyzer.analyze_execution_error(execution_result)
        
        print(f"✅ Error analysis:")
        print(f"   Error type: {analysis.error_type}")
        print(f"   Pattern: {analysis.error_pattern}")
        print(f"   Root cause: {analysis.root_cause}")
        print(f"   Severity: {analysis.severity}")
        print(f"   Impact score: {analysis.impact_score}")
        print(f"   Contributing factors: {len(analysis.contributing_factors)}")
        
        # Test statistiche
        stats = analyzer.get_error_statistics()
        print(f"✅ Error statistics:")
        print(f"   Total errors: {stats.get('total_errors', 0)}")
        
        return analysis is not None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_patch_generator():
    """Test generatore patch."""
    print("\n🔧 TEST PATCH GENERATOR")
    
    try:
        from neuroglyph.cognitive.patch_generator import PatchGenerator
        from neuroglyph.cognitive.patcher_structures import ErrorAnalysis, ErrorType
        
        # Crea generator
        generator = PatchGenerator()
        
        # Crea ErrorAnalysis
        error = ErrorAnalysis(
            error_type=ErrorType.RUNTIME_ERROR,
            error_message="name 'x' is not defined",
            error_pattern="undefined_variable",
            severity="medium",
            impact_score=0.6
        )
        error.metadata = {"variable_name": "x"}
        
        # Genera patch
        patches = generator.generate_patches(error)
        
        print(f"✅ Patch generation:")
        print(f"   Error pattern: {error.error_pattern}")
        print(f"   Patches generated: {len(patches)}")
        
        for i, patch in enumerate(patches[:3], 1):
            print(f"   Patch {i}:")
            print(f"      Type: {patch.patch_type}")
            print(f"      Description: {patch.patch_description}")
            print(f"      Strategy: {patch.patch_strategy}")
            print(f"      Confidence: {patch.confidence:.3f}")
            print(f"      Risk: {patch.risk_level}")
        
        # Test statistiche
        stats = generator.get_generation_statistics()
        print(f"✅ Generation statistics:")
        print(f"   Strategies: {stats['strategies_available']}")
        print(f"   Max candidates: {stats['max_candidates']}")
        
        return len(patches) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_patcher():
    """Test adaptive patcher completo."""
    print("\n🔧 TEST ADAPTIVE PATCHER")
    
    try:
        from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
        from neuroglyph.cognitive.sandbox_structures import (
            ExecutionResult, ExecutionStatus, ExecutionContext, ExecutionMode
        )
        
        # Crea patcher
        patcher = NGAdaptivePatcher()
        
        # Crea ExecutionResult con errore
        execution_result = ExecutionResult()
        execution_result.success = False
        execution_result.status = ExecutionStatus.FAILED
        execution_result.error_type = "NameError"
        execution_result.error_message = "name 'y' is not defined"
        
        # Crea ExecutionContext
        context = ExecutionContext(
            code="print(y)",  # Variabile non definita
            mode=ExecutionMode.EXEC
        )
        execution_result.execution_context = context
        execution_result.finalize()
        
        # Applica patch
        patch_result = patcher.patch_execution_error(execution_result)
        
        print(f"✅ Adaptive patching:")
        print(f"   Original error: {execution_result.error_message}")
        print(f"   Patch applied: {patch_result is not None}")
        
        if patch_result:
            print(f"   Patch success: {patch_result.success}")
            print(f"   Patch status: {patch_result.status}")
            print(f"   Execution time: {patch_result.execution_time:.3f}s")
            if patch_result.error_message:
                print(f"   Error: {patch_result.error_message}")
            if patch_result.output:
                print(f"   Output: {patch_result.output}")
        
        # Test statistiche apprendimento
        stats = patcher.get_learning_statistics()
        print(f"✅ Learning statistics:")
        print(f"   Errors seen: {stats['total_errors_seen']}")
        print(f"   Errors fixed: {stats['total_errors_fixed']}")
        print(f"   Patches generated: {stats['total_patches_generated']}")
        print(f"   Success rate: {stats['overall_success_rate']:.3f}")
        print(f"   Learned patterns: {stats['learned_patterns']}")
        
        return patch_result is not None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_syntax_error_patching():
    """Test correzione errori di sintassi."""
    print("\n✏️ TEST SYNTAX ERROR PATCHING")
    
    try:
        from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
        from neuroglyph.cognitive.sandbox_structures import (
            ExecutionResult, ExecutionStatus, ExecutionContext, ExecutionMode
        )
        
        # Crea patcher
        patcher = NGAdaptivePatcher()
        
        # Test errore sintassi (due punti mancanti)
        execution_result = ExecutionResult()
        execution_result.success = False
        execution_result.status = ExecutionStatus.FAILED
        execution_result.error_type = "SyntaxError"
        execution_result.error_message = "invalid syntax"
        
        context = ExecutionContext(
            code="if x > 0\n    print('positive')",  # Mancano i due punti
            mode=ExecutionMode.EXEC
        )
        execution_result.execution_context = context
        execution_result.finalize()
        
        # Applica patch
        patch_result = patcher.patch_execution_error(execution_result)
        
        print(f"✅ Syntax error patching:")
        print(f"   Original code: {context.code}")
        print(f"   Patch applied: {patch_result is not None}")
        
        if patch_result:
            print(f"   Success: {patch_result.success}")
            print(f"   Status: {patch_result.status}")
        
        return patch_result is not None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_learning_adaptation():
    """Test adattamento apprendimento."""
    print("\n🧠 TEST LEARNING ADAPTATION")
    
    try:
        from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
        from neuroglyph.cognitive.patcher_structures import PatcherConfig, LearningMode
        
        # Crea patcher con configurazione specifica
        config = PatcherConfig(
            learning_mode=LearningMode.AGGRESSIVE,
            learning_rate=0.2,
            adaptation_frequency=5
        )
        patcher = NGAdaptivePatcher(config)
        
        print(f"✅ Learning configuration:")
        print(f"   Mode: {patcher.learning_state.learning_mode}")
        print(f"   Rate: {patcher.learning_state.learning_rate}")
        print(f"   Adaptation threshold: {patcher.learning_state.adaptation_threshold}")
        
        # Simula alcuni errori per testare adattamento
        initial_rate = patcher.learning_state.learning_rate
        
        # Simula fallimenti per trigger adattamento
        patcher.learning_state.total_patches_generated = 15
        patcher.learning_state.total_patches_successful = 3  # Basso success rate
        patcher.learning_state.update_success_rate()
        
        print(f"✅ Before adaptation:")
        print(f"   Success rate: {patcher.learning_state.overall_success_rate:.3f}")
        print(f"   Learning rate: {patcher.learning_state.learning_rate:.3f}")
        
        # Trigger adattamento
        if patcher.learning_state.should_adapt():
            patcher._adapt_learning()
            
            print(f"✅ After adaptation:")
            print(f"   Learning rate: {patcher.learning_state.learning_rate:.3f}")
            print(f"   Rate changed: {patcher.learning_state.learning_rate != initial_rate}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_error_types():
    """Test correzione tipi multipli di errori."""
    print("\n🔄 TEST MULTIPLE ERROR TYPES")
    
    try:
        from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
        from neuroglyph.cognitive.sandbox_structures import (
            ExecutionResult, ExecutionStatus, ExecutionContext, ExecutionMode
        )
        
        # Crea patcher
        patcher = NGAdaptivePatcher()
        
        # Test diversi tipi di errori
        error_cases = [
            {
                "name": "NameError",
                "error_type": "NameError",
                "message": "name 'z' is not defined",
                "code": "print(z)"
            },
            {
                "name": "ImportError", 
                "error_type": "ImportError",
                "message": "No module named 'nonexistent'",
                "code": "import nonexistent"
            },
            {
                "name": "AttributeError",
                "error_type": "AttributeError", 
                "message": "'str' object has no attribute 'nonexistent'",
                "code": "'hello'.nonexistent()"
            }
        ]
        
        results = []
        
        for case in error_cases:
            # Crea ExecutionResult
            execution_result = ExecutionResult()
            execution_result.success = False
            execution_result.status = ExecutionStatus.FAILED
            execution_result.error_type = case["error_type"]
            execution_result.error_message = case["message"]
            
            context = ExecutionContext(
                code=case["code"],
                mode=ExecutionMode.EXEC
            )
            execution_result.execution_context = context
            execution_result.finalize()
            
            # Applica patch
            patch_result = patcher.patch_execution_error(execution_result)
            
            result = {
                "name": case["name"],
                "patched": patch_result is not None,
                "success": patch_result.success if patch_result else False
            }
            results.append(result)
            
            print(f"✅ {case['name']}:")
            print(f"   Patched: {result['patched']}")
            print(f"   Success: {result['success']}")
        
        # Statistiche finali
        stats = patcher.get_learning_statistics()
        print(f"✅ Final statistics:")
        print(f"   Total errors: {stats['total_errors_seen']}")
        print(f"   Success rate: {stats['overall_success_rate']:.3f}")
        
        return len([r for r in results if r["patched"]]) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """Test performance del patcher."""
    print("\n⚡ TEST PERFORMANCE")
    
    try:
        import time
        from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
        from neuroglyph.cognitive.sandbox_structures import (
            ExecutionResult, ExecutionStatus, ExecutionContext, ExecutionMode
        )
        
        # Crea patcher
        patcher = NGAdaptivePatcher()
        
        # Test performance con molti errori
        start_time = time.time()
        results = []
        
        for i in range(5):  # Test con 5 errori
            execution_result = ExecutionResult()
            execution_result.success = False
            execution_result.status = ExecutionStatus.FAILED
            execution_result.error_type = "NameError"
            execution_result.error_message = f"name 'var_{i}' is not defined"
            
            context = ExecutionContext(
                code=f"print(var_{i})",
                mode=ExecutionMode.EXEC
            )
            execution_result.execution_context = context
            execution_result.finalize()
            
            patch_result = patcher.patch_execution_error(execution_result)
            results.append(patch_result)
        
        total_time = time.time() - start_time
        
        successful = sum(1 for r in results if r and r.success)
        avg_time = total_time / len(results)
        
        print(f"✅ Performance test:")
        print(f"   Errors processed: {len(results)}")
        print(f"   Successful patches: {successful}")
        print(f"   Total time: {total_time:.3f}s")
        print(f"   Avg time per error: {avg_time:.3f}s")
        print(f"   Errors per second: {len(results) / total_time:.1f}")
        
        # Performance target: <2s per error
        performance_ok = avg_time < 2.0
        print(f"   Performance target (<2s): {'✅' if performance_ok else '❌'}")
        
        return performance_ok
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 NEUROGLYPH ADAPTIVE PATCHER TEST")
    print("=" * 80)
    
    # Esegui tutti i test
    test_results = []
    
    test_results.append(("Patcher Structures", test_patcher_structures()))
    test_results.append(("Error Analyzer", test_error_analyzer()))
    test_results.append(("Patch Generator", test_patch_generator()))
    test_results.append(("Adaptive Patcher", test_adaptive_patcher()))
    test_results.append(("Syntax Error Patching", test_syntax_error_patching()))
    test_results.append(("Learning Adaptation", test_learning_adaptation()))
    test_results.append(("Multiple Error Types", test_multiple_error_types()))
    test_results.append(("Performance", test_performance()))
    
    print("\n" + "=" * 80)
    print("🔧 RISULTATI FINALI NG_ADAPTIVE_PATCHER:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    success_rate = passed / len(test_results)
    print(f"\n🎯 SUCCESS RATE: {success_rate:.1%} ({passed}/{len(test_results)})")
    
    if success_rate >= 0.8:
        print("✅ NG_ADAPTIVE_PATCHER TEST: SUCCESSO!")
        print("   🔧 Auto-correzione intelligente funzionante")
        print("   🧠 Apprendimento adattivo operativo")
        print("   🔍 Error analysis avanzato")
        print("   🛠️ Patch generation automatica")
        print("   📊 Learning statistics implementate")
        print("   ⚡ Performance <2s per errore")
        print("   ✅ Sesto modulo cognitivo funzionante!")
    else:
        print("❌ NG_ADAPTIVE_PATCHER TEST: Miglioramenti necessari")
        print(f"   - Success rate: {success_rate:.1%} (target: ≥80%)")
        print("   - Verificare auto-correzione e apprendimento")
    
    print(f"\n🚀 CONCLUSIONE NG_ADAPTIVE_PATCHER:")
    if success_rate >= 0.8:
        print("   🎉 NGAdaptivePatcher è PRONTO per la produzione!")
        print("   🔧 Auto-correzione intelligente")
        print("   🧠 Apprendimento adattivo continuo")
        print("   ✅ Parser → Prioritizer → Memory → Reasoner → SelfCheck → Sandbox → AdaptivePatcher")
        print("   🚀 PIPELINE COGNITIVA QUASI COMPLETA!")
        print("   🎯 Prossimo step: NG_LEARNER per apprendimento avanzato")
    else:
        print("   🔧 Debugging necessario per adaptive patcher")
        print("   🧠 Verificare algoritmi di apprendimento")
