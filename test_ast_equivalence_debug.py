#!/usr/bin/env python3
"""
Debug AST-Equivalenza per Mixed Constructs
Identifica esattamente dove l'AST non è equivalente
"""

import ast
from neuroglyph.core.encoder.encoder import NGEncoder

def debug_mixed_constructs():
    """Debug del caso mixed constructs che fallisce AST-equivalenza."""
    print("🔍 DEBUG AST-EQUIVALENZA: Mixed Constructs")
    
    # Codice problematico dal test
    code = """
def advanced_processor(data_file, threshold=0.5):
    results = []
    
    with open(data_file, 'r') as f:
        lines = f.readlines()
    
    # Lambda + generator + f-string
    processor = lambda x: float(x.strip()) if x.strip().isdigit() else 0.0
    valid_numbers = (processor(line) for line in lines if line.strip())
    
    for num in valid_numbers:
        if num > threshold:
            # F-string con lambda
            message = f"Processing {num:.2f}: {(lambda x: 'high' if x > 0.8 else 'medium')(num)}"
            results.append(message)
    
    return results
"""
    
    # Parse AST originale
    original_ast = ast.parse(code)
    original_dump = ast.dump(original_ast, include_attributes=False)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    
    # Test round-trip AST-based
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    # Parse AST ricostruito
    reconstructed_ast = ast.parse(result.decoding_result.reconstructed_code)
    reconstructed_dump = ast.dump(reconstructed_ast, include_attributes=False)
    
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    
    # Confronto dettagliato
    print(f"\n🔍 AST DUMP ORIGINALE (primi 500 char):")
    print(original_dump[:500] + "..." if len(original_dump) > 500 else original_dump)
    
    print(f"\n🔍 AST DUMP RICOSTRUITO (primi 500 char):")
    print(reconstructed_dump[:500] + "..." if len(reconstructed_dump) > 500 else reconstructed_dump)
    
    # Trova differenze
    ast_equivalent = (original_dump == reconstructed_dump)
    print(f"\n📊 AST EQUIVALENTE: {'✅' if ast_equivalent else '❌'}")
    
    if not ast_equivalent:
        print(f"\n🔍 ANALISI DIFFERENZE:")
        
        # Trova prima differenza
        min_len = min(len(original_dump), len(reconstructed_dump))
        first_diff = -1
        for i in range(min_len):
            if original_dump[i] != reconstructed_dump[i]:
                first_diff = i
                break
        
        if first_diff >= 0:
            start = max(0, first_diff - 50)
            end = min(len(original_dump), first_diff + 50)
            
            print(f"   - Prima differenza al carattere {first_diff}")
            print(f"   - Originale: ...{original_dump[start:end]}...")
            print(f"   - Ricostruito: ...{reconstructed_dump[start:end]}...")
        
        # Analizza lunghezze
        print(f"   - Lunghezza originale: {len(original_dump)}")
        print(f"   - Lunghezza ricostruita: {len(reconstructed_dump)}")
        print(f"   - Differenza: {len(reconstructed_dump) - len(original_dump)}")
    
    return ast_equivalent, original_dump, reconstructed_dump

def debug_minimal_case():
    """Debug con caso minimale che riproduce il problema."""
    print("\n🔍 DEBUG CASO MINIMALE:")
    
    # Caso minimale: lambda dentro f-string
    minimal_code = """
def test_func(num):
    message = f"Result: {(lambda x: 'high' if x > 0.8 else 'medium')(num)}"
    return message
"""
    
    print(f"📝 CODICE MINIMALE:")
    print(minimal_code)
    
    # Parse AST originale
    original_ast = ast.parse(minimal_code)
    original_dump = ast.dump(original_ast, include_attributes=False)
    
    # Test round-trip
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(minimal_code, encoding_level=5)
    
    # Parse AST ricostruito
    reconstructed_ast = ast.parse(result.decoding_result.reconstructed_code)
    reconstructed_dump = ast.dump(reconstructed_ast, include_attributes=False)
    
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    
    # Confronto
    ast_equivalent = (original_dump == reconstructed_dump)
    print(f"\n📊 AST EQUIVALENTE: {'✅' if ast_equivalent else '❌'}")
    
    if not ast_equivalent:
        print(f"\n🔍 DUMP ORIGINALE:")
        print(original_dump)
        print(f"\n🔍 DUMP RICOSTRUITO:")
        print(reconstructed_dump)
    
    return ast_equivalent

def debug_f_string_lambda():
    """Debug specifico per f-string con lambda."""
    print("\n🔍 DEBUG F-STRING + LAMBDA:")
    
    # F-string con lambda
    fstring_code = """
message = f"Processing {num:.2f}: {(lambda x: 'high' if x > 0.8 else 'medium')(num)}"
"""
    
    print(f"📝 F-STRING + LAMBDA:")
    print(fstring_code)
    
    # Analizza AST
    tree = ast.parse(fstring_code)
    print(f"\n🔍 AST STRUCTURE:")
    print(ast.dump(tree, indent=2))
    
    # Test compressione
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(fstring_code, encoding_level=5)
    
    print(f"\n📊 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    
    # Verifica AST
    original_ast = ast.parse(fstring_code)
    reconstructed_ast = ast.parse(result.decoding_result.reconstructed_code)
    
    original_dump = ast.dump(original_ast, include_attributes=False)
    reconstructed_dump = ast.dump(reconstructed_ast, include_attributes=False)
    
    ast_equivalent = (original_dump == reconstructed_dump)
    print(f"\n📊 AST EQUIVALENTE: {'✅' if ast_equivalent else '❌'}")
    
    return ast_equivalent

if __name__ == "__main__":
    print("🔍 DEBUG AST-EQUIVALENZA MIXED CONSTRUCTS")
    print("=" * 80)
    
    # Debug caso completo
    equiv1, orig_dump, recon_dump = debug_mixed_constructs()
    
    # Debug caso minimale
    equiv2 = debug_minimal_case()
    
    # Debug f-string + lambda specifico
    equiv3 = debug_f_string_lambda()
    
    print("\n" + "=" * 80)
    print("🔍 RISULTATI DEBUG:")
    print(f"   - Mixed completo: {'✅' if equiv1 else '❌'}")
    print(f"   - Caso minimale: {'✅' if equiv2 else '❌'}")
    print(f"   - F-string + Lambda: {'✅' if equiv3 else '❌'}")
    
    if not any([equiv1, equiv2, equiv3]):
        print("\n❌ PROBLEMA IDENTIFICATO:")
        print("   - Lambda dentro F-string non preserva AST equivalenza")
        print("   - Necessario correggere visit_JoinedStr o visit_Lambda")
        print("   - Verificare copy_location e fix_missing_locations")
    else:
        print("\n✅ NESSUN PROBLEMA RILEVATO:")
        print("   - AST equivalenza funziona correttamente")
        print("   - Possibile problema nel test originale")
