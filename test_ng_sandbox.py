#!/usr/bin/env python3
"""
Test per NEUROGLYPH Sandbox
Verifica esecuzione sicura del codice
"""

def test_sandbox_structures():
    """Test strutture del sandbox."""
    print("🏖️ TEST SANDBOX STRUCTURES")
    
    try:
        from neuroglyph.cognitive.sandbox_structures import (
            ExecutionContext, ExecutionResult, ResourceLimits, SandboxConfig,
            ExecutionStatus, ExecutionMode, SecurityLevel
        )
        
        # Test ResourceLimits
        limits = ResourceLimits(
            max_execution_time=2.0,
            max_memory_mb=50,
            allow_network=False
        )
        
        print(f"✅ ResourceLimits created:")
        print(f"   Max time: {limits.max_execution_time}s")
        print(f"   Max memory: {limits.max_memory_mb}MB")
        print(f"   Allow network: {limits.allow_network}")
        
        # Test ExecutionContext
        context = ExecutionContext(
            code="x = 1 + 1",
            mode=ExecutionMode.EXEC,
            security_level=SecurityLevel.STANDARD,
            resource_limits=limits
        )
        
        print(f"✅ ExecutionContext created:")
        print(f"   Code: {context.code}")
        print(f"   Mode: {context.mode}")
        print(f"   Security: {context.security_level}")
        
        # Test ExecutionResult
        result = ExecutionResult(execution_context=context)
        result.status = ExecutionStatus.COMPLETED
        result.result = 2
        result.finalize()
        
        print(f"✅ ExecutionResult:")
        print(f"   Status: {result.status}")
        print(f"   Success: {result.success}")
        print(f"   Duration: {result.duration:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_security_monitor():
    """Test monitor di sicurezza."""
    print("\n🔒 TEST SECURITY MONITOR")
    
    try:
        from neuroglyph.cognitive.security_monitor import SecurityMonitor
        from neuroglyph.cognitive.sandbox_structures import SecurityLevel
        
        # Crea monitor
        monitor = SecurityMonitor()
        
        # Test codice sicuro
        safe_code = "x = 1 + 1\nprint(x)"
        violations = monitor.analyze_code_security(safe_code, SecurityLevel.STANDARD)
        
        print(f"✅ Safe code analysis:")
        print(f"   Code: {safe_code}")
        print(f"   Violations: {len(violations)}")
        
        # Test codice pericoloso
        dangerous_code = "import os\nos.system('rm -rf /')"
        violations = monitor.analyze_code_security(dangerous_code, SecurityLevel.HIGH)
        
        print(f"✅ Dangerous code analysis:")
        print(f"   Code: {dangerous_code}")
        print(f"   Violations: {len(violations)}")
        
        if violations:
            violation = violations[0]
            print(f"   First violation: {violation.violation_type} - {violation.message}")
        
        # Test globals sicuri
        safe_globals = monitor.create_safe_globals(SecurityLevel.STANDARD)
        
        print(f"✅ Safe globals created:")
        print(f"   Keys: {len(safe_globals)}")
        print(f"   Has math: {'math' in safe_globals}")
        print(f"   Has os: {'os' in safe_globals}")
        
        return len(violations) > 0  # Dovrebbe trovare violazioni nel codice pericoloso
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_execution():
    """Test esecuzione base."""
    print("\n🏖️ TEST BASIC EXECUTION")
    
    try:
        from neuroglyph.cognitive.sandbox import NGSandbox
        from neuroglyph.cognitive.sandbox_structures import ExecutionStatus
        
        # Crea sandbox
        sandbox = NGSandbox()
        
        # Test esecuzione semplice
        result = sandbox.execute_code("x = 2 + 3")
        
        print(f"✅ Simple execution:")
        print(f"   Status: {result.status}")
        print(f"   Success: {result.success}")
        print(f"   Duration: {result.duration:.3f}s")
        print(f"   Error: {result.error_message}")
        
        # Test valutazione espressione
        result = sandbox.evaluate_expression("5 * 6")
        
        print(f"✅ Expression evaluation:")
        print(f"   Result: {result.result}")
        print(f"   Status: {result.status}")
        print(f"   Success: {result.success}")
        
        # Test con output
        result = sandbox.execute_code("print('Hello, Sandbox!')")
        
        print(f"✅ Output execution:")
        print(f"   Stdout: '{result.stdout.strip()}'")
        print(f"   Status: {result.status}")
        
        return result.status == ExecutionStatus.COMPLETED
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_security_blocking():
    """Test blocco operazioni pericolose."""
    print("\n🚫 TEST SECURITY BLOCKING")
    
    try:
        from neuroglyph.cognitive.sandbox import NGSandbox
        from neuroglyph.cognitive.sandbox_structures import ExecutionStatus, SecurityLevel
        
        # Crea sandbox con sicurezza alta
        sandbox = NGSandbox()
        
        # Test import bloccato
        result = sandbox.execute_code(
            "import os\nos.listdir('/')",
            security_level=SecurityLevel.HIGH
        )
        
        print(f"✅ Blocked import test:")
        print(f"   Status: {result.status}")
        print(f"   Error type: {result.error_type}")
        print(f"   Blocked: {result.status == ExecutionStatus.BLOCKED}")
        
        # Test funzione pericolosa
        result = sandbox.execute_code(
            "exec('print(\"dangerous\")')",
            security_level=SecurityLevel.HIGH
        )
        
        print(f"✅ Dangerous function test:")
        print(f"   Status: {result.status}")
        print(f"   Blocked: {result.status in [ExecutionStatus.BLOCKED, ExecutionStatus.FAILED]}")
        
        # Test codice sicuro
        result = sandbox.execute_code(
            "import math\nresult = math.sqrt(16)",
            security_level=SecurityLevel.STANDARD
        )
        
        print(f"✅ Safe code test:")
        print(f"   Status: {result.status}")
        print(f"   Success: {result.success}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_timeout_handling():
    """Test gestione timeout."""
    print("\n⏰ TEST TIMEOUT HANDLING")
    
    try:
        from neuroglyph.cognitive.sandbox import NGSandbox
        from neuroglyph.cognitive.sandbox_structures import ExecutionStatus
        
        # Crea sandbox
        sandbox = NGSandbox()
        
        # Test timeout
        result = sandbox.execute_code(
            "import time\ntime.sleep(2)",  # Sleep 2 secondi
            timeout=1.0  # Timeout 1 secondo
        )
        
        print(f"✅ Timeout test:")
        print(f"   Status: {result.status}")
        print(f"   Duration: {result.duration:.3f}s")
        print(f"   Timed out: {result.status == ExecutionStatus.TIMEOUT}")
        print(f"   Error: {result.error_message}")
        
        # Test esecuzione veloce
        result = sandbox.execute_code(
            "x = sum(range(100))",
            timeout=2.0
        )
        
        print(f"✅ Fast execution:")
        print(f"   Status: {result.status}")
        print(f"   Duration: {result.duration:.3f}s")
        print(f"   Completed: {result.status == ExecutionStatus.COMPLETED}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_code_validation():
    """Test validazione codice."""
    print("\n✅ TEST CODE VALIDATION")
    
    try:
        from neuroglyph.cognitive.sandbox import NGSandbox
        from neuroglyph.cognitive.sandbox_structures import ExecutionStatus
        
        # Crea sandbox
        sandbox = NGSandbox()
        
        # Test codice valido
        result = sandbox.validate_code("x = 1 + 1\nprint(x)")
        
        print(f"✅ Valid code test:")
        print(f"   Status: {result.status}")
        print(f"   Valid: {result.result}")
        print(f"   Success: {result.success}")
        
        # Test codice invalido
        result = sandbox.validate_code("x = 1 +")  # Syntax error
        
        print(f"✅ Invalid code test:")
        print(f"   Status: {result.status}")
        print(f"   Error type: {result.error_type}")
        print(f"   Failed: {result.status == ExecutionStatus.FAILED}")
        
        # Test sicurezza codice
        safe = sandbox.is_code_safe("x = math.sqrt(16)")
        dangerous = sandbox.is_code_safe("import os; os.system('ls')")
        
        print(f"✅ Code safety test:")
        print(f"   Safe code is safe: {safe}")
        print(f"   Dangerous code is safe: {dangerous}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_resource_monitoring():
    """Test monitoraggio risorse."""
    print("\n📊 TEST RESOURCE MONITORING")
    
    try:
        from neuroglyph.cognitive.sandbox import NGSandbox
        from neuroglyph.cognitive.sandbox_structures import ResourceLimits, ExecutionStatus
        
        # Crea sandbox
        sandbox = NGSandbox()
        
        # Test con limiti di memoria
        limits = ResourceLimits(
            max_execution_time=3.0,
            max_memory_mb=200,
            max_output_size=1000
        )
        
        # Esegui codice che usa memoria
        result = sandbox.execute_code(
            "data = list(range(1000))\nresult = sum(data)",
            timeout=2.0
        )
        
        print(f"✅ Resource monitoring:")
        print(f"   Status: {result.status}")
        print(f"   Duration: {result.duration:.3f}s")
        print(f"   Peak memory: {result.resource_usage.peak_memory_mb:.1f}MB")
        print(f"   Output size: {result.resource_usage.stdout_size} bytes")
        print(f"   Exceeded limits: {result.exceeded_limits}")
        
        return result.status == ExecutionStatus.COMPLETED
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """Test performance del sandbox."""
    print("\n⚡ TEST PERFORMANCE")
    
    try:
        import time
        from neuroglyph.cognitive.sandbox import NGSandbox
        
        # Crea sandbox
        sandbox = NGSandbox()
        
        # Test performance con molte esecuzioni
        start_time = time.time()
        results = []
        
        for i in range(10):
            result = sandbox.execute_code(f"x = {i} * 2")
            results.append(result)
        
        total_time = time.time() - start_time
        
        successful = sum(1 for r in results if r.success)
        avg_time = total_time / len(results)
        
        print(f"✅ Performance test:")
        print(f"   Executions: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Total time: {total_time:.3f}s")
        print(f"   Avg time per execution: {avg_time:.3f}s")
        print(f"   Executions per second: {len(results) / total_time:.1f}")
        
        # Performance target: <0.1s per execution
        performance_ok = avg_time < 0.1
        print(f"   Performance target (<0.1s): {'✅' if performance_ok else '❌'}")
        
        return performance_ok and successful == len(results)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🏖️ NEUROGLYPH SANDBOX TEST")
    print("=" * 80)
    
    # Esegui tutti i test
    test_results = []
    
    test_results.append(("Sandbox Structures", test_sandbox_structures()))
    test_results.append(("Security Monitor", test_security_monitor()))
    test_results.append(("Basic Execution", test_basic_execution()))
    test_results.append(("Security Blocking", test_security_blocking()))
    test_results.append(("Timeout Handling", test_timeout_handling()))
    test_results.append(("Code Validation", test_code_validation()))
    test_results.append(("Resource Monitoring", test_resource_monitoring()))
    test_results.append(("Performance", test_performance()))
    
    print("\n" + "=" * 80)
    print("🏖️ RISULTATI FINALI NG_SANDBOX:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    success_rate = passed / len(test_results)
    print(f"\n🎯 SUCCESS RATE: {success_rate:.1%} ({passed}/{len(test_results)})")
    
    if success_rate >= 0.8:
        print("✅ NG_SANDBOX TEST: SUCCESSO!")
        print("   🏖️ Esecuzione sicura funzionante")
        print("   🔒 Security monitor operativo")
        print("   ⏰ Timeout handling implementato")
        print("   📊 Resource monitoring attivo")
        print("   🚫 Operazioni pericolose bloccate")
        print("   ⚡ Performance <0.1s per esecuzione")
        print("   ✅ Quinto modulo cognitivo funzionante!")
    else:
        print("❌ NG_SANDBOX TEST: Miglioramenti necessari")
        print(f"   - Success rate: {success_rate:.1%} (target: ≥80%)")
        print("   - Verificare sicurezza e performance")
    
    print(f"\n🚀 CONCLUSIONE NG_SANDBOX:")
    if success_rate >= 0.8:
        print("   🎉 NGSandbox è PRONTO per la produzione!")
        print("   🏖️ Esecuzione sicura del codice")
        print("   🔒 Controlli di sicurezza avanzati")
        print("   ✅ Parser → Prioritizer → Memory → Reasoner → SelfCheck → Sandbox")
        print("   🚀 PIPELINE COGNITIVA AVANZATA!")
        print("   🎯 Prossimo step: NG_ADAPTIVE_PATCHER per auto-correzione")
    else:
        print("   🔧 Debugging necessario per sandbox")
        print("   🔒 Verificare controlli di sicurezza")
