{"summary": {"total_tests": 16, "encoding_successes": 0, "decoding_successes": 0, "round_trip_successes": 0, "ast_equivalences": 0, "encoding_success_rate": 0.0, "decoding_success_rate": 0.0, "round_trip_success_rate": 0.0, "ast_equivalence_rate": 0.0, "avg_encoding_time": 0, "avg_decoding_time": 0, "avg_compression_ratio": 0, "avg_symbols_count": 0}, "compression_comparison": {"neuroglyph_ratio": 0, "gzip_ratio": 0, "brotli_ratio": 0, "vs_gzip_factor": 0, "vs_brotli_factor": 0}, "file_size_analysis": {"small_files": {"count": 0, "avg_compression": 0, "ast_equivalence_rate": 0}, "medium_files": {"count": 0, "avg_compression": 0, "ast_equivalence_rate": 0}, "large_files": {"count": 0, "avg_compression": 0, "ast_equivalence_rate": 0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/optimize.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/_missing.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/_arpack.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/_chunking.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/fixes.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/_available_if.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/discovery.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/scikit-learn-main/sklearn/utils/_user_interface.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/accessor.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/roperator.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/missing.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/arraylike.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/construction.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/config_init.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/flags.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_8y8s2huf/pandas-main/pandas/core/sorting.py", "file_size": 0, "lines_count": 0, "encoding_success": false, "encoding_time": 0.0, "compressed_size": 0, "compression_ratio": 0.0, "symbols_count": 0, "decoding_success": false, "decoding_time": 0.0, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": -1, "gzip_size": 0, "brotli_size": 0, "gzip_ratio": 0.0, "brotli_ratio": 0.0, "error_message": "'dict' object has no attribute 'indent_char'"}]}