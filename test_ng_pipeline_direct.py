#!/usr/bin/env python3
"""
Test diretto della pipeline NEUROGLYPH con configurazione minima
"""

import sys
from pathlib import Path

# Aggiungi il path del progetto
sys.path.insert(0, str(Path(__file__).parent))

def test_pipeline_minimal():
    """Test pipeline con configurazione minima."""
    print("🧪 Test Pipeline Minima")
    print("-" * 40)
    
    try:
        from neuroglyph.cognitive import NGIntegration
        from neuroglyph.cognitive.integration import PipelineConfig, ExecutionMode
        
        # Configurazione ultra-minima
        config = PipelineConfig(
            execution_mode=ExecutionMode.DRY_RUN,
            enable_self_check=False,
            enable_sandbox=False,
            enable_adaptive_patching=False,
            enable_learning=False,
            enable_detailed_logging=True
        )
        
        print("⚙️ Inizializzazione pipeline...")
        pipeline = NGIntegration(config)
        
        print("🔄 Esecuzione pipeline...")
        source_code = "print('Hello NEUROGLYPH')"
        result = pipeline.run(source_code)
        
        print(f"✅ Pipeline completata!")
        print(f"   - Success: {result.success}")
        print(f"   - Stage: {result.current_stage.value}")
        print(f"   - Error: {result.error_message or 'None'}")
        print(f"   - Trace steps: {len(result.execution_trace)}")
        
        for i, trace in enumerate(result.execution_trace, 1):
            print(f"     {i}. {trace}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pipeline_with_memory():
    """Test pipeline con memory abilitata."""
    print("\n🧪 Test Pipeline con Memory")
    print("-" * 40)
    
    try:
        from neuroglyph.cognitive import NGIntegration
        from neuroglyph.cognitive.integration import PipelineConfig, ExecutionMode
        
        # Configurazione con memory
        config = PipelineConfig(
            execution_mode=ExecutionMode.DRY_RUN,
            enable_self_check=False,
            enable_sandbox=False,
            enable_adaptive_patching=False,
            enable_learning=False,
            enable_detailed_logging=True
        )
        
        print("⚙️ Inizializzazione pipeline...")
        pipeline = NGIntegration(config)
        
        print("🔄 Esecuzione pipeline...")
        source_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

result = fibonacci(5)
print(f"Fibonacci(5) = {result}")
"""
        result = pipeline.run(source_code)
        
        print(f"✅ Pipeline completata!")
        print(f"   - Success: {result.success}")
        print(f"   - Stage: {result.current_stage.value}")
        print(f"   - Final output: {result.final_output}")
        
        if result.metrics:
            print(f"   - Timing: {result.metrics.stage_timings}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pipeline_statistics():
    """Test statistiche pipeline."""
    print("\n🧪 Test Statistiche Pipeline")
    print("-" * 40)
    
    try:
        from neuroglyph.cognitive import NGIntegration
        
        pipeline = NGIntegration()
        stats = pipeline.get_pipeline_statistics()
        
        print("📊 Statistiche Pipeline:")
        for key, value in stats.items():
            print(f"   - {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_learner_standalone():
    """Test del learner standalone."""
    print("\n🧪 Test Learner Standalone")
    print("-" * 40)
    
    try:
        from neuroglyph.cognitive import NGLearner
        from neuroglyph.cognitive.learner_structures import LearnerConfig, LearningMode
        from neuroglyph.cognitive.patcher_structures import PatchResult, PatchStatus
        
        # Configurazione learner
        config = LearnerConfig(
            learning_mode=LearningMode.BALANCED,
            save_patterns=False
        )
        
        learner = NGLearner(config)
        print("✅ Learner inizializzato")
        
        # Simula alcuni patch results
        patch_results = [
            PatchResult(
                patch_id="test_1",
                error_id="error_1",
                success=True,
                status=PatchStatus.SUCCESSFUL,
                metadata={"error_type": "NameError", "patch_strategy": "variable_initialization"}
            ),
            PatchResult(
                patch_id="test_2",
                error_id="error_2",
                success=True,
                status=PatchStatus.SUCCESSFUL,
                metadata={"error_type": "ImportError", "patch_strategy": "import_addition"}
            )
        ]
        
        # Test learning
        learning_result = learner.learn(patch_results)
        print(f"✅ Learning completato: {learning_result}")
        
        # Test statistiche
        stats = learner.get_learning_statistics()
        print(f"📊 Learning stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore learner: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Esegue tutti i test diretti."""
    print("🚀 NEUROGLYPH Direct Pipeline Tests")
    print("=" * 50)
    
    tests = [
        ("Pipeline Minima", test_pipeline_minimal),
        ("Pipeline con Memory", test_pipeline_with_memory),
        ("Statistiche Pipeline", test_pipeline_statistics),
        ("Learner Standalone", test_learner_standalone)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success, None))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False, str(e)))
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RISULTATI TEST DIRETTI")
    print("=" * 50)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for test_name, success, error in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"      Error: {error}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("\n🎉 Tutti i test diretti sono passati!")
        print("\n🚀 NEUROGLYPH Pipeline è operativa!")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test falliti.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
