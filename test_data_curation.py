#!/usr/bin/env python3
"""
Test per NEUROGLYPH Data Curation Pipeline
Fase 1 - Validazione Data Engineering & Curation
"""

import tempfile
import json
from pathlib import Path
import sys
import os

# Aggiungi il path del progetto
sys.path.insert(0, str(Path(__file__).parent))

# Implementazione semplice di jsonlines per evitare dipendenze esterne
class SimpleJSONLines:
    @staticmethod
    def open(file_path, mode='r'):
        return SimpleJSONLinesFile(file_path, mode)

class SimpleJSONLinesFile:
    def __init__(self, file_path, mode='r'):
        self.file_path = file_path
        self.mode = mode
        self.file = None

    def __enter__(self):
        self.file = open(self.file_path, self.mode, encoding='utf-8')
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.file:
            self.file.close()

    def __iter__(self):
        for line in self.file:
            line = line.strip()
            if line:
                yield json.loads(line)

    def write(self, obj):
        self.file.write(json.dumps(obj) + '\n')

# Monkey patch per compatibilità
import sys
sys.modules['jsonlines'] = SimpleJSONLines

from scripts.data_curation import DataCurator, QualityMetrics
from neuroglyph.evaluation.benchmarks.logiqa_loader import LogiQALoader
from neuroglyph.evaluation.benchmarks.gsm8k_loader import GSM8KLoader


def test_data_curator_initialization():
    """Test inizializzazione DataCurator."""
    print("\n🧪 Test DataCurator Initialization")
    print("-" * 40)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            curator = DataCurator(temp_dir)
            
            # Verifica directory create
            assert curator.data_dir.exists()
            assert curator.raw_dir.exists()
            assert curator.processed_dir.exists()
            assert curator.symbolic_dir.exists()
            assert curator.splits_dir.exists()
            assert curator.reports_dir.exists()
            
            # Verifica dataset supportati
            assert 'logiqa' in curator.datasets
            assert 'gsm8k' in curator.datasets
            
            print("✅ DataCurator inizializzato correttamente")
            print(f"   - Directory create: {len([d for d in [curator.raw_dir, curator.processed_dir, curator.symbolic_dir, curator.splits_dir, curator.reports_dir] if d.exists()])}/5")
            print(f"   - Dataset supportati: {len(curator.datasets)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Errore inizializzazione: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_mock_dataset_processing():
    """Test processing di dataset mock."""
    print("\n🧪 Test Mock Dataset Processing")
    print("-" * 40)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            curator = DataCurator(temp_dir)
            
            # Test processing LogiQA (mock)
            success_logiqa = curator.process_dataset('logiqa')
            print(f"{'✅' if success_logiqa else '❌'} LogiQA processing: {success_logiqa}")
            
            # Test processing GSM8K (mock)
            success_gsm8k = curator.process_dataset('gsm8k')
            print(f"{'✅' if success_gsm8k else '❌'} GSM8K processing: {success_gsm8k}")
            
            # Verifica file processati
            logiqa_processed = curator.processed_dir / "logiqa_processed.jsonl"
            gsm8k_processed = curator.processed_dir / "gsm8k_processed.jsonl"
            
            logiqa_exists = logiqa_processed.exists()
            gsm8k_exists = gsm8k_processed.exists()
            
            print(f"{'✅' if logiqa_exists else '❌'} LogiQA file processato: {logiqa_exists}")
            print(f"{'✅' if gsm8k_exists else '❌'} GSM8K file processato: {gsm8k_exists}")
            
            # Verifica contenuto
            if logiqa_exists:
                with SimpleJSONLines.open(logiqa_processed) as reader:
                    logiqa_samples = list(reader)
                print(f"   - LogiQA samples: {len(logiqa_samples)}")

            if gsm8k_exists:
                with SimpleJSONLines.open(gsm8k_processed) as reader:
                    gsm8k_samples = list(reader)
                print(f"   - GSM8K samples: {len(gsm8k_samples)}")
            
            return success_logiqa and success_gsm8k and logiqa_exists and gsm8k_exists
            
    except Exception as e:
        print(f"❌ Errore processing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_quality_validation():
    """Test validazione qualità dataset."""
    print("\n🧪 Test Quality Validation")
    print("-" * 40)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            curator = DataCurator(temp_dir)
            
            # Processa dataset per avere dati da validare
            curator.process_dataset('logiqa')
            curator.process_dataset('gsm8k')
            
            # Test quality validation
            logiqa_metrics = curator.validate_quality('logiqa')
            gsm8k_metrics = curator.validate_quality('gsm8k')
            
            print(f"📊 LogiQA Quality Metrics:")
            print(f"   - Completeness: {logiqa_metrics.completeness:.2%}")
            print(f"   - Consistency: {logiqa_metrics.consistency:.2%}")
            print(f"   - Validity: {logiqa_metrics.validity:.2%}")
            print(f"   - Uniqueness: {logiqa_metrics.uniqueness:.2%}")
            
            print(f"📊 GSM8K Quality Metrics:")
            print(f"   - Completeness: {gsm8k_metrics.completeness:.2%}")
            print(f"   - Consistency: {gsm8k_metrics.consistency:.2%}")
            print(f"   - Validity: {gsm8k_metrics.validity:.2%}")
            print(f"   - Uniqueness: {gsm8k_metrics.uniqueness:.2%}")
            
            # Verifica quality reports
            logiqa_report = curator.reports_dir / "logiqa_quality_report.json"
            gsm8k_report = curator.reports_dir / "gsm8k_quality_report.json"
            
            reports_exist = logiqa_report.exists() and gsm8k_report.exists()
            print(f"{'✅' if reports_exist else '❌'} Quality reports generati: {reports_exist}")
            
            # Quality gates
            quality_threshold = 0.8
            logiqa_pass = (logiqa_metrics.completeness >= quality_threshold and 
                          logiqa_metrics.uniqueness >= quality_threshold)
            gsm8k_pass = (gsm8k_metrics.completeness >= quality_threshold and 
                         gsm8k_metrics.uniqueness >= quality_threshold)
            
            print(f"{'✅' if logiqa_pass else '❌'} LogiQA quality gates: {logiqa_pass}")
            print(f"{'✅' if gsm8k_pass else '❌'} GSM8K quality gates: {gsm8k_pass}")
            
            return logiqa_pass and gsm8k_pass and reports_exist
            
    except Exception as e:
        print(f"❌ Errore quality validation: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_benchmark_loaders_integration():
    """Test integrazione con benchmark loaders."""
    print("\n🧪 Test Benchmark Loaders Integration")
    print("-" * 40)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            curator = DataCurator(temp_dir)
            
            # Processa dataset
            curator.process_dataset('logiqa')
            curator.process_dataset('gsm8k')
            
            # Test LogiQA loader
            logiqa_loader = LogiQALoader()
            logiqa_processed = curator.processed_dir / "logiqa_processed.jsonl"
            
            # Usa mock samples se file non esiste
            logiqa_samples = logiqa_loader.load_samples(str(logiqa_processed), max_samples=5)
            print(f"✅ LogiQA loader: {len(logiqa_samples)} samples caricati")
            
            # Test GSM8K loader
            gsm8k_loader = GSM8KLoader()
            gsm8k_processed = curator.processed_dir / "gsm8k_processed.jsonl"
            
            gsm8k_samples = gsm8k_loader.load_samples(str(gsm8k_processed), max_samples=5)
            print(f"✅ GSM8K loader: {len(gsm8k_samples)} samples caricati")
            
            # Test format prompt
            if logiqa_samples:
                logiqa_prompt = logiqa_loader.format_prompt(logiqa_samples[0])
                print(f"✅ LogiQA prompt formatting: {len(logiqa_prompt)} chars")
            
            if gsm8k_samples:
                gsm8k_prompt = gsm8k_loader.format_prompt(gsm8k_samples[0])
                print(f"✅ GSM8K prompt formatting: {len(gsm8k_prompt)} chars")
            
            # Test response parsing
            if logiqa_samples:
                logiqa_response = logiqa_loader.parse_response("The answer is B", logiqa_samples[0])
                print(f"✅ LogiQA response parsing: '{logiqa_response}'")
            
            if gsm8k_samples:
                gsm8k_response = gsm8k_loader.parse_response("The answer is 42", gsm8k_samples[0])
                print(f"✅ GSM8K response parsing: {gsm8k_response}")
            
            success = len(logiqa_samples) > 0 and len(gsm8k_samples) > 0
            return success
            
    except Exception as e:
        print(f"❌ Errore benchmark loaders: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_end_to_end_pipeline():
    """Test pipeline end-to-end completa."""
    print("\n🧪 Test End-to-End Pipeline")
    print("-" * 40)
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 Working directory: {temp_dir}")
            
            # 1. Inizializzazione
            curator = DataCurator(temp_dir)
            print("✅ Step 1: DataCurator inizializzato")
            
            # 2. Processing (mock data)
            datasets_processed = []
            for dataset_name in ['logiqa', 'gsm8k']:
                if curator.process_dataset(dataset_name):
                    datasets_processed.append(dataset_name)
            
            print(f"✅ Step 2: {len(datasets_processed)} dataset processati")
            
            # 3. Quality validation
            quality_reports = []
            for dataset_name in datasets_processed:
                metrics = curator.validate_quality(dataset_name)
                if metrics.completeness > 0.8:
                    quality_reports.append(dataset_name)
            
            print(f"✅ Step 3: {len(quality_reports)} quality reports generati")
            
            # 4. Loader integration
            loaders_tested = 0
            if 'logiqa' in datasets_processed:
                loader = LogiQALoader()
                samples = loader.load_samples("non_existent.jsonl", max_samples=3)
                if len(samples) > 0:
                    loaders_tested += 1
            
            if 'gsm8k' in datasets_processed:
                loader = GSM8KLoader()
                samples = loader.load_samples("non_existent.jsonl", max_samples=3)
                if len(samples) > 0:
                    loaders_tested += 1
            
            print(f"✅ Step 4: {loaders_tested} loaders testati")
            
            # 5. Final validation
            pipeline_success = (
                len(datasets_processed) >= 2 and
                len(quality_reports) >= 2 and
                loaders_tested >= 2
            )
            
            print(f"\n🎯 Pipeline Results:")
            print(f"   - Dataset processati: {len(datasets_processed)}/2")
            print(f"   - Quality reports: {len(quality_reports)}/2")
            print(f"   - Loaders testati: {loaders_tested}/2")
            print(f"   - Success: {'✅' if pipeline_success else '❌'}")
            
            return pipeline_success
            
    except Exception as e:
        print(f"❌ Errore pipeline end-to-end: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Esegue tutti i test di data curation."""
    print("🗃️ NEUROGLYPH DATA CURATION TESTS")
    print("=" * 60)
    
    tests = [
        ("DataCurator Initialization", test_data_curator_initialization),
        ("Mock Dataset Processing", test_mock_dataset_processing),
        ("Quality Validation", test_quality_validation),
        ("Benchmark Loaders Integration", test_benchmark_loaders_integration),
        ("End-to-End Pipeline", test_end_to_end_pipeline)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 RUNNING: {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{'✅ PASSED' if result else '❌ FAILED'}: {test_name}")
        except Exception as e:
            print(f"\n❌ ERROR in {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 FINAL RESULTS")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n🏆 SUMMARY: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 ALL DATA CURATION TESTS PASSED!")
        print("✅ Ready for Phase 2: Modular Design & Unit Testing")
    else:
        print("⚠️ Some tests failed - review and fix before proceeding")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
