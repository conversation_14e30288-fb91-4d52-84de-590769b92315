"""
File di esempio per testare ngcompress CLI
"""

def process_data(items, threshold=0.5):
    """Processa una lista di dati con soglia."""
    results = []
    with open('data.txt', 'r') as f:
        lines = f.readlines()
    for item in items:
        try:
            if isinstance(item, dict):
                value = item.get('value', 0)
                if value > threshold:
                    message = f"Processing {value:.2f}: {(lambda x: 'high' if x > 0.8 else 'medium')(value)}"
                    results.append(message)
            elif isinstance(item, (int, float)):
                if item > threshold:
                    results.append(f'Number: {item}')
        except Exception as e:
            print(f'Error processing {item}: {e}')
            continue
    return results

class DataProcessor:
    """Classe per processare dati."""

    def __init__(self, threshold: float=0.5):
        self.threshold = threshold
        self.results = []

    def process(self, data):
        """Processa i dati."""
        return process_data(data, self.threshold)

    @property
    def count(self):
        """Numero di risultati."""
        return len(self.results)
if __name__ == '__main__':
    processor = DataProcessor(threshold=0.3)
    test_data = [{'value': 0.8}, {'value': 0.2}, 0.9, 0.1]
    results = processor.process(test_data)
    print(f'Processed {len(results)} items')