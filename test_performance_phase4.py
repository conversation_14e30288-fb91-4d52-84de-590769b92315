#!/usr/bin/env python3
"""
FASE 4.0.2 - Performance Testing per NEUROGLYPH Patch Engine

Test per verificare che il target <10ms patch generation sia raggiunto.
"""

import time
import statistics
from typing import List, Dict, Any

from neuroglyph.cognitive.patch_generator import <PERSON><PERSON>enerator
from neuroglyph.cognitive.patcher_structures import ErrorAnalysis


def create_test_error_analyses() -> List[ErrorAnalysis]:
    """Crea test cases per performance testing."""
    test_cases = [
        # Import errors
        ErrorAnalysis(
            error_id="perf_test_1",
            error_type="import",
            error_message="ModuleNotFoundError: No module named 'numpy'",
            error_pattern="import_error",
            error_context="import numpy as np",
            severity="high"
        ),
        
        # Syntax errors
        ErrorAnalysis(
            error_id="perf_test_2", 
            error_type="syntax",
            error_message="SyntaxError: invalid syntax",
            error_pattern="syntax_invalid",
            error_context="if x == 5",
            severity="high"
        ),
        
        # Runtime errors
        ErrorAnalysis(
            error_id="perf_test_3",
            error_type="runtime", 
            error_message="NameError: name 'undefined_var' is not defined",
            error_pattern="undefined_variable",
            error_context="print(undefined_var)",
            severity="medium"
        ),
        
        # Attribute errors
        ErrorAnalysis(
            error_id="perf_test_4",
            error_type="runtime",
            error_message="AttributeError: 'str' object has no attribute 'append'",
            error_pattern="undefined_attribute", 
            error_context="my_string.append('test')",
            severity="medium"
        ),
        
        # Index errors
        ErrorAnalysis(
            error_id="perf_test_5",
            error_type="runtime",
            error_message="IndexError: list index out of range",
            error_pattern="index_error",
            error_context="my_list[10]",
            severity="medium"
        ),
        
        # Type errors
        ErrorAnalysis(
            error_id="perf_test_6",
            error_type="runtime",
            error_message="TypeError: unsupported operand type(s) for +: 'int' and 'str'",
            error_pattern="type_mismatch",
            error_context="result = 5 + 'hello'",
            severity="medium"
        ),
        
        # Performance errors
        ErrorAnalysis(
            error_id="perf_test_7",
            error_type="performance",
            error_message="TimeoutError: Operation timed out after 30 seconds",
            error_pattern="timeout_error",
            error_context="long_running_function()",
            severity="low"
        ),
        
        # Security errors
        ErrorAnalysis(
            error_id="perf_test_8",
            error_type="security",
            error_message="SecurityError: Potential SQL injection detected",
            error_pattern="injection_attack",
            error_context="query = f'SELECT * FROM users WHERE id = {user_id}'",
            severity="critical"
        ),
        
        # Logic errors
        ErrorAnalysis(
            error_id="perf_test_9",
            error_type="logic",
            error_message="LogicalError: Contradiction detected in premises",
            error_pattern="logical_contradiction",
            error_context="assert x > 5 and x < 3",
            severity="high"
        ),
        
        # Default pattern
        ErrorAnalysis(
            error_id="perf_test_10",
            error_type="unknown",
            error_message="UnknownError: Something went wrong",
            error_pattern="unknown_pattern",
            error_context="mysterious_function()",
            severity="medium"
        )
    ]
    
    return test_cases


def benchmark_patch_generation(generator: PatchGenerator, test_cases: List[ErrorAnalysis], 
                              iterations: int = 100) -> Dict[str, Any]:
    """
    Benchmark patch generation performance.
    
    Args:
        generator: PatchGenerator instance
        test_cases: Lista di test cases
        iterations: Numero di iterazioni per test
        
    Returns:
        Risultati benchmark
    """
    print(f"🚀 Starting performance benchmark with {iterations} iterations...")
    print(f"📊 Testing {len(test_cases)} different error patterns")
    
    all_latencies = []
    pattern_latencies = {}
    
    for i in range(iterations):
        if i % 10 == 0:
            print(f"   Progress: {i}/{iterations} iterations completed")
        
        for test_case in test_cases:
            # Reset state per ogni test
            generator.reset_state()
            
            # Misura latency
            start_time = time.perf_counter()
            
            # Usa metodo ottimizzato se disponibile
            if hasattr(generator, 'generate_patches_optimized'):
                patches = generator.generate_patches_optimized(test_case)
            else:
                patches = generator.generate_patches(test_case)
            
            end_time = time.perf_counter()
            
            latency_ms = (end_time - start_time) * 1000
            all_latencies.append(latency_ms)
            
            # Raggruppa per pattern
            pattern = test_case.error_pattern
            if pattern not in pattern_latencies:
                pattern_latencies[pattern] = []
            pattern_latencies[pattern].append(latency_ms)
    
    # Calcola statistiche
    results = {
        "total_tests": len(all_latencies),
        "avg_latency_ms": statistics.mean(all_latencies),
        "median_latency_ms": statistics.median(all_latencies),
        "p95_latency_ms": statistics.quantiles(all_latencies, n=20)[18],  # 95th percentile
        "p99_latency_ms": statistics.quantiles(all_latencies, n=100)[98],  # 99th percentile
        "min_latency_ms": min(all_latencies),
        "max_latency_ms": max(all_latencies),
        "std_dev_ms": statistics.stdev(all_latencies),
        "target_met": statistics.mean(all_latencies) < 10.0,
        "pattern_breakdown": {}
    }
    
    # Breakdown per pattern
    for pattern, latencies in pattern_latencies.items():
        results["pattern_breakdown"][pattern] = {
            "avg_ms": statistics.mean(latencies),
            "median_ms": statistics.median(latencies),
            "count": len(latencies)
        }
    
    return results


def print_performance_report(results: Dict[str, Any], generator: PatchGenerator):
    """Stampa report performance dettagliato."""
    print("\n" + "="*80)
    print("🎯 FASE 4.0.2 - PERFORMANCE BENCHMARK RESULTS")
    print("="*80)
    
    # Risultati generali
    print(f"\n📊 OVERALL PERFORMANCE:")
    print(f"   Total tests: {results['total_tests']:,}")
    print(f"   Average latency: {results['avg_latency_ms']:.2f}ms")
    print(f"   Median latency: {results['median_latency_ms']:.2f}ms")
    print(f"   P95 latency: {results['p95_latency_ms']:.2f}ms")
    print(f"   P99 latency: {results['p99_latency_ms']:.2f}ms")
    print(f"   Min latency: {results['min_latency_ms']:.2f}ms")
    print(f"   Max latency: {results['max_latency_ms']:.2f}ms")
    print(f"   Std deviation: {results['std_dev_ms']:.2f}ms")
    
    # Target check
    target_status = "✅ TARGET MET" if results['target_met'] else "❌ TARGET MISSED"
    print(f"\n🎯 TARGET (<10ms): {target_status}")
    print(f"   Current avg: {results['avg_latency_ms']:.2f}ms")
    print(f"   Target: <10.00ms")
    
    # Performance grade
    avg_latency = results['avg_latency_ms']
    if avg_latency < 5.0:
        grade = "A+ (Excellent)"
    elif avg_latency < 10.0:
        grade = "A (Target Met)"
    elif avg_latency < 20.0:
        grade = "B (Good)"
    elif avg_latency < 50.0:
        grade = "C (Acceptable)"
    else:
        grade = "D (Needs Improvement)"
    
    print(f"   Performance Grade: {grade}")
    
    # Breakdown per pattern
    print(f"\n📋 PATTERN BREAKDOWN:")
    for pattern, stats in results['pattern_breakdown'].items():
        status = "✅" if stats['avg_ms'] < 10.0 else "❌"
        print(f"   {status} {pattern}: {stats['avg_ms']:.2f}ms avg ({stats['count']} tests)")
    
    # Metriche optimizer se disponibili
    if hasattr(generator, 'get_performance_metrics'):
        optimizer_metrics = generator.get_performance_metrics()
        print(f"\n🔧 OPTIMIZER METRICS:")
        print(f"   Cache hit rate: {optimizer_metrics.get('cache_hit_rate', 0):.1f}%")
        print(f"   Cache size: {optimizer_metrics.get('cache_size', 0)}")
        print(f"   Performance grade: {optimizer_metrics.get('performance_grade', 'N/A')}")


def main():
    """Main performance testing function."""
    print("🚀 NEUROGLYPH Patch Engine - Performance Testing")
    print("="*60)
    
    # Crea generator
    print("🔧 Initializing PatchGenerator...")
    generator = PatchGenerator()
    
    # Crea test cases
    print("📋 Creating test cases...")
    test_cases = create_test_error_analyses()
    
    # Esegui benchmark
    print("⏱️ Running performance benchmark...")
    results = benchmark_patch_generation(generator, test_cases, iterations=50)
    
    # Stampa report
    print_performance_report(results, generator)
    
    # Conclusioni
    print(f"\n🎉 PERFORMANCE TEST COMPLETED!")
    if results['target_met']:
        print("✅ Target <10ms ACHIEVED! Ready for production.")
    else:
        print("❌ Target <10ms NOT MET. Further optimization needed.")
        print(f"   Current: {results['avg_latency_ms']:.2f}ms")
        print(f"   Improvement needed: {results['avg_latency_ms'] - 10.0:.2f}ms")


if __name__ == "__main__":
    main()
