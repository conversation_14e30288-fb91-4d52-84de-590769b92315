#!/usr/bin/env python3
"""
Test per NEUROGLYPH Context Prioritizer
Verifica la classificazione di urgenza, rischio e dominio
"""

from neuroglyph.cognitive.context_prioritizer import NGContextPrioritizer
from neuroglyph.cognitive.data_structures import ParsedPrompt, DomainType


def create_test_prompt(text: str, tokens: list = None, symbols: list = None, intents: list = None) -> ParsedPrompt:
    """Crea un ParsedPrompt di test."""
    return ParsedPrompt(
        original_text=text,
        tokens=tokens or text.split(),
        segments=[],
        intents=intents or [],
        symbols=symbols or [],
        metadata={}
    )


def test_urgency_classification():
    """Test classificazione urgenza."""
    print("🎯 TEST URGENCY CLASSIFICATION")
    
    prioritizer = NGContextPrioritizer()
    
    test_cases = [
        # Urgenza critica
        ("URGENT: Fix critical bug immediately!", 0.9, "critical urgency"),
        ("Emergency! Server is down ASAP!", 0.9, "emergency keywords"),
        ("Critical error needs immediate fix!!!", 0.9, "multiple urgency indicators"),
        
        # Urgenza alta
        ("Quick fix needed for this bug", 0.7, "high urgency"),
        ("Help! I'm stuck with this error", 0.6, "help seeking"),
        ("Fast solution required now", 0.7, "time pressure"),
        
        # Urgenza media
        ("Can you solve this problem?", 0.5, "medium urgency"),
        ("Debug this issue when possible", 0.4, "problem solving"),
        
        # Urgenza bassa
        ("Optimize this function later", 0.2, "low urgency"),
        ("Improve code quality", 0.1, "minimal urgency"),
        ("General question about Python", 0.0, "no urgency"),
    ]
    
    results = []
    for text, expected_min, description in test_cases:
        prompt = create_test_prompt(text)
        priority = prioritizer.prioritize(prompt)
        
        success = priority.urgency >= expected_min
        results.append(success)
        
        print(f"   {'✅' if success else '❌'} {description}")
        print(f"      Text: '{text}'")
        print(f"      Urgency: {priority.urgency:.3f} (expected: ≥{expected_min})")
        print(f"      Level: {priority.urgency_level.name}")
        print(f"      Reasoning: {priority.reasoning}")
        print()
    
    success_rate = sum(results) / len(results)
    print(f"📊 URGENCY CLASSIFICATION: {success_rate:.1%} success rate")
    return success_rate


def test_risk_assessment():
    """Test valutazione rischio."""
    print("\n🎯 TEST RISK ASSESSMENT")
    
    prioritizer = NGContextPrioritizer()
    
    test_cases = [
        # Rischio alto
        ("Delete all files in directory", 0.8, "destructive operation"),
        ("Execute sudo command with admin privileges", 0.8, "elevated privileges"),
        ("Run eval() on user input", 0.8, "dangerous execution"),
        ("Access password database", 0.7, "sensitive data"),
        
        # Rischio medio
        ("Connect to network socket", 0.4, "network operation"),
        ("Write file to disk", 0.3, "file operation"),
        ("Query database table", 0.4, "database operation"),
        
        # Rischio basso
        ("Read configuration file", 0.2, "safe file read"),
        ("Calculate mathematical formula", 0.0, "safe calculation"),
        ("Display text message", 0.0, "safe display"),
    ]
    
    results = []
    for text, expected_min, description in test_cases:
        prompt = create_test_prompt(text)
        priority = prioritizer.prioritize(prompt)
        
        success = priority.risk >= expected_min
        results.append(success)
        
        print(f"   {'✅' if success else '❌'} {description}")
        print(f"      Text: '{text}'")
        print(f"      Risk: {priority.risk:.3f} (expected: ≥{expected_min})")
        print(f"      Level: {priority.risk_level.name}")
        print()
    
    success_rate = sum(results) / len(results)
    print(f"📊 RISK ASSESSMENT: {success_rate:.1%} success rate")
    return success_rate


def test_domain_mapping():
    """Test mappatura domini."""
    print("\n🎯 TEST DOMAIN MAPPING")
    
    prioritizer = NGContextPrioritizer()
    
    test_cases = [
        # Domini specifici
        ("Create a Python function with classes", DomainType.CODE, "code domain"),
        ("Analyze CSV data and create visualization", DomainType.DATA, "data domain"),
        ("Configure server and install services", DomainType.SYSTEM, "system domain"),
        ("Implement security authentication", DomainType.SECURITY, "security domain"),
        ("Make HTTP API request to server", DomainType.NETWORK, "network domain"),
        ("Calculate matrix multiplication", DomainType.MATH, "math domain"),
        
        # Dominio generale
        ("Hello, how are you?", DomainType.GENERAL, "general domain"),
        ("What's the weather like?", DomainType.GENERAL, "general question"),
    ]
    
    results = []
    for text, expected_domain, description in test_cases:
        prompt = create_test_prompt(text)
        priority = prioritizer.prioritize(prompt)
        
        success = priority.domain == expected_domain
        results.append(success)
        
        print(f"   {'✅' if success else '❌'} {description}")
        print(f"      Text: '{text}'")
        print(f"      Domain: {priority.domain.value} (expected: {expected_domain.value})")
        print()
    
    success_rate = sum(results) / len(results)
    print(f"📊 DOMAIN MAPPING: {success_rate:.1%} success rate")
    return success_rate


def test_symbol_analysis():
    """Test analisi simboli."""
    print("\n🎯 TEST SYMBOL ANALYSIS")
    
    prioritizer = NGContextPrioritizer()
    
    test_cases = [
        # Simboli con urgenza/rischio
        ("Fix this function", ["⟨⟩§func_0§"], 0.3, "function symbol urgency"),
        ("Handle errors properly", ["⟐§try_0§"], 0.6, "try/except symbol risk"),
        ("Process file data", ["⎋§with_0§"], 0.4, "with statement symbol risk"),
        ("Format output string", ["⌦§fstr_0§"], 0.0, "f-string symbol safe"),
        
        # Combinazioni simboli
        ("Complex error handling", ["⟨⟩§func_0§", "⟐§try_1§"], 0.6, "multiple symbols"),
    ]
    
    results = []
    for text, symbols, expected_min_score, description in test_cases:
        prompt = create_test_prompt(text, symbols=symbols)
        priority = prioritizer.prioritize(prompt)
        
        # Verifica che almeno urgenza o rischio sia >= expected
        success = max(priority.urgency, priority.risk) >= expected_min_score
        results.append(success)
        
        print(f"   {'✅' if success else '❌'} {description}")
        print(f"      Symbols: {symbols}")
        print(f"      Urgency: {priority.urgency:.3f}, Risk: {priority.risk:.3f}")
        print(f"      Max Score: {max(priority.urgency, priority.risk):.3f} (expected: ≥{expected_min_score})")
        print()
    
    success_rate = sum(results) / len(results)
    print(f"📊 SYMBOL ANALYSIS: {success_rate:.1%} success rate")
    return success_rate


def test_priority_vector_properties():
    """Test proprietà PriorityVector."""
    print("\n🎯 TEST PRIORITY VECTOR PROPERTIES")
    
    prioritizer = NGContextPrioritizer()
    
    # Test con prompt critico
    critical_prompt = create_test_prompt(
        "URGENT: Critical security vulnerability needs immediate fix!",
        symbols=["⟐§try_0§", "⟨⟩§func_1§"]
    )
    
    priority = prioritizer.prioritize(critical_prompt)
    
    print(f"📊 CRITICAL PROMPT ANALYSIS:")
    print(f"   Text: '{critical_prompt.original_text}'")
    print(f"   Urgency: {priority.urgency:.3f} ({priority.urgency_level.name})")
    print(f"   Risk: {priority.risk:.3f} ({priority.risk_level.name})")
    print(f"   Domain: {priority.domain.value}")
    print(f"   Confidence: {priority.confidence:.3f}")
    print(f"   Priority Score: {priority.priority_score:.3f}")
    print(f"   Reasoning: {priority.reasoning}")
    print(f"   Metadata: {priority.metadata}")
    
    # Verifica proprietà
    tests = [
        (0.0 <= priority.urgency <= 1.0, "urgency in valid range"),
        (0.0 <= priority.risk <= 1.0, "risk in valid range"),
        (0.0 <= priority.confidence <= 1.0, "confidence in valid range"),
        (0.0 <= priority.priority_score <= 1.0, "priority score in valid range"),
        (priority.urgency >= 0.8, "high urgency detected"),
        (priority.domain in DomainType, "valid domain type"),
        (len(priority.reasoning) > 0, "reasoning provided"),
        ("processing_time" in priority.metadata, "metadata contains processing time"),
    ]
    
    results = []
    print(f"\n📋 PROPERTY VALIDATION:")
    for test_result, description in tests:
        results.append(test_result)
        print(f"   {'✅' if test_result else '❌'} {description}")
    
    success_rate = sum(results) / len(results)
    print(f"\n📊 PROPERTY VALIDATION: {success_rate:.1%} success rate")
    return success_rate


def test_performance():
    """Test performance del prioritizer."""
    print("\n🎯 TEST PERFORMANCE")
    
    prioritizer = NGContextPrioritizer()
    
    import time
    
    # Test con prompt di diverse lunghezze
    test_prompts = [
        "Quick fix",  # Corto
        "Analyze this data processing function and optimize performance",  # Medio
        "Create a comprehensive security system with authentication, authorization, encryption, and audit logging for enterprise application" * 3,  # Lungo
    ]
    
    total_time = 0
    for i, text in enumerate(test_prompts):
        prompt = create_test_prompt(text)
        
        start_time = time.time()
        priority = prioritizer.prioritize(prompt)
        end_time = time.time()
        
        processing_time = end_time - start_time
        total_time += processing_time
        
        print(f"   📊 Prompt {i+1} ({len(text)} chars):")
        print(f"      Processing time: {processing_time*1000:.2f}ms")
        print(f"      Priority score: {priority.priority_score:.3f}")
        print(f"      Confidence: {priority.confidence:.3f}")
    
    avg_time = total_time / len(test_prompts)
    print(f"\n📊 PERFORMANCE SUMMARY:")
    print(f"   Average processing time: {avg_time*1000:.2f}ms")
    print(f"   Total time: {total_time*1000:.2f}ms")
    
    # Performance target: <10ms per prompt
    performance_ok = avg_time < 0.01
    print(f"   Performance target (<10ms): {'✅' if performance_ok else '❌'}")
    
    return performance_ok


if __name__ == "__main__":
    print("🎯 NEUROGLYPH CONTEXT PRIORITIZER TEST")
    print("=" * 80)
    
    # Esegui tutti i test
    urgency_success = test_urgency_classification()
    risk_success = test_risk_assessment()
    domain_success = test_domain_mapping()
    symbol_success = test_symbol_analysis()
    property_success = test_priority_vector_properties()
    performance_ok = test_performance()
    
    print("\n" + "=" * 80)
    print("🎯 RISULTATI FINALI CONTEXT PRIORITIZER:")
    print(f"   Urgency Classification: {urgency_success:.1%}")
    print(f"   Risk Assessment: {risk_success:.1%}")
    print(f"   Domain Mapping: {domain_success:.1%}")
    print(f"   Symbol Analysis: {symbol_success:.1%}")
    print(f"   Property Validation: {property_success:.1%}")
    print(f"   Performance: {'✅' if performance_ok else '❌'}")
    
    # Calcola success rate complessivo
    overall_success = (urgency_success + risk_success + domain_success + 
                      symbol_success + property_success) / 5
    
    print(f"\n🎯 OVERALL SUCCESS RATE: {overall_success:.1%}")
    
    if overall_success >= 0.8 and performance_ok:
        print("✅ CONTEXT PRIORITIZER TEST: SUCCESSO!")
        print("   🎯 Classificazione accurata ≥80%")
        print("   ⚡ Performance <10ms per prompt")
        print("   🧠 Primo modulo cognitivo funzionante!")
    else:
        print("❌ CONTEXT PRIORITIZER TEST: Miglioramenti necessari")
        if overall_success < 0.8:
            print(f"   - Accuratezza: {overall_success:.1%} (target: ≥80%)")
        if not performance_ok:
            print("   - Performance: >10ms (target: <10ms)")
    
    print(f"\n🚀 CONCLUSIONE:")
    if overall_success >= 0.8:
        print("   🎉 NG_CONTEXT_PRIORITIZER è PRONTO!")
        print("   🎯 Primo modulo cognitivo implementato con successo")
        print("   🧠 NEUROGLYPH cognitive pipeline iniziata")
        print("   ✅ Prossimo step: NG_MEMORY implementation")
    else:
        print("   🔧 Ottimizzazioni necessarie prima di procedere")
        print("   📊 Analizzare casi di fallimento")
        print("   🎯 Migliorare pattern recognition")
