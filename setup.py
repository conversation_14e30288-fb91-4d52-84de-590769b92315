#!/usr/bin/env python3
"""
Setup script per NEUROGLYPH
Installa il package e il CLI tool ngcompress
"""

from setuptools import setup, find_packages
import os

# Leggi README per long description
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "NEUROGLYPH - Compressione simbolica AST-based per Python"

# Leggi requirements
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="neuroglyph",
    version="2.0.0",
    description="NEUROGLYPH - Compressione simbolica AST-based per Python",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="NEUROGLYPH Team",
    author_email="<EMAIL>",
    url="https://github.com/neuroglyph/neuroglyph",
    
    # Package configuration
    packages=find_packages(),
    include_package_data=True,
    package_data={
        'neuroglyph': [
            'data/*.json',
            'data/*.txt',
            'templates/*.py',
        ],
    },
    
    # Dependencies
    install_requires=read_requirements(),
    python_requires=">=3.8",
    
    # Entry points per CLI
    entry_points={
        'console_scripts': [
            'ngcompress=neuroglyph.cli.ngcompress:main',
        ],
    },
    
    # Classificatori PyPI
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Software Development :: Compilers",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    
    # Keywords
    keywords=[
        "compression", "symbolic", "ast", "python", "code-analysis",
        "semantic-compression", "neuroglyph", "ai", "machine-learning"
    ],
    
    # Project URLs
    project_urls={
        "Bug Reports": "https://github.com/neuroglyph/neuroglyph/issues",
        "Source": "https://github.com/neuroglyph/neuroglyph",
        "Documentation": "https://neuroglyph.readthedocs.io/",
    },
    
    # Extras
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "docs": [
            "sphinx>=4.0",
            "sphinx-rtd-theme>=1.0",
            "myst-parser>=0.15",
        ],
        "performance": [
            "cython>=0.29",
            "numba>=0.50",
        ],
    },
    
    # Zip safe
    zip_safe=False,
)
