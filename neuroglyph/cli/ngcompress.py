#!/usr/bin/env python3
"""
NEUROGLYPH CLI Tool - ngcompress
Strumento da linea di comando per compressione/decompressione AST-based
"""

import argparse
import json
import sys
import time
from pathlib import Path
from typing import Optional, List, Dict, Any

from neuroglyph.core.encoder.encoder import NGEncoder


class NGCompressCLI:
    """CLI per NEUROGLYPH compression/decompression."""
    
    def __init__(self):
        self.encoder = NGEncoder()
        self.verbose = False
    
    def compress_file(self, 
                     input_path: Path, 
                     output_path: Optional[Path] = None,
                     registry_path: Optional[Path] = None,
                     level: int = 5,
                     verbose: bool = False) -> Dict[str, Any]:
        """
        Comprimi un file Python usando NEUROGLYPH AST-based.
        
        Args:
            input_path: File Python da comprimere
            output_path: File output compresso (default: input + .ngc)
            registry_path: File registry JSON (default: input + .ngreg)
            level: Livello compressione 1-5
            verbose: Output dettagliato
            
        Returns:
            Dizionario con statistiche compressione
        """
        self.verbose = verbose
        
        if self.verbose:
            print(f"🎯 NEUROGLYPH COMPRESS: {input_path}")
        
        # Leggi file sorgente
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
        except Exception as e:
            print(f"❌ Errore lettura {input_path}: {e}")
            return {"success": False, "error": str(e)}
        
        # Determina percorsi output
        if output_path is None:
            output_path = input_path.with_suffix('.ngc')
        if registry_path is None:
            registry_path = input_path.with_suffix('.ngreg')
        
        # Comprimi con NEUROGLYPH
        start_time = time.time()
        try:
            result = self.encoder.encode_ast_based(
                source_code=source_code,
                encoding_level=level
            )
            compress_time = time.time() - start_time
            
            if self.verbose:
                print(f"   ✅ Compressione completata in {compress_time:.3f}s")
                print(f"   📊 Simboli generati: {len(result.compressed_symbols)}")
                print(f"   📊 Compressione: {result.compression_result.compression_ratio:.1f}%")
        
        except Exception as e:
            print(f"❌ Errore compressione: {e}")
            return {"success": False, "error": str(e)}
        
        # Salva file compresso (solo simboli e metadati base)
        try:
            compressed_data = {
                "symbols": result.compressed_symbols,
                "original_length": len(source_code),
                "compressed_length": len(result.compressed_symbols),
                "compression_ratio": result.compression_result.compression_ratio,
                "encoding_level": level,
                "encoding_timestamp": result.metadata.get("encoding_timestamp"),
                "encoder_version": result.metadata.get("encoder_version"),
                "language": result.metadata.get("language"),
                "neuroglyph_version": "2.0_AST"
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(compressed_data, f, indent=2, ensure_ascii=False)
            
            if self.verbose:
                print(f"   💾 Salvato: {output_path}")
        
        except Exception as e:
            print(f"❌ Errore salvataggio {output_path}: {e}")
            return {"success": False, "error": str(e)}
        
        # Salva registry (serializza AST come stringa)
        try:
            import pickle
            import base64

            # Serializza payload registry e compressed AST
            payload_registry = result.metadata.get("payload_registry", {})
            compressed_ast = result.metadata.get("compressed_ast")

            # Converti AST in base64 per JSON
            compressed_ast_b64 = None
            if compressed_ast:
                ast_bytes = pickle.dumps(compressed_ast)
                compressed_ast_b64 = base64.b64encode(ast_bytes).decode('utf-8')

            # Converti payload registry in base64
            payload_registry_b64 = None
            if payload_registry:
                registry_bytes = pickle.dumps(payload_registry)
                payload_registry_b64 = base64.b64encode(registry_bytes).decode('utf-8')

            registry_data = {
                "payload_registry_b64": payload_registry_b64,
                "compressed_ast_b64": compressed_ast_b64,
                "encoding_timestamp": result.metadata.get("encoding_timestamp"),
                "neuroglyph_version": "2.0_AST"
            }

            with open(registry_path, 'w', encoding='utf-8') as f:
                json.dump(registry_data, f, indent=2, ensure_ascii=False)
            
            if self.verbose:
                print(f"   📋 Registry: {registry_path}")
        
        except Exception as e:
            print(f"❌ Errore salvataggio registry {registry_path}: {e}")
            return {"success": False, "error": str(e)}
        
        # Statistiche finali
        stats = {
            "success": True,
            "input_file": str(input_path),
            "output_file": str(output_path),
            "registry_file": str(registry_path),
            "original_size": len(source_code),
            "compressed_symbols": len(result.compressed_symbols),
            "compression_ratio": result.compression_result.compression_ratio,
            "compression_time": compress_time,
            "encoding_level": level
        }
        
        if self.verbose:
            print(f"✅ COMPRESS COMPLETATO:")
            print(f"   📁 Input: {input_path} ({len(source_code)} chars)")
            print(f"   📁 Output: {output_path} ({len(result.compressed_symbols)} symbols)")
            print(f"   📊 Compressione: {result.compression_result.compression_ratio:.1f}%")
            print(f"   ⏱️ Tempo: {compress_time:.3f}s")
        
        return stats
    
    def decompress_file(self,
                       input_path: Path,
                       registry_path: Path,
                       output_path: Optional[Path] = None,
                       verbose: bool = False) -> Dict[str, Any]:
        """
        Decomprimi un file NEUROGLYPH.
        
        Args:
            input_path: File compresso .ngc
            registry_path: File registry .ngreg
            output_path: File Python output (default: input senza .ngc)
            verbose: Output dettagliato
            
        Returns:
            Dizionario con statistiche decompressione
        """
        self.verbose = verbose
        
        if self.verbose:
            print(f"🎯 NEUROGLYPH DECOMPRESS: {input_path}")
        
        # Leggi file compresso
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                compressed_data = json.load(f)
        except Exception as e:
            print(f"❌ Errore lettura {input_path}: {e}")
            return {"success": False, "error": str(e)}
        
        # Leggi registry
        try:
            with open(registry_path, 'r', encoding='utf-8') as f:
                registry_data = json.load(f)
        except Exception as e:
            print(f"❌ Errore lettura registry {registry_path}: {e}")
            return {"success": False, "error": str(e)}
        
        # Determina percorso output
        if output_path is None:
            if input_path.suffix == '.ngc':
                output_path = input_path.with_suffix('.py')
            else:
                output_path = input_path.with_suffix('.restored.py')
        
        # Decomprimi con NEUROGLYPH
        start_time = time.time()
        try:
            import pickle
            import base64

            # Deserializza payload registry e compressed AST da base64
            metadata = {
                "encoding_type": "ast_based",
                "encoding_timestamp": registry_data.get("encoding_timestamp"),
                "language": compressed_data.get("language", "python")
            }

            # Deserializza payload registry
            if "payload_registry_b64" in registry_data and registry_data["payload_registry_b64"]:
                registry_bytes = base64.b64decode(registry_data["payload_registry_b64"])
                metadata["payload_registry"] = pickle.loads(registry_bytes)

            # Deserializza compressed AST
            if "compressed_ast_b64" in registry_data and registry_data["compressed_ast_b64"]:
                ast_bytes = base64.b64decode(registry_data["compressed_ast_b64"])
                metadata["compressed_ast"] = pickle.loads(ast_bytes)

            result = self.encoder.decode_ast_based(
                compressed_symbols=compressed_data["symbols"],
                metadata=metadata
            )
            decompress_time = time.time() - start_time
            
            if self.verbose:
                print(f"   ✅ Decompressione completata in {decompress_time:.3f}s")
                print(f"   📊 Sintassi valida: {'✅' if result.reconstruction_result.syntax_valid else '❌'}")
                print(f"   📊 Fidelity: {result.fidelity_score:.3f}")
        
        except Exception as e:
            print(f"❌ Errore decompressione: {e}")
            return {"success": False, "error": str(e)}
        
        # Salva file ricostruito
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(result.reconstructed_code)
            
            if self.verbose:
                print(f"   💾 Salvato: {output_path}")
        
        except Exception as e:
            print(f"❌ Errore salvataggio {output_path}: {e}")
            return {"success": False, "error": str(e)}
        
        # Statistiche finali
        stats = {
            "success": True,
            "input_file": str(input_path),
            "registry_file": str(registry_path),
            "output_file": str(output_path),
            "compressed_symbols": len(compressed_data["symbols"]),
            "reconstructed_size": len(result.reconstructed_code),
            "fidelity_score": result.fidelity_score,
            "syntax_valid": result.reconstruction_result.syntax_valid,
            "decompression_time": decompress_time
        }
        
        if self.verbose:
            print(f"✅ DECOMPRESS COMPLETATO:")
            print(f"   📁 Input: {input_path} ({len(compressed_data['symbols'])} symbols)")
            print(f"   📁 Output: {output_path} ({len(result.reconstructed_code)} chars)")
            print(f"   📊 Fidelity: {result.fidelity_score:.3f}")
            print(f"   ⏱️ Tempo: {decompress_time:.3f}s")
        
        return stats
    
    def batch_compress(self,
                      input_dir: Path,
                      output_dir: Optional[Path] = None,
                      level: int = 5,
                      verbose: bool = False) -> Dict[str, Any]:
        """
        Comprimi tutti i file Python in una directory.
        
        Args:
            input_dir: Directory con file .py
            output_dir: Directory output (default: input_dir/compressed)
            level: Livello compressione
            verbose: Output dettagliato
            
        Returns:
            Statistiche batch compression
        """
        self.verbose = verbose
        
        if not input_dir.is_dir():
            print(f"❌ {input_dir} non è una directory")
            return {"success": False, "error": "Not a directory"}
        
        # Trova file Python
        py_files = list(input_dir.rglob("*.py"))
        if not py_files:
            print(f"❌ Nessun file .py trovato in {input_dir}")
            return {"success": False, "error": "No Python files found"}
        
        # Determina directory output
        if output_dir is None:
            output_dir = input_dir / "compressed"
        output_dir.mkdir(exist_ok=True)
        
        if self.verbose:
            print(f"🎯 BATCH COMPRESS: {len(py_files)} file in {input_dir}")
            print(f"   📁 Output: {output_dir}")
        
        # Comprimi ogni file
        results = []
        total_start = time.time()
        
        for py_file in py_files:
            # Mantieni struttura directory
            rel_path = py_file.relative_to(input_dir)
            output_file = output_dir / rel_path.with_suffix('.ngc')
            registry_file = output_dir / rel_path.with_suffix('.ngreg')
            
            # Crea directory se necessario
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Comprimi file
            result = self.compress_file(
                input_path=py_file,
                output_path=output_file,
                registry_path=registry_file,
                level=level,
                verbose=False  # Evita spam in batch
            )
            
            results.append(result)
            
            if self.verbose and result["success"]:
                print(f"   ✅ {rel_path}: {result['compression_ratio']:.1f}% compressione")
            elif not result["success"]:
                print(f"   ❌ {rel_path}: {result.get('error', 'Unknown error')}")
        
        total_time = time.time() - total_start
        
        # Statistiche aggregate
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]
        
        if successful:
            avg_compression = sum(r["compression_ratio"] for r in successful) / len(successful)
            total_original = sum(r["original_size"] for r in successful)
            total_symbols = sum(r["compressed_symbols"] for r in successful)
        else:
            avg_compression = 0
            total_original = 0
            total_symbols = 0
        
        stats = {
            "success": len(failed) == 0,
            "total_files": len(py_files),
            "successful": len(successful),
            "failed": len(failed),
            "avg_compression_ratio": avg_compression,
            "total_original_size": total_original,
            "total_compressed_symbols": total_symbols,
            "total_time": total_time,
            "files_per_second": len(py_files) / total_time if total_time > 0 else 0
        }
        
        if self.verbose:
            print(f"✅ BATCH COMPRESS COMPLETATO:")
            print(f"   📊 File processati: {len(successful)}/{len(py_files)}")
            print(f"   📊 Compressione media: {avg_compression:.1f}%")
            print(f"   📊 Dimensione originale: {total_original:,} chars")
            print(f"   📊 Simboli compressi: {total_symbols:,}")
            print(f"   ⏱️ Tempo totale: {total_time:.3f}s")
            print(f"   ⚡ Velocità: {stats['files_per_second']:.1f} file/s")
        
        return stats


def main():
    """Entry point per CLI ngcompress."""
    parser = argparse.ArgumentParser(
        description="NEUROGLYPH CLI - Compressione simbolica AST-based per Python",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Esempi:
  # Comprimi un file
  ngcompress compress script.py
  
  # Comprimi con livello specifico
  ngcompress compress script.py --level 3 --output compressed.ngc
  
  # Decomprimi un file
  ngcompress decompress compressed.ngc --registry compressed.ngreg
  
  # Batch compression di una directory
  ngcompress batch /path/to/project --output /path/to/compressed
  
  # Verifica round-trip
  ngcompress verify script.py
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandi disponibili')
    
    # Comando compress
    compress_parser = subparsers.add_parser('compress', help='Comprimi file Python')
    compress_parser.add_argument('input', type=Path, help='File Python da comprimere')
    compress_parser.add_argument('--output', '-o', type=Path, help='File output (.ngc)')
    compress_parser.add_argument('--registry', '-r', type=Path, help='File registry (.ngreg)')
    compress_parser.add_argument('--level', '-l', type=int, default=5, choices=[1,2,3,4,5], 
                                help='Livello compressione (default: 5)')
    compress_parser.add_argument('--verbose', '-v', action='store_true', help='Output dettagliato')
    
    # Comando decompress
    decompress_parser = subparsers.add_parser('decompress', help='Decomprimi file NEUROGLYPH')
    decompress_parser.add_argument('input', type=Path, help='File compresso (.ngc)')
    decompress_parser.add_argument('--registry', '-r', type=Path, required=True, help='File registry (.ngreg)')
    decompress_parser.add_argument('--output', '-o', type=Path, help='File Python output')
    decompress_parser.add_argument('--verbose', '-v', action='store_true', help='Output dettagliato')
    
    # Comando batch
    batch_parser = subparsers.add_parser('batch', help='Batch compression directory')
    batch_parser.add_argument('input_dir', type=Path, help='Directory con file Python')
    batch_parser.add_argument('--output', '-o', type=Path, help='Directory output')
    batch_parser.add_argument('--level', '-l', type=int, default=5, choices=[1,2,3,4,5],
                             help='Livello compressione (default: 5)')
    batch_parser.add_argument('--verbose', '-v', action='store_true', help='Output dettagliato')
    
    # Comando verify
    verify_parser = subparsers.add_parser('verify', help='Verifica round-trip')
    verify_parser.add_argument('input', type=Path, help='File Python da verificare')
    verify_parser.add_argument('--level', '-l', type=int, default=5, choices=[1,2,3,4,5],
                              help='Livello compressione (default: 5)')
    verify_parser.add_argument('--verbose', '-v', action='store_true', help='Output dettagliato')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    cli = NGCompressCLI()
    
    try:
        if args.command == 'compress':
            result = cli.compress_file(
                input_path=args.input,
                output_path=args.output,
                registry_path=args.registry,
                level=args.level,
                verbose=args.verbose
            )
            return 0 if result["success"] else 1
        
        elif args.command == 'decompress':
            result = cli.decompress_file(
                input_path=args.input,
                registry_path=args.registry,
                output_path=args.output,
                verbose=args.verbose
            )
            return 0 if result["success"] else 1
        
        elif args.command == 'batch':
            result = cli.batch_compress(
                input_dir=args.input_dir,
                output_dir=args.output,
                level=args.level,
                verbose=args.verbose
            )
            return 0 if result["success"] else 1
        
        elif args.command == 'verify':
            # Implementa verifica round-trip
            print(f"🎯 VERIFY: {args.input}")
            
            with open(args.input, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            encoder = NGEncoder()
            result = encoder.round_trip_test_ast_based(
                source_code=source_code,
                encoding_level=args.level
            )
            
            print(f"✅ VERIFY COMPLETATO:")
            print(f"   📊 Fidelity: {result.fidelity_score:.3f}")
            print(f"   📊 Compressione: {result.compression_ratio:.1f}%")
            print(f"   📊 AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
            print(f"   📊 Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
            
            return 0 if result.fidelity_score >= 0.95 else 1
    
    except Exception as e:
        print(f"❌ Errore: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
