#!/usr/bin/env python3
"""
NEUROGLYPH Pipeline CLI
Tool unificato per eseguire la pipeline cognitiva completa
"""

import sys
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional

# Aggiungi il path del progetto
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from neuroglyph.cognitive import NGIntegration
from neuroglyph.cognitive.integration import PipelineConfig, ExecutionMode
from neuroglyph.cognitive.learner_structures import LearnerConfig, LearningMode


def load_source_code(file_path: str) -> str:
    """Carica codice sorgente da file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ File non trovato: {file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Errore nella lettura del file: {e}")
        sys.exit(1)


def create_pipeline_config(args) -> PipelineConfig:
    """Crea configurazione pipeline da argomenti CLI."""
    # Modalità di esecuzione
    execution_mode = ExecutionMode.PRODUCTION
    if args.mode == "debug":
        execution_mode = ExecutionMode.DEBUG
    elif args.mode == "dry_run":
        execution_mode = ExecutionMode.DRY_RUN
    elif args.mode == "benchmark":
        execution_mode = ExecutionMode.BENCHMARK
    
    # Configurazione learner
    learner_config = None
    if args.enable_learning:
        learning_mode = LearningMode.BALANCED
        if args.learning_mode == "conservative":
            learning_mode = LearningMode.CONSERVATIVE
        elif args.learning_mode == "aggressive":
            learning_mode = LearningMode.AGGRESSIVE
        elif args.learning_mode == "experimental":
            learning_mode = LearningMode.EXPERIMENTAL
        
        learner_config = LearnerConfig(
            learning_mode=learning_mode,
            min_pattern_confidence=args.min_confidence,
            save_patterns=args.save_patterns
        )
    
    return PipelineConfig(
        execution_mode=execution_mode,
        enable_self_check=args.enable_self_check,
        enable_sandbox=args.enable_sandbox,
        enable_adaptive_patching=args.enable_patching,
        enable_learning=args.enable_learning,
        max_execution_time=args.timeout,
        enable_detailed_logging=args.verbose,
        save_execution_trace=args.save_trace,
        learner_config=learner_config
    )


def print_pipeline_result(result, verbose: bool = False):
    """Stampa risultato della pipeline in formato leggibile."""
    print("\n" + "="*60)
    print(f"🔍 NEUROGLYPH Pipeline Result - {result.execution_id}")
    print("="*60)
    
    # Status generale
    status_icon = "✅" if result.success else "❌"
    print(f"{status_icon} Status: {'SUCCESS' if result.success else 'FAILED'}")
    print(f"📊 Stage: {result.current_stage.value}")
    
    if result.error_message:
        print(f"❌ Error: {result.error_message}")
    
    # Timing
    if result.metrics:
        total_time = result.metrics.end_time - result.metrics.start_time if result.metrics.end_time else 0
        print(f"⏱️  Total Time: {total_time:.3f}s")
        print(f"📈 Success Rate: {result.metrics.execution_success_rate:.1%}")
    
    # Output finale
    if result.final_output:
        print(f"\n📤 Final Output:")
        print("-" * 40)
        print(result.final_output)
        print("-" * 40)
    
    # Dettagli verbose
    if verbose:
        print(f"\n🔍 Detailed Information:")
        
        # Trace di esecuzione
        if result.execution_trace:
            print(f"\n📋 Execution Trace:")
            for i, trace in enumerate(result.execution_trace, 1):
                print(f"  {i:2d}. {trace}")
        
        # Warning
        if result.warnings:
            print(f"\n⚠️  Warnings:")
            for warning in result.warnings:
                print(f"  • {warning}")
        
        # Timing per stadio
        if result.metrics and result.metrics.stage_timings:
            print(f"\n⏱️  Stage Timings:")
            for stage, timing in result.metrics.stage_timings.items():
                print(f"  • {stage}: {timing:.3f}s")
        
        # Risultati patch
        if result.patch_results:
            successful_patches = sum(1 for p in result.patch_results if p.success)
            print(f"\n🔧 Patch Results: {successful_patches}/{len(result.patch_results)} successful")
            for i, patch in enumerate(result.patch_results, 1):
                status = "✓" if patch.success else "✗"
                print(f"  {i}. {status} {patch.patch_id}: {patch.error_message or 'Success'}")
        
        # Risultati learning
        if result.learning_results:
            print(f"\n🧠 Learning Results:")
            for key, value in result.learning_results.items():
                print(f"  • {key}: {value}")


def run_pipeline_command(args):
    """Esegue comando run della pipeline."""
    print("🚀 Avvio NEUROGLYPH Pipeline...")
    
    # Carica codice sorgente
    if args.input_file:
        source_code = load_source_code(args.input_file)
        print(f"📁 Caricato file: {args.input_file}")
    else:
        source_code = args.code
        print(f"📝 Codice inline fornito")
    
    # Crea configurazione
    config = create_pipeline_config(args)
    
    # Inizializza pipeline
    print(f"⚙️  Modalità: {config.execution_mode.value}")
    pipeline = NGIntegration(config)
    
    # Esegui pipeline
    start_time = time.time()
    result = pipeline.run(source_code)
    execution_time = time.time() - start_time
    
    # Stampa risultati
    print_pipeline_result(result, args.verbose)
    
    # Salva risultati se richiesto
    if args.output_file:
        output_data = {
            "execution_id": result.execution_id,
            "success": result.success,
            "final_output": result.final_output,
            "execution_time": execution_time,
            "stage_timings": result.metrics.stage_timings if result.metrics else {},
            "warnings": result.warnings,
            "patch_count": len(result.patch_results),
            "learning_results": result.learning_results
        }
        
        with open(args.output_file, 'w') as f:
            json.dump(output_data, f, indent=2)
        print(f"💾 Risultati salvati in: {args.output_file}")
    
    # Exit code basato su successo
    sys.exit(0 if result.success else 1)


def stats_command(args):
    """Esegue comando stats per statistiche pipeline."""
    print("📊 NEUROGLYPH Pipeline Statistics")
    print("="*50)
    
    # Crea pipeline per ottenere statistiche
    pipeline = NGIntegration()
    stats = pipeline.get_pipeline_statistics()
    
    if "message" in stats:
        print(stats["message"])
        return
    
    print(f"Total Executions: {stats['total_executions']}")
    print(f"Successful Executions: {stats['successful_executions']}")
    print(f"Success Rate: {stats['success_rate']:.1%}")
    print(f"Recent Executions (1h): {stats['recent_executions']}")
    
    if stats.get('stage_timings'):
        print(f"\nAverage Stage Timings:")
        for stage, timing in stats['stage_timings'].items():
            print(f"  • {stage}: {timing['average']:.3f}s (min: {timing['min']:.3f}s, max: {timing['max']:.3f}s)")


def main():
    """Funzione principale CLI."""
    parser = argparse.ArgumentParser(
        description="NEUROGLYPH Pipeline - Sistema cognitivo unificato",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Esempi:
  # Esegui pipeline su file
  ngpipeline run my_script.py
  
  # Modalità debug con learning
  ngpipeline run my_script.py --mode debug --enable-learning --verbose
  
  # Dry run senza esecuzione
  ngpipeline run my_script.py --mode dry_run
  
  # Codice inline
  ngpipeline run --code "print('Hello, NEUROGLYPH!')"
  
  # Statistiche
  ngpipeline stats
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandi disponibili')
    
    # Comando RUN
    run_parser = subparsers.add_parser('run', help='Esegui pipeline su codice')
    
    # Input
    input_group = run_parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('input_file', nargs='?', help='File di codice sorgente')
    input_group.add_argument('--code', help='Codice sorgente inline')
    
    # Modalità esecuzione
    run_parser.add_argument('--mode', choices=['production', 'debug', 'dry_run', 'benchmark'],
                           default='production', help='Modalità di esecuzione')
    
    # Controllo moduli
    run_parser.add_argument('--enable-self-check', action='store_true', default=True,
                           help='Abilita self-check')
    run_parser.add_argument('--enable-sandbox', action='store_true', default=True,
                           help='Abilita sandbox execution')
    run_parser.add_argument('--enable-patching', action='store_true', default=True,
                           help='Abilita adaptive patching')
    run_parser.add_argument('--enable-learning', action='store_true', default=False,
                           help='Abilita learning module')
    
    # Configurazione learning
    run_parser.add_argument('--learning-mode', choices=['conservative', 'balanced', 'aggressive', 'experimental'],
                           default='balanced', help='Modalità di apprendimento')
    run_parser.add_argument('--min-confidence', type=float, default=0.3,
                           help='Confidenza minima per pattern')
    run_parser.add_argument('--save-patterns', action='store_true', default=True,
                           help='Salva pattern appresi')
    
    # Output e logging
    run_parser.add_argument('--verbose', '-v', action='store_true',
                           help='Output dettagliato')
    run_parser.add_argument('--output-file', '-o',
                           help='File per salvare risultati JSON')
    run_parser.add_argument('--save-trace', action='store_true', default=True,
                           help='Salva trace di esecuzione')
    
    # Limiti
    run_parser.add_argument('--timeout', type=float, default=300.0,
                           help='Timeout esecuzione (secondi)')
    
    # Comando STATS
    stats_parser = subparsers.add_parser('stats', help='Mostra statistiche pipeline')
    
    # Parse argomenti
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Esegui comando
    if args.command == 'run':
        run_pipeline_command(args)
    elif args.command == 'stats':
        stats_command(args)


if __name__ == '__main__':
    main()
