"""
NEUROGLYPH Template Engine

Engine per il caricamento e rendering dei template di codice.
Supporta placeholder dinamici e categorizzazione dei template.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CodeTemplate:
    """Rappresentazione di un template di codice."""
    template_id: str
    description: str
    template: str
    parameters: List[str]
    category: str
    complexity: str
    
    def render(self, params: Optional[Dict[str, Any]] = None) -> str:
        """
        Renderizza il template sostituendo i placeholder.
        
        Args:
            params: Parametri per sostituire i placeholder
            
        Returns:
            Codice renderizzato
        """
        if not params:
            return self.template
        
        try:
            return self.template.format(**params)
        except KeyError as e:
            raise ValueError(f"Parametro mancante per template {self.template_id}: {e}")


class TemplateEngine:
    """
    Engine per gestione template di codice NEUROGLYPH.
    
    Carica template da JSON e fornisce interfacce per:
    - Listing template disponibili
    - Rendering con parametri
    - Categorizzazione e filtraggio
    """
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        Inizializza il Template Engine.
        
        Args:
            template_dir: Directory contenente code_templates.json
        """
        if template_dir is None:
            # Default: directory corrente del modulo
            template_dir = Path(__file__).parent
        
        self.template_dir = Path(template_dir)
        self.templates: Dict[str, CodeTemplate] = {}
        
        # Carica template
        self._load_templates()
        
        logger.info(f"🔧 TemplateEngine inizializzato con {len(self.templates)} template")
    
    def _load_templates(self):
        """Carica template dal file JSON."""
        template_file = self.template_dir / "code_templates.json"
        
        if not template_file.exists():
            logger.error(f"❌ File template non trovato: {template_file}")
            return
        
        try:
            with open(template_file, "r", encoding="utf-8") as f:
                template_data = json.load(f)
            
            for template_id, data in template_data.items():
                template = CodeTemplate(
                    template_id=template_id,
                    description=data["description"],
                    template=data["template"],
                    parameters=data.get("parameters", []),
                    category=data.get("category", "general"),
                    complexity=data.get("complexity", "basic")
                )
                self.templates[template_id] = template
            
            logger.info(f"✅ Caricati {len(self.templates)} template da {template_file}")
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento template: {e}")
    
    def list_templates(self) -> List[str]:
        """
        Restituisce lista di tutti i template ID disponibili.
        
        Returns:
            Lista template ID
        """
        return list(self.templates.keys())
    
    def get_template(self, template_id: str) -> Optional[CodeTemplate]:
        """
        Restituisce un template specifico.
        
        Args:
            template_id: ID del template
            
        Returns:
            CodeTemplate o None se non trovato
        """
        return self.templates.get(template_id)
    
    def get_description(self, template_id: str) -> Optional[str]:
        """
        Restituisce la descrizione di un template.
        
        Args:
            template_id: ID del template
            
        Returns:
            Descrizione o None se template non trovato
        """
        template = self.templates.get(template_id)
        return template.description if template else None
    
    def render(self, template_id: str, params: Optional[Dict[str, Any]] = None) -> str:
        """
        Renderizza un template con parametri opzionali.
        
        Args:
            template_id: ID del template da renderizzare
            params: Parametri per sostituire placeholder
            
        Returns:
            Codice renderizzato
            
        Raises:
            ValueError: Se template non trovato o parametri mancanti
        """
        template = self.templates.get(template_id)
        if not template:
            raise ValueError(f"Template '{template_id}' non trovato")
        
        return template.render(params)
    
    def list_by_category(self, category: str) -> List[str]:
        """
        Restituisce template filtrati per categoria.
        
        Args:
            category: Categoria da filtrare
            
        Returns:
            Lista template ID della categoria
        """
        return [
            template_id for template_id, template in self.templates.items()
            if template.category == category
        ]
    
    def list_by_complexity(self, complexity: str) -> List[str]:
        """
        Restituisce template filtrati per complessità.
        
        Args:
            complexity: Livello di complessità ("basic", "intermediate", "advanced")
            
        Returns:
            Lista template ID del livello specificato
        """
        return [
            template_id for template_id, template in self.templates.items()
            if template.complexity == complexity
        ]
    
    def get_categories(self) -> List[str]:
        """
        Restituisce tutte le categorie disponibili.
        
        Returns:
            Lista categorie uniche
        """
        return list(set(template.category for template in self.templates.values()))
    
    def get_complexities(self) -> List[str]:
        """
        Restituisce tutti i livelli di complessità disponibili.
        
        Returns:
            Lista livelli di complessità
        """
        return list(set(template.complexity for template in self.templates.values()))
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Restituisce statistiche sui template caricati.
        
        Returns:
            Dizionario con statistiche
        """
        categories = {}
        complexities = {}
        
        for template in self.templates.values():
            categories[template.category] = categories.get(template.category, 0) + 1
            complexities[template.complexity] = complexities.get(template.complexity, 0) + 1
        
        return {
            "total_templates": len(self.templates),
            "categories": categories,
            "complexities": complexities,
            "template_ids": list(self.templates.keys())
        }
