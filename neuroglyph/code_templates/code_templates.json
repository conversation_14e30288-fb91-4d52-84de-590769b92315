{"euclid_gcd": {"description": "Calcola il massimo comune divisore usando l'algoritmo di Euclide", "template": "def gcd(a, b):\n    \"\"\"Calcola il massimo comune divisore usando l'algoritmo di Euclide.\"\"\"\n    while b:\n        a, b = b, a % b\n    return a\n", "parameters": [], "category": "mathematics", "complexity": "basic"}, "fibonacci_recursive": {"description": "Fibonacci ricorsivo", "template": "def fib(n):\n    \"\"\"Calcola il numero di Fibonacci ricorsivamente.\"\"\"\n    if n <= 0:\n        return 0\n    elif n == 1:\n        return 1\n    else:\n        return fib(n-1) + fib(n-2)\n", "parameters": [], "category": "mathematics", "complexity": "basic"}, "fibonacci_iterative": {"description": "Fibonacci iterativo (più efficiente)", "template": "def fib_iter(n):\n    \"\"\"Calcola il numero di Fibonacci iterativamente.\"\"\"\n    if n <= 0:\n        return 0\n    elif n == 1:\n        return 1\n    \n    a, b = 0, 1\n    for _ in range(2, n + 1):\n        a, b = b, a + b\n    return b\n", "parameters": [], "category": "mathematics", "complexity": "intermediate"}, "binary_search": {"description": "Ricerca binaria su lista ordinata", "template": "def binary_search(arr, target):\n    \"\"\"Ricerca binaria su lista ordinata.\"\"\"\n    left, right = 0, len(arr) - 1\n    \n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            return mid\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    \n    return -1  # Non trovato\n", "parameters": [], "category": "algorithms", "complexity": "intermediate"}, "quicksort": {"description": "Algoritmo di ordinamento QuickSort", "template": "def quicksort(arr):\n    \"\"\"Algoritmo di ordinamento QuickSort.\"\"\"\n    if len(arr) <= 1:\n        return arr\n    \n    pivot = arr[len(arr) // 2]\n    left = [x for x in arr if x < pivot]\n    middle = [x for x in arr if x == pivot]\n    right = [x for x in arr if x > pivot]\n    \n    return quicksort(left) + middle + quicksort(right)\n", "parameters": [], "category": "algorithms", "complexity": "advanced"}, "factorial_recursive": {"description": "Calcolo fattoriale ricorsivo", "template": "def factorial(n):\n    \"\"\"Calcola il fattoriale di n ricorsivamente.\"\"\"\n    if n <= 1:\n        return 1\n    return n * factorial(n - 1)\n", "parameters": [], "category": "mathematics", "complexity": "basic"}, "prime_check": {"description": "Verifica se un numero è primo", "template": "def is_prime(n):\n    \"\"\"Verifica se un numero è primo.\"\"\"\n    if n < 2:\n        return False\n    if n == 2:\n        return True\n    if n % 2 == 0:\n        return False\n    \n    for i in range(3, int(n**0.5) + 1, 2):\n        if n % i == 0:\n            return False\n    return True\n", "parameters": [], "category": "mathematics", "complexity": "intermediate"}, "merge_sort": {"description": "Algoritmo di ordinamento MergeSort", "template": "def merge_sort(arr):\n    \"\"\"Algoritmo di ordinamento MergeSort.\"\"\"\n    if len(arr) <= 1:\n        return arr\n    \n    mid = len(arr) // 2\n    left = merge_sort(arr[:mid])\n    right = merge_sort(arr[mid:])\n    \n    return merge(left, right)\n\ndef merge(left, right):\n    \"\"\"Unisce due liste ordinate.\"\"\"\n    result = []\n    i = j = 0\n    \n    while i < len(left) and j < len(right):\n        if left[i] <= right[j]:\n            result.append(left[i])\n            i += 1\n        else:\n            result.append(right[j])\n            j += 1\n    \n    result.extend(left[i:])\n    result.extend(right[j:])\n    return result\n", "parameters": [], "category": "algorithms", "complexity": "advanced"}}