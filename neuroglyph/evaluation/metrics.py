#!/usr/bin/env python3
"""
NEUROGLYPH Evaluation Metrics
Metriche centralizzate per benchmark reasoning.
"""

import time
import statistics
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timezone


@dataclass
class BenchmarkResult:
    """Risultato completo di un benchmark."""
    
    # Identificazione
    benchmark_name: str
    model_name: str
    timestamp: datetime
    
    # Metriche base
    total_samples: int
    valid_samples: int
    correct_samples: int
    accuracy: float
    
    # Performance metrics
    avg_latency: float
    p95_latency: float
    total_time: float
    
    # Error analysis
    error_analysis: Dict[str, int] = field(default_factory=dict)
    
    # Metadata aggiuntivi
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validazione post-inizializzazione."""
        if self.valid_samples > 0:
            assert 0.0 <= self.accuracy <= 1.0, f"Accuracy deve essere 0-1, got {self.accuracy}"
            assert self.correct_samples <= self.valid_samples, "Correct samples > valid samples"
        
    @property
    def success_rate(self) -> float:
        """Tasso di successo (samples validi / totali)."""
        return self.valid_samples / self.total_samples if self.total_samples > 0 else 0.0
    
    @property
    def error_rate(self) -> float:
        """Tasso di errore."""
        return 1.0 - self.success_rate
    
    @property
    def throughput(self) -> float:
        """Throughput (samples/secondo)."""
        return self.valid_samples / self.total_time if self.total_time > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte a dizionario per serializzazione."""
        return {
            'benchmark_name': self.benchmark_name,
            'model_name': self.model_name,
            'timestamp': self.timestamp.isoformat(),
            'total_samples': self.total_samples,
            'valid_samples': self.valid_samples,
            'correct_samples': self.correct_samples,
            'accuracy': self.accuracy,
            'success_rate': self.success_rate,
            'error_rate': self.error_rate,
            'avg_latency': self.avg_latency,
            'p95_latency': self.p95_latency,
            'throughput': self.throughput,
            'total_time': self.total_time,
            'error_analysis': self.error_analysis,
            'metadata': self.metadata
        }


@dataclass
class MetricTracker:
    """Tracker per metriche in tempo reale."""
    
    name: str
    values: List[float] = field(default_factory=list)
    start_time: Optional[float] = None
    
    def start(self):
        """Inizia tracking."""
        self.start_time = time.time()
    
    def record(self, value: float):
        """Registra un valore."""
        self.values.append(value)
    
    def stop(self) -> float:
        """Ferma tracking e ritorna durata."""
        if self.start_time is None:
            return 0.0
        duration = time.time() - self.start_time
        self.record(duration)
        self.start_time = None
        return duration
    
    @property
    def count(self) -> int:
        return len(self.values)
    
    @property
    def mean(self) -> float:
        return statistics.mean(self.values) if self.values else 0.0
    
    @property
    def median(self) -> float:
        return statistics.median(self.values) if self.values else 0.0
    
    @property
    def std(self) -> float:
        return statistics.stdev(self.values) if len(self.values) > 1 else 0.0
    
    @property
    def min_val(self) -> float:
        return min(self.values) if self.values else 0.0
    
    @property
    def max_val(self) -> float:
        return max(self.values) if self.values else 0.0
    
    def percentile(self, p: float) -> float:
        """Calcola percentile."""
        if not self.values:
            return 0.0
        sorted_values = sorted(self.values)
        index = int(p * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]


class EvaluationMetrics:
    """
    Classe centralizzata per calcolo e tracking metriche.
    """
    
    def __init__(self):
        self.trackers: Dict[str, MetricTracker] = {}
        self.counters: Dict[str, int] = {}
        
    def create_tracker(self, name: str) -> MetricTracker:
        """Crea un nuovo tracker."""
        tracker = MetricTracker(name)
        self.trackers[name] = tracker
        return tracker
    
    def get_tracker(self, name: str) -> Optional[MetricTracker]:
        """Ottieni tracker esistente."""
        return self.trackers.get(name)
    
    def increment_counter(self, name: str, value: int = 1):
        """Incrementa contatore."""
        self.counters[name] = self.counters.get(name, 0) + value
    
    def get_counter(self, name: str) -> int:
        """Ottieni valore contatore."""
        return self.counters.get(name, 0)
    
    def calculate_accuracy(self, correct: int, total: int) -> float:
        """Calcola accuracy."""
        return correct / total if total > 0 else 0.0
    
    def calculate_precision_recall_f1(
        self, 
        true_positives: int, 
        false_positives: int, 
        false_negatives: int
    ) -> Dict[str, float]:
        """Calcola precision, recall, F1."""
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0.0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0.0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    def calculate_latency_metrics(self, latencies: List[float]) -> Dict[str, float]:
        """Calcola metriche di latenza."""
        if not latencies:
            return {
                'mean': 0.0,
                'median': 0.0,
                'p95': 0.0,
                'p99': 0.0,
                'min': 0.0,
                'max': 0.0,
                'std': 0.0
            }
        
        sorted_latencies = sorted(latencies)
        n = len(sorted_latencies)
        
        return {
            'mean': statistics.mean(latencies),
            'median': statistics.median(latencies),
            'p95': sorted_latencies[int(0.95 * n)],
            'p99': sorted_latencies[int(0.99 * n)],
            'min': min(latencies),
            'max': max(latencies),
            'std': statistics.stdev(latencies) if n > 1 else 0.0
        }
    
    def analyze_error_patterns(self, errors: List[str]) -> Dict[str, int]:
        """Analizza pattern di errori."""
        error_counts = {}
        
        for error in errors:
            # Categorizza errori per tipo
            if 'timeout' in error.lower():
                category = 'timeout'
            elif 'syntax' in error.lower():
                category = 'syntax_error'
            elif 'import' in error.lower():
                category = 'import_error'
            elif 'name' in error.lower() and 'not defined' in error.lower():
                category = 'name_error'
            elif 'type' in error.lower():
                category = 'type_error'
            elif 'value' in error.lower():
                category = 'value_error'
            else:
                category = 'other'
            
            error_counts[category] = error_counts.get(category, 0) + 1
        
        return error_counts
    
    def generate_summary_stats(self) -> Dict[str, Any]:
        """Genera statistiche riassuntive."""
        summary = {
            'trackers': {},
            'counters': dict(self.counters),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        for name, tracker in self.trackers.items():
            summary['trackers'][name] = {
                'count': tracker.count,
                'mean': tracker.mean,
                'median': tracker.median,
                'std': tracker.std,
                'min': tracker.min_val,
                'max': tracker.max_val,
                'p95': tracker.percentile(0.95),
                'p99': tracker.percentile(0.99)
            }
        
        return summary
    
    def reset(self):
        """Reset di tutte le metriche."""
        self.trackers.clear()
        self.counters.clear()


class BenchmarkComparator:
    """
    Comparatore per risultati di benchmark multipli.
    """
    
    @staticmethod
    def compare_results(results: List[BenchmarkResult]) -> Dict[str, Any]:
        """Confronta risultati di benchmark multipli."""
        if not results:
            return {}
        
        comparison = {
            'benchmark_count': len(results),
            'models': list(set(r.model_name for r in results)),
            'benchmarks': list(set(r.benchmark_name for r in results)),
            'accuracy_stats': {},
            'latency_stats': {},
            'best_performers': {},
            'trends': {}
        }
        
        # Accuracy statistics
        accuracies = [r.accuracy for r in results]
        comparison['accuracy_stats'] = {
            'mean': statistics.mean(accuracies),
            'median': statistics.median(accuracies),
            'min': min(accuracies),
            'max': max(accuracies),
            'std': statistics.stdev(accuracies) if len(accuracies) > 1 else 0.0
        }
        
        # Latency statistics
        latencies = [r.avg_latency for r in results]
        comparison['latency_stats'] = {
            'mean': statistics.mean(latencies),
            'median': statistics.median(latencies),
            'min': min(latencies),
            'max': max(latencies),
            'std': statistics.stdev(latencies) if len(latencies) > 1 else 0.0
        }
        
        # Best performers
        best_accuracy = max(results, key=lambda r: r.accuracy)
        best_latency = min(results, key=lambda r: r.avg_latency)
        
        comparison['best_performers'] = {
            'accuracy': {
                'model': best_accuracy.model_name,
                'benchmark': best_accuracy.benchmark_name,
                'value': best_accuracy.accuracy
            },
            'latency': {
                'model': best_latency.model_name,
                'benchmark': best_latency.benchmark_name,
                'value': best_latency.avg_latency
            }
        }
        
        return comparison
    
    @staticmethod
    def generate_leaderboard(results: List[BenchmarkResult]) -> List[Dict[str, Any]]:
        """Genera leaderboard ordinata per performance."""
        # Calcola score composito (accuracy pesata per velocità)
        scored_results = []
        for result in results:
            # Score = accuracy * (1 - normalized_latency)
            max_latency = max(r.avg_latency for r in results) if results else 1.0
            normalized_latency = result.avg_latency / max_latency if max_latency > 0 else 0.0
            composite_score = result.accuracy * (1.0 - normalized_latency * 0.1)  # 10% weight to latency
            
            scored_results.append({
                'model': result.model_name,
                'benchmark': result.benchmark_name,
                'accuracy': result.accuracy,
                'avg_latency': result.avg_latency,
                'composite_score': composite_score,
                'timestamp': result.timestamp
            })
        
        # Ordina per score composito
        return sorted(scored_results, key=lambda x: x['composite_score'], reverse=True)
