#!/usr/bin/env python3
"""
NEUROGLYPH Evaluation Logger
Logger centralizzato per raccolta metriche evaluation.
"""

import logging
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from dataclasses import dataclass, asdict


@dataclass
class EvaluationLogEntry:
    """Entry del log di evaluation."""
    
    timestamp: datetime
    level: str
    benchmark_name: str
    message: str
    metrics: Optional[Dict[str, Any]] = None
    sample_id: Optional[str] = None
    error_details: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte a dizionario per serializzazione."""
        return {
            'timestamp': self.timestamp.isoformat(),
            'level': self.level,
            'benchmark_name': self.benchmark_name,
            'message': self.message,
            'metrics': self.metrics,
            'sample_id': self.sample_id,
            'error_details': self.error_details
        }


class EvaluationLogger:
    """
    Logger centralizzato per evaluation con supporto per:
    - Accuracy tracking
    - Tempo medio per prompt  
    - Analisi degli errori
    - Export JSON strutturato
    """
    
    def __init__(self, benchmark_name: str, log_dir: str = "evaluation_logs"):
        self.benchmark_name = benchmark_name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Setup logging standard
        self.logger = logging.getLogger(f"neuroglyph.evaluation.{benchmark_name}")
        self.logger.setLevel(logging.INFO)
        
        # File handler per log testuali
        log_file = self.log_dir / f"{benchmark_name}_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Evita duplicati
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
        
        # Storage per log strutturati
        self.structured_logs: list[EvaluationLogEntry] = []
        
        # Metriche in tempo reale
        self.metrics = {
            'total_samples': 0,
            'correct_samples': 0,
            'error_samples': 0,
            'total_time': 0.0,
            'sample_times': [],
            'error_types': {},
            'start_time': time.time()
        }
        
        self.info(f"🚀 EvaluationLogger inizializzato per {benchmark_name}")
    
    def info(self, message: str, metrics: Optional[Dict[str, Any]] = None, sample_id: Optional[str] = None):
        """Log info con metriche opzionali."""
        self.logger.info(message)
        self._add_structured_log('INFO', message, metrics, sample_id)
    
    def warning(self, message: str, metrics: Optional[Dict[str, Any]] = None, sample_id: Optional[str] = None):
        """Log warning con metriche opzionali."""
        self.logger.warning(message)
        self._add_structured_log('WARNING', message, metrics, sample_id)
    
    def error(self, message: str, error_details: Optional[str] = None, sample_id: Optional[str] = None):
        """Log error con dettagli opzionali."""
        self.logger.error(message)
        self._add_structured_log('ERROR', message, None, sample_id, error_details)
    
    def debug(self, message: str, metrics: Optional[Dict[str, Any]] = None, sample_id: Optional[str] = None):
        """Log debug con metriche opzionali."""
        self.logger.debug(message)
        self._add_structured_log('DEBUG', message, metrics, sample_id)
    
    def _add_structured_log(
        self, 
        level: str, 
        message: str, 
        metrics: Optional[Dict[str, Any]] = None,
        sample_id: Optional[str] = None,
        error_details: Optional[str] = None
    ):
        """Aggiunge entry al log strutturato."""
        entry = EvaluationLogEntry(
            timestamp=datetime.now(timezone.utc),
            level=level,
            benchmark_name=self.benchmark_name,
            message=message,
            metrics=metrics,
            sample_id=sample_id,
            error_details=error_details
        )
        self.structured_logs.append(entry)
    
    def log_sample_result(
        self, 
        sample_id: str, 
        is_correct: bool, 
        latency: float,
        error_message: Optional[str] = None
    ):
        """Log risultato di un singolo sample."""
        self.metrics['total_samples'] += 1
        self.metrics['sample_times'].append(latency)
        
        if is_correct:
            self.metrics['correct_samples'] += 1
            self.info(
                f"✅ Sample {sample_id} correct in {latency:.3f}s",
                metrics={'latency': latency, 'correct': True},
                sample_id=sample_id
            )
        else:
            self.metrics['error_samples'] += 1
            
            # Categorizza errore
            if error_message:
                error_type = self._categorize_error(error_message)
                self.metrics['error_types'][error_type] = self.metrics['error_types'].get(error_type, 0) + 1
                
                self.warning(
                    f"❌ Sample {sample_id} failed in {latency:.3f}s: {error_type}",
                    metrics={'latency': latency, 'correct': False, 'error_type': error_type},
                    sample_id=sample_id
                )
            else:
                self.warning(
                    f"❌ Sample {sample_id} failed in {latency:.3f}s",
                    metrics={'latency': latency, 'correct': False},
                    sample_id=sample_id
                )
    
    def _categorize_error(self, error_message: str) -> str:
        """Categorizza tipo di errore."""
        error_lower = error_message.lower()
        
        if 'timeout' in error_lower:
            return 'timeout'
        elif 'syntax' in error_lower:
            return 'syntax_error'
        elif 'import' in error_lower:
            return 'import_error'
        elif 'name' in error_lower and 'not defined' in error_lower:
            return 'name_error'
        elif 'type' in error_lower:
            return 'type_error'
        elif 'value' in error_lower:
            return 'value_error'
        elif 'reasoning' in error_lower:
            return 'reasoning_error'
        elif 'parsing' in error_lower:
            return 'parsing_error'
        else:
            return 'other'
    
    def log_progress(self, current: int, total: int):
        """Log progress con metriche in tempo reale."""
        if current % 10 == 0 or current == total:
            elapsed = time.time() - self.metrics['start_time']
            accuracy = self.get_current_accuracy()
            avg_latency = self.get_average_latency()
            
            self.info(
                f"📈 Progress: {current}/{total} ({current/total:.1%}) - "
                f"Accuracy: {accuracy:.1%} - Avg Latency: {avg_latency:.3f}s",
                metrics={
                    'progress': current / total,
                    'current_accuracy': accuracy,
                    'avg_latency': avg_latency,
                    'elapsed_time': elapsed
                }
            )
    
    def get_current_accuracy(self) -> float:
        """Ottieni accuracy corrente."""
        if self.metrics['total_samples'] == 0:
            return 0.0
        return self.metrics['correct_samples'] / self.metrics['total_samples']
    
    def get_average_latency(self) -> float:
        """Ottieni latenza media corrente."""
        if not self.metrics['sample_times']:
            return 0.0
        return sum(self.metrics['sample_times']) / len(self.metrics['sample_times'])
    
    def get_error_analysis(self) -> Dict[str, Any]:
        """Ottieni analisi degli errori."""
        total_errors = self.metrics['error_samples']
        
        analysis = {
            'total_errors': total_errors,
            'error_rate': total_errors / self.metrics['total_samples'] if self.metrics['total_samples'] > 0 else 0.0,
            'error_types': dict(self.metrics['error_types']),
            'top_error_types': []
        }
        
        # Top error types
        if self.metrics['error_types']:
            sorted_errors = sorted(
                self.metrics['error_types'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            analysis['top_error_types'] = sorted_errors[:5]
        
        return analysis
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Ottieni summary delle performance."""
        elapsed = time.time() - self.metrics['start_time']
        
        return {
            'total_samples': self.metrics['total_samples'],
            'correct_samples': self.metrics['correct_samples'],
            'error_samples': self.metrics['error_samples'],
            'accuracy': self.get_current_accuracy(),
            'avg_latency': self.get_average_latency(),
            'total_time': elapsed,
            'throughput': self.metrics['total_samples'] / elapsed if elapsed > 0 else 0.0,
            'error_analysis': self.get_error_analysis()
        }
    
    def export_structured_logs(self, output_path: Optional[str] = None) -> str:
        """Esporta log strutturati in JSON."""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.log_dir / f"{self.benchmark_name}_{timestamp}_structured.json"
        
        export_data = {
            'benchmark_name': self.benchmark_name,
            'export_timestamp': datetime.now(timezone.utc).isoformat(),
            'performance_summary': self.get_performance_summary(),
            'logs': [entry.to_dict() for entry in self.structured_logs]
        }
        
        with open(output_path, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        self.info(f"📄 Log strutturati esportati: {output_path}")
        return str(output_path)
    
    def reset_metrics(self):
        """Reset delle metriche per nuovo run."""
        self.metrics = {
            'total_samples': 0,
            'correct_samples': 0,
            'error_samples': 0,
            'total_time': 0.0,
            'sample_times': [],
            'error_types': {},
            'start_time': time.time()
        }
        self.structured_logs.clear()
        self.info("🔄 Metriche resettate per nuovo run")
    
    def close(self):
        """Chiude logger e esporta log finali."""
        final_summary = self.get_performance_summary()
        
        self.info(
            f"🏁 Evaluation completata - "
            f"Accuracy: {final_summary['accuracy']:.1%} - "
            f"Avg Latency: {final_summary['avg_latency']:.3f}s - "
            f"Throughput: {final_summary['throughput']:.2f} samples/s"
        )
        
        # Export automatico dei log strutturati
        self.export_structured_logs()
        
        # Chiudi handlers
        for handler in self.logger.handlers:
            handler.close()
            self.logger.removeHandler(handler)
