#!/usr/bin/env python3
"""
NEUROGLYPH Quality Gates
Sistema di quality gates per benchmark evaluation.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .metrics import BenchmarkResult


class GateStatus(Enum):
    """Status di un quality gate."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    NOT_EVALUATED = "not_evaluated"


@dataclass
class QualityGate:
    """Definizione di un quality gate."""
    
    name: str
    metric_name: str
    threshold: float
    operator: str  # 'gte', 'lte', 'eq', 'gt', 'lt'
    severity: str  # 'critical', 'major', 'minor'
    description: str
    
    def evaluate(self, value: float) -> GateStatus:
        """Valuta il gate contro un valore."""
        if self.operator == 'gte':
            return GateStatus.PASSED if value >= self.threshold else GateStatus.FAILED
        elif self.operator == 'lte':
            return GateStatus.PASSED if value <= self.threshold else GateStatus.FAILED
        elif self.operator == 'gt':
            return GateStatus.PASSED if value > self.threshold else GateStatus.FAILED
        elif self.operator == 'lt':
            return GateStatus.PASSED if value < self.threshold else GateStatus.FAILED
        elif self.operator == 'eq':
            return GateStatus.PASSED if abs(value - self.threshold) < 0.001 else GateStatus.FAILED
        else:
            return GateStatus.NOT_EVALUATED


@dataclass
class GateResult:
    """Risultato di valutazione di un gate."""
    
    gate: QualityGate
    status: GateStatus
    actual_value: float
    threshold: float
    message: str
    
    @property
    def passed(self) -> bool:
        return self.status == GateStatus.PASSED
    
    @property
    def failed(self) -> bool:
        return self.status == GateStatus.FAILED


@dataclass
class QualityGateReport:
    """Report completo dei quality gates."""
    
    benchmark_name: str
    model_name: str
    gate_results: List[GateResult]
    overall_status: GateStatus
    critical_failures: int
    major_failures: int
    minor_failures: int
    
    @property
    def passed(self) -> bool:
        return self.overall_status == GateStatus.PASSED
    
    @property
    def total_gates(self) -> int:
        return len(self.gate_results)
    
    @property
    def passed_gates(self) -> int:
        return sum(1 for result in self.gate_results if result.passed)
    
    @property
    def failed_gates(self) -> int:
        return sum(1 for result in self.gate_results if result.failed)
    
    @property
    def pass_rate(self) -> float:
        return self.passed_gates / self.total_gates if self.total_gates > 0 else 0.0


class QualityGates:
    """
    Sistema di quality gates per validation benchmark.
    
    Soglie di successo:
    - LogiQA ≥ 75%
    - GSM8K ≥ 90% 
    - HumanEval ≥ 60%
    """
    
    def __init__(self):
        self.gates = self._initialize_default_gates()
    
    def _initialize_default_gates(self) -> Dict[str, List[QualityGate]]:
        """Inizializza quality gates di default per ogni benchmark."""
        
        return {
            'logiqa': [
                QualityGate(
                    name='logiqa_accuracy',
                    metric_name='accuracy',
                    threshold=0.75,
                    operator='gte',
                    severity='critical',
                    description='LogiQA accuracy must be ≥ 75%'
                ),
                QualityGate(
                    name='logiqa_latency_p95',
                    metric_name='p95_latency',
                    threshold=15.0,
                    operator='lte',
                    severity='major',
                    description='LogiQA P95 latency must be ≤ 15s'
                ),
                QualityGate(
                    name='logiqa_success_rate',
                    metric_name='success_rate',
                    threshold=0.95,
                    operator='gte',
                    severity='major',
                    description='LogiQA success rate must be ≥ 95%'
                )
            ],
            
            'gsm8k': [
                QualityGate(
                    name='gsm8k_accuracy',
                    metric_name='accuracy',
                    threshold=0.90,
                    operator='gte',
                    severity='critical',
                    description='GSM8K accuracy must be ≥ 90%'
                ),
                QualityGate(
                    name='gsm8k_latency_p95',
                    metric_name='p95_latency',
                    threshold=20.0,
                    operator='lte',
                    severity='major',
                    description='GSM8K P95 latency must be ≤ 20s'
                ),
                QualityGate(
                    name='gsm8k_success_rate',
                    metric_name='success_rate',
                    threshold=0.95,
                    operator='gte',
                    severity='major',
                    description='GSM8K success rate must be ≥ 95%'
                )
            ],
            
            'humaneval': [
                QualityGate(
                    name='humaneval_accuracy',
                    metric_name='accuracy',
                    threshold=0.60,
                    operator='gte',
                    severity='critical',
                    description='HumanEval accuracy must be ≥ 60%'
                ),
                QualityGate(
                    name='humaneval_latency_p95',
                    metric_name='p95_latency',
                    threshold=30.0,
                    operator='lte',
                    severity='major',
                    description='HumanEval P95 latency must be ≤ 30s'
                ),
                QualityGate(
                    name='humaneval_success_rate',
                    metric_name='success_rate',
                    threshold=0.90,
                    operator='gte',
                    severity='major',
                    description='HumanEval success rate must be ≥ 90%'
                )
            ],
            
            'general': [
                QualityGate(
                    name='throughput_minimum',
                    metric_name='throughput',
                    threshold=0.1,
                    operator='gte',
                    severity='minor',
                    description='Throughput must be ≥ 0.1 samples/s'
                ),
                QualityGate(
                    name='error_rate_maximum',
                    metric_name='error_rate',
                    threshold=0.10,
                    operator='lte',
                    severity='major',
                    description='Error rate must be ≤ 10%'
                )
            ]
        }
    
    def add_custom_gate(self, benchmark_name: str, gate: QualityGate):
        """Aggiunge un quality gate personalizzato."""
        if benchmark_name not in self.gates:
            self.gates[benchmark_name] = []
        self.gates[benchmark_name].append(gate)
    
    def check_gates(self, result: BenchmarkResult, config=None) -> QualityGateReport:
        """
        Verifica quality gates per un risultato di benchmark.
        
        Args:
            result: Risultato del benchmark
            config: Configurazione opzionale con soglie custom
            
        Returns:
            QualityGateReport con risultati dettagliati
        """
        benchmark_name = result.benchmark_name.lower()
        
        # Ottieni gates per il benchmark
        benchmark_gates = self.gates.get(benchmark_name, [])
        general_gates = self.gates.get('general', [])
        all_gates = benchmark_gates + general_gates
        
        # Override con config se fornita
        if config and hasattr(config, 'target_accuracy'):
            # Aggiorna soglia accuracy se specificata in config
            for gate in all_gates:
                if gate.metric_name == 'accuracy':
                    gate.threshold = config.target_accuracy
        
        # Valuta ogni gate
        gate_results = []
        for gate in all_gates:
            gate_result = self._evaluate_gate(gate, result)
            gate_results.append(gate_result)
        
        # Calcola status complessivo
        overall_status = self._calculate_overall_status(gate_results)
        
        # Conta failures per severity
        critical_failures = sum(1 for r in gate_results if r.failed and r.gate.severity == 'critical')
        major_failures = sum(1 for r in gate_results if r.failed and r.gate.severity == 'major')
        minor_failures = sum(1 for r in gate_results if r.failed and r.gate.severity == 'minor')
        
        return QualityGateReport(
            benchmark_name=result.benchmark_name,
            model_name=result.model_name,
            gate_results=gate_results,
            overall_status=overall_status,
            critical_failures=critical_failures,
            major_failures=major_failures,
            minor_failures=minor_failures
        )
    
    def _evaluate_gate(self, gate: QualityGate, result: BenchmarkResult) -> GateResult:
        """Valuta un singolo quality gate."""
        
        # Estrai valore della metrica dal risultato
        if hasattr(result, gate.metric_name):
            actual_value = getattr(result, gate.metric_name)
        elif gate.metric_name in result.metadata:
            actual_value = result.metadata[gate.metric_name]
        else:
            # Calcola metriche derivate
            if gate.metric_name == 'success_rate':
                actual_value = result.success_rate
            elif gate.metric_name == 'error_rate':
                actual_value = result.error_rate
            elif gate.metric_name == 'throughput':
                actual_value = result.throughput
            else:
                return GateResult(
                    gate=gate,
                    status=GateStatus.NOT_EVALUATED,
                    actual_value=0.0,
                    threshold=gate.threshold,
                    message=f"Metric '{gate.metric_name}' not found"
                )
        
        # Valuta gate
        status = gate.evaluate(actual_value)
        
        # Genera messaggio
        if status == GateStatus.PASSED:
            message = f"✅ {gate.description} (actual: {actual_value:.3f})"
        elif status == GateStatus.FAILED:
            message = f"❌ {gate.description} (actual: {actual_value:.3f}, threshold: {gate.threshold})"
        else:
            message = f"⚠️ {gate.description} (not evaluated)"
        
        return GateResult(
            gate=gate,
            status=status,
            actual_value=actual_value,
            threshold=gate.threshold,
            message=message
        )
    
    def _calculate_overall_status(self, gate_results: List[GateResult]) -> GateStatus:
        """Calcola status complessivo dai risultati dei gate."""
        
        # Se ci sono failures critici, overall è FAILED
        critical_failures = [r for r in gate_results if r.failed and r.gate.severity == 'critical']
        if critical_failures:
            return GateStatus.FAILED
        
        # Se ci sono failures major, overall è WARNING
        major_failures = [r for r in gate_results if r.failed and r.gate.severity == 'major']
        if major_failures:
            return GateStatus.WARNING
        
        # Se ci sono failures minor, overall è WARNING
        minor_failures = [r for r in gate_results if r.failed and r.gate.severity == 'minor']
        if minor_failures:
            return GateStatus.WARNING
        
        # Se tutti i gate sono passati, overall è PASSED
        all_passed = all(r.passed for r in gate_results)
        if all_passed:
            return GateStatus.PASSED
        
        # Default
        return GateStatus.NOT_EVALUATED
    
    def generate_report_summary(self, report: QualityGateReport) -> str:
        """Genera summary testuale del report."""
        
        summary_lines = [
            f"🎯 Quality Gates Report - {report.benchmark_name}",
            f"Model: {report.model_name}",
            f"Overall Status: {report.overall_status.value.upper()}",
            f"Gates: {report.passed_gates}/{report.total_gates} passed ({report.pass_rate:.1%})",
            ""
        ]
        
        if report.critical_failures > 0:
            summary_lines.append(f"🚨 Critical Failures: {report.critical_failures}")
        
        if report.major_failures > 0:
            summary_lines.append(f"⚠️ Major Failures: {report.major_failures}")
        
        if report.minor_failures > 0:
            summary_lines.append(f"ℹ️ Minor Failures: {report.minor_failures}")
        
        summary_lines.append("")
        summary_lines.append("📋 Gate Details:")
        
        for result in report.gate_results:
            summary_lines.append(f"  {result.message}")
        
        return "\n".join(summary_lines)
    
    def export_gates_config(self, output_path: str):
        """Esporta configurazione gates in JSON."""
        import json
        
        config_data = {}
        for benchmark_name, gates in self.gates.items():
            config_data[benchmark_name] = [
                {
                    'name': gate.name,
                    'metric_name': gate.metric_name,
                    'threshold': gate.threshold,
                    'operator': gate.operator,
                    'severity': gate.severity,
                    'description': gate.description
                }
                for gate in gates
            ]
        
        with open(output_path, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def load_gates_config(self, config_path: str):
        """Carica configurazione gates da JSON."""
        import json
        
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        self.gates.clear()
        
        for benchmark_name, gates_config in config_data.items():
            self.gates[benchmark_name] = [
                QualityGate(**gate_config)
                for gate_config in gates_config
            ]
