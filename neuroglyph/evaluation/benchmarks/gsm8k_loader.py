#!/usr/bin/env python3
"""
NEUROGLYPH GSM8K Benchmark Loader
Fase 5.2.3 - GSM8K Mathematical Reasoning Integration

Implementa:
- JSONL loader per dataset GSM8K
- Parsing mathematical word problems
- Extraction di numerical answers
- Validazione mathematical reasoning
"""

import json
import re
from pathlib import Path
from typing import List, Any, Dict, Optional
from dataclasses import dataclass

from ..harness import BenchmarkLoader, EvaluationSample


@dataclass
class GSM8KSample:
    """Sample GSM8K con metadati specifici."""
    
    id: str
    question: str
    answer: str
    numerical_answer: float
    category: Optional[str] = None
    difficulty: Optional[str] = None
    
    def to_evaluation_sample(self) -> EvaluationSample:
        """Converte a EvaluationSample standard."""
        return EvaluationSample(
            sample_id=self.id,
            prompt=self._format_prompt(),
            ground_truth=str(self.numerical_answer),
            metadata={
                'category': self.category,
                'difficulty': self.difficulty,
                'full_answer': self.answer,
                'question': self.question,
                'numerical_answer': self.numerical_answer
            }
        )
    
    def _format_prompt(self) -> str:
        """Formatta prompt per mathematical reasoning."""
        return f"""Solve this math problem step by step:

Question: {self.question}

Please provide your reasoning and the final numerical answer."""


class GSM8KLoader(BenchmarkLoader):
    """
    Loader per benchmark GSM8K.
    
    Supporta:
    - JSONL format standard
    - Mathematical word problems
    - Numerical answer extraction
    - Step-by-step reasoning validation
    """
    
    def __init__(self, prompt_template: Optional[str] = None):
        self.prompt_template = prompt_template or self._get_default_prompt_template()
        self.supported_formats = ['.jsonl', '.json']
        
    def _get_default_prompt_template(self) -> str:
        """Template di prompt di default per GSM8K."""
        return """You are an expert mathematician. Solve the following math problem step by step.

Question: {question}

Show your work and provide the final numerical answer."""
    
    def load_samples(self, data_path: str, max_samples: Optional[int] = None) -> List[EvaluationSample]:
        """
        Carica samples GSM8K da file JSONL.
        
        Args:
            data_path: Path al file JSONL GSM8K
            max_samples: Numero massimo di samples da caricare
            
        Returns:
            Lista di EvaluationSample
        """
        data_file = Path(data_path)
        
        if not data_file.exists():
            # Se il file non esiste, crea samples mock per testing
            return self._create_mock_samples(max_samples or 10)
        
        if data_file.suffix not in self.supported_formats:
            raise ValueError(f"Formato file non supportato: {data_file.suffix}. Supportati: {self.supported_formats}")
        
        gsm8k_samples = []
        
        try:
            if data_file.suffix == '.jsonl':
                gsm8k_samples = self._load_jsonl(data_file)
            else:  # .json
                gsm8k_samples = self._load_json(data_file)
                
        except Exception as e:
            print(f"⚠️ Errore caricamento {data_path}: {e}")
            print(f"🧪 Usando samples mock per testing")
            return self._create_mock_samples(max_samples or 10)
        
        # Limita numero di samples se richiesto
        if max_samples:
            gsm8k_samples = gsm8k_samples[:max_samples]
        
        # Converte a EvaluationSample
        evaluation_samples = [sample.to_evaluation_sample() for sample in gsm8k_samples]
        
        print(f"📊 GSM8K: Caricati {len(evaluation_samples)} samples")
        if gsm8k_samples:
            difficulties = [s.difficulty for s in gsm8k_samples if s.difficulty]
            if difficulties:
                from collections import Counter
                diff_counts = Counter(difficulties)
                print(f"📂 Difficoltà: {dict(diff_counts)}")
        
        return evaluation_samples
    
    def format_prompt(self, sample: EvaluationSample) -> str:
        """
        Formatta prompt per GSM8K.
        
        Args:
            sample: Sample da formattare
            
        Returns:
            Prompt formattato
        """
        question = sample.metadata.get('question', sample.prompt)
        return self.prompt_template.format(question=question)
    
    def parse_response(self, response: str, sample: EvaluationSample) -> float:
        """
        Parsa risposta del modello per estrarre numerical answer.
        
        Args:
            response: Risposta del modello
            sample: Sample originale
            
        Returns:
            Numerical answer estratto
        """
        # Cerca pattern numerici nella risposta
        patterns = [
            r'(?:final answer|answer|result).*?(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)\s*$',  # Numero alla fine
            r'=\s*(\d+(?:\.\d+)?)',  # Dopo uguale
            r'(\d+(?:\.\d+)?)\s*(?:dollars?|cents?|units?)?$'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.MULTILINE)
            if matches:
                try:
                    return float(matches[-1])  # Prendi l'ultimo match
                except ValueError:
                    continue
        
        # Se non trova nulla, prova a estrarre qualsiasi numero
        numbers = re.findall(r'\d+(?:\.\d+)?', response)
        if numbers:
            try:
                return float(numbers[-1])
            except ValueError:
                pass
        
        # Default fallback
        return 0.0
    
    def evaluate_response(self, prediction: Any, ground_truth: Any) -> bool:
        """
        Valuta correttezza della risposta numerica.
        
        Args:
            prediction: Risposta predetta (float)
            ground_truth: Risposta corretta (str o float)
            
        Returns:
            True se la risposta è corretta
        """
        try:
            pred_val = float(prediction)
            true_val = float(ground_truth)
            
            # Tolleranza per errori di floating point
            tolerance = 1e-6
            return abs(pred_val - true_val) < tolerance
            
        except (ValueError, TypeError):
            return False
    
    def _load_jsonl(self, file_path: Path) -> List[GSM8KSample]:
        """Carica samples da file JSONL."""
        samples = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                sample = self._parse_gsm8k_sample(line, f"line_{line_num}")
                if sample:
                    samples.append(sample)
        
        return samples
    
    def _load_json(self, file_path: Path) -> List[GSM8KSample]:
        """Carica samples da file JSON."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        samples = []
        if isinstance(data, list):
            for i, item in enumerate(data):
                sample = self._parse_gsm8k_sample(json.dumps(item), f"item_{i}")
                if sample:
                    samples.append(sample)
        
        return samples
    
    def _parse_gsm8k_sample(self, json_line: str, sample_id: str) -> Optional[GSM8KSample]:
        """Parsa singolo sample GSM8K."""
        try:
            data = json.loads(json_line)
            
            # Campi richiesti
            question = data.get('question', '').strip()
            answer = data.get('answer', '').strip()
            
            if not question or not answer:
                return None
            
            # Estrai numerical answer dalla risposta
            numerical_answer = self._extract_numerical_answer(answer)
            
            # ID del sample
            id_field = data.get('id', data.get('idx', sample_id))
            
            # Metadati opzionali
            category = data.get('category', 'mathematical_reasoning')
            difficulty = data.get('difficulty', 'medium')  # GSM8K è generalmente medium
            
            return GSM8KSample(
                id=str(id_field),
                question=question,
                answer=answer,
                numerical_answer=numerical_answer,
                category=category,
                difficulty=difficulty
            )
            
        except Exception as e:
            print(f"⚠️ Errore parsing sample {sample_id}: {e}")
            return None
    
    def _extract_numerical_answer(self, answer: str) -> float:
        """Estrae numerical answer dalla risposta GSM8K."""
        # GSM8K answers spesso terminano con "#### NUMBER"
        hash_pattern = r'####\s*(\d+(?:\.\d+)?)'
        match = re.search(hash_pattern, answer)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                pass
        
        # Fallback: cerca numeri nella risposta
        numbers = re.findall(r'\d+(?:\.\d+)?', answer)
        if numbers:
            try:
                return float(numbers[-1])  # Prendi l'ultimo numero
            except ValueError:
                pass
        
        return 0.0
    
    def _create_mock_samples(self, count: int) -> List[EvaluationSample]:
        """Crea samples mock per testing."""
        
        mock_samples = [
            GSM8KSample(
                id="gsm8k_mock_1",
                question="Janet's ducks lay 16 eggs per day. She eats 3 for breakfast every morning and bakes 4 into muffins for her friends every day. She sells the remainder at the farmers' market daily for $2 per fresh duck egg. How much in dollars does she make every day at the farmers' market?",
                answer="Janet's ducks lay 16 eggs per day. She eats 3 for breakfast and uses 4 for muffins, so she uses 3 + 4 = 7 eggs. She has 16 - 7 = 9 eggs left to sell. She sells them for $2 each, so she makes 9 * 2 = $18. #### 18",
                numerical_answer=18.0,
                category="mathematical_reasoning",
                difficulty="medium"
            ),
            GSM8KSample(
                id="gsm8k_mock_2",
                question="A robe takes 2 bolts of blue fiber and half that much white fiber. How many bolts of fiber does it take?",
                answer="The robe takes 2 bolts of blue fiber. It takes half that much white fiber, so 2 / 2 = 1 bolt of white fiber. In total it takes 2 + 1 = 3 bolts of fiber. #### 3",
                numerical_answer=3.0,
                category="mathematical_reasoning", 
                difficulty="easy"
            ),
            GSM8KSample(
                id="gsm8k_mock_3",
                question="Josh decides to try flipping a house. He buys a house for $80,000 and then puts in $50,000 in repairs. This increased the value of the house by 150%. How much profit did he make?",
                answer="The cost of the house and repairs came out to 80,000 + 50,000 = $130,000. He increased the value of the house by 150%, so the new value is 80,000 * 1.5 = $120,000 more than the original value. So the new value is 80,000 + 120,000 = $200,000. So he made a profit of 200,000 - 130,000 = $70,000. #### 70000",
                numerical_answer=70000.0,
                category="mathematical_reasoning",
                difficulty="hard"
            )
        ]
        
        # Replica samples se serve di più
        samples = []
        for i in range(count):
            base_sample = mock_samples[i % len(mock_samples)]
            sample = GSM8KSample(
                id=f"gsm8k_mock_{i+1}",
                question=base_sample.question,
                answer=base_sample.answer,
                numerical_answer=base_sample.numerical_answer,
                category=base_sample.category,
                difficulty=base_sample.difficulty
            )
            samples.append(sample.to_evaluation_sample())
        
        return samples
