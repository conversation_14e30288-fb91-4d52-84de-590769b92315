"""
NEUROGLYPH Evaluation Benchmarks
Fase 5.2 - Reasoning Benchmarks Implementation

Benchmark loaders specifici per:
- LogiQA (logical reasoning)
- GSM8K (mathematical reasoning)
- HumanEval (code generation)
- ARC (commonsense reasoning)
- HellaSwag (reading comprehension)
"""

from .logiqa_loader import LogiQALoader

# Placeholder per futuri loaders - implementazione incrementale
try:
    from .gsm8k_loader import GSM8KLoader
    GSM8K_AVAILABLE = True
except ImportError:
    GSM8K_AVAILABLE = False

try:
    from .humaneval_loader import HumanEvalLoader
    HUMANEVAL_AVAILABLE = True
except ImportError:
    HUMANEVAL_AVAILABLE = False

__all__ = [
    'LogiQALoader',
]

# Aggiungi loaders disponibili dinamicamente
if GSM8K_AVAILABLE:
    __all__.append('GSM8KLoader')
if HUMANEVAL_AVAILABLE:
    __all__.append('HumanEvalLoader')

__version__ = "5.2.3"
