"""
NEUROGLYPH v2.0 - GOD MODE
Il primo LLM veramente pensante con ragionamento simbolico deterministico

Import leggero: i sotto-moduli pesanti sono caricati
solo al primo accesso (PEP 562 – __getattr__).
"""

__version__ = "2.0.0"
__author__ = "NEUROGLYPH Team"
__description__ = "Il primo LLM veramente pensante con ragionamento simbolico deterministico"

__all__ = [
    "__version__",
    "__author__",
    "__description__",
    # Moduli e classi (tutti lazy-loaded)
    "utils",
    "NGParser",
    "NGMemory",
    "NGReasoner",
    "NGSandbox",
    "NGSelfCheck",
    "NGAdaptivePatcher",
    "NeuroglyphEncoder",
    "NeuroglyphDecoder",
]


def __getattr__(name: str):
    """
    Lazy loader: mappa nome->modulo/pacchetto solo quando serve.
    Evita import circolari e riduce l'overhead di start-up.
    """
    import importlib  # Import locale per evitare import circolari

    lazy_table = {
        "utils": ("neuroglyph.utils", None),  # Import modulo intero
        "NGParser": ("neuroglyph.core.parser.ng_parser", "NGParser"),
        "NGMemory": ("neuroglyph.cognitive.memory", "NGMemory"),
        "NGReasoner": ("neuroglyph.cognitive.reasoner", "NGReasoner"),
        "NGSandbox": ("neuroglyph.cognitive.sandbox", "NGSandbox"),
        "NGSelfCheck": ("neuroglyph.cognitive.self_check", "NGSelfCheck"),
        "NGAdaptivePatcher": ("neuroglyph.cognitive.adaptive_patcher", "NGAdaptivePatcher"),
        "NeuroglyphEncoder": ("neuroglyph.core.encoder.encoder", "NeuroglyphEncoder"),
        "NeuroglyphDecoder": ("neuroglyph.core.decoder.decoder", "NeuroglyphDecoder"),
    }

    if name in lazy_table:
        module_path, class_name = lazy_table[name]
        module = importlib.import_module(module_path)

        if class_name is None:
            # Import modulo intero
            obj = module
        else:
            # Import classe specifica
            obj = getattr(module, class_name)

        globals()[name] = obj  # Cache nel namespace pkg
        return obj

    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")


def get_version() -> str:
    """Restituisce la versione di NEUROGLYPH."""
    return __version__

def get_registry_info() -> dict:
    """Restituisce informazioni sul registry simbolico."""
    return {
        "total_symbols": 9236,
        "registry_file": "neuroglyph_ULTIMATE_registry.json",
        "version": "ULTIMATE.1.0",
        "zero_splitting": True,
        "atomic_mapping": True
    }

def get_architecture_info() -> dict:
    """Restituisce informazioni sull'architettura NG-THINK."""
    return {
        "v1_base": {
            "modules": 5,
            "description": "Moduli base per parsing e ragionamento"
        },
        "v2_antifragile": {
            "modules": 4,
            "description": "Architettura antifragile con auto-correzione"
        },
        "v3_ultra": {
            "modules": 6,
            "description": "Ultra fine-grained con tracciabilità completa"
        }
    }
