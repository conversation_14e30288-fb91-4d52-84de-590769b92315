#!/usr/bin/env python3
"""
NEUROGLYPH Decoder v2.0

Implementa ricostruzione intelligente del codice sorgente da strutture AST
con formattazione, indentazione e preservazione commenti.
"""

import ast
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

@dataclass
class FormattingOptions:
    """Opzioni di formattazione per ricostruzione codice."""
    indent_size: int = 4
    indent_char: str = " "
    max_line_length: int = 88
    preserve_comments: bool = True
    add_type_hints: bool = False
    style: str = "pep8"  # pep8, google, black

@dataclass
class ReconstructionResult:
    """Risultato della ricostruzione codice sorgente."""
    source_code: str
    reconstruction_info: Dict[str, Any]
    formatting_applied: List[str]
    syntax_valid: bool
    line_count: int

class NGDecoder:
    """
    Decoder NEUROGLYPH per ricostruzione intelligente del codice sorgente
    da strutture AST con formattazione e ottimizzazioni.
    """
    
    def __init__(self, formatting_options: Optional[FormattingOptions] = None):
        """
        Inizializza il decoder NEUROGLYPH.
        
        Args:
            formatting_options: Opzioni di formattazione personalizzate
        """
        self.formatting_options = formatting_options or FormattingOptions()
        self.comment_templates = self._initialize_comment_templates()
        
        print("🔧 NGDecoder inizializzato")
        print(f"   - Indentazione: {self.formatting_options.indent_size} {self.formatting_options.indent_char}")
        print(f"   - Stile: {self.formatting_options.style}")
        print(f"   - Preserva commenti: {self.formatting_options.preserve_comments}")
    
    def _initialize_comment_templates(self) -> Dict[str, str]:
        """Inizializza template per commenti placeholder."""
        return {
            "FunctionDef": "# Function: {name}",
            "ClassDef": "# Class: {name}",
            "If": "# Conditional logic",
            "For": "# Loop iteration",
            "While": "# While loop",
            "Try": "# Error handling",
            "With": "# Context manager",
            "AsyncFunctionDef": "# Async function: {name}",
            "Import": "# Import dependencies",
            "Assign": "# Variable assignment"
        }
    
    def _reconstruct_source(self, 
                           ast_structure: ast.AST,
                           formatting_options: Optional[FormattingOptions] = None,
                           preserve_comments: bool = True) -> ReconstructionResult:
        """
        Implementa ricostruzione del codice sorgente da AST.
        
        Args:
            ast_structure: Struttura AST da ricostruire
            formatting_options: Opzioni di formattazione
            preserve_comments: Preservare commenti placeholder
            
        Returns:
            ReconstructionResult con codice ricostruito
        """
        print("🔄 Ricostruzione codice sorgente da AST")
        
        options = formatting_options or self.formatting_options
        
        # Valida AST
        try:
            ast.fix_missing_locations(ast_structure)
            print("✅ AST validato e locations fissate")
        except Exception as e:
            print(f"⚠️ Problemi validazione AST: {e}")
        
        # Ricostruisci codice base con fallback multipli
        base_code = None
        reconstruction_method = "unknown"

        # Tentativo 1: ast.unparse (Python 3.9+)
        try:
            base_code = ast.unparse(ast_structure)
            reconstruction_method = "ast.unparse"
            print("✅ Codice base ricostruito con ast.unparse")
        except Exception as e:
            print(f"❌ Errore ast.unparse: {e}")

            # Tentativo 2: astor (se disponibile)
            try:
                import astor
                base_code = astor.to_source(ast_structure)
                reconstruction_method = "astor"
                print("✅ Codice base ricostruito con astor")
            except ImportError:
                print("⚠️ astor non disponibile")
            except Exception as e:
                print(f"❌ Errore astor: {e}")

            # Tentativo 3: Ricostruzione manuale migliorata
            if base_code is None:
                try:
                    base_code = self._enhanced_manual_reconstruction(ast_structure)
                    reconstruction_method = "enhanced_manual"
                    print("✅ Codice base ricostruito con ricostruzione manuale migliorata")
                except Exception as e:
                    print(f"❌ Errore ricostruzione manuale: {e}")
                    # Fallback finale: ricostruzione basica
                    base_code = self._basic_fallback_reconstruction(ast_structure)
                    reconstruction_method = "basic_fallback"
                    print("⚠️ Usato fallback basico")
        
        # Applica formattazione
        formatted_code = self._apply_formatting(base_code, options)
        
        # 🎯 PATCH 4: Disabilita commenti per evitare errori di sintassi
        # Aggiungi commenti se richiesto
        if preserve_comments and False:  # Temporaneamente disabilitato
            formatted_code = self._add_placeholder_comments(formatted_code, ast_structure)
        
        # Valida sintassi
        syntax_valid = self._validate_syntax(formatted_code)
        
        # Informazioni ricostruzione
        reconstruction_info = {
            "ast_nodes_processed": self._count_ast_nodes(ast_structure),
            "original_ast_type": type(ast_structure).__name__,
            "formatting_options": options.__dict__,
            "reconstruction_method": reconstruction_method
        }
        
        formatting_applied = [
            f"indentation_{options.indent_size}_{options.indent_char}",
            f"style_{options.style}",
            f"max_line_{options.max_line_length}"
        ]
        
        if preserve_comments:
            formatting_applied.append("placeholder_comments")
        
        result = ReconstructionResult(
            source_code=formatted_code,
            reconstruction_info=reconstruction_info,
            formatting_applied=formatting_applied,
            syntax_valid=syntax_valid,
            line_count=len(formatted_code.split('\n'))
        )
        
        print(f"✅ Ricostruzione completata:")
        print(f"   - Linee generate: {result.line_count}")
        print(f"   - Sintassi valida: {result.syntax_valid}")
        print(f"   - Formattazioni applicate: {len(formatting_applied)}")
        
        return result
    
    def _manual_reconstruction(self, ast_node: ast.AST, indent_level: int = 0) -> str:
        """Ricostruzione manuale per AST complessi."""
        indent = self.formatting_options.indent_char * (self.formatting_options.indent_size * indent_level)
        
        if isinstance(ast_node, ast.Module):
            lines = []
            for stmt in ast_node.body:
                lines.append(self._manual_reconstruction(stmt, indent_level))
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.FunctionDef):
            name = getattr(ast_node, 'name', 'placeholder_function')
            args = ', '.join(arg.arg for arg in ast_node.args.args) if ast_node.args.args else ''
            
            lines = [f"{indent}def {name}({args}):"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.AsyncFunctionDef):
            name = getattr(ast_node, 'name', 'async_placeholder_function')
            args = ', '.join(arg.arg for arg in ast_node.args.args) if ast_node.args.args else ''
            
            lines = [f"{indent}async def {name}({args}):"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.ClassDef):
            name = getattr(ast_node, 'name', 'PlaceholderClass')
            bases = ', '.join(self._manual_reconstruction(base, 0) for base in ast_node.bases) if ast_node.bases else ''
            
            if bases:
                lines = [f"{indent}class {name}({bases}):"]
            else:
                lines = [f"{indent}class {name}:"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.If):
            test = self._manual_reconstruction(ast_node.test, 0) if ast_node.test else 'condition'
            lines = [f"{indent}if {test}:"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            if ast_node.orelse:
                lines.append(f"{indent}else:")
                for stmt in ast_node.orelse:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.For):
            target = getattr(ast_node.target, 'id', 'item') if hasattr(ast_node.target, 'id') else 'item'
            iter_expr = getattr(ast_node.iter, 'id', 'iterable') if hasattr(ast_node.iter, 'id') else 'iterable'
            
            lines = [f"{indent}for {target} in {iter_expr}:"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.While):
            test = self._manual_reconstruction(ast_node.test, 0) if ast_node.test else 'condition'
            lines = [f"{indent}while {test}:"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.Try):
            lines = [f"{indent}try:"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            if ast_node.handlers:
                for handler in ast_node.handlers:
                    lines.append(f"{indent}except:")
                    lines.append(f"{indent}    pass")
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.With):
            context = 'context_manager'
            if ast_node.items and ast_node.items[0].context_expr:
                if hasattr(ast_node.items[0].context_expr, 'id'):
                    context = ast_node.items[0].context_expr.id
            
            lines = [f"{indent}with {context}:"]
            
            if ast_node.body:
                for stmt in ast_node.body:
                    lines.append(self._manual_reconstruction(stmt, indent_level + 1))
            else:
                lines.append(f"{indent}    pass")
            
            return '\n'.join(lines)
        
        elif isinstance(ast_node, ast.Assign):
            targets = ', '.join(
                getattr(target, 'id', 'variable') if hasattr(target, 'id') else 'variable'
                for target in ast_node.targets
            )
            value = 'None'
            if isinstance(ast_node.value, ast.Constant):
                value = repr(ast_node.value.value)
            elif hasattr(ast_node.value, 'id'):
                value = ast_node.value.id
            
            return f"{indent}{targets} = {value}"
        
        elif isinstance(ast_node, ast.Return):
            value = 'None'
            if ast_node.value:
                if isinstance(ast_node.value, ast.Constant):
                    value = repr(ast_node.value.value)
                elif hasattr(ast_node.value, 'id'):
                    value = ast_node.value.id
            
            return f"{indent}return {value}"
        
        elif isinstance(ast_node, ast.Pass):
            return f"{indent}pass"
        
        elif isinstance(ast_node, ast.Name):
            return getattr(ast_node, 'id', 'placeholder')
        
        elif isinstance(ast_node, ast.Constant):
            return repr(ast_node.value)
        
        else:
            # Nodo sconosciuto
            return f"{indent}# Unknown AST node: {type(ast_node).__name__}"
    
    def _apply_formatting(self, code: str, options: FormattingOptions) -> str:
        """Applica formattazione al codice."""
        lines = code.split('\n')
        formatted_lines = []
        
        for line in lines:
            # Normalizza indentazione
            stripped = line.lstrip()
            if stripped:
                # Conta livello indentazione
                indent_level = (len(line) - len(stripped)) // 4
                new_indent = options.indent_char * (options.indent_size * indent_level)
                formatted_lines.append(new_indent + stripped)
            else:
                formatted_lines.append('')
        
        # Rimuovi linee vuote eccessive
        final_lines = []
        prev_empty = False
        
        for line in formatted_lines:
            if line.strip() == '':
                if not prev_empty:
                    final_lines.append('')
                prev_empty = True
            else:
                final_lines.append(line)
                prev_empty = False
        
        return '\n'.join(final_lines)
    
    def _add_placeholder_comments(self, code: str, ast_structure: ast.AST) -> str:
        """Aggiunge commenti placeholder al codice."""
        if not self.formatting_options.preserve_comments:
            return code
        
        lines = code.split('\n')
        commented_lines = []
        
        for line in lines:
            stripped = line.strip()
            
            # Aggiungi commenti per costrutti specifici
            if stripped.startswith('def ') and not stripped.startswith('def __'):
                func_name = stripped.split('(')[0].replace('def ', '')
                comment = f"# Function: {func_name}"
                commented_lines.append(line)
                commented_lines.append(' ' * len(line.split(stripped)[0]) + comment)
            
            elif stripped.startswith('class '):
                class_name = stripped.split('(')[0].replace('class ', '').replace(':', '')
                comment = f"# Class: {class_name}"
                commented_lines.append(line)
                commented_lines.append(' ' * len(line.split(stripped)[0]) + comment)
            
            elif stripped.startswith('if '):
                commented_lines.append(line + "  # Conditional logic")
            
            elif stripped.startswith('for '):
                commented_lines.append(line + "  # Loop iteration")
            
            elif stripped.startswith('while '):
                commented_lines.append(line + "  # While loop")
            
            elif stripped.startswith('try:'):
                commented_lines.append(line + "  # Error handling")
            
            elif stripped.startswith('with '):
                commented_lines.append(line + "  # Context manager")
            
            else:
                commented_lines.append(line)
        
        return '\n'.join(commented_lines)
    
    def _validate_syntax(self, code: str) -> bool:
        """Valida la sintassi del codice ricostruito."""
        try:
            ast.parse(code)
            return True
        except SyntaxError:
            return False
    
    def _count_ast_nodes(self, ast_structure: ast.AST) -> int:
        """Conta il numero di nodi nell'AST."""
        count = 1
        for child in ast.iter_child_nodes(ast_structure):
            count += self._count_ast_nodes(child)
        return count
    
    def decode(self, 
              ast_structure: ast.AST,
              formatting_options: Optional[FormattingOptions] = None,
              preserve_comments: bool = True) -> ReconstructionResult:
        """
        Interfaccia pubblica per decodifica AST → codice sorgente.
        
        Args:
            ast_structure: Struttura AST da decodificare
            formatting_options: Opzioni di formattazione
            preserve_comments: Preservare commenti placeholder
            
        Returns:
            ReconstructionResult con codice ricostruito
        """
        return self._reconstruct_source(ast_structure, formatting_options, preserve_comments)

    def _enhanced_manual_reconstruction(self, ast_node: ast.AST, indent_level: int = 0) -> str:
        """Ricostruzione manuale migliorata con gestione di più nodi AST."""
        indent = "    " * indent_level

        # Gestione nodi base (già implementati)
        if isinstance(ast_node, (ast.Module, ast.FunctionDef, ast.AsyncFunctionDef,
                                ast.ClassDef, ast.If, ast.For, ast.While, ast.Try,
                                ast.With, ast.Assign, ast.Return, ast.Pass,
                                ast.Name, ast.Constant)):
            return self._manual_reconstruction(ast_node, indent_level)

        # Gestione nodi aggiuntivi
        elif isinstance(ast_node, ast.Import):
            names = ', '.join(alias.name for alias in ast_node.names)
            return f"{indent}import {names}"

        elif isinstance(ast_node, ast.ImportFrom):
            module = ast_node.module or ''
            names = ', '.join(alias.name for alias in ast_node.names)
            return f"{indent}from {module} import {names}"

        elif isinstance(ast_node, ast.Expr):
            value = self._enhanced_manual_reconstruction(ast_node.value, 0)
            return f"{indent}{value}"

        elif isinstance(ast_node, ast.Call):
            func_name = self._get_node_name(ast_node.func)
            args = []
            if ast_node.args:
                for arg in ast_node.args:
                    args.append(self._enhanced_manual_reconstruction(arg, 0))
            args_str = ', '.join(args)
            return f"{func_name}({args_str})"

        elif isinstance(ast_node, ast.Attribute):
            value = self._get_node_name(ast_node.value)
            return f"{value}.{ast_node.attr}"

        elif isinstance(ast_node, ast.BinOp):
            left = self._enhanced_manual_reconstruction(ast_node.left, 0)
            right = self._enhanced_manual_reconstruction(ast_node.right, 0)
            op = self._get_operator_symbol(ast_node.op)
            return f"{left} {op} {right}"

        elif isinstance(ast_node, ast.Compare):
            left = self._enhanced_manual_reconstruction(ast_node.left, 0)
            comparisons = []
            for i, (op, comp) in enumerate(zip(ast_node.ops, ast_node.comparators)):
                op_symbol = self._get_comparison_symbol(op)
                comp_str = self._enhanced_manual_reconstruction(comp, 0)
                comparisons.append(f"{op_symbol} {comp_str}")
            return f"{left} {' '.join(comparisons)}"

        elif isinstance(ast_node, ast.List):
            elements = []
            if ast_node.elts:
                for elt in ast_node.elts:
                    elements.append(self._enhanced_manual_reconstruction(elt, 0))
            return f"[{', '.join(elements)}]"

        elif isinstance(ast_node, ast.Dict):
            items = []
            if ast_node.keys and ast_node.values:
                for key, value in zip(ast_node.keys, ast_node.values):
                    key_str = self._enhanced_manual_reconstruction(key, 0) if key else 'None'
                    value_str = self._enhanced_manual_reconstruction(value, 0)
                    items.append(f"{key_str}: {value_str}")
            return f"{{{', '.join(items)}}}"

        elif isinstance(ast_node, ast.Tuple):
            elements = []
            if ast_node.elts:
                for elt in ast_node.elts:
                    elements.append(self._enhanced_manual_reconstruction(elt, 0))
            return f"({', '.join(elements)})"

        else:
            # Fallback per nodi non gestiti
            return f"# Unsupported AST node: {type(ast_node).__name__}"

    def _basic_fallback_reconstruction(self, ast_node: ast.AST) -> str:
        """Fallback basico che genera sempre codice valido."""
        if isinstance(ast_node, ast.Module):
            return "# Reconstructed module\npass"
        elif isinstance(ast_node, ast.FunctionDef):
            return f"def {getattr(ast_node, 'name', 'function')}():\n    pass"
        elif isinstance(ast_node, ast.ClassDef):
            return f"class {getattr(ast_node, 'name', 'Class')}:\n    pass"
        else:
            return "# Fallback reconstruction\npass"

    def _get_node_name(self, node: ast.AST) -> str:
        """Estrae il nome da un nodo AST."""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            value = self._get_node_name(node.value)
            return f"{value}.{node.attr}"
        elif isinstance(node, ast.Constant):
            return repr(node.value)
        else:
            return "unknown"

    def _get_operator_symbol(self, op: ast.operator) -> str:
        """Converte operatore AST in simbolo."""
        op_map = {
            ast.Add: '+', ast.Sub: '-', ast.Mult: '*', ast.Div: '/',
            ast.Mod: '%', ast.Pow: '**', ast.LShift: '<<', ast.RShift: '>>',
            ast.BitOr: '|', ast.BitXor: '^', ast.BitAnd: '&', ast.FloorDiv: '//'
        }
        return op_map.get(type(op), '?')

    def _get_comparison_symbol(self, op: ast.cmpop) -> str:
        """Converte operatore di confronto AST in simbolo."""
        op_map = {
            ast.Eq: '==', ast.NotEq: '!=', ast.Lt: '<', ast.LtE: '<=',
            ast.Gt: '>', ast.GtE: '>=', ast.Is: 'is', ast.IsNot: 'is not',
            ast.In: 'in', ast.NotIn: 'not in'
        }
        return op_map.get(type(op), '?')
