#!/usr/bin/env python3
"""
NEUROGLYPH Encoder v2.0

Encoder principale che coordina compressione semantica, mappatura simbolica
e ricostruzione per intelligenza simbolica completa.
"""

import ast
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .semantic_compressor import SemanticCompressor, CompressionResult, CompressionLevel
from .symbol_mapper import SymbolMapper, MappingResult
from .decoder import NGDecoder, ReconstructionResult, FormattingOptions
from .ast_compressor import NeuroglyphASTCompressor, NeuroglyphASTDecompressor

@dataclass
class EncodingResult:
    """Risultato completo dell'encoding NEUROGLYPH."""
    compressed_symbols: List[str]
    compression_result: CompressionResult
    original_code: str
    encoding_level: int
    language: str
    metadata: Dict[str, Any]

@dataclass
class DecodingResult:
    """Risultato completo del decoding NEUROGLYPH."""
    reconstructed_code: str
    mapping_result: MappingResult
    reconstruction_result: ReconstructionResult
    round_trip_successful: bool
    fidelity_score: float

@dataclass
class RoundTripResult:
    """Risultato del test round-trip completo."""
    original_code: str
    reconstructed_code: str
    encoding_result: EncodingResult
    decoding_result: DecodingResult
    ast_equivalent: bool
    semantic_equivalent: bool
    compression_ratio: float
    fidelity_score: float

class NGEncoder:
    """
    Encoder principale NEUROGLYPH per intelligenza simbolica completa.
    
    Coordina:
    - Compressione semantica avanzata
    - Mappatura simbolica bidirezionale  
    - Ricostruzione codice sorgente
    - Test round-trip e validazione
    """
    
    def __init__(self, 
                 registry_path: str = "neuroglyph_ULTIMATE_registry.json",
                 default_encoding_level: int = 3,
                 default_language: str = "python"):
        """
        Inizializza l'encoder NEUROGLYPH.
        
        Args:
            registry_path: Percorso al registry simbolico
            default_encoding_level: Livello di encoding di default (1-5)
            default_language: Linguaggio di programmazione di default
        """
        self.registry_path = registry_path
        self.default_encoding_level = default_encoding_level
        self.default_language = default_language
        
        # Inizializza componenti
        self.semantic_compressor = SemanticCompressor(registry_path)
        self.symbol_mapper = SymbolMapper()
        self.decoder = NGDecoder()
        self.ast_compressor = None  # Inizializzato on-demand

        print("🧠 NGEncoder inizializzato")
        print(f"   - Livello encoding default: {default_encoding_level}")
        print(f"   - Linguaggio default: {default_language}")
        print(f"   - Registry: {registry_path}")
    
    def encode(self, 
              source_code: str,
              encoding_level: Optional[int] = None,
              language: Optional[str] = None) -> EncodingResult:
        """
        Codifica codice sorgente in simboli NEUROGLYPH.
        
        Args:
            source_code: Codice sorgente da codificare
            encoding_level: Livello di compressione (1-5)
            language: Linguaggio di programmazione
            
        Returns:
            EncodingResult con simboli compressi e metadati
        """
        level = encoding_level or self.default_encoding_level
        lang = language or self.default_language
        
        print(f"🔧 Encoding codice sorgente (livello {level}, {lang})")
        print(f"   - Lunghezza originale: {len(source_code)} caratteri")
        
        # Valida codice sorgente
        if not self._validate_source_code(source_code, lang):
            raise ValueError(f"Codice sorgente non valido per linguaggio {lang}")
        
        # Compressione semantica
        compression_result = self.semantic_compressor.compress(
            source_code=source_code,
            encoding_level=level,
            language=lang
        )
        
        # FASE 2 - UNISCI METADATI DEL COMPRESSOR CON QUELLI DELL'ENCODER
        metadata = {
            "encoding_timestamp": self._get_timestamp(),
            "encoder_version": "2.0",
            "original_length": len(source_code),
            "compressed_length": len(compression_result.compressed_symbols),
            "compression_ratio": compression_result.compression_ratio,
            "patterns_applied": compression_result.patterns_applied,
            "encoding_level": level,
            "language": lang
        }

        # Aggiungi metadati del compressor (payload e symbol_metadata)
        if hasattr(compression_result, 'compression_metadata'):
            compression_metadata = compression_result.compression_metadata
            metadata.update({
                "payload": compression_metadata.get("payload", []),
                "symbol_metadata": compression_metadata.get("symbol_metadata", {}),
                "pattern_mappings": compression_metadata.get("pattern_mappings", {})
            })
        
        result = EncodingResult(
            compressed_symbols=compression_result.compressed_symbols,
            compression_result=compression_result,
            original_code=source_code,
            encoding_level=level,
            language=lang,
            metadata=metadata
        )
        
        print(f"✅ Encoding completato:")
        print(f"   - Simboli generati: {len(result.compressed_symbols)}")
        print(f"   - Compressione: {compression_result.compression_ratio:.1f}%")
        
        return result

    def encode_ast_based(self,
                        source_code: str,
                        encoding_level: Optional[int] = None,
                        language: Optional[str] = None) -> EncodingResult:
        """
        Codifica codice sorgente usando AST-based compression per fidelity perfetta.

        Args:
            source_code: Codice sorgente da codificare
            encoding_level: Livello di compressione (1-5)
            language: Linguaggio di programmazione

        Returns:
            EncodingResult con simboli compressi AST-based
        """
        level = encoding_level or self.default_encoding_level
        lang = language or self.default_language

        print(f"🎯 AST-BASED Encoding (livello {level}, {lang})")
        print(f"   - Lunghezza originale: {len(source_code)} caratteri")

        # Valida codice sorgente
        if not self._validate_source_code(source_code, lang):
            raise ValueError(f"Codice sorgente non valido per linguaggio {lang}")

        # Inizializza AST compressor
        if self.ast_compressor is None:
            self.ast_compressor = NeuroglyphASTCompressor(encoding_level=level)

        # Compressione AST-based
        ast_result = self.ast_compressor.compress_ast(source_code)

        # Crea CompressionResult compatibile
        compression_result = CompressionResult(
            compressed_symbols=ast_result["compressed_symbols"],
            compression_metadata={
                "payload_registry": ast_result["payload_registry"],
                "compressed_ast": ast_result["compressed_ast"],
                "original_ast": ast_result["original_ast"],
                "encoding_type": "ast_based"
            },
            original_length=ast_result["original_length"],
            compressed_length=ast_result["compressed_length"],
            compression_ratio=ast_result["compression_ratio"],
            patterns_applied=["ast_function", "ast_while", "ast_if"]
        )

        # Metadati completi
        metadata = {
            "encoding_timestamp": self._get_timestamp(),
            "encoder_version": "2.0_AST",
            "original_length": len(source_code),
            "compressed_length": len(ast_result["compressed_symbols"]),
            "compression_ratio": ast_result["compression_ratio"],
            "patterns_applied": ["ast_function", "ast_while", "ast_if"],
            "encoding_level": level,
            "language": lang,
            "encoding_type": "ast_based",
            "payload_registry": ast_result["payload_registry"],
            "compressed_ast": ast_result["compressed_ast"]
        }

        result = EncodingResult(
            compressed_symbols=ast_result["compressed_symbols"],
            compression_result=compression_result,
            original_code=source_code,
            encoding_level=level,
            language=lang,
            metadata=metadata
        )

        print(f"✅ AST-BASED Encoding completato:")
        print(f"   - Simboli generati: {len(result.compressed_symbols)}")
        print(f"   - Compressione: {ast_result['compression_ratio']:.1f}%")

        return result

    def decode(self,
              compressed_symbols: List[str],
              metadata: Dict[str, Any],
              formatting_options: Optional[FormattingOptions] = None) -> DecodingResult:
        """
        Decodifica simboli NEUROGLYPH in codice sorgente.
        
        Args:
            compressed_symbols: Simboli compressi da decodificare
            metadata: Metadati di encoding
            formatting_options: Opzioni di formattazione
            
        Returns:
            DecodingResult con codice ricostruito
        """
        print(f"🔄 Decoding simboli NEUROGLYPH")
        print(f"   - Simboli da decodificare: {len(compressed_symbols)}")
        
        target_language = metadata.get("language", self.default_language)
        
        # Mappatura simboli → AST
        mapping_result = self.symbol_mapper.reverse_map(
            compressed_symbols=compressed_symbols,
            metadata=metadata,
            target_language=target_language
        )
        
        # Ricostruzione codice sorgente
        reconstruction_result = self.decoder.decode(
            ast_structure=mapping_result.ast_structure,
            formatting_options=formatting_options,
            preserve_comments=True
        )
        
        # Calcola fidelity score
        fidelity_score = self._calculate_fidelity_score(
            mapping_result, reconstruction_result
        )
        
        # Determina successo round-trip
        round_trip_successful = (
            reconstruction_result.syntax_valid and
            fidelity_score >= 0.8 and
            mapping_result.mapping_info["mapped_symbols"] > 0
        )
        
        result = DecodingResult(
            reconstructed_code=reconstruction_result.source_code,
            mapping_result=mapping_result,
            reconstruction_result=reconstruction_result,
            round_trip_successful=round_trip_successful,
            fidelity_score=fidelity_score
        )
        
        print(f"✅ Decoding completato:")
        print(f"   - Codice ricostruito: {reconstruction_result.line_count} linee")
        print(f"   - Sintassi valida: {reconstruction_result.syntax_valid}")
        print(f"   - Fidelity score: {fidelity_score:.2f}")
        print(f"   - Round-trip: {'✅' if round_trip_successful else '❌'}")
        
        return result

    def decode_ast_based(self,
                        compressed_symbols: List[str],
                        metadata: Dict[str, Any],
                        formatting_options: Optional[FormattingOptions] = None) -> DecodingResult:
        """
        Decodifica simboli NEUROGLYPH usando AST-based decompression per fidelity perfetta.

        Args:
            compressed_symbols: Simboli compressi da decodificare
            metadata: Metadati di encoding AST-based
            formatting_options: Opzioni di formattazione

        Returns:
            DecodingResult con codice ricostruito AST-based
        """
        print(f"🎯 AST-BASED Decoding")
        print(f"   - Simboli da decodificare: {len(compressed_symbols)}")

        # Verifica che sia encoding AST-based
        if metadata.get("encoding_type") != "ast_based":
            print("   ⚠️ Non è encoding AST-based, uso decoding standard")
            return self.decode(compressed_symbols, metadata, formatting_options)

        # Estrai payload registry
        payload_registry = metadata.get("payload_registry", {})

        # Inizializza AST decompressor
        ast_decompressor = NeuroglyphASTDecompressor(payload_registry)

        # 🎯 DECOMPRESSIONE AST-BASED: Usa AST compresso invece di simboli
        compressed_ast = metadata.get("compressed_ast")
        if compressed_ast:
            # Usa decompressione AST diretta per migliore fidelity
            reconstructed_ast = ast_decompressor.decompress_ast(compressed_ast)
        else:
            # Fallback a decompressione simboli (legacy)
            reconstructed_ast = ast_decompressor.decompress_symbols(compressed_symbols)

        # Converti AST in codice sorgente
        try:
            reconstructed_code = ast.unparse(reconstructed_ast)
            syntax_valid = True
        except Exception as e:
            print(f"   ❌ Errore unparse AST: {e}")
            reconstructed_code = "# Errore ricostruzione AST"
            syntax_valid = False

        # Crea MappingResult compatibile
        mapping_result = MappingResult(
            ast_structure=reconstructed_ast,
            mapping_info={
                "total_symbols": len(compressed_symbols),
                "mapped_symbols": len(compressed_symbols),
                "unknown_symbols": 0,
                "mapping_type": "ast_based"
            },
            placeholders_used=[],
            reconstruction_metadata={
                "reconstruction_type": "ast_based",
                "payload_registry_size": len(payload_registry)
            }
        )

        # Crea ReconstructionResult
        line_count = len(reconstructed_code.split('\n'))
        reconstruction_result = ReconstructionResult(
            source_code=reconstructed_code,
            reconstruction_info={
                "reconstruction_type": "ast_based",
                "ast_nodes": len(list(ast.walk(reconstructed_ast)))
            },
            formatting_applied=["ast_unparse"],
            syntax_valid=syntax_valid,
            line_count=line_count
        )

        # Calcola fidelity score (dovrebbe essere perfetto per AST-based)
        fidelity_score = 1.0 if syntax_valid else 0.0

        # Round-trip sempre successful per AST-based se sintassi valida
        round_trip_successful = syntax_valid

        result = DecodingResult(
            reconstructed_code=reconstructed_code,
            mapping_result=mapping_result,
            reconstruction_result=reconstruction_result,
            round_trip_successful=round_trip_successful,
            fidelity_score=fidelity_score
        )

        print(f"✅ AST-BASED Decoding completato:")
        print(f"   - Codice ricostruito: {reconstruction_result.line_count} linee")
        print(f"   - Sintassi valida: {syntax_valid}")
        print(f"   - Fidelity score: {fidelity_score:.2f}")
        print(f"   - Round-trip: {'✅' if round_trip_successful else '❌'}")

        return result

    def round_trip_test_ast_based(self,
                                 source_code: str,
                                 encoding_level: Optional[int] = None,
                                 language: Optional[str] = None,
                                 formatting_options: Optional[FormattingOptions] = None) -> RoundTripResult:
        """
        Esegue test round-trip AST-based per fidelity perfetta.

        Args:
            source_code: Codice sorgente originale
            encoding_level: Livello di encoding
            language: Linguaggio di programmazione
            formatting_options: Opzioni di formattazione

        Returns:
            RoundTripResult con risultati AST-based
        """
        print(f"🎯 AST-BASED Round-trip test")
        print(f"   - Codice originale: {len(source_code)} caratteri")

        # Encoding AST-based
        encoding_result = self.encode_ast_based(
            source_code=source_code,
            encoding_level=encoding_level,
            language=language
        )

        # Decoding AST-based
        decoding_result = self.decode_ast_based(
            compressed_symbols=encoding_result.compressed_symbols,
            metadata=encoding_result.metadata,
            formatting_options=formatting_options
        )

        # Confronto AST (dovrebbe essere perfetto)
        ast_equivalent = self._compare_ast_structures(
            source_code,
            decoding_result.reconstructed_code,
            language or self.default_language
        )

        # Confronto semantico (dovrebbe essere perfetto)
        semantic_equivalent = self._compare_semantic_meaning(
            source_code,
            decoding_result.reconstructed_code,
            language or self.default_language
        )

        # Fidelity score (dovrebbe essere 1.0 per AST-based)
        overall_fidelity = decoding_result.fidelity_score

        result = RoundTripResult(
            original_code=source_code,
            reconstructed_code=decoding_result.reconstructed_code,
            encoding_result=encoding_result,
            decoding_result=decoding_result,
            ast_equivalent=ast_equivalent,
            semantic_equivalent=semantic_equivalent,
            compression_ratio=encoding_result.compression_result.compression_ratio,
            fidelity_score=overall_fidelity
        )

        print(f"✅ AST-BASED Round-trip completato:")
        print(f"   - AST equivalente: {'✅' if ast_equivalent else '❌'}")
        print(f"   - Semantica equivalente: {'✅' if semantic_equivalent else '❌'}")
        print(f"   - Fidelity complessivo: {overall_fidelity:.2f}")
        print(f"   - Compressione: {encoding_result.compression_result.compression_ratio:.1f}%")

        return result

    def round_trip_test(self,
                       source_code: str,
                       encoding_level: Optional[int] = None,
                       language: Optional[str] = None,
                       formatting_options: Optional[FormattingOptions] = None) -> RoundTripResult:
        """
        Esegue test round-trip completo: encode → decode → confronto.
        
        Args:
            source_code: Codice sorgente originale
            encoding_level: Livello di encoding
            language: Linguaggio di programmazione
            formatting_options: Opzioni di formattazione
            
        Returns:
            RoundTripResult con risultati completi del test
        """
        print(f"🔄 Test round-trip completo")
        print(f"   - Codice originale: {len(source_code)} caratteri")
        
        # Encoding
        encoding_result = self.encode(
            source_code=source_code,
            encoding_level=encoding_level,
            language=language
        )
        
        # Decoding
        decoding_result = self.decode(
            compressed_symbols=encoding_result.compressed_symbols,
            metadata=encoding_result.metadata,
            formatting_options=formatting_options
        )
        
        # Confronto AST
        ast_equivalent = self._compare_ast_structures(
            source_code, 
            decoding_result.reconstructed_code,
            language or self.default_language
        )
        
        # Confronto semantico
        semantic_equivalent = self._compare_semantic_meaning(
            source_code,
            decoding_result.reconstructed_code,
            language or self.default_language
        )
        
        # Calcola fidelity score complessivo
        overall_fidelity = (
            decoding_result.fidelity_score * 0.4 +
            (1.0 if ast_equivalent else 0.0) * 0.4 +
            (1.0 if semantic_equivalent else 0.0) * 0.2
        )
        
        result = RoundTripResult(
            original_code=source_code,
            reconstructed_code=decoding_result.reconstructed_code,
            encoding_result=encoding_result,
            decoding_result=decoding_result,
            ast_equivalent=ast_equivalent,
            semantic_equivalent=semantic_equivalent,
            compression_ratio=encoding_result.compression_result.compression_ratio,
            fidelity_score=overall_fidelity
        )
        
        print(f"✅ Round-trip test completato:")
        print(f"   - AST equivalente: {'✅' if ast_equivalent else '❌'}")
        print(f"   - Semantica equivalente: {'✅' if semantic_equivalent else '❌'}")
        print(f"   - Fidelity complessivo: {overall_fidelity:.2f}")
        print(f"   - Compressione: {encoding_result.compression_result.compression_ratio:.1f}%")
        
        return result
    
    def _validate_source_code(self, source_code: str, language: str) -> bool:
        """Valida il codice sorgente per il linguaggio specificato."""
        if language == "python":
            try:
                ast.parse(source_code)
                return True
            except SyntaxError:
                return False
        
        # Per altri linguaggi, validazione base
        return len(source_code.strip()) > 0
    
    def _calculate_fidelity_score(self, 
                                 mapping_result: MappingResult,
                                 reconstruction_result: ReconstructionResult) -> float:
        """Calcola score di fidelity basato su mapping e ricostruzione."""
        score = 0.0
        
        # Contributo mapping (40%)
        if mapping_result.mapping_info["total_symbols"] > 0:
            mapping_ratio = mapping_result.mapping_info["mapped_symbols"] / mapping_result.mapping_info["total_symbols"]
            score += mapping_ratio * 0.4
        
        # Contributo sintassi (30%)
        if reconstruction_result.syntax_valid:
            score += 0.3
        
        # Contributo formattazione (20%)
        if reconstruction_result.formatting_applied:
            score += 0.2
        
        # Contributo linee generate (10%)
        if reconstruction_result.line_count > 0:
            score += 0.1
        
        return min(1.0, score)
    
    def _compare_ast_structures(self, code1: str, code2: str, language: str) -> bool:
        """Confronta strutture AST di due codici."""
        if language != "python":
            return True  # Skip per altri linguaggi
        
        try:
            ast1 = ast.parse(code1)
            ast2 = ast.parse(code2)
            
            # Confronta dump AST normalizzati
            dump1 = ast.dump(ast1, annotate_fields=False, include_attributes=False)
            dump2 = ast.dump(ast2, annotate_fields=False, include_attributes=False)
            
            return dump1 == dump2
        except:
            return False
    
    def _compare_semantic_meaning(self, code1: str, code2: str, language: str) -> bool:
        """Confronta significato semantico di due codici."""
        # Implementazione semplificata - confronta strutture principali
        if language == "python":
            try:
                ast1 = ast.parse(code1)
                ast2 = ast.parse(code2)
                
                # Conta tipi di nodi
                nodes1 = [type(node).__name__ for node in ast.walk(ast1)]
                nodes2 = [type(node).__name__ for node in ast.walk(ast2)]
                
                # Confronta distribuzione tipi nodi
                from collections import Counter
                return Counter(nodes1) == Counter(nodes2)
            except:
                return False
        
        return True  # Default per altri linguaggi
    
    def _get_timestamp(self) -> str:
        """Genera timestamp per metadati."""
        import datetime
        return datetime.datetime.now().isoformat()
    
    # Metodi di compatibilità per interfaccia legacy
    def _ultra_semantic_compression(self, *args, **kwargs):
        """Metodo legacy - usa semantic_compressor."""
        return self.semantic_compressor._ultra_semantic_compression(*args, **kwargs)
    
    def _reverse_symbol_mapping(self, *args, **kwargs):
        """Metodo legacy - usa symbol_mapper."""
        return self.symbol_mapper._reverse_symbol_mapping(*args, **kwargs)
    
    def _reconstruct_source(self, *args, **kwargs):
        """Metodo legacy - usa decoder."""
        return self.decoder._reconstruct_source(*args, **kwargs)
