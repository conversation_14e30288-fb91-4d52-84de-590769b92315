"""
NEUROGLYPH Logic Reasoner Stage
Stage per reasoning logico formale nella pipeline
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List

from ..pipeline_engine import PipelineStage, PipelinePayload
from ...bus.event_bus import get_event_bus

# Import lazy per evitare cicli
try:
    from ...logic.logic_engine import FormalLogicEngine
    from ...logic.formula import Formula, parse_formula
    LOGIC_AVAILABLE = True
except ImportError:
    LOGIC_AVAILABLE = False

logger = logging.getLogger(__name__)


class LogicReasonerStage(PipelineStage):
    """
    Stage per reasoning logico formale.
    
    Features:
    - Deduzione logica con FormalLogicEngine
    - Proof tree generation
    - Fallback a pattern matching se necessario
    - Event publishing per proof completion
    """
    
    def __init__(self, stage_id: str, config: Optional[Dict[str, Any]] = None):
        """
        Inizializza Logic Reasoner stage.
        
        Args:
            stage_id: ID univoco dello stage
            config: Configurazione stage
        """
        super().__init__(stage_id, config)
        
        # Configurazione
        self.max_depth = config.get('max_depth', 8) if config else 8
        self.max_steps = config.get('max_steps', 50) if config else 50
        self.enable_fallback = config.get('enable_fallback', True) if config else True
        self.proof_required = config.get('proof_required', False) if config else False
        
        # Inizializza Logic Engine se disponibile
        self.logic_engine = None
        if LOGIC_AVAILABLE:
            try:
                self.logic_engine = FormalLogicEngine(
                    max_depth=self.max_depth,
                    max_steps=self.max_steps
                )
                logger.info(f"✅ FormalLogicEngine inizializzato per stage {stage_id}")
            except Exception as e:
                logger.warning(f"⚠️ Errore inizializzazione FormalLogicEngine: {e}")
        else:
            logger.warning(f"⚠️ Logic Engine non disponibile per stage {stage_id}")
        
        logger.info(f"🧠 LogicReasonerStage {stage_id} inizializzato")
    
    async def process(self, payload: PipelinePayload) -> PipelinePayload:
        """
        Processa payload con reasoning logico.
        
        Args:
            payload: Payload con dati per reasoning
            
        Returns:
            Payload con risultati reasoning
        """
        # Check se reasoning logico è richiesto
        requires_proof = payload.data.get('requires_proof', self.proof_required)
        
        if not requires_proof:
            logger.debug(f"🔄 Stage {self.stage_id}: reasoning logico non richiesto")
            return payload
        
        # Estrai dati per reasoning
        premises_data = payload.data.get('premises', [])
        goal_data = payload.data.get('goal')
        statement = payload.data.get('statement', '')
        
        logger.debug(f"🧠 Stage {self.stage_id}: reasoning richiesto")
        logger.debug(f"   Premises: {len(premises_data)}")
        logger.debug(f"   Goal: {goal_data}")
        logger.debug(f"   Statement: {statement[:100]}..." if statement else "")
        
        # Simula processing
        await asyncio.sleep(0.01)
        
        if self.logic_engine and (premises_data and goal_data):
            # Reasoning logico formale
            return await self._formal_reasoning(payload, premises_data, goal_data)
        elif self.enable_fallback:
            # Fallback pattern matching
            return await self._fallback_reasoning(payload)
        else:
            # Nessun reasoning disponibile
            logger.warning(f"⚠️ Stage {self.stage_id}: reasoning non disponibile")
            return payload.with_data(
                reasoning_result={
                    'success': False,
                    'error': 'Logic reasoning not available',
                    'fallback_used': False
                }
            )
    
    async def _formal_reasoning(self, payload: PipelinePayload, 
                               premises_data: List, goal_data: Any) -> PipelinePayload:
        """Reasoning logico formale con FormalLogicEngine."""
        try:
            # Parse premises e goal
            premises = []
            for premise_data in premises_data:
                if isinstance(premise_data, str):
                    premise = parse_formula(premise_data)
                    premises.append(premise)
                elif isinstance(premise_data, dict) and 'formula' in premise_data:
                    premise = parse_formula(premise_data['formula'])
                    premises.append(premise)
            
            if isinstance(goal_data, str):
                goal = parse_formula(goal_data)
            elif isinstance(goal_data, dict) and 'formula' in goal_data:
                goal = parse_formula(goal_data['formula'])
            else:
                raise ValueError(f"Goal format non supportato: {type(goal_data)}")
            
            logger.debug(f"🔍 Parsed {len(premises)} premises e 1 goal")
            
            # Esegui deduzione
            result = self.logic_engine.deduce(premises, goal)

            # Se formal reasoning fallisce e fallback è abilitato
            if not result.success and self.enable_fallback:
                logger.info("🔄 Formal reasoning fallito, fallback a pattern matching")
                return await self._fallback_reasoning(payload)

            # Prepara risultato
            reasoning_result = {
                'success': result.success,
                'proof_tree_id': result.proof_tree.proof_id if result.proof_tree else None,
                'steps_count': result.steps_count,
                'depth': result.depth,
                'duration': result.duration,
                'method': 'formal_logic'
            }

            if result.success and result.proof_tree:
                reasoning_result.update({
                    'proof_valid': result.proof_tree.is_valid,
                    'proof_steps': len(result.proof_tree.steps),
                    'natural_language_proof': result.proof_tree.to_natural_language()
                })
            else:
                reasoning_result['error'] = result.error_message

            # Pubblica evento
            bus = get_event_bus()
            await bus.publish(
                topic='reasoning.formal_completed',
                payload={
                    'stage_id': self.stage_id,
                    'success': result.success,
                    'proof_tree_id': reasoning_result.get('proof_tree_id'),
                    'steps_count': result.steps_count,
                    'depth': result.depth
                },
                source=self.stage_id,
                correlation_id=payload.trace_id
            )

            logger.info(f"✅ Formal reasoning completato: {result.success}")

            return payload.with_data(
                reasoning_result=reasoning_result,
                proof_tree=result.proof_tree.to_dict() if result.proof_tree else None,
                reasoning_depth=result.depth
            )
            
        except Exception as e:
            logger.error(f"❌ Errore formal reasoning: {e}")
            
            if self.enable_fallback:
                logger.info("🔄 Fallback a pattern matching")
                return await self._fallback_reasoning(payload)
            else:
                return payload.with_data(
                    reasoning_result={
                        'success': False,
                        'error': str(e),
                        'method': 'formal_logic'
                    }
                )
    
    async def _fallback_reasoning(self, payload: PipelinePayload) -> PipelinePayload:
        """Fallback pattern matching reasoning."""
        logger.debug(f"🔄 Stage {self.stage_id}: fallback pattern matching")
        
        # Simula pattern matching
        await asyncio.sleep(0.005)
        
        statement = payload.data.get('statement', '')
        
        # Pattern matching semplice
        patterns_found = []
        confidence = 0.5
        
        if 'if' in statement.lower() and 'then' in statement.lower():
            patterns_found.append('implication')
            confidence += 0.2
        
        if 'all' in statement.lower() or 'every' in statement.lower():
            patterns_found.append('universal')
            confidence += 0.1
        
        if 'some' in statement.lower() or 'exists' in statement.lower():
            patterns_found.append('existential')
            confidence += 0.1
        
        reasoning_result = {
            'success': True,
            'method': 'pattern_matching',
            'patterns_found': patterns_found,
            'confidence': min(confidence, 1.0),
            'fallback_used': True
        }
        
        # Pubblica evento
        bus = get_event_bus()
        await bus.publish(
            topic='reasoning.fallback_completed',
            payload={
                'stage_id': self.stage_id,
                'patterns_found': patterns_found,
                'confidence': confidence
            },
            source=self.stage_id,
            correlation_id=payload.trace_id
        )
        
        logger.info(f"✅ Fallback reasoning completato: {len(patterns_found)} pattern")
        
        return payload.with_data(
            reasoning_result=reasoning_result,
            reasoning_depth=1  # Shallow reasoning
        )
    
    def get_logic_engine_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche Logic Engine."""
        if self.logic_engine:
            return self.logic_engine.get_statistics()
        return {
            'logic_engine_available': False,
            'proofs_attempted': 0,
            'proofs_successful': 0,
            'success_rate': 0.0
        }
