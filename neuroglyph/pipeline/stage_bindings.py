"""
NEUROGLYPH Stage Bindings
Stage concreti per componenti esistenti
"""

import asyncio
import logging
from typing import Dict, Any, Optional

from .pipeline_engine import PipelineStage, PipelinePayload
from ..bus.event_bus import get_event_bus

# Import lazy per evitare cicli
try:
    from ..cognitive.validation_structures import ValidationResult
    from ..cognitive.adaptive_patcher import NGAdaptivePatcher
    from ..learning.pattern_extractor import PatternExtractor
    from ..learning.knowledge_graph_builder import KnowledgeGraphBuilder
    COMPONENTS_AVAILABLE = True
except ImportError:
    COMPONENTS_AVAILABLE = False

logger = logging.getLogger(__name__)


class VRValidatorStage(PipelineStage):
    """
    Stage per Parser + Validator.
    
    Input: codice sorgente
    Output: ValidationResult
    """
    
    def __init__(self, stage_id: str, config: Optional[Dict[str, Any]] = None):
        """Inizializza VR Validator stage."""
        super().__init__(stage_id, config)
        
        # TODO: Inizializza parser e validator quando disponibili
        self.parser = None
        self.validator = None
        
        logger.info(f"🔍 VRValidatorStage {stage_id} inizializzato")
    
    async def process(self, payload: PipelinePayload) -> PipelinePayload:
        """
        Processa codice sorgente → ValidationResult.
        
        Args:
            payload: Payload con 'source_code'
            
        Returns:
            Payload con 'validation_result'
        """
        source_code = payload.data.get('source_code', '')
        
        if not source_code:
            raise ValueError("source_code mancante nel payload")
        
        # Simula parsing e validazione
        await asyncio.sleep(0.01)  # Simula processing
        
        # Per ora crea ValidationResult mock
        if COMPONENTS_AVAILABLE:
            validation_result = ValidationResult()
            # TODO: Parsing e validazione reali
        else:
            validation_result = {
                'is_valid': True,
                'errors': [],
                'warnings': []
            }
        
        # Pubblica evento
        bus = get_event_bus()
        await bus.publish(
            topic='validation.completed',
            payload={
                'stage_id': self.stage_id,
                'validation_result': validation_result,
                'source_code_length': len(source_code)
            },
            source=self.stage_id,
            correlation_id=payload.trace_id
        )
        
        return payload.with_data(
            validation_result=validation_result,
            parsed_ast=f"mock_ast_for_{len(source_code)}_chars"
        )


class ErrorAnalysisStage(PipelineStage):
    """
    Stage per analisi errori.
    
    Input: ValidationResult
    Output: ErrorAnalysis
    """
    
    def __init__(self, stage_id: str, config: Optional[Dict[str, Any]] = None):
        """Inizializza Error Analysis stage."""
        super().__init__(stage_id, config)
        
        # TODO: Inizializza error analyzer quando disponibile
        self.error_analyzer = None
        
        logger.info(f"🔬 ErrorAnalysisStage {stage_id} inizializzato")
    
    async def process(self, payload: PipelinePayload) -> PipelinePayload:
        """
        Processa ValidationResult → ErrorAnalysis.
        
        Args:
            payload: Payload con 'validation_result'
            
        Returns:
            Payload con 'error_analysis'
        """
        validation_result = payload.data.get('validation_result')
        
        if not validation_result:
            raise ValueError("validation_result mancante nel payload")
        
        # Simula analisi errori
        await asyncio.sleep(0.005)  # Simula processing
        
        # Crea error analysis mock
        if COMPONENTS_AVAILABLE and hasattr(validation_result, 'is_valid'):
            # ValidationResult reale
            is_valid = validation_result.is_valid
            has_errors = validation_result.has_errors if hasattr(validation_result, 'has_errors') else False
        else:
            # ValidationResult mock (dict)
            is_valid = validation_result.get('is_valid', True) if isinstance(validation_result, dict) else True
            has_errors = not is_valid

        error_analysis = {
            'error_count': 0 if is_valid else 2,
            'error_patterns': ['syntax_error', 'import_error'] if has_errors else [],
            'severity': 'medium' if has_errors else 'low',
            'suggested_fixes': ['add_import', 'fix_syntax'] if has_errors else []
        }
        
        # Pubblica evento
        bus = get_event_bus()
        await bus.publish(
            topic='analysis.completed',
            payload={
                'stage_id': self.stage_id,
                'error_analysis': error_analysis
            },
            source=self.stage_id,
            correlation_id=payload.trace_id
        )
        
        return payload.with_data(error_analysis=error_analysis)


class PatchStage(PipelineStage):
    """
    Stage per AdaptivePatcher.
    
    Input: ValidationResult + ErrorAnalysis
    Output: PatchResults
    """
    
    def __init__(self, stage_id: str, config: Optional[Dict[str, Any]] = None):
        """Inizializza Patch stage."""
        super().__init__(stage_id, config)
        
        # Inizializza AdaptivePatcher se disponibile
        self.patcher = None
        if COMPONENTS_AVAILABLE:
            try:
                db_path = config.get('db_path', 'pipeline_learning.db') if config else 'pipeline_learning.db'
                self.patcher = NGAdaptivePatcher(db_path=db_path)
                logger.info(f"✅ AdaptivePatcher inizializzato con DB: {db_path}")
            except Exception as e:
                logger.warning(f"⚠️ Errore inizializzazione AdaptivePatcher: {e}")
        
        logger.info(f"🔧 PatchStage {stage_id} inizializzato")
    
    async def process(self, payload: PipelinePayload) -> PipelinePayload:
        """
        Processa ValidationResult → PatchResults.
        
        Args:
            payload: Payload con 'validation_result' e 'error_analysis'
            
        Returns:
            Payload con 'patch_results'
        """
        validation_result = payload.data.get('validation_result')
        error_analysis = payload.data.get('error_analysis')
        
        if not validation_result:
            raise ValueError("validation_result mancante nel payload")
        
        # Simula patching
        await asyncio.sleep(0.02)  # Simula processing
        
        patch_results = []
        
        if self.patcher and COMPONENTS_AVAILABLE:
            try:
                # Usa AdaptivePatcher reale
                patch_results = self.patcher.patch_validation_errors(validation_result)
            except Exception as e:
                logger.error(f"❌ Errore AdaptivePatcher: {e}")
                patch_results = []
        else:
            # Mock patch results
            if error_analysis and error_analysis.get('error_count', 0) > 0:
                patch_results = [
                    {
                        'patch_id': f'patch_{i}',
                        'success': True,
                        'description': f'Fixed {error_type}'
                    }
                    for i, error_type in enumerate(error_analysis.get('error_patterns', []))
                ]
        
        # Pubblica evento
        bus = get_event_bus()
        await bus.publish(
            topic='patching.completed',
            payload={
                'stage_id': self.stage_id,
                'patch_count': len(patch_results),
                'success_count': sum(1 for p in patch_results if p.get('success', False))
            },
            source=self.stage_id,
            correlation_id=payload.trace_id
        )
        
        return payload.with_data(patch_results=patch_results)


class LearnerStage(PipelineStage):
    """
    Stage per PatternExtractor + KnowledgeGraph.
    
    Input: PatchResults
    Output: LearningUpdate
    """
    
    def __init__(self, stage_id: str, config: Optional[Dict[str, Any]] = None):
        """Inizializza Learner stage."""
        super().__init__(stage_id, config)
        
        # Inizializza learning components se disponibili
        self.pattern_extractor = None
        self.knowledge_graph = None
        
        if COMPONENTS_AVAILABLE:
            try:
                db_path = config.get('db_path', 'pipeline_learning.db') if config else 'pipeline_learning.db'
                self.pattern_extractor = PatternExtractor(db_path)
                self.knowledge_graph = KnowledgeGraphBuilder(db_path.replace('.db', '_kg.db'))
                logger.info(f"✅ Learning components inizializzati")
            except Exception as e:
                logger.warning(f"⚠️ Errore inizializzazione learning: {e}")
        
        logger.info(f"🧠 LearnerStage {stage_id} inizializzato")
    
    async def process(self, payload: PipelinePayload) -> PipelinePayload:
        """
        Processa PatchResults → LearningUpdate.
        
        Args:
            payload: Payload con 'patch_results' e 'error_analysis'
            
        Returns:
            Payload con 'learning_update'
        """
        patch_results = payload.data.get('patch_results', [])
        error_analysis = payload.data.get('error_analysis', {})
        
        # Simula learning
        await asyncio.sleep(0.01)  # Simula processing
        
        learning_update = {
            'patterns_updated': 0,
            'knowledge_graph_nodes': 0,
            'knowledge_graph_edges': 0
        }
        
        if self.knowledge_graph and patch_results:
            try:
                # Aggiorna knowledge graph per patch riuscite
                for patch_result in patch_results:
                    if patch_result.get('success', False):
                        error_types = error_analysis.get('error_patterns', ['unknown'])
                        for error_type in error_types:
                            self.knowledge_graph.create_error_patch_pattern_chain(
                                error_type=error_type,
                                patch_id=patch_result.get('patch_id', 'unknown'),
                                pattern_id=f"pattern_{error_type}"
                            )
                
                # Ottieni stats aggiornate
                kg_stats = self.knowledge_graph.get_summary()
                learning_update.update({
                    'knowledge_graph_nodes': kg_stats.get('total_nodes', 0),
                    'knowledge_graph_edges': kg_stats.get('total_edges', 0)
                })
                
            except Exception as e:
                logger.error(f"❌ Errore aggiornamento KG: {e}")
        
        if self.pattern_extractor:
            try:
                # Ottieni stats pattern extractor
                pe_stats = self.pattern_extractor.get_stats()
                learning_update['patterns_updated'] = pe_stats.get('total_patterns', 0)
                
            except Exception as e:
                logger.error(f"❌ Errore stats pattern extractor: {e}")
        
        # Pubblica evento
        bus = get_event_bus()
        await bus.publish(
            topic='learning.completed',
            payload={
                'stage_id': self.stage_id,
                'learning_update': learning_update
            },
            source=self.stage_id,
            correlation_id=payload.trace_id
        )
        
        return payload.with_data(learning_update=learning_update)
