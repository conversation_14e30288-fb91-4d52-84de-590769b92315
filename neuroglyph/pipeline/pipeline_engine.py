"""
NEUROGLYPH Pipeline Engine
Motore DAG asincrono basato su asyncio.TaskGroup
"""

import asyncio
import time
import json
import logging
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum
import yaml
from pathlib import Path

logger = logging.getLogger(__name__)


class StageStatus(Enum):
    """Stati di uno stage pipeline."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass(frozen=True)
class PipelinePayload:
    """Payload immutabile per pipeline."""
    
    stage_id: str
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    trace_id: str = field(default_factory=lambda: f"trace_{int(time.time() * 1000)}")
    timestamp: float = field(default_factory=time.time)
    
    def with_data(self, **kwargs) -> 'PipelinePayload':
        """Crea nuovo payload con dati aggiornati."""
        new_data = {**self.data, **kwargs}
        return PipelinePayload(
            stage_id=self.stage_id,
            data=new_data,
            metadata=self.metadata,
            trace_id=self.trace_id,
            timestamp=self.timestamp
        )
    
    def with_metadata(self, **kwargs) -> 'PipelinePayload':
        """Crea nuovo payload con metadata aggiornati."""
        new_metadata = {**self.metadata, **kwargs}
        return PipelinePayload(
            stage_id=self.stage_id,
            data=self.data,
            metadata=new_metadata,
            trace_id=self.trace_id,
            timestamp=self.timestamp
        )


@dataclass
class StageMetrics:
    """Metriche per stage pipeline."""
    
    executions: int = 0
    successes: int = 0
    failures: int = 0
    total_duration: float = 0.0
    last_execution: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """Calcola success rate."""
        return self.successes / self.executions if self.executions > 0 else 0.0
    
    @property
    def avg_duration(self) -> float:
        """Calcola durata media."""
        return self.total_duration / self.executions if self.executions > 0 else 0.0


class PipelineStage(ABC):
    """
    Stage base per pipeline DAG.
    
    Ogni stage ha hooks before/after e gestione errori.
    """
    
    def __init__(self, stage_id: str, config: Optional[Dict[str, Any]] = None):
        """
        Inizializza stage.
        
        Args:
            stage_id: ID univoco dello stage
            config: Configurazione stage
        """
        self.stage_id = stage_id
        self.config = config or {}
        self.status = StageStatus.PENDING
        self.metrics = StageMetrics()
        
        # Hooks
        self.before_hooks: List[Callable] = []
        self.after_hooks: List[Callable] = []
        
        logger.info(f"🔧 Stage {stage_id} inizializzato")
    
    async def execute(self, payload: PipelinePayload) -> PipelinePayload:
        """
        Esegue stage con hooks e gestione errori.
        
        Args:
            payload: Payload di input
            
        Returns:
            Payload di output
        """
        start_time = time.time()
        self.status = StageStatus.RUNNING
        
        try:
            # Before hooks
            for hook in self.before_hooks:
                await self._run_hook(hook, payload)
            
            # Esecuzione principale
            logger.debug(f"🚀 Esecuzione stage {self.stage_id} (trace: {payload.trace_id})")
            result = await self.process(payload)
            
            # After hooks
            for hook in self.after_hooks:
                await self._run_hook(hook, result)
            
            # Aggiorna metriche successo
            duration = time.time() - start_time
            self.metrics.executions += 1
            self.metrics.successes += 1
            self.metrics.total_duration += duration
            self.metrics.last_execution = time.time()
            
            self.status = StageStatus.COMPLETED
            
            logger.debug(f"✅ Stage {self.stage_id} completato in {duration:.3f}s")
            return result
            
        except Exception as e:
            # Aggiorna metriche fallimento
            duration = time.time() - start_time
            self.metrics.executions += 1
            self.metrics.failures += 1
            self.metrics.total_duration += duration
            self.metrics.last_execution = time.time()
            
            self.status = StageStatus.FAILED
            
            logger.error(f"❌ Stage {self.stage_id} fallito: {e}")
            raise
    
    @abstractmethod
    async def process(self, payload: PipelinePayload) -> PipelinePayload:
        """
        Logica principale dello stage.
        
        Args:
            payload: Payload di input
            
        Returns:
            Payload di output
        """
        pass
    
    async def _run_hook(self, hook: Callable, payload: PipelinePayload):
        """Esegue hook con gestione errori."""
        try:
            if asyncio.iscoroutinefunction(hook):
                await hook(payload)
            else:
                hook(payload)
        except Exception as e:
            logger.warning(f"⚠️ Hook fallito in stage {self.stage_id}: {e}")
    
    def add_before_hook(self, hook: Callable):
        """Aggiunge hook before."""
        self.before_hooks.append(hook)
    
    def add_after_hook(self, hook: Callable):
        """Aggiunge hook after."""
        self.after_hooks.append(hook)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Ottiene metriche stage."""
        return {
            'stage_id': self.stage_id,
            'status': self.status.value,
            'executions': self.metrics.executions,
            'successes': self.metrics.successes,
            'failures': self.metrics.failures,
            'success_rate': self.metrics.success_rate,
            'avg_duration': self.metrics.avg_duration,
            'last_execution': self.metrics.last_execution
        }


@dataclass
class PipelineConfig:
    """Configurazione pipeline da YAML."""
    
    name: str
    stages: List[Dict[str, Any]]
    dependencies: Dict[str, List[str]] = field(default_factory=dict)
    config: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'PipelineConfig':
        """Carica configurazione da file YAML."""
        with open(yaml_path, 'r') as f:
            data = yaml.safe_load(f)
        
        return cls(
            name=data['name'],
            stages=data['stages'],
            dependencies=data.get('dependencies', {}),
            config=data.get('config', {})
        )


class PipelineEngine:
    """
    Motore pipeline DAG asincrono.
    
    Features:
    - DAG execution con asyncio.TaskGroup
    - Dependency resolution automatico
    - Metrics collection per stage
    - Error handling e retry
    - Structured logging
    """
    
    def __init__(self, config: PipelineConfig):
        """
        Inizializza pipeline engine.
        
        Args:
            config: Configurazione pipeline
        """
        self.config = config
        self.stages: Dict[str, PipelineStage] = {}
        self.stage_registry: Dict[str, type] = {}
        
        # Stato pipeline
        self.is_running = False
        self.execution_count = 0
        
        logger.info(f"🏗️ PipelineEngine inizializzato: {config.name}")
    
    def register_stage(self, stage_type: str, stage_class: type):
        """Registra tipo di stage."""
        self.stage_registry[stage_type] = stage_class
        logger.debug(f"📝 Registrato stage type: {stage_type}")
    
    def build_pipeline(self):
        """Costruisce pipeline da configurazione."""
        # Crea stages
        for stage_config in self.config.stages:
            stage_id = stage_config['id']
            stage_type = stage_config['type']
            stage_params = stage_config.get('config', {})
            
            if stage_type not in self.stage_registry:
                raise ValueError(f"Stage type non registrato: {stage_type}")
            
            stage_class = self.stage_registry[stage_type]
            stage = stage_class(stage_id, stage_params)
            self.stages[stage_id] = stage
        
        logger.info(f"✅ Pipeline costruita con {len(self.stages)} stages")
    
    async def execute(self, initial_payload: PipelinePayload) -> Dict[str, Any]:
        """
        Esegue pipeline DAG.
        
        Args:
            initial_payload: Payload iniziale
            
        Returns:
            Risultati esecuzione
        """
        if not self.stages:
            raise RuntimeError("Pipeline non costruita. Chiamare build_pipeline() prima.")
        
        self.is_running = True
        self.execution_count += 1
        start_time = time.time()
        
        try:
            # Risolvi ordine esecuzione DAG
            execution_order = self._resolve_dag_order()
            
            logger.info(f"🚀 Avvio pipeline {self.config.name} (trace: {initial_payload.trace_id})")
            logger.debug(f"   Ordine esecuzione: {execution_order}")
            
            # Esegui stages in ordine
            current_payload = initial_payload
            results = {}
            
            async with asyncio.TaskGroup() as tg:
                for stage_id in execution_order:
                    stage = self.stages[stage_id]
                    
                    # Per ora esecuzione seriale - TODO: parallelizzazione per stages indipendenti
                    current_payload = await stage.execute(current_payload)
                    results[stage_id] = current_payload.data
            
            duration = time.time() - start_time
            
            logger.info(f"✅ Pipeline {self.config.name} completata in {duration:.3f}s")
            
            return {
                'success': True,
                'duration': duration,
                'trace_id': initial_payload.trace_id,
                'results': results,
                'metrics': self.get_metrics()
            }
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"❌ Pipeline {self.config.name} fallita: {e}")
            
            return {
                'success': False,
                'duration': duration,
                'trace_id': initial_payload.trace_id,
                'error': str(e),
                'metrics': self.get_metrics()
            }
            
        finally:
            self.is_running = False
    
    def _resolve_dag_order(self) -> List[str]:
        """Risolve ordine esecuzione DAG con topological sort."""
        # Implementazione semplificata - ordine da config
        return [stage['id'] for stage in self.config.stages]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Ottiene metriche pipeline."""
        return {
            'pipeline_name': self.config.name,
            'execution_count': self.execution_count,
            'is_running': self.is_running,
            'stages': {
                stage_id: stage.get_metrics() 
                for stage_id, stage in self.stages.items()
            }
        }
    
    def get_stage(self, stage_id: str) -> Optional[PipelineStage]:
        """Ottiene stage per ID."""
        return self.stages.get(stage_id)
