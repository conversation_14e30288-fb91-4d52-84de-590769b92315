"""
NEUROGLYPH Pipeline Module
Orchestrazione DAG asincrona per Parser → Analyzer → Patcher → Learner
"""

from .pipeline_engine import PipelineEngine, PipelineStage, PipelineConfig
from .stage_bindings import (
    VRValidatorStage, ErrorAnalysisStage, PatchStage, LearnerStage
)

__all__ = [
    'PipelineEngine',
    'PipelineStage', 
    'PipelineConfig',
    'VRValidatorStage',
    'ErrorAnalysisStage',
    'PatchStage',
    'LearnerStage'
]
