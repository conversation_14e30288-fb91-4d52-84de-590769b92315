"""
NEUROGLYPH Dual-Validation System

Sistema di validazione duale che combina:
- Validazione simbolica (proof trees, logic engine)
- Validazione probabilistica (confidence scoring, LLM verification)
- Controllo coerenza tra i due approcci
- Blocco automatico output non verificabili

Fase 7.1 - Zero-Hallucination Validation
"""

import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum

from ..logic.logic_engine import FormalLogicEngine
from ..logic.proof_tree import ProofTree
from ..logic.formula import Formula
from .open_world_validator import OpenWorldValidator, FactCheckResult

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """Livelli di validazione."""
    STRICT = "strict"      # Richiede validazione simbolica + probabilistica
    MODERATE = "moderate"  # Richiede almeno una validazione
    PERMISSIVE = "permissive"  # Accetta con warning


class ValidationResult(Enum):
    """Risultati di validazione."""
    VERIFIED = "verified"          # Completamente verificato
    PROBABLE = "probable"          # Probabilmente corretto
    UNCERTAIN = "uncertain"        # Incerto, richiede attenzione
    CONTRADICTED = "contradicted"  # Contraddetto da evidenze
    BLOCKED = "blocked"           # Bloccato per sicurezza


@dataclass
class DualValidationResult:
    """Risultato di validazione duale."""
    input_claim: str
    symbolic_validation: Optional[bool] = None
    probabilistic_validation: Optional[bool] = None
    symbolic_confidence: float = 0.0
    probabilistic_confidence: float = 0.0
    combined_confidence: float = 0.0
    validation_result: ValidationResult = ValidationResult.UNCERTAIN
    proof_tree: Optional[ProofTree] = None
    fact_check_result: Optional[FactCheckResult] = None
    coherence_score: float = 0.0
    warnings: List[str] = field(default_factory=list)
    blocking_reasons: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)


class SymbolicValidator:
    """Validator simbolico basato su logic engine."""
    
    def __init__(self, logic_engine: Optional[FormalLogicEngine] = None):
        """
        Inizializza symbolic validator.
        
        Args:
            logic_engine: Engine logico per validazione
        """
        self.logic_engine = logic_engine or FormalLogicEngine(max_depth=8)
        
        # Metriche
        self.validations_performed = 0
        self.successful_validations = 0
        
        logger.debug("🔧 SymbolicValidator inizializzato")
    
    def validate_claim(self, claim: str, context_formulas: Optional[List[Formula]] = None) -> Tuple[bool, float, Optional[ProofTree]]:
        """
        Valida claim usando reasoning simbolico.
        
        Args:
            claim: Affermazione da validare
            context_formulas: Formule di contesto per reasoning
            
        Returns:
            Tupla (is_valid, confidence, proof_tree)
        """
        self.validations_performed += 1
        
        try:
            # Converte claim in formula logica
            goal_formula = self._parse_claim_to_formula(claim)
            
            if not goal_formula:
                return False, 0.0, None
            
            # Usa formule di contesto come premesse
            premises = context_formulas or []
            
            # Tenta deduzione
            result = self.logic_engine.deduce(premises, goal_formula)
            
            if result.success and result.proof_tree:
                self.successful_validations += 1
                
                # Calcola confidence basata su qualità del proof
                confidence = self._calculate_proof_confidence(result.proof_tree)
                
                return True, confidence, result.proof_tree
            else:
                # Tenta confutazione (prova che la claim è falsa)
                negated_goal = self._negate_formula(goal_formula)
                refutation_result = self.logic_engine.deduce(premises, negated_goal)
                
                if refutation_result.success:
                    # La negazione è provabile → claim è falsa
                    return False, 0.9, refutation_result.proof_tree
                else:
                    # Non provabile né confutabile → incerto
                    return False, 0.1, None
        
        except Exception as e:
            logger.error(f"❌ Errore validazione simbolica: {e}")
            return False, 0.0, None
    
    def _parse_claim_to_formula(self, claim: str) -> Optional[Formula]:
        """Converte claim testuale in formula logica."""
        # Implementazione semplificata - in produzione usare parser NLP
        from ..logic.formula import Predicate, Variable
        
        # Pattern semplici per demo
        if "gcd" in claim.lower() or "greatest common divisor" in claim.lower():
            return Predicate("greatest_common_divisor", [Variable("a"), Variable("b")])
        elif "prime" in claim.lower():
            return Predicate("prime", [Variable("n")])
        elif "fibonacci" in claim.lower():
            return Predicate("fibonacci", [Variable("n")])
        else:
            # Fallback: crea predicato generico
            words = claim.split()[:3]  # Prime 3 parole
            predicate_name = "_".join(w.lower() for w in words if w.isalpha())
            return Predicate(predicate_name, [Variable("x")])
    
    def _negate_formula(self, formula: Formula) -> Formula:
        """Nega una formula logica."""
        from ..logic.formula import Negation
        return Negation(formula)
    
    def _calculate_proof_confidence(self, proof_tree: ProofTree) -> float:
        """Calcola confidence basata su qualità del proof."""
        if not proof_tree or not proof_tree.steps:
            return 0.0
        
        # Fattori di qualità
        depth_score = min(proof_tree.depth / 5.0, 1.0)  # Profondità ottimale ~5
        steps_score = min(len(proof_tree.steps) / 10.0, 1.0)  # ~10 step ottimali
        
        # Penalizza proof troppo semplici o troppo complessi
        if proof_tree.depth < 2:
            depth_score *= 0.7
        if proof_tree.depth > 10:
            depth_score *= 0.8
        
        return (depth_score + steps_score) / 2.0
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche validazione simbolica."""
        success_rate = (self.successful_validations / self.validations_performed 
                       if self.validations_performed > 0 else 0.0)
        
        return {
            'validations_performed': self.validations_performed,
            'successful_validations': self.successful_validations,
            'success_rate': success_rate
        }


class ProbabilisticValidator:
    """Validator probabilistico basato su confidence scoring."""
    
    def __init__(self):
        """Inizializza probabilistic validator."""
        # Metriche
        self.validations_performed = 0
        self.high_confidence_validations = 0
        
        # Soglie confidence
        self.high_confidence_threshold = 0.8
        self.low_confidence_threshold = 0.3
        
        logger.debug("📊 ProbabilisticValidator inizializzato")
    
    def validate_claim(self, claim: str, context: Optional[Dict[str, Any]] = None) -> Tuple[bool, float]:
        """
        Valida claim usando scoring probabilistico.
        
        Args:
            claim: Affermazione da validare
            context: Contesto aggiuntivo
            
        Returns:
            Tupla (is_probable, confidence)
        """
        self.validations_performed += 1
        
        try:
            # Calcola confidence basata su multiple euristiche
            confidence_scores = []
            
            # 1. Lunghezza e complessità
            length_score = self._calculate_length_score(claim)
            confidence_scores.append(length_score)
            
            # 2. Presenza di parole chiave affidabili
            keyword_score = self._calculate_keyword_score(claim)
            confidence_scores.append(keyword_score)
            
            # 3. Struttura grammaticale
            grammar_score = self._calculate_grammar_score(claim)
            confidence_scores.append(grammar_score)
            
            # 4. Coerenza con contesto
            if context:
                context_score = self._calculate_context_score(claim, context)
                confidence_scores.append(context_score)
            
            # Combina score
            combined_confidence = sum(confidence_scores) / len(confidence_scores)
            
            # Determina validazione
            is_probable = combined_confidence >= self.low_confidence_threshold
            
            if combined_confidence >= self.high_confidence_threshold:
                self.high_confidence_validations += 1
            
            return is_probable, combined_confidence
        
        except Exception as e:
            logger.error(f"❌ Errore validazione probabilistica: {e}")
            return False, 0.0
    
    def _calculate_length_score(self, claim: str) -> float:
        """Calcola score basato su lunghezza claim."""
        length = len(claim.split())
        
        # Lunghezza ottimale: 5-20 parole
        if 5 <= length <= 20:
            return 0.8
        elif 3 <= length <= 30:
            return 0.6
        else:
            return 0.3
    
    def _calculate_keyword_score(self, claim: str) -> float:
        """Calcola score basato su parole chiave affidabili."""
        reliable_keywords = [
            'theorem', 'proof', 'algorithm', 'formula', 'equation',
            'definition', 'property', 'function', 'method', 'result'
        ]
        
        unreliable_keywords = [
            'maybe', 'possibly', 'might', 'could', 'perhaps',
            'probably', 'seems', 'appears', 'allegedly'
        ]
        
        claim_lower = claim.lower()
        
        reliable_count = sum(1 for kw in reliable_keywords if kw in claim_lower)
        unreliable_count = sum(1 for kw in unreliable_keywords if kw in claim_lower)
        
        # Score basato su presenza parole affidabili vs non affidabili
        if unreliable_count > 0:
            return 0.2
        elif reliable_count > 0:
            return 0.9
        else:
            return 0.5
    
    def _calculate_grammar_score(self, claim: str) -> float:
        """Calcola score basato su struttura grammaticale."""
        # Controlli semplici per grammatica
        score = 0.5  # Base score
        
        # Ha verbo?
        verbs = ['is', 'are', 'was', 'were', 'has', 'have', 'can', 'will', 'equals']
        if any(verb in claim.lower().split() for verb in verbs):
            score += 0.2
        
        # Ha punteggiatura appropriata?
        if claim.endswith('.') or claim.endswith('!'):
            score += 0.1
        
        # Non ha errori evidenti?
        if not any(char in claim for char in ['??', '!!', '...']):
            score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_context_score(self, claim: str, context: Dict[str, Any]) -> float:
        """Calcola score basato su coerenza con contesto."""
        score = 0.5  # Base score
        
        # Verifica coerenza con dominio
        if 'domain' in context:
            domain = context['domain'].lower()
            claim_lower = claim.lower()
            
            domain_keywords = {
                'math': ['number', 'equation', 'formula', 'theorem'],
                'logic': ['proof', 'implies', 'therefore', 'conclusion'],
                'coding': ['function', 'algorithm', 'method', 'implementation']
            }
            
            if domain in domain_keywords:
                keywords = domain_keywords[domain]
                if any(kw in claim_lower for kw in keywords):
                    score += 0.3
        
        return min(score, 1.0)
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche validazione probabilistica."""
        high_confidence_rate = (self.high_confidence_validations / self.validations_performed 
                               if self.validations_performed > 0 else 0.0)
        
        return {
            'validations_performed': self.validations_performed,
            'high_confidence_validations': self.high_confidence_validations,
            'high_confidence_rate': high_confidence_rate,
            'high_confidence_threshold': self.high_confidence_threshold,
            'low_confidence_threshold': self.low_confidence_threshold
        }


class DualValidationSystem:
    """
    Sistema di validazione duale NEUROGLYPH.
    
    Combina validazione simbolica e probabilistica per garantire
    zero hallucination con controllo di coerenza.
    """
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.STRICT):
        """
        Inizializza dual validation system.
        
        Args:
            validation_level: Livello di validazione richiesto
        """
        self.validation_level = validation_level
        
        # Componenti di validazione
        self.symbolic_validator = SymbolicValidator()
        self.probabilistic_validator = ProbabilisticValidator()
        self.open_world_validator = OpenWorldValidator()
        
        # Soglie per decisioni
        self.coherence_threshold = 0.7
        self.blocking_threshold = 0.3
        
        # Metriche
        self.total_validations = 0
        self.verified_outputs = 0
        self.blocked_outputs = 0
        
        logger.info(f"🔒 DualValidationSystem inizializzato (livello: {validation_level.value})")
    
    def validate_output(self, claim: str, context: Optional[Dict[str, Any]] = None) -> DualValidationResult:
        """
        Valida output usando sistema duale.
        
        Args:
            claim: Affermazione da validare
            context: Contesto per validazione
            
        Returns:
            Risultato validazione duale
        """
        self.total_validations += 1
        
        logger.info(f"🔍 Validazione duale: {claim[:100]}...")
        
        result = DualValidationResult(input_claim=claim)
        
        try:
            # 1. Validazione simbolica
            symbolic_valid, symbolic_conf, proof_tree = self.symbolic_validator.validate_claim(claim)
            result.symbolic_validation = symbolic_valid
            result.symbolic_confidence = symbolic_conf
            result.proof_tree = proof_tree
            
            # 2. Validazione probabilistica
            prob_valid, prob_conf = self.probabilistic_validator.validate_claim(claim, context)
            result.probabilistic_validation = prob_valid
            result.probabilistic_confidence = prob_conf
            
            # 3. Validazione open-world (se richiesta)
            if self.validation_level == ValidationLevel.STRICT:
                fact_check = self.open_world_validator.verify_claim(claim)
                result.fact_check_result = fact_check
            
            # 4. Calcola coerenza tra validazioni
            result.coherence_score = self._calculate_coherence(result)
            
            # 5. Combina confidence
            result.combined_confidence = self._combine_confidence(result)
            
            # 6. Determina risultato finale
            result.validation_result = self._determine_final_result(result)
            
            # 7. Controlla se bloccare output
            self._check_blocking_conditions(result)
            
            # Aggiorna metriche
            if result.validation_result == ValidationResult.VERIFIED:
                self.verified_outputs += 1
            elif result.validation_result == ValidationResult.BLOCKED:
                self.blocked_outputs += 1
            
            logger.info(f"✅ Validazione completata: {result.validation_result.value} "
                       f"(confidence: {result.combined_confidence:.2f})")
            
            return result
        
        except Exception as e:
            logger.error(f"❌ Errore validazione duale: {e}")
            
            result.validation_result = ValidationResult.BLOCKED
            result.blocking_reasons.append(f"Errore validazione: {e}")
            self.blocked_outputs += 1
            
            return result
    
    def _calculate_coherence(self, result: DualValidationResult) -> float:
        """Calcola coerenza tra validazioni."""
        # Se entrambe le validazioni sono disponibili
        if result.symbolic_validation is not None and result.probabilistic_validation is not None:
            # Coerenza alta se entrambe concordano
            if result.symbolic_validation == result.probabilistic_validation:
                return 0.9
            else:
                # Coerenza basata su confidence
                conf_diff = abs(result.symbolic_confidence - result.probabilistic_confidence)
                return max(0.1, 1.0 - conf_diff)
        
        # Se solo una validazione è disponibile
        return 0.5
    
    def _combine_confidence(self, result: DualValidationResult) -> float:
        """Combina confidence da multiple validazioni."""
        confidences = []
        weights = []
        
        # Validazione simbolica (peso alto)
        if result.symbolic_validation is not None:
            confidences.append(result.symbolic_confidence)
            weights.append(0.4)
        
        # Validazione probabilistica (peso medio)
        if result.probabilistic_validation is not None:
            confidences.append(result.probabilistic_confidence)
            weights.append(0.3)
        
        # Validazione open-world (peso alto se disponibile)
        if result.fact_check_result:
            confidences.append(result.fact_check_result.confidence)
            weights.append(0.3)
        
        if not confidences:
            return 0.0
        
        # Media pesata
        weighted_sum = sum(c * w for c, w in zip(confidences, weights))
        total_weight = sum(weights)
        
        combined = weighted_sum / total_weight
        
        # Penalizza se coerenza è bassa
        if result.coherence_score < self.coherence_threshold:
            combined *= result.coherence_score
        
        return combined
    
    def _determine_final_result(self, result: DualValidationResult) -> ValidationResult:
        """Determina risultato finale di validazione."""
        # Controlla livello di validazione richiesto
        if self.validation_level == ValidationLevel.STRICT:
            # Richiede validazione simbolica + probabilistica + open-world
            if (result.symbolic_validation and 
                result.probabilistic_validation and 
                result.fact_check_result and 
                result.fact_check_result.is_verified and
                result.coherence_score >= self.coherence_threshold):
                return ValidationResult.VERIFIED
            elif result.combined_confidence < self.blocking_threshold:
                return ValidationResult.BLOCKED
            else:
                return ValidationResult.UNCERTAIN
        
        elif self.validation_level == ValidationLevel.MODERATE:
            # Richiede almeno una validazione forte
            if ((result.symbolic_validation and result.symbolic_confidence > 0.7) or
                (result.probabilistic_validation and result.probabilistic_confidence > 0.7)):
                return ValidationResult.PROBABLE
            elif result.combined_confidence < self.blocking_threshold:
                return ValidationResult.BLOCKED
            else:
                return ValidationResult.UNCERTAIN
        
        else:  # PERMISSIVE
            # Accetta con warning se confidence minima
            if result.combined_confidence > 0.5:
                return ValidationResult.PROBABLE
            else:
                return ValidationResult.UNCERTAIN
    
    def _check_blocking_conditions(self, result: DualValidationResult):
        """Controlla condizioni per bloccare output."""
        # Blocca se contraddizioni evidenti
        if result.fact_check_result and result.fact_check_result.contradictions:
            result.validation_result = ValidationResult.BLOCKED
            result.blocking_reasons.append("Contraddizioni trovate in fact-checking")
        
        # Blocca se coerenza troppo bassa
        if result.coherence_score < 0.3:
            result.validation_result = ValidationResult.BLOCKED
            result.blocking_reasons.append(f"Coerenza troppo bassa: {result.coherence_score:.2f}")
        
        # Blocca se confidence combinata troppo bassa
        if result.combined_confidence < self.blocking_threshold:
            result.validation_result = ValidationResult.BLOCKED
            result.blocking_reasons.append(f"Confidence troppo bassa: {result.combined_confidence:.2f}")
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche complete del sistema."""
        verification_rate = (self.verified_outputs / self.total_validations 
                           if self.total_validations > 0 else 0.0)
        
        blocking_rate = (self.blocked_outputs / self.total_validations 
                        if self.total_validations > 0 else 0.0)
        
        return {
            'total_validations': self.total_validations,
            'verified_outputs': self.verified_outputs,
            'blocked_outputs': self.blocked_outputs,
            'verification_rate': verification_rate,
            'blocking_rate': blocking_rate,
            'validation_level': self.validation_level.value,
            'symbolic_stats': self.symbolic_validator.get_validation_statistics(),
            'probabilistic_stats': self.probabilistic_validator.get_validation_statistics(),
            'open_world_stats': self.open_world_validator.get_validation_statistics()
        }
