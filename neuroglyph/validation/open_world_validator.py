"""
NEUROGLYPH Open-World Validator

Sistema di validazione open-world per fact-checking contro fonti reali:
- Wikipedia API integration
- Wikidata SPARQL queries  
- DBpedia knowledge base
- Real-world fact verification

Fase 7.1 - Zero-Hallucination Validation
"""

import logging
import time
import json
import requests
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from urllib.parse import quote
import sqlite3

logger = logging.getLogger(__name__)


@dataclass
class FactCheckResult:
    """Risultato di fact-checking."""
    claim: str
    is_verified: bool
    confidence: float
    sources: List[str] = field(default_factory=list)
    evidence: List[str] = field(default_factory=list)
    contradictions: List[str] = field(default_factory=list)
    verification_method: str = ""
    timestamp: float = field(default_factory=time.time)


@dataclass
class OpenWorldQuery:
    """Query per validazione open-world."""
    query_id: str
    query_text: str
    expected_answer: Optional[str] = None
    domain: str = "general"
    query_type: str = "factual"  # factual, mathematical, logical
    metadata: Dict[str, Any] = field(default_factory=dict)


class WikipediaValidator:
    """Validator per Wikipedia API."""
    
    def __init__(self):
        """Inizializza Wikipedia validator."""
        self.base_url = "https://en.wikipedia.org/api/rest_v1"
        self.search_url = "https://en.wikipedia.org/w/api.php"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'NEUROGLYPH/1.0 (https://github.com/neuroglyph) Educational Research'
        })
    
    def search_articles(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Cerca articoli Wikipedia per query.
        
        Args:
            query: Query di ricerca
            limit: Numero massimo risultati
            
        Returns:
            Lista di articoli trovati
        """
        try:
            params = {
                'action': 'query',
                'format': 'json',
                'list': 'search',
                'srsearch': query,
                'srlimit': limit
            }
            
            response = self.session.get(self.search_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'query' in data and 'search' in data['query']:
                return data['query']['search']
            
            return []
            
        except Exception as e:
            logger.error(f"❌ Errore ricerca Wikipedia: {e}")
            return []
    
    def get_article_content(self, title: str) -> Optional[str]:
        """
        Ottiene contenuto di un articolo Wikipedia.
        
        Args:
            title: Titolo articolo
            
        Returns:
            Contenuto articolo o None
        """
        try:
            # Usa extract API per ottenere riassunto
            params = {
                'action': 'query',
                'format': 'json',
                'titles': title,
                'prop': 'extracts',
                'exintro': True,
                'explaintext': True,
                'exsectionformat': 'plain'
            }
            
            response = self.session.get(self.search_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'query' in data and 'pages' in data['query']:
                pages = data['query']['pages']
                for page_id, page_data in pages.items():
                    if 'extract' in page_data:
                        return page_data['extract']
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Errore recupero articolo Wikipedia: {e}")
            return None
    
    def verify_fact(self, claim: str) -> FactCheckResult:
        """
        Verifica un fatto contro Wikipedia.
        
        Args:
            claim: Affermazione da verificare
            
        Returns:
            Risultato verifica
        """
        # Estrai entità chiave dalla claim
        entities = self._extract_entities(claim)
        
        evidence = []
        sources = []
        contradictions = []
        
        for entity in entities:
            # Cerca articoli correlati
            articles = self.search_articles(entity, limit=3)
            
            for article in articles:
                title = article['title']
                content = self.get_article_content(title)
                
                if content:
                    # Verifica se il contenuto supporta la claim
                    if self._content_supports_claim(claim, content):
                        evidence.append(f"Wikipedia: {title} - {content[:200]}...")
                        sources.append(f"https://en.wikipedia.org/wiki/{quote(title)}")
                    elif self._content_contradicts_claim(claim, content):
                        contradictions.append(f"Wikipedia: {title} - {content[:200]}...")
        
        # Determina risultato
        is_verified = len(evidence) > 0 and len(contradictions) == 0
        confidence = min(len(evidence) / max(len(entities), 1), 1.0)
        
        if contradictions:
            confidence = 0.0
        
        return FactCheckResult(
            claim=claim,
            is_verified=is_verified,
            confidence=confidence,
            sources=sources,
            evidence=evidence,
            contradictions=contradictions,
            verification_method="wikipedia_api"
        )
    
    def _extract_entities(self, claim: str) -> List[str]:
        """Estrae entità chiave da una claim."""
        # Implementazione semplificata - in produzione usare NER
        words = claim.split()
        
        # Cerca nomi propri (parole con maiuscola)
        entities = []
        for word in words:
            if word[0].isupper() and len(word) > 2:
                entities.append(word)
        
        # Cerca pattern numerici
        numbers = re.findall(r'\b\d{4}\b', claim)  # Anni
        entities.extend(numbers)
        
        return list(set(entities))
    
    def _content_supports_claim(self, claim: str, content: str) -> bool:
        """Verifica se il contenuto supporta la claim."""
        claim_lower = claim.lower()
        content_lower = content.lower()
        
        # Cerca parole chiave della claim nel contenuto
        claim_words = set(claim_lower.split())
        content_words = set(content_lower.split())
        
        # Se almeno 50% delle parole della claim sono nel contenuto
        overlap = len(claim_words.intersection(content_words))
        return overlap >= len(claim_words) * 0.5
    
    def _content_contradicts_claim(self, claim: str, content: str) -> bool:
        """Verifica se il contenuto contraddice la claim."""
        # Cerca pattern di contraddizione
        contradiction_patterns = [
            'not', 'never', 'false', 'incorrect', 'wrong',
            'however', 'but', 'although', 'despite'
        ]
        
        content_lower = content.lower()
        
        for pattern in contradiction_patterns:
            if pattern in content_lower:
                # Verifica se la contraddizione è relativa alla claim
                if any(word in content_lower for word in claim.lower().split()):
                    return True
        
        return False


class WikidataValidator:
    """Validator per Wikidata SPARQL."""
    
    def __init__(self):
        """Inizializza Wikidata validator."""
        self.sparql_endpoint = "https://query.wikidata.org/sparql"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'NEUROGLYPH/1.0 Educational Research',
            'Accept': 'application/sparql-results+json'
        })
    
    def query_sparql(self, sparql_query: str) -> Optional[Dict[str, Any]]:
        """
        Esegue query SPARQL su Wikidata.
        
        Args:
            sparql_query: Query SPARQL
            
        Returns:
            Risultati query o None
        """
        try:
            params = {'query': sparql_query}
            response = self.session.get(self.sparql_endpoint, params=params, timeout=15)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"❌ Errore query Wikidata: {e}")
            return None
    
    def verify_mathematical_fact(self, claim: str) -> FactCheckResult:
        """
        Verifica fatto matematico contro Wikidata.
        
        Args:
            claim: Affermazione matematica
            
        Returns:
            Risultato verifica
        """
        # Query per costanti matematiche note
        if "pi" in claim.lower() or "π" in claim:
            sparql = """
            SELECT ?item ?itemLabel ?value WHERE {
              ?item wdt:P31 wd:Q3968 .  # mathematical constant
              ?item rdfs:label "pi"@en .
              OPTIONAL { ?item wdt:P1181 ?value }
              SERVICE wikibase:label { bd:serviceParam wikibase:language "en" }
            }
            """
            
            results = self.query_sparql(sparql)
            
            if results and 'results' in results:
                evidence = []
                for binding in results['results']['bindings']:
                    if 'value' in binding:
                        evidence.append(f"Wikidata: π = {binding['value']['value']}")
                
                return FactCheckResult(
                    claim=claim,
                    is_verified=len(evidence) > 0,
                    confidence=1.0 if evidence else 0.0,
                    evidence=evidence,
                    verification_method="wikidata_sparql"
                )
        
        # Fallback per altri fatti matematici
        return FactCheckResult(
            claim=claim,
            is_verified=False,
            confidence=0.0,
            verification_method="wikidata_sparql_no_match"
        )


class OpenWorldValidator:
    """
    Validator principale per validazione open-world.
    
    Coordina Wikipedia, Wikidata e altre fonti per fact-checking completo.
    """
    
    def __init__(self, db_path: str = "open_world_validation.db"):
        """
        Inizializza Open World Validator.
        
        Args:
            db_path: Percorso database per cache risultati
        """
        self.db_path = db_path
        self.db = None
        
        # Validators specifici
        self.wikipedia_validator = WikipediaValidator()
        self.wikidata_validator = WikidataValidator()
        
        # Cache per evitare richieste duplicate
        self.verification_cache: Dict[str, FactCheckResult] = {}
        
        # Metriche
        self.total_verifications = 0
        self.verified_facts = 0
        self.contradicted_facts = 0
        
        self._initialize_database()
        
        logger.info("🌍 OpenWorldValidator inizializzato")
        logger.info(f"   - Database: {self.db_path}")
    
    def _initialize_database(self):
        """Inizializza database per cache."""
        self.db = sqlite3.connect(self.db_path, check_same_thread=False)
        
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS fact_verifications (
                claim_hash TEXT PRIMARY KEY,
                claim TEXT NOT NULL,
                is_verified BOOLEAN NOT NULL,
                confidence REAL NOT NULL,
                sources TEXT,  -- JSON
                evidence TEXT,  -- JSON
                contradictions TEXT,  -- JSON
                verification_method TEXT,
                timestamp REAL NOT NULL
            )
        """)
        
        self.db.commit()
    
    def verify_claim(self, claim: str, use_cache: bool = True) -> FactCheckResult:
        """
        Verifica una claim contro fonti open-world.
        
        Args:
            claim: Affermazione da verificare
            use_cache: Usa cache se disponibile
            
        Returns:
            Risultato verifica aggregato
        """
        self.total_verifications += 1
        
        # Controlla cache
        claim_hash = str(hash(claim))
        if use_cache and claim_hash in self.verification_cache:
            logger.debug(f"🔄 Cache hit per claim: {claim[:50]}...")
            return self.verification_cache[claim_hash]
        
        # Controlla database cache
        if use_cache:
            cached_result = self._get_cached_result(claim_hash)
            if cached_result:
                self.verification_cache[claim_hash] = cached_result
                return cached_result
        
        logger.info(f"🔍 Verificando claim: {claim[:100]}...")
        
        # Verifica con multiple fonti
        results = []
        
        # 1. Wikipedia
        try:
            wiki_result = self.wikipedia_validator.verify_fact(claim)
            results.append(wiki_result)
        except Exception as e:
            logger.error(f"❌ Errore verifica Wikipedia: {e}")
        
        # 2. Wikidata (per fatti matematici)
        if self._is_mathematical_claim(claim):
            try:
                wikidata_result = self.wikidata_validator.verify_mathematical_fact(claim)
                results.append(wikidata_result)
            except Exception as e:
                logger.error(f"❌ Errore verifica Wikidata: {e}")
        
        # Aggrega risultati
        aggregated_result = self._aggregate_results(claim, results)
        
        # Cache risultato
        self.verification_cache[claim_hash] = aggregated_result
        self._cache_result(claim_hash, aggregated_result)
        
        # Aggiorna metriche
        if aggregated_result.is_verified:
            self.verified_facts += 1
        elif aggregated_result.contradictions:
            self.contradicted_facts += 1
        
        return aggregated_result
    
    def _is_mathematical_claim(self, claim: str) -> bool:
        """Verifica se una claim è matematica."""
        math_keywords = [
            'pi', 'π', 'euler', 'fibonacci', 'prime', 'theorem',
            'equation', 'formula', 'constant', 'number', '='
        ]
        
        claim_lower = claim.lower()
        return any(keyword in claim_lower for keyword in math_keywords)
    
    def _aggregate_results(self, claim: str, results: List[FactCheckResult]) -> FactCheckResult:
        """Aggrega risultati da multiple fonti."""
        if not results:
            return FactCheckResult(
                claim=claim,
                is_verified=False,
                confidence=0.0,
                verification_method="no_sources"
            )
        
        # Combina evidenze e contraddizioni
        all_evidence = []
        all_sources = []
        all_contradictions = []
        methods = []
        
        for result in results:
            all_evidence.extend(result.evidence)
            all_sources.extend(result.sources)
            all_contradictions.extend(result.contradictions)
            methods.append(result.verification_method)
        
        # Calcola confidence aggregata
        confidences = [r.confidence for r in results if r.confidence > 0]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        # Determina verifica finale
        has_evidence = len(all_evidence) > 0
        has_contradictions = len(all_contradictions) > 0
        
        is_verified = has_evidence and not has_contradictions
        
        # Penalizza se ci sono contraddizioni
        if has_contradictions:
            avg_confidence *= 0.5
        
        return FactCheckResult(
            claim=claim,
            is_verified=is_verified,
            confidence=avg_confidence,
            sources=list(set(all_sources)),
            evidence=all_evidence,
            contradictions=all_contradictions,
            verification_method="+".join(set(methods))
        )
    
    def _get_cached_result(self, claim_hash: str) -> Optional[FactCheckResult]:
        """Recupera risultato dalla cache database."""
        cursor = self.db.execute("""
            SELECT claim, is_verified, confidence, sources, evidence, 
                   contradictions, verification_method, timestamp
            FROM fact_verifications WHERE claim_hash = ?
        """, (claim_hash,))
        
        row = cursor.fetchone()
        if row:
            return FactCheckResult(
                claim=row[0],
                is_verified=bool(row[1]),
                confidence=row[2],
                sources=json.loads(row[3]) if row[3] else [],
                evidence=json.loads(row[4]) if row[4] else [],
                contradictions=json.loads(row[5]) if row[5] else [],
                verification_method=row[6],
                timestamp=row[7]
            )
        
        return None
    
    def _cache_result(self, claim_hash: str, result: FactCheckResult):
        """Salva risultato nella cache database."""
        self.db.execute("""
            INSERT OR REPLACE INTO fact_verifications
            (claim_hash, claim, is_verified, confidence, sources, evidence,
             contradictions, verification_method, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            claim_hash,
            result.claim,
            result.is_verified,
            result.confidence,
            json.dumps(result.sources),
            json.dumps(result.evidence),
            json.dumps(result.contradictions),
            result.verification_method,
            result.timestamp
        ))
        
        self.db.commit()
    
    def batch_verify(self, claims: List[str]) -> List[FactCheckResult]:
        """
        Verifica batch di claims.
        
        Args:
            claims: Lista di affermazioni
            
        Returns:
            Lista risultati verifica
        """
        results = []
        
        for claim in claims:
            try:
                result = self.verify_claim(claim)
                results.append(result)
            except Exception as e:
                logger.error(f"❌ Errore verifica claim '{claim}': {e}")
                results.append(FactCheckResult(
                    claim=claim,
                    is_verified=False,
                    confidence=0.0,
                    verification_method="error"
                ))
        
        return results
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche di validazione."""
        verification_rate = (self.verified_facts / self.total_verifications 
                           if self.total_verifications > 0 else 0.0)
        
        contradiction_rate = (self.contradicted_facts / self.total_verifications 
                             if self.total_verifications > 0 else 0.0)
        
        return {
            'total_verifications': self.total_verifications,
            'verified_facts': self.verified_facts,
            'contradicted_facts': self.contradicted_facts,
            'verification_rate': verification_rate,
            'contradiction_rate': contradiction_rate,
            'cache_size': len(self.verification_cache)
        }
