"""
FASE 4.1 - Corpus Collector per NEUROGLYPH

Raccolta di corpus real-world per AST round-trip testing:
- Repository GitHub popolari
- Jupyter notebooks
- StackOverflow snippets
- Edge cases con simboli Unicode
"""

import os
import shutil
import subprocess
import requests
from typing import List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass
import json
import time


@dataclass
class CorpusSource:
    """Definizione di una sorgente di corpus."""
    name: str
    type: str  # 'github', 'local', 'url'
    source: str  # URL o path
    description: str
    target_files: int = 100
    max_file_size_kb: int = 500


class CorpusCollector:
    """
    FASE 4.1: Collector per corpus real-world Python.
    
    Raccoglie file Python da diverse sorgenti per testing AST round-trip.
    """
    
    def __init__(self, corpus_dir: Path = Path("corpus_realworld")):
        self.corpus_dir = corpus_dir
        self.corpus_dir.mkdir(exist_ok=True)
        
        # Sorgenti predefinite
        self.sources = [
            CorpusSource(
                name="numpy_core",
                type="github",
                source="https://github.com/numpy/numpy.git",
                description="NumPy core library - scientific computing",
                target_files=50,
                max_file_size_kb=200
            ),
            CorpusSource(
                name="pandas_core",
                type="github", 
                source="https://github.com/pandas-dev/pandas.git",
                description="Pandas data analysis library",
                target_files=50,
                max_file_size_kb=200
            ),
            CorpusSource(
                name="django_core",
                type="github",
                source="https://github.com/django/django.git",
                description="Django web framework",
                target_files=40,
                max_file_size_kb=150
            ),
            CorpusSource(
                name="flask_core",
                type="github",
                source="https://github.com/pallets/flask.git",
                description="Flask micro web framework",
                target_files=30,
                max_file_size_kb=100
            ),
            CorpusSource(
                name="requests_lib",
                type="github",
                source="https://github.com/psf/requests.git",
                description="Requests HTTP library",
                target_files=20,
                max_file_size_kb=100
            )
        ]
        
        # Statistiche
        self.collected_stats = {
            "total_files": 0,
            "total_size_mb": 0,
            "sources_processed": 0,
            "files_by_source": {}
        }
    
    def collect_all_sources(self, max_total_files: int = 200) -> Dict[str, Any]:
        """
        Raccoglie corpus da tutte le sorgenti configurate.
        
        Args:
            max_total_files: Numero massimo totale di file
            
        Returns:
            Statistiche della raccolta
        """
        print(f"🚀 Starting corpus collection to: {self.corpus_dir}")
        print(f"📊 Target: {max_total_files} files from {len(self.sources)} sources")
        
        collected_files = 0
        
        for source in self.sources:
            if collected_files >= max_total_files:
                break
            
            remaining_files = max_total_files - collected_files
            target_files = min(source.target_files, remaining_files)
            
            print(f"\n📁 Processing source: {source.name}")
            print(f"   Type: {source.type}")
            print(f"   Target files: {target_files}")
            
            try:
                files_collected = self._collect_from_source(source, target_files)
                collected_files += files_collected
                self.collected_stats["files_by_source"][source.name] = files_collected
                self.collected_stats["sources_processed"] += 1
                
                print(f"   ✅ Collected: {files_collected} files")
                
            except Exception as e:
                print(f"   ❌ Error collecting from {source.name}: {e}")
                self.collected_stats["files_by_source"][source.name] = 0
        
        # Calcola statistiche finali
        self._calculate_final_stats()
        
        print(f"\n🎉 Corpus collection completed!")
        print(f"   Total files: {self.collected_stats['total_files']}")
        print(f"   Total size: {self.collected_stats['total_size_mb']:.2f} MB")
        print(f"   Sources processed: {self.collected_stats['sources_processed']}")
        
        return self.collected_stats
    
    def _collect_from_source(self, source: CorpusSource, target_files: int) -> int:
        """Raccoglie file da una singola sorgente."""
        if source.type == "github":
            return self._collect_from_github(source, target_files)
        elif source.type == "local":
            return self._collect_from_local(source, target_files)
        else:
            print(f"⚠️ Unsupported source type: {source.type}")
            return 0
    
    def _collect_from_github(self, source: CorpusSource, target_files: int) -> int:
        """Raccoglie file da repository GitHub."""
        repo_name = source.source.split('/')[-1].replace('.git', '')
        temp_dir = self.corpus_dir / f"temp_{repo_name}"
        target_dir = self.corpus_dir / source.name
        
        try:
            # Clone repository (shallow per velocità)
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
            
            print(f"   🔄 Cloning repository...")
            subprocess.run([
                "git", "clone", "--depth", "1", 
                source.source, str(temp_dir)
            ], check=True, capture_output=True)
            
            # Trova file Python
            python_files = list(temp_dir.rglob("*.py"))
            
            # Filtra per dimensione
            valid_files = []
            for file_path in python_files:
                try:
                    size_kb = file_path.stat().st_size / 1024
                    if size_kb <= source.max_file_size_kb:
                        valid_files.append(file_path)
                except:
                    continue
            
            # Seleziona file rappresentativi
            selected_files = self._select_representative_files(valid_files, target_files)
            
            # Copia file selezionati
            target_dir.mkdir(exist_ok=True)
            copied_count = 0
            
            for i, file_path in enumerate(selected_files):
                try:
                    # Crea struttura directory relativa
                    rel_path = file_path.relative_to(temp_dir)
                    target_file = target_dir / rel_path
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Copia file
                    shutil.copy2(file_path, target_file)
                    copied_count += 1
                    
                except Exception as e:
                    print(f"     ⚠️ Error copying {file_path}: {e}")
                    continue
            
            return copied_count
            
        finally:
            # Cleanup
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
    
    def _collect_from_local(self, source: CorpusSource, target_files: int) -> int:
        """Raccoglie file da directory locale."""
        source_path = Path(source.source)
        if not source_path.exists():
            print(f"   ❌ Local path not found: {source_path}")
            return 0
        
        target_dir = self.corpus_dir / source.name
        target_dir.mkdir(exist_ok=True)
        
        # Trova file Python
        python_files = list(source_path.rglob("*.py"))
        
        # Filtra e seleziona
        valid_files = [f for f in python_files if f.stat().st_size / 1024 <= source.max_file_size_kb]
        selected_files = self._select_representative_files(valid_files, target_files)
        
        # Copia file
        copied_count = 0
        for file_path in selected_files:
            try:
                rel_path = file_path.relative_to(source_path)
                target_file = target_dir / rel_path
                target_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(file_path, target_file)
                copied_count += 1
            except Exception as e:
                print(f"     ⚠️ Error copying {file_path}: {e}")
        
        return copied_count
    
    def _select_representative_files(self, files: List[Path], target_count: int) -> List[Path]:
        """Seleziona file rappresentativi dal corpus."""
        if len(files) <= target_count:
            return files
        
        # Strategia di selezione:
        # 1. Diversità di dimensioni
        # 2. Diversità di directory
        # 3. File con nomi interessanti
        
        # Ordina per dimensione
        files_with_size = [(f, f.stat().st_size) for f in files]
        files_with_size.sort(key=lambda x: x[1])
        
        # Seleziona distribuendo per dimensione
        selected = []
        step = len(files_with_size) // target_count
        
        for i in range(0, len(files_with_size), max(1, step)):
            if len(selected) >= target_count:
                break
            selected.append(files_with_size[i][0])
        
        # Aggiungi file con nomi interessanti se c'è spazio
        interesting_patterns = ['test_', 'example_', 'demo_', '__init__', 'main']
        for file_path in files:
            if len(selected) >= target_count:
                break
            if any(pattern in file_path.name for pattern in interesting_patterns):
                if file_path not in selected:
                    selected.append(file_path)
        
        return selected[:target_count]
    
    def _calculate_final_stats(self):
        """Calcola statistiche finali del corpus."""
        total_files = 0
        total_size = 0
        
        for source_dir in self.corpus_dir.iterdir():
            if source_dir.is_dir() and not source_dir.name.startswith('temp_'):
                for file_path in source_dir.rglob("*.py"):
                    total_files += 1
                    total_size += file_path.stat().st_size
        
        self.collected_stats["total_files"] = total_files
        self.collected_stats["total_size_mb"] = total_size / 1024 / 1024
    
    def create_edge_cases_corpus(self) -> int:
        """Crea corpus di edge cases per testing avanzato."""
        edge_cases_dir = self.corpus_dir / "edge_cases"
        edge_cases_dir.mkdir(exist_ok=True)
        
        edge_cases = [
            # Unicode symbols
            ("unicode_symbols.py", '''
# Test Unicode symbols in code
π = 3.14159
α = 0.05
β = 2.0
δ = α * β

def calculate_∑(values):
    """Calculate sum with Unicode function name."""
    return sum(values)

# Mathematical symbols
∞ = float('inf')
∅ = set()
∈ = lambda x, s: x in s
'''),
            
            # Complex string literals
            ("complex_strings.py", '''
# Complex string literals
emoji_string = "Hello 👋 World 🌍!"
math_formula = "E = mc²"
mixed_quotes = """This has 'single' and "double" quotes"""
raw_string = r"Raw string with \\backslashes\\"
f_string = f"F-string with {emoji_string}"
'''),
            
            # Nested structures
            ("nested_structures.py", '''
# Deeply nested structures
nested_dict = {
    'level1': {
        'level2': {
            'level3': [
                {'item': i} for i in range(10)
            ]
        }
    }
}

def nested_function():
    def inner_function():
        def deepest_function():
            return "deep"
        return deepest_function()
    return inner_function()
'''),
            
            # Edge case syntax
            ("edge_syntax.py", '''
# Edge case Python syntax
async def async_function():
    async with some_context() as ctx:
        async for item in async_iterator():
            yield item

@decorator_with_args(arg1="value")
@another_decorator
class EdgeCaseClass:
    def __init__(self, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs
    
    def method_with_annotations(self, x: int, y: str = "default") -> bool:
        return isinstance(x, int) and isinstance(y, str)
''')
        ]
        
        created_count = 0
        for filename, content in edge_cases:
            file_path = edge_cases_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content.strip())
            created_count += 1
        
        print(f"✅ Created {created_count} edge case files")
        return created_count
    
    def save_corpus_manifest(self):
        """Salva manifest del corpus per riferimento."""
        manifest = {
            "creation_time": time.time(),
            "sources": [
                {
                    "name": source.name,
                    "type": source.type,
                    "source": source.source,
                    "description": source.description,
                    "files_collected": self.collected_stats["files_by_source"].get(source.name, 0)
                }
                for source in self.sources
            ],
            "statistics": self.collected_stats
        }
        
        manifest_path = self.corpus_dir / "corpus_manifest.json"
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print(f"📋 Corpus manifest saved to: {manifest_path}")


def create_sample_corpus():
    """Crea un corpus di esempio per testing immediato."""
    sample_dir = Path("sample_corpus")
    sample_dir.mkdir(exist_ok=True)
    
    samples = [
        ("simple_function.py", '''
def hello_world():
    """Simple hello world function."""
    print("Hello, World!")
    return True

if __name__ == "__main__":
    hello_world()
'''),
        
        ("class_example.py", '''
class Calculator:
    """Simple calculator class."""
    
    def __init__(self):
        self.result = 0
    
    def add(self, x, y):
        self.result = x + y
        return self.result
    
    def multiply(self, x, y):
        self.result = x * y
        return self.result

calc = Calculator()
print(calc.add(2, 3))
'''),
        
        ("list_comprehension.py", '''
# List comprehensions and generators
numbers = [1, 2, 3, 4, 5]
squares = [x**2 for x in numbers if x % 2 == 0]
cubes = (x**3 for x in numbers)

# Dictionary comprehension
square_dict = {x: x**2 for x in numbers}

# Set comprehension
unique_squares = {x**2 for x in numbers}
''')
    ]
    
    for filename, content in samples:
        file_path = sample_dir / filename
        with open(file_path, 'w') as f:
            f.write(content.strip())
    
    print(f"✅ Created sample corpus with {len(samples)} files in: {sample_dir}")
    return sample_dir
