"""
NEUROGLYPH Pattern Extractor
Mining frequenze su patch_history + knowledge graph per pattern learning
"""

import time
import json
import threading
import sqlite3
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from collections import Counter, defaultdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class ExtractedPattern:
    """Pattern estratto dal mining."""
    
    pattern_id: str
    pattern_name: str
    error_types: List[str]
    patch_strategies: List[str]
    
    # Metriche mining
    support: int                    # Numero occorrenze
    confidence: float              # Tasso successo
    lift: float = 1.0              # Lift rispetto a baseline
    
    # Metadati
    first_seen: float = field(default_factory=time.time)
    last_seen: float = field(default_factory=time.time)
    version: int = 1
    
    # Proprietà pattern
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serializza pattern a dict."""
        return {
            'pattern_id': self.pattern_id,
            'pattern_name': self.pattern_name,
            'error_types': self.error_types,
            'patch_strategies': self.patch_strategies,
            'support': self.support,
            'confidence': self.confidence,
            'lift': self.lift,
            'first_seen': self.first_seen,
            'last_seen': self.last_seen,
            'version': self.version,
            'properties': self.properties
        }


class PatternExtractor:
    """
    Pattern Extractor per NEUROGLYPH meta-learning.
    
    Features:
    - Mining frequenze su patch_history con regole: support ≥ 3, confidence ≥ 0.6
    - Batch offline ogni 15 min via thread
    - Incremental learning con last_extracted_ts
    - Pattern versioning (pattern_id, rev)
    - Safety con circuit-breaker su false-positive burst
    """
    
    def __init__(self, db_path: str = "learning.db", 
                 min_support: int = 3, min_confidence: float = 0.6,
                 extraction_interval: float = 900.0):  # 15 min
        """
        Inizializza Pattern Extractor.
        
        Args:
            db_path: Percorso database SQLite
            min_support: Support minimo per pattern
            min_confidence: Confidence minima per pattern
            extraction_interval: Intervallo estrazione in secondi
        """
        self.db_path = db_path
        self.min_support = min_support
        self.min_confidence = min_confidence
        self.extraction_interval = extraction_interval
        
        # Pattern estratti
        self.patterns: Dict[str, ExtractedPattern] = {}
        
        # Stato estrazione
        self.last_extracted_ts = 0.0
        self.extraction_count = 0
        self.total_patterns_found = 0
        
        # Circuit breaker per false-positive burst
        self.circuit_breaker = {
            'consecutive_failures': 0,
            'failure_threshold': 10,
            'disabled_until': 0,
            'disable_duration': 300.0  # 5 min
        }
        
        # Threading
        self._lock = threading.RLock()
        self._shutdown = False
        
        # Database
        self.db = None
        self._setup_database()
        self._load_patterns()
        
        # Avvia thread estrazione
        self._start_extraction_thread()
        
        logger.info(f"🔍 PatternExtractor inizializzato")
        logger.info(f"   - Database: {db_path}")
        logger.info(f"   - Min support: {min_support}")
        logger.info(f"   - Min confidence: {min_confidence}")
        logger.info(f"   - Extraction interval: {extraction_interval}s")
        logger.info(f"   - Pattern caricati: {len(self.patterns)}")
    
    def _setup_database(self):
        """Setup database con schema pattern extraction."""
        try:
            self.db = sqlite3.connect(
                self.db_path,
                isolation_level=None,
                check_same_thread=False,
                timeout=30.0
            )
            
            cur = self.db.cursor()
            
            # Schema pattern extraction
            cur.executescript("""
                -- Tabella pattern estratti
                CREATE TABLE IF NOT EXISTS extracted_patterns(
                    pattern_id TEXT PRIMARY KEY,
                    pattern_name TEXT NOT NULL,
                    error_types TEXT NOT NULL,
                    patch_strategies TEXT NOT NULL,
                    support INTEGER NOT NULL,
                    confidence REAL NOT NULL,
                    lift REAL DEFAULT 1.0,
                    first_seen REAL NOT NULL,
                    last_seen REAL NOT NULL,
                    version INTEGER DEFAULT 1,
                    properties TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
                
                -- Tabella statistiche pattern
                CREATE TABLE IF NOT EXISTS pattern_stats(
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts REAL NOT NULL,
                    last_extracted_ts REAL NOT NULL,
                    extraction_count INTEGER NOT NULL,
                    total_patterns INTEGER NOT NULL,
                    new_patterns INTEGER NOT NULL,
                    updated_patterns INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
                
                -- Indici
                CREATE INDEX IF NOT EXISTS idx_ep_support
                  ON extracted_patterns(support DESC);
                  
                CREATE INDEX IF NOT EXISTS idx_ep_confidence
                  ON extracted_patterns(confidence DESC);
                  
                CREATE INDEX IF NOT EXISTS idx_ep_last_seen
                  ON extracted_patterns(last_seen DESC);
                  
                CREATE INDEX IF NOT EXISTS idx_ps_ts
                  ON pattern_stats(ts DESC);
            """)
            
            logger.info("✅ Pattern Extractor database setup completato")
            
        except Exception as e:
            logger.error(f"❌ Errore setup PE database: {e}")
            raise
    
    def _load_patterns(self):
        """Carica pattern estratti dal database."""
        try:
            cur = self.db.cursor()
            
            cur.execute("""
                SELECT pattern_id, pattern_name, error_types, patch_strategies,
                       support, confidence, lift, first_seen, last_seen, version, properties
                FROM extracted_patterns
            """)
            
            for row in cur.fetchall():
                (pattern_id, pattern_name, error_types_json, patch_strategies_json,
                 support, confidence, lift, first_seen, last_seen, version, props_json) = row
                
                pattern = ExtractedPattern(
                    pattern_id=pattern_id,
                    pattern_name=pattern_name,
                    error_types=json.loads(error_types_json),
                    patch_strategies=json.loads(patch_strategies_json),
                    support=support,
                    confidence=confidence,
                    lift=lift,
                    first_seen=first_seen,
                    last_seen=last_seen,
                    version=version,
                    properties=json.loads(props_json) if props_json else {}
                )
                
                self.patterns[pattern_id] = pattern
            
            # Carica ultimo timestamp estrazione
            cur.execute("SELECT MAX(last_extracted_ts) FROM pattern_stats")
            result = cur.fetchone()
            if result and result[0]:
                self.last_extracted_ts = result[0]
            
            logger.info(f"✅ Pattern caricati: {len(self.patterns)}")
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento pattern: {e}")
    
    def _start_extraction_thread(self):
        """Avvia thread per estrazione periodica."""
        def extraction_worker():
            while not self._shutdown:
                time.sleep(self.extraction_interval)
                if not self._shutdown:
                    self.extract_patterns()
        
        extraction_thread = threading.Thread(target=extraction_worker, daemon=True)
        extraction_thread.start()
        logger.info("✅ Thread estrazione pattern avviato")
    
    def _is_circuit_breaker_active(self) -> bool:
        """Verifica se circuit breaker è attivo."""
        return time.time() < self.circuit_breaker['disabled_until']
    
    def _update_circuit_breaker(self, success: bool):
        """Aggiorna circuit breaker."""
        if success:
            self.circuit_breaker['consecutive_failures'] = 0
        else:
            self.circuit_breaker['consecutive_failures'] += 1
            if (self.circuit_breaker['consecutive_failures'] >= 
                self.circuit_breaker['failure_threshold']):
                self.circuit_breaker['disabled_until'] = (
                    time.time() + self.circuit_breaker['disable_duration']
                )
                logger.warning(f"🚨 Pattern extraction circuit breaker attivato")
    
    def extract_patterns(self) -> Dict[str, Any]:
        """
        Estrae pattern da patch_history.
        
        Returns:
            Statistiche estrazione
        """
        if self._is_circuit_breaker_active():
            return {"status": "circuit_breaker_active"}
        
        with self._lock:
            try:
                start_time = time.time()
                
                # Query patch_history incrementale
                cur = self.db.cursor()
                cur.execute("""
                    SELECT error_type, status, meta
                    FROM patch_history
                    WHERE ts > ?
                    ORDER BY ts ASC
                """, (self.last_extracted_ts,))
                
                patch_records = cur.fetchall()
                
                if not patch_records:
                    return {"status": "no_new_data", "records": 0}
                
                # Mining pattern
                new_patterns, updated_patterns = self._mine_patterns(patch_records)
                
                # Aggiorna timestamp
                self.last_extracted_ts = start_time
                self.extraction_count += 1
                
                # Registra statistiche
                self._record_extraction_stats(len(new_patterns), len(updated_patterns))
                
                # Aggiorna circuit breaker
                self._update_circuit_breaker(True)
                
                stats = {
                    "status": "success",
                    "records_processed": len(patch_records),
                    "new_patterns": len(new_patterns),
                    "updated_patterns": len(updated_patterns),
                    "total_patterns": len(self.patterns),
                    "extraction_time_ms": (time.time() - start_time) * 1000
                }
                
                logger.info(f"✅ Pattern extraction completata: {stats}")
                return stats
                
            except Exception as e:
                logger.error(f"❌ Errore estrazione pattern: {e}")
                self._update_circuit_breaker(False)
                return {"status": "error", "error": str(e)}

    def _mine_patterns(self, patch_records: List[Tuple]) -> Tuple[List[str], List[str]]:
        """
        Mining pattern da patch records.

        Args:
            patch_records: Lista (error_type, status, meta)

        Returns:
            (new_patterns, updated_patterns)
        """
        # Analizza combinazioni error_type → patch_strategy
        combinations = defaultdict(lambda: {'total': 0, 'success': 0, 'strategies': Counter()})

        for error_type, status, meta_json in patch_records:
            try:
                meta = json.loads(meta_json) if meta_json else {}
                patch_strategy = meta.get('patch_strategy', 'unknown')

                key = (error_type, patch_strategy)
                combinations[key]['total'] += 1

                if status == 'successful':
                    combinations[key]['success'] += 1

                combinations[key]['strategies'][patch_strategy] += 1

            except Exception as e:
                logger.warning(f"⚠️ Errore parsing meta: {e}")
                continue

        # Estrai pattern che soddisfano criteri
        new_patterns = []
        updated_patterns = []

        for (error_type, patch_strategy), stats in combinations.items():
            support = stats['total']
            confidence = stats['success'] / stats['total'] if stats['total'] > 0 else 0.0

            # Verifica criteri mining
            if support >= self.min_support and confidence >= self.min_confidence:
                pattern_id = f"pattern_{error_type}_{patch_strategy}_{int(time.time())}"

                # Verifica se pattern simile già esiste
                existing_pattern = self._find_similar_pattern(error_type, patch_strategy)

                if existing_pattern:
                    # Aggiorna pattern esistente
                    self._update_pattern(existing_pattern, support, confidence)
                    updated_patterns.append(existing_pattern.pattern_id)
                else:
                    # Crea nuovo pattern
                    new_pattern = self._create_pattern(pattern_id, error_type, patch_strategy, support, confidence)
                    if new_pattern:
                        new_patterns.append(pattern_id)

        return new_patterns, updated_patterns

    def _find_similar_pattern(self, error_type: str, patch_strategy: str) -> Optional[ExtractedPattern]:
        """Trova pattern simile esistente."""
        for pattern in self.patterns.values():
            if (error_type in pattern.error_types and
                patch_strategy in pattern.patch_strategies):
                return pattern
        return None

    def _create_pattern(self, pattern_id: str, error_type: str, patch_strategy: str,
                       support: int, confidence: float) -> Optional[ExtractedPattern]:
        """Crea nuovo pattern."""
        try:
            pattern = ExtractedPattern(
                pattern_id=pattern_id,
                pattern_name=f"{error_type}→{patch_strategy}",
                error_types=[error_type],
                patch_strategies=[patch_strategy],
                support=support,
                confidence=confidence,
                lift=self._calculate_lift(error_type, patch_strategy, confidence)
            )

            # Salva nel database
            cur = self.db.cursor()
            cur.execute("""
                INSERT INTO extracted_patterns
                (pattern_id, pattern_name, error_types, patch_strategies,
                 support, confidence, lift, first_seen, last_seen, version, properties)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                pattern.pattern_id,
                pattern.pattern_name,
                json.dumps(pattern.error_types),
                json.dumps(pattern.patch_strategies),
                pattern.support,
                pattern.confidence,
                pattern.lift,
                pattern.first_seen,
                pattern.last_seen,
                pattern.version,
                json.dumps(pattern.properties)
            ))

            # Aggiungi in memoria
            self.patterns[pattern_id] = pattern
            self.total_patterns_found += 1

            return pattern

        except Exception as e:
            logger.error(f"❌ Errore creazione pattern {pattern_id}: {e}")
            return None

    def _update_pattern(self, pattern: ExtractedPattern, new_support: int, new_confidence: float):
        """Aggiorna pattern esistente."""
        try:
            # Aggiorna metriche (media pesata)
            total_observations = pattern.support + new_support
            pattern.confidence = (
                (pattern.confidence * pattern.support + new_confidence * new_support) /
                total_observations
            )
            pattern.support = total_observations
            pattern.last_seen = time.time()
            pattern.version += 1

            # Aggiorna nel database
            cur = self.db.cursor()
            cur.execute("""
                UPDATE extracted_patterns
                SET support = ?, confidence = ?, last_seen = ?, version = ?
                WHERE pattern_id = ?
            """, (
                pattern.support,
                pattern.confidence,
                pattern.last_seen,
                pattern.version,
                pattern.pattern_id
            ))

        except Exception as e:
            logger.error(f"❌ Errore aggiornamento pattern {pattern.pattern_id}: {e}")

    def _calculate_lift(self, error_type: str, patch_strategy: str, confidence: float) -> float:
        """Calcola lift del pattern rispetto a baseline."""
        try:
            # Query baseline confidence per patch_strategy
            cur = self.db.cursor()
            cur.execute("""
                SELECT
                    SUM(CASE WHEN status = 'successful' THEN 1 ELSE 0 END) as successes,
                    COUNT(*) as total
                FROM patch_history
                WHERE JSON_EXTRACT(meta, '$.patch_strategy') = ?
            """, (patch_strategy,))

            result = cur.fetchone()
            if result and result[1] > 0:
                baseline_confidence = result[0] / result[1]
                return confidence / baseline_confidence if baseline_confidence > 0 else 1.0

            return 1.0

        except Exception as e:
            logger.warning(f"⚠️ Errore calcolo lift: {e}")
            return 1.0

    def _record_extraction_stats(self, new_patterns: int, updated_patterns: int):
        """Registra statistiche estrazione."""
        try:
            cur = self.db.cursor()
            cur.execute("""
                INSERT INTO pattern_stats
                (ts, last_extracted_ts, extraction_count, total_patterns, new_patterns, updated_patterns)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                time.time(),
                self.last_extracted_ts,
                self.extraction_count,
                len(self.patterns),
                new_patterns,
                updated_patterns
            ))

        except Exception as e:
            logger.error(f"❌ Errore registrazione stats: {e}")

    def get_top_patterns(self, limit: int = 10, sort_by: str = "confidence") -> List[ExtractedPattern]:
        """
        Ottiene top pattern.

        Args:
            limit: Numero massimo pattern
            sort_by: Campo per ordinamento (confidence, support, lift)

        Returns:
            Lista pattern ordinati
        """
        patterns = list(self.patterns.values())

        if sort_by == "confidence":
            patterns.sort(key=lambda p: p.confidence, reverse=True)
        elif sort_by == "support":
            patterns.sort(key=lambda p: p.support, reverse=True)
        elif sort_by == "lift":
            patterns.sort(key=lambda p: p.lift, reverse=True)
        else:
            patterns.sort(key=lambda p: p.last_seen, reverse=True)

        return patterns[:limit]

    def get_pattern_by_id(self, pattern_id: str) -> Optional[ExtractedPattern]:
        """Ottiene pattern per ID."""
        return self.patterns.get(pattern_id)

    def get_stats(self) -> Dict[str, Any]:
        """Statistiche pattern extractor."""
        return {
            "total_patterns": len(self.patterns),
            "extraction_count": self.extraction_count,
            "last_extracted_ts": self.last_extracted_ts,
            "min_support": self.min_support,
            "min_confidence": self.min_confidence,
            "circuit_breaker": self.circuit_breaker,
            "avg_confidence": sum(p.confidence for p in self.patterns.values()) / len(self.patterns) if self.patterns else 0.0,
            "avg_support": sum(p.support for p in self.patterns.values()) / len(self.patterns) if self.patterns else 0.0
        }

    def shutdown(self):
        """Shutdown graceful."""
        self._shutdown = True
        logger.info("🔄 PatternExtractor shutdown")
