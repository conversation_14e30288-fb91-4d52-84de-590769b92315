"""
NEUROGLYPH Pattern Learning Engine - Autonomous Evolution

Sistema di apprendimento automatico per identificare pattern ricorrenti
e proporre nuovi simboli per aumentare la copertura simbolica.

Fase 7.0 - Autonomous Evolution
"""

import logging
import time
import json
import sqlite3
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import Counter, defaultdict
import re
import ast

from ..utils.registry_loader import load_registry_fast
from ..logic.proof_tree import ProofTree, ProofStep
from ..cognitive.ng_decoder import NGDecoder

logger = logging.getLogger(__name__)


@dataclass
class PatternCandidate:
    """Candidato per nuovo pattern simbolico."""
    pattern_id: str
    pattern_type: str  # 'ast', 'proof', 'semantic', 'hybrid'
    frequency: int
    confidence: float
    description: str
    suggested_symbol: Optional[str] = None
    ast_nodes: List[str] = field(default_factory=list)
    proof_rules: List[str] = field(default_factory=list)
    semantic_context: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)


@dataclass
class SymbolGap:
    """Gap identificato nella copertura simbolica."""
    gap_id: str
    domain: str
    missing_patterns: List[str]
    frequency: int
    priority: float  # 0.0-1.0
    suggested_symbols: List[str] = field(default_factory=list)
    rationale: str = ""


class PatternLearningEngine:
    """
    Engine per apprendimento automatico di pattern e evoluzione simbolica.
    
    Features:
    - Analisi automatica proof trees per pattern ricorrenti
    - Identificazione gap nella copertura simbolica
    - Proposta automatica di nuovi simboli
    - Learning incrementale da utilizzo reale
    """
    
    def __init__(self, db_path: str = "pattern_learning.db"):
        """
        Inizializza Pattern Learning Engine.
        
        Args:
            db_path: Percorso database per persistenza pattern
        """
        self.db_path = db_path
        self.db = None
        
        # Registry simbolico corrente
        self.current_registry = {}
        self.symbol_coverage = {}
        
        # Pattern identificati
        self.pattern_candidates: List[PatternCandidate] = []
        self.symbol_gaps: List[SymbolGap] = []
        
        # Statistiche learning
        self.patterns_analyzed = 0
        self.symbols_proposed = 0
        self.gaps_identified = 0
        
        # Configurazione learning
        self.min_pattern_frequency = 3
        self.min_confidence_threshold = 0.6
        self.max_candidates_per_run = 50
        
        self._initialize_database()
        self._load_current_registry()
        
        logger.info(f"🧠 PatternLearningEngine inizializzato")
        logger.info(f"   - Registry: {len(self.current_registry)} simboli")
        logger.info(f"   - Database: {self.db_path}")
    
    def _initialize_database(self):
        """Inizializza database SQLite per persistenza."""
        self.db = sqlite3.connect(self.db_path, check_same_thread=False)
        
        # Tabella pattern candidati
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS pattern_candidates (
                pattern_id TEXT PRIMARY KEY,
                pattern_type TEXT NOT NULL,
                frequency INTEGER NOT NULL,
                confidence REAL NOT NULL,
                description TEXT NOT NULL,
                suggested_symbol TEXT,
                ast_nodes TEXT,  -- JSON
                proof_rules TEXT,  -- JSON
                semantic_context TEXT,  -- JSON
                created_at REAL NOT NULL,
                status TEXT DEFAULT 'pending'  -- pending, approved, rejected
            )
        """)
        
        # Tabella gap simbolici
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS symbol_gaps (
                gap_id TEXT PRIMARY KEY,
                domain TEXT NOT NULL,
                missing_patterns TEXT NOT NULL,  -- JSON
                frequency INTEGER NOT NULL,
                priority REAL NOT NULL,
                suggested_symbols TEXT,  -- JSON
                rationale TEXT,
                created_at REAL NOT NULL,
                status TEXT DEFAULT 'open'  -- open, addressed, closed
            )
        """)
        
        # Tabella utilizzo pattern
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS pattern_usage (
                usage_id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_signature TEXT NOT NULL,
                context_type TEXT NOT NULL,  -- proof, ast, semantic
                frequency INTEGER DEFAULT 1,
                last_seen REAL NOT NULL,
                metadata TEXT  -- JSON
            )
        """)
        
        self.db.commit()
        logger.debug("✅ Database pattern learning inizializzato")
    
    def _load_current_registry(self):
        """Carica registry simbolico corrente."""
        try:
            registry = load_registry_fast()
            self.current_registry = registry['symbols']
            
            # Analizza copertura per dominio
            domain_coverage = defaultdict(int)
            for symbol_data in self.current_registry.values():
                domain = symbol_data.get('domain', 'unknown')
                domain_coverage[domain] += 1
            
            self.symbol_coverage = dict(domain_coverage)
            
            logger.info(f"✅ Registry caricato: {len(self.current_registry)} simboli")
            logger.info(f"   - Domini coperti: {len(self.symbol_coverage)}")
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento registry: {e}")
            self.current_registry = {}
            self.symbol_coverage = {}
    
    def analyze_proof_patterns(self, proof_trees: List[ProofTree]) -> List[PatternCandidate]:
        """
        Analizza proof trees per identificare pattern ricorrenti.
        
        Args:
            proof_trees: Lista di proof trees da analizzare
            
        Returns:
            Lista di pattern candidati identificati
        """
        logger.info(f"🔍 Analizzando {len(proof_trees)} proof trees...")
        
        # Contatori per pattern
        rule_sequences = Counter()
        formula_patterns = Counter()
        depth_patterns = Counter()
        
        for proof in proof_trees:
            self.patterns_analyzed += 1
            
            # Analizza sequenze di regole
            if proof.steps:
                rule_sequence = tuple(step.rule.value for step in proof.steps)
                rule_sequences[rule_sequence] += 1
                
                # Analizza pattern di formule
                for step in proof.steps:
                    if step.formula:
                        formula_pattern = self._extract_formula_pattern(step.formula)
                        formula_patterns[formula_pattern] += 1
                
                # Analizza profondità
                depth_patterns[proof.depth] += 1
        
        # Identifica candidati da pattern frequenti
        candidates = []
        
        # Pattern da sequenze di regole
        for rule_seq, freq in rule_sequences.most_common(20):
            if freq >= self.min_pattern_frequency:
                candidate = PatternCandidate(
                    pattern_id=f"rule_seq_{hash(rule_seq)}",
                    pattern_type="proof",
                    frequency=freq,
                    confidence=min(freq / len(proof_trees), 1.0),
                    description=f"Sequenza regole: {' → '.join(rule_seq)}",
                    proof_rules=list(rule_seq)
                )
                candidates.append(candidate)
        
        # Pattern da formule
        for formula_pattern, freq in formula_patterns.most_common(15):
            if freq >= self.min_pattern_frequency:
                candidate = PatternCandidate(
                    pattern_id=f"formula_{hash(formula_pattern)}",
                    pattern_type="semantic",
                    frequency=freq,
                    confidence=min(freq / len(proof_trees), 1.0),
                    description=f"Pattern formula: {formula_pattern}",
                    semantic_context={"formula_pattern": formula_pattern}
                )
                candidates.append(candidate)
        
        # Filtra per confidence
        candidates = [c for c in candidates if c.confidence >= self.min_confidence_threshold]
        
        logger.info(f"✅ Identificati {len(candidates)} pattern candidati")
        
        return candidates
    
    def _extract_formula_pattern(self, formula) -> str:
        """Estrae pattern da formula logica."""
        try:
            formula_str = str(formula)
            
            # Normalizza variabili (x, y, z → VAR)
            normalized = re.sub(r'\b[a-z]\b', 'VAR', formula_str)
            
            # Normalizza predicati (P, Q, R → PRED)
            normalized = re.sub(r'\b[A-Z]\b', 'PRED', normalized)
            
            return normalized
            
        except Exception:
            return "unknown_pattern"
    
    def analyze_ast_patterns(self, code_samples: List[str]) -> List[PatternCandidate]:
        """
        Analizza codice per identificare pattern AST ricorrenti.
        
        Args:
            code_samples: Lista di campioni di codice
            
        Returns:
            Lista di pattern AST candidati
        """
        logger.info(f"🔍 Analizzando {len(code_samples)} campioni di codice...")
        
        ast_patterns = Counter()
        node_sequences = Counter()
        
        for code in code_samples:
            try:
                tree = ast.parse(code)
                
                # Estrai pattern di nodi
                for node in ast.walk(tree):
                    node_type = type(node).__name__
                    ast_patterns[node_type] += 1
                    
                    # Analizza sequenze di nodi figli
                    if hasattr(node, 'body') and node.body:
                        child_sequence = tuple(type(child).__name__ for child in node.body[:3])
                        node_sequences[child_sequence] += 1
                
            except SyntaxError:
                continue
        
        # Identifica pattern non coperti
        candidates = []
        
        for node_seq, freq in node_sequences.most_common(10):
            if freq >= self.min_pattern_frequency:
                # Verifica se pattern è già coperto
                if not self._is_ast_pattern_covered(node_seq):
                    candidate = PatternCandidate(
                        pattern_id=f"ast_seq_{hash(node_seq)}",
                        pattern_type="ast",
                        frequency=freq,
                        confidence=min(freq / len(code_samples), 1.0),
                        description=f"Sequenza AST: {' → '.join(node_seq)}",
                        ast_nodes=list(node_seq)
                    )
                    candidates.append(candidate)
        
        logger.info(f"✅ Identificati {len(candidates)} pattern AST candidati")
        
        return candidates
    
    def _is_ast_pattern_covered(self, node_sequence: Tuple[str, ...]) -> bool:
        """Verifica se un pattern AST è già coperto da simboli esistenti."""
        # Per ora, controllo semplice - da migliorare
        from ..core.encoder.symbol_mapper import SymbolMapper
        
        try:
            mapper = SymbolMapper()
            covered_nodes = set(mapper.ast_mappings.values())
            
            # Verifica se tutti i nodi della sequenza sono coperti
            return all(node in covered_nodes for node in node_sequence)
            
        except Exception:
            return False
    
    def identify_symbol_gaps(self) -> List[SymbolGap]:
        """
        Identifica gap nella copertura simbolica per dominio.
        
        Returns:
            Lista di gap identificati
        """
        logger.info("🔍 Identificando gap nella copertura simbolica...")
        
        gaps = []
        
        # Analizza copertura per dominio
        domain_priorities = {
            'logic': 0.9,
            'math': 0.8,
            'coding': 0.8,
            'control_flow': 0.7,
            'data_structures': 0.6
        }
        
        for domain, priority in domain_priorities.items():
            current_coverage = self.symbol_coverage.get(domain, 0)
            
            # Identifica pattern mancanti per dominio
            missing_patterns = self._identify_missing_patterns_for_domain(domain)
            
            if missing_patterns:
                gap = SymbolGap(
                    gap_id=f"gap_{domain}_{int(time.time())}",
                    domain=domain,
                    missing_patterns=missing_patterns,
                    frequency=len(missing_patterns),
                    priority=priority,
                    rationale=f"Dominio {domain} ha solo {current_coverage} simboli, mancano pattern comuni"
                )
                gaps.append(gap)
        
        self.symbol_gaps.extend(gaps)
        self.gaps_identified += len(gaps)
        
        logger.info(f"✅ Identificati {len(gaps)} gap simbolici")
        
        return gaps
    
    def _identify_missing_patterns_for_domain(self, domain: str) -> List[str]:
        """Identifica pattern mancanti per un dominio specifico."""
        # Pattern comuni per dominio
        domain_patterns = {
            'logic': [
                'nested_quantifiers', 'modal_logic', 'temporal_logic',
                'fuzzy_logic', 'multi_valued_logic'
            ],
            'math': [
                'matrix_operations', 'calculus_notation', 'set_theory',
                'topology', 'abstract_algebra'
            ],
            'coding': [
                'async_patterns', 'decorators', 'metaclasses',
                'context_managers', 'generators'
            ],
            'control_flow': [
                'exception_handling', 'pattern_matching', 'coroutines',
                'state_machines', 'event_loops'
            ]
        }
        
        return domain_patterns.get(domain, [])
    
    def propose_new_symbols(self, candidates: List[PatternCandidate]) -> List[Dict[str, Any]]:
        """
        Propone nuovi simboli basati sui pattern candidati.
        
        Args:
            candidates: Lista di pattern candidati
            
        Returns:
            Lista di proposte di nuovi simboli
        """
        logger.info(f"💡 Proponendo simboli per {len(candidates)} pattern...")
        
        proposals = []
        
        for candidate in candidates[:self.max_candidates_per_run]:
            # Genera simbolo proposto
            suggested_symbol = self._generate_symbol_for_pattern(candidate)
            
            if suggested_symbol:
                proposal = {
                    'pattern_id': candidate.pattern_id,
                    'suggested_symbol': suggested_symbol,
                    'pattern_type': candidate.pattern_type,
                    'frequency': candidate.frequency,
                    'confidence': candidate.confidence,
                    'description': candidate.description,
                    'rationale': self._generate_rationale(candidate),
                    'priority': self._calculate_priority(candidate)
                }
                proposals.append(proposal)
                
                # Aggiorna candidato
                candidate.suggested_symbol = suggested_symbol
        
        self.symbols_proposed += len(proposals)
        
        # Persisti candidati nel database
        self._persist_candidates(candidates)
        
        logger.info(f"✅ Proposti {len(proposals)} nuovi simboli")
        
        return proposals
    
    def _generate_symbol_for_pattern(self, candidate: PatternCandidate) -> Optional[str]:
        """Genera simbolo Unicode appropriato per il pattern."""
        # Mapping base pattern → simboli Unicode
        symbol_pools = {
            'proof': ['⊢', '⊣', '⊤', '⊥', '⊨', '⊩', '⊪', '⊫'],
            'ast': ['⟨', '⟩', '⟪', '⟫', '⟬', '⟭', '⟮', '⟯'],
            'semantic': ['◊', '◈', '◉', '◎', '●', '◐', '◑', '◒'],
            'hybrid': ['⧫', '⧪', '⧬', '⧭', '⧮', '⧯', '⧰', '⧱']
        }
        
        pool = symbol_pools.get(candidate.pattern_type, symbol_pools['hybrid'])
        
        # Seleziona simbolo non ancora utilizzato
        used_symbols = set(self.current_registry.keys())
        
        for symbol in pool:
            if symbol not in used_symbols:
                return symbol
        
        # Se tutti i simboli del pool sono usati, genera combinazione
        return f"{pool[0]}{pool[1]}"  # Fallback
    
    def _generate_rationale(self, candidate: PatternCandidate) -> str:
        """Genera rationale per la proposta di simbolo."""
        return f"Pattern '{candidate.description}' appare {candidate.frequency} volte " \
               f"con confidence {candidate.confidence:.2f}. " \
               f"Simbolo proposto per ottimizzare copertura dominio {candidate.pattern_type}."
    
    def _calculate_priority(self, candidate: PatternCandidate) -> float:
        """Calcola priorità per il candidato."""
        # Fattori: frequenza, confidence, tipo pattern
        frequency_score = min(candidate.frequency / 10.0, 1.0)
        confidence_score = candidate.confidence
        
        type_weights = {
            'proof': 0.9,
            'ast': 0.8,
            'semantic': 0.7,
            'hybrid': 0.6
        }
        type_score = type_weights.get(candidate.pattern_type, 0.5)
        
        return (frequency_score * 0.4 + confidence_score * 0.4 + type_score * 0.2)
    
    def _persist_candidates(self, candidates: List[PatternCandidate]):
        """Persiste candidati nel database."""
        for candidate in candidates:
            self.db.execute("""
                INSERT OR REPLACE INTO pattern_candidates 
                (pattern_id, pattern_type, frequency, confidence, description,
                 suggested_symbol, ast_nodes, proof_rules, semantic_context, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                candidate.pattern_id,
                candidate.pattern_type,
                candidate.frequency,
                candidate.confidence,
                candidate.description,
                candidate.suggested_symbol,
                json.dumps(candidate.ast_nodes),
                json.dumps(candidate.proof_rules),
                json.dumps(candidate.semantic_context),
                candidate.created_at
            ))
        
        self.db.commit()
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche del learning engine."""
        return {
            'patterns_analyzed': self.patterns_analyzed,
            'symbols_proposed': self.symbols_proposed,
            'gaps_identified': self.gaps_identified,
            'current_registry_size': len(self.current_registry),
            'symbol_coverage_by_domain': self.symbol_coverage,
            'active_candidates': len(self.pattern_candidates),
            'active_gaps': len(self.symbol_gaps)
        }
