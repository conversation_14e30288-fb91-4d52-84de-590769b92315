"""
NEUROGLYPH Cold Start Learning System

Sistema per l'apprendimento automatico di nuovi domini di conoscenza
senza supervisione umana, con bootstrap e auto-validazione.

Fase 7.0 - Autonomous Evolution
"""

import logging
import time
import json
import sqlite3
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import Counter, defaultdict
import re
import ast

from ..utils.registry_loader import load_registry_fast
from .pattern_learning_engine import PatternLearningEngine, PatternCandidate
from .symbol_evolution_system import SymbolEvolutionSystem

logger = logging.getLogger(__name__)


@dataclass
class DomainBootstrap:
    """Configurazione bootstrap per nuovo dominio."""
    domain_name: str
    seed_patterns: List[str]
    expected_symbols: int
    confidence_threshold: float
    validation_rules: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LearningSession:
    """Sessione di apprendimento per un dominio."""
    session_id: str
    domain_name: str
    start_time: float
    patterns_discovered: int = 0
    symbols_proposed: int = 0
    validation_score: float = 0.0
    status: str = "active"  # active, completed, failed
    learned_patterns: List[str] = field(default_factory=list)
    proposed_symbols: List[str] = field(default_factory=list)


class ColdStartLearningSystem:
    """
    Sistema di apprendimento cold start per nuovi domini NEUROGLYPH.
    
    Features:
    - Bootstrap automatico di nuovi domini
    - Apprendimento incrementale senza supervisione
    - Auto-validazione della qualità
    - Integrazione con Pattern Learning e Symbol Evolution
    """
    
    def __init__(self, db_path: str = "cold_start_learning.db"):
        """
        Inizializza Cold Start Learning System.
        
        Args:
            db_path: Percorso database per persistenza learning
        """
        self.db_path = db_path
        self.db = None
        
        # Componenti integrati
        self.pattern_engine = PatternLearningEngine()
        self.evolution_system = SymbolEvolutionSystem()
        
        # Sessioni di apprendimento
        self.active_sessions: Dict[str, LearningSession] = {}
        self.completed_sessions: List[LearningSession] = []
        
        # Configurazione learning
        self.min_patterns_for_domain = 5
        self.max_learning_iterations = 100
        self.validation_threshold = 0.7
        self.bootstrap_timeout = 300  # 5 minuti
        
        # Metriche
        self.domains_learned = 0
        self.total_patterns_discovered = 0
        self.total_symbols_created = 0
        
        self._initialize_database()
        
        logger.info(f"🚀 ColdStartLearningSystem inizializzato")
        logger.info(f"   - Database: {self.db_path}")
    
    def _initialize_database(self):
        """Inizializza database per persistenza learning."""
        self.db = sqlite3.connect(self.db_path, check_same_thread=False)
        
        # Tabella sessioni learning
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS learning_sessions (
                session_id TEXT PRIMARY KEY,
                domain_name TEXT NOT NULL,
                start_time REAL NOT NULL,
                end_time REAL,
                patterns_discovered INTEGER DEFAULT 0,
                symbols_proposed INTEGER DEFAULT 0,
                validation_score REAL DEFAULT 0.0,
                status TEXT DEFAULT 'active',
                learned_patterns TEXT,  -- JSON
                proposed_symbols TEXT,  -- JSON
                metadata TEXT  -- JSON
            )
        """)
        
        # Tabella pattern scoperti
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS discovered_patterns (
                pattern_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                domain_name TEXT NOT NULL,
                pattern_text TEXT NOT NULL,
                frequency INTEGER NOT NULL,
                confidence REAL NOT NULL,
                validation_score REAL,
                discovered_at REAL NOT NULL,
                FOREIGN KEY (session_id) REFERENCES learning_sessions (session_id)
            )
        """)
        
        # Tabella simboli proposti
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS proposed_symbols (
                symbol_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                domain_name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                pattern_basis TEXT NOT NULL,
                confidence REAL NOT NULL,
                validation_score REAL,
                status TEXT DEFAULT 'proposed',  -- proposed, validated, rejected
                created_at REAL NOT NULL,
                FOREIGN KEY (session_id) REFERENCES learning_sessions (session_id)
            )
        """)
        
        self.db.commit()
        logger.debug("✅ Database cold start learning inizializzato")
    
    def bootstrap_new_domain(self, domain_config: DomainBootstrap) -> str:
        """
        Avvia bootstrap di un nuovo dominio.
        
        Args:
            domain_config: Configurazione bootstrap
            
        Returns:
            ID della sessione di learning
        """
        session_id = f"session_{domain_config.domain_name}_{int(time.time())}"
        
        logger.info(f"🚀 Avviando bootstrap dominio '{domain_config.domain_name}'...")
        
        # Crea sessione learning
        session = LearningSession(
            session_id=session_id,
            domain_name=domain_config.domain_name,
            start_time=time.time()
        )
        
        self.active_sessions[session_id] = session
        
        # Persisti sessione
        self._persist_session(session)
        
        # Avvia learning asincrono
        self._start_learning_process(session, domain_config)
        
        logger.info(f"✅ Bootstrap avviato per dominio '{domain_config.domain_name}' (sessione: {session_id})")
        
        return session_id
    
    def _start_learning_process(self, session: LearningSession, config: DomainBootstrap):
        """Avvia processo di learning per la sessione."""
        logger.info(f"🧠 Avviando learning per sessione {session.session_id}...")
        
        try:
            # Fase 1: Analisi pattern seed
            seed_patterns = self._analyze_seed_patterns(config.seed_patterns)
            session.learned_patterns.extend(seed_patterns)
            
            # Fase 2: Scoperta pattern incrementale
            discovered_patterns = self._discover_patterns_incrementally(
                session, config, max_iterations=self.max_learning_iterations
            )
            
            # Fase 3: Proposta simboli
            proposed_symbols = self._propose_symbols_for_patterns(
                session, discovered_patterns, config
            )
            
            # Fase 4: Validazione
            validation_score = self._validate_learning_results(session, config)
            session.validation_score = validation_score
            
            # Completa sessione
            if validation_score >= self.validation_threshold:
                session.status = "completed"
                self.domains_learned += 1
                logger.info(f"✅ Learning completato per dominio '{session.domain_name}' (score: {validation_score:.2f})")
            else:
                session.status = "failed"
                logger.warning(f"⚠️ Learning fallito per dominio '{session.domain_name}' (score: {validation_score:.2f})")
            
            # Sposta da active a completed
            if session.session_id in self.active_sessions:
                del self.active_sessions[session.session_id]
            self.completed_sessions.append(session)
            
            # Aggiorna database
            self._update_session_status(session)
            
        except Exception as e:
            logger.error(f"❌ Errore durante learning sessione {session.session_id}: {e}")
            session.status = "failed"
            self._update_session_status(session)
    
    def _analyze_seed_patterns(self, seed_patterns: List[str]) -> List[str]:
        """Analizza pattern seed per identificare caratteristiche del dominio."""
        analyzed_patterns = []
        
        for pattern in seed_patterns:
            # Normalizza pattern
            normalized = self._normalize_pattern(pattern)
            
            # Estrai caratteristiche
            features = self._extract_pattern_features(normalized)
            
            if features:
                analyzed_patterns.append(normalized)
        
        logger.info(f"✅ Analizzati {len(analyzed_patterns)} pattern seed")
        
        return analyzed_patterns
    
    def _normalize_pattern(self, pattern: str) -> str:
        """Normalizza pattern per analisi."""
        # Rimuovi whitespace extra
        normalized = re.sub(r'\s+', ' ', pattern.strip())
        
        # Normalizza variabili
        normalized = re.sub(r'\b[a-z]\b', 'VAR', normalized)
        
        # Normalizza numeri
        normalized = re.sub(r'\b\d+\b', 'NUM', normalized)
        
        return normalized
    
    def _extract_pattern_features(self, pattern: str) -> Dict[str, Any]:
        """Estrae caratteristiche da un pattern."""
        features = {
            'length': len(pattern),
            'has_operators': bool(re.search(r'[+\-*/=<>]', pattern)),
            'has_parentheses': '(' in pattern and ')' in pattern,
            'has_brackets': '[' in pattern and ']' in pattern,
            'has_logic': bool(re.search(r'\b(and|or|not|if|then)\b', pattern.lower())),
            'complexity': self._calculate_pattern_complexity(pattern)
        }
        
        return features
    
    def _calculate_pattern_complexity(self, pattern: str) -> float:
        """Calcola complessità di un pattern."""
        # Fattori di complessità
        factors = {
            'length': len(pattern) / 100.0,
            'nesting': pattern.count('(') + pattern.count('['),
            'operators': len(re.findall(r'[+\-*/=<>]', pattern)),
            'keywords': len(re.findall(r'\b(and|or|not|if|then|for|while)\b', pattern.lower()))
        }
        
        return sum(factors.values()) / len(factors)
    
    def _discover_patterns_incrementally(self, session: LearningSession, 
                                       config: DomainBootstrap,
                                       max_iterations: int) -> List[PatternCandidate]:
        """Scopre pattern incrementalmente per il dominio."""
        logger.info(f"🔍 Scoperta pattern incrementale per {session.domain_name}...")
        
        discovered_patterns = []
        iteration = 0
        
        while iteration < max_iterations and len(discovered_patterns) < config.expected_symbols:
            iteration += 1
            
            # Genera variazioni dei pattern esistenti
            new_patterns = self._generate_pattern_variations(session.learned_patterns)
            
            # Valida nuovi pattern
            validated_patterns = self._validate_patterns(new_patterns, config)
            
            # Converti in PatternCandidate
            for pattern in validated_patterns:
                candidate = PatternCandidate(
                    pattern_id=f"{session.session_id}_pattern_{len(discovered_patterns)}",
                    pattern_type="discovered",
                    frequency=1,  # Inizialmente 1
                    confidence=0.5,  # Da validare
                    description=f"Pattern scoperto per dominio {session.domain_name}: {pattern}",
                    semantic_context={"domain": session.domain_name, "pattern": pattern}
                )
                discovered_patterns.append(candidate)
            
            # Aggiorna pattern appresi
            session.learned_patterns.extend(validated_patterns)
            session.patterns_discovered = len(discovered_patterns)
            
            # Log progresso
            if iteration % 10 == 0:
                logger.info(f"   Iterazione {iteration}: {len(discovered_patterns)} pattern scoperti")
        
        self.total_patterns_discovered += len(discovered_patterns)
        
        logger.info(f"✅ Scoperti {len(discovered_patterns)} pattern in {iteration} iterazioni")
        
        return discovered_patterns
    
    def _generate_pattern_variations(self, base_patterns: List[str]) -> List[str]:
        """Genera variazioni dei pattern base."""
        variations = []
        
        for pattern in base_patterns:
            # Variazione 1: Sostituzioni semplici
            var1 = pattern.replace('VAR', 'X').replace('NUM', '1')
            variations.append(var1)
            
            # Variazione 2: Aggiunta operatori
            if '+' not in pattern:
                var2 = pattern + ' + VAR'
                variations.append(var2)
            
            # Variazione 3: Nesting
            var3 = f"({pattern})"
            variations.append(var3)
            
            # Variazione 4: Combinazione
            if len(base_patterns) > 1:
                other_pattern = base_patterns[(base_patterns.index(pattern) + 1) % len(base_patterns)]
                var4 = f"{pattern} AND {other_pattern}"
                variations.append(var4)
        
        # Rimuovi duplicati
        return list(set(variations))
    
    def _validate_patterns(self, patterns: List[str], config: DomainBootstrap) -> List[str]:
        """Valida pattern usando regole del dominio."""
        validated = []
        
        for pattern in patterns:
            # Validazione base
            if len(pattern) < 3 or len(pattern) > 100:
                continue
            
            # Validazione regole specifiche dominio
            is_valid = True
            for rule in config.validation_rules:
                if not self._apply_validation_rule(pattern, rule):
                    is_valid = False
                    break
            
            if is_valid:
                validated.append(pattern)
        
        return validated
    
    def _apply_validation_rule(self, pattern: str, rule: str) -> bool:
        """Applica regola di validazione a un pattern."""
        # Regole semplici per ora
        if rule == "no_empty":
            return len(pattern.strip()) > 0
        elif rule == "has_structure":
            return bool(re.search(r'[A-Z]|[a-z]|\d', pattern))
        elif rule == "balanced_parens":
            return pattern.count('(') == pattern.count(')')
        else:
            return True  # Regola sconosciuta = passa
    
    def _propose_symbols_for_patterns(self, session: LearningSession, 
                                    patterns: List[PatternCandidate],
                                    config: DomainBootstrap) -> List[str]:
        """Propone simboli per i pattern scoperti."""
        logger.info(f"💡 Proponendo simboli per {len(patterns)} pattern...")
        
        proposed_symbols = []
        
        # Pool di simboli Unicode per dominio
        symbol_pools = {
            'math': ['∑', '∏', '∫', '∂', '∇', '∆', '∞', '∅'],
            'logic': ['⊢', '⊣', '⊤', '⊥', '⊨', '⊩', '⊪', '⊫'],
            'coding': ['⟨', '⟩', '⟪', '⟫', '⟬', '⟭', '⟮', '⟯'],
            'general': ['◊', '◈', '◉', '◎', '●', '◐', '◑', '◒']
        }
        
        pool = symbol_pools.get(config.domain_name, symbol_pools['general'])
        
        for i, pattern in enumerate(patterns[:config.expected_symbols]):
            if i < len(pool):
                symbol = pool[i]
                proposed_symbols.append(symbol)
                
                # Persisti proposta
                self._persist_symbol_proposal(session, pattern, symbol)
        
        session.proposed_symbols = proposed_symbols
        session.symbols_proposed = len(proposed_symbols)
        self.total_symbols_created += len(proposed_symbols)
        
        logger.info(f"✅ Proposti {len(proposed_symbols)} simboli")
        
        return proposed_symbols
    
    def _validate_learning_results(self, session: LearningSession, 
                                 config: DomainBootstrap) -> float:
        """Valida risultati del learning."""
        logger.info(f"🔍 Validando risultati learning per {session.domain_name}...")
        
        scores = []
        
        # Score 1: Numero pattern scoperti vs attesi
        pattern_score = min(session.patterns_discovered / config.expected_symbols, 1.0)
        scores.append(pattern_score)
        
        # Score 2: Qualità pattern (complessità media)
        if session.learned_patterns:
            complexity_scores = [self._calculate_pattern_complexity(p) for p in session.learned_patterns]
            avg_complexity = sum(complexity_scores) / len(complexity_scores)
            complexity_score = min(avg_complexity, 1.0)
            scores.append(complexity_score)
        
        # Score 3: Copertura simbolica
        symbol_score = min(session.symbols_proposed / config.expected_symbols, 1.0)
        scores.append(symbol_score)
        
        # Score finale (media pesata)
        final_score = sum(scores) / len(scores) if scores else 0.0
        
        logger.info(f"✅ Validation score: {final_score:.2f}")
        
        return final_score
    
    def _persist_session(self, session: LearningSession):
        """Persiste sessione nel database."""
        self.db.execute("""
            INSERT OR REPLACE INTO learning_sessions
            (session_id, domain_name, start_time, patterns_discovered, 
             symbols_proposed, validation_score, status, learned_patterns, proposed_symbols)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            session.session_id,
            session.domain_name,
            session.start_time,
            session.patterns_discovered,
            session.symbols_proposed,
            session.validation_score,
            session.status,
            json.dumps(session.learned_patterns),
            json.dumps(session.proposed_symbols)
        ))
        
        self.db.commit()
    
    def _update_session_status(self, session: LearningSession):
        """Aggiorna status sessione nel database."""
        self.db.execute("""
            UPDATE learning_sessions 
            SET end_time = ?, patterns_discovered = ?, symbols_proposed = ?,
                validation_score = ?, status = ?, learned_patterns = ?, proposed_symbols = ?
            WHERE session_id = ?
        """, (
            time.time(),
            session.patterns_discovered,
            session.symbols_proposed,
            session.validation_score,
            session.status,
            json.dumps(session.learned_patterns),
            json.dumps(session.proposed_symbols),
            session.session_id
        ))
        
        self.db.commit()
    
    def _persist_symbol_proposal(self, session: LearningSession, 
                                pattern: PatternCandidate, symbol: str):
        """Persiste proposta simbolo nel database."""
        self.db.execute("""
            INSERT INTO proposed_symbols
            (symbol_id, session_id, domain_name, symbol, pattern_basis, 
             confidence, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            f"{session.session_id}_{symbol}",
            session.session_id,
            session.domain_name,
            symbol,
            pattern.description,
            pattern.confidence,
            time.time()
        ))
        
        self.db.commit()
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche del sistema di learning."""
        return {
            'domains_learned': self.domains_learned,
            'total_patterns_discovered': self.total_patterns_discovered,
            'total_symbols_created': self.total_symbols_created,
            'active_sessions': len(self.active_sessions),
            'completed_sessions': len(self.completed_sessions),
            'avg_validation_score': sum(s.validation_score for s in self.completed_sessions) / len(self.completed_sessions) if self.completed_sessions else 0.0
        }
