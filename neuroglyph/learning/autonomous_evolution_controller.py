"""
NEUROGLYPH Autonomous Evolution Controller

Controller principale per coordinare l'evoluzione autonoma del sistema:
- Pattern Learning Engine
- Symbol Evolution System  
- Cold Start Learning System

Fase 7.0 - Autonomous Evolution
"""

import logging
import time
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path

from .pattern_learning_engine import PatternLearningEngine, PatternCandidate
from .symbol_evolution_system import SymbolEvolutionSystem, SymbolEvolutionProposal
from .cold_start_learning import ColdStartLearningSystem, DomainBootstrap
from ..logic.proof_tree import ProofTree

logger = logging.getLogger(__name__)


@dataclass
class EvolutionConfig:
    """Configurazione per evoluzione autonoma."""
    enable_pattern_learning: bool = True
    enable_symbol_evolution: bool = True
    enable_cold_start_learning: bool = True
    learning_interval_seconds: int = 3600  # 1 ora
    evolution_interval_seconds: int = 7200  # 2 ore
    max_concurrent_sessions: int = 3
    auto_apply_high_confidence: bool = False
    confidence_threshold_auto_apply: float = 0.9


class AutonomousEvolutionController:
    """
    Controller principale per evoluzione autonoma NEUROGLYPH.
    
    Coordina tutti i sistemi di learning e evoluzione per:
    - Apprendimento continuo di pattern
    - Evoluzione automatica simboli
    - Bootstrap di nuovi domini
    - Applicazione automatica miglioramenti
    """
    
    def __init__(self, config: Optional[EvolutionConfig] = None):
        """
        Inizializza Autonomous Evolution Controller.
        
        Args:
            config: Configurazione evoluzione
        """
        self.config = config or EvolutionConfig()
        
        # Inizializza componenti
        self.pattern_engine = PatternLearningEngine() if self.config.enable_pattern_learning else None
        self.evolution_system = SymbolEvolutionSystem() if self.config.enable_symbol_evolution else None
        self.cold_start_system = ColdStartLearningSystem() if self.config.enable_cold_start_learning else None
        
        # Stato controller
        self.is_running = False
        self.last_learning_run = 0.0
        self.last_evolution_run = 0.0
        
        # Metriche aggregate
        self.total_patterns_learned = 0
        self.total_symbols_evolved = 0
        self.total_domains_bootstrapped = 0
        self.evolution_cycles_completed = 0
        
        # Queue per proof trees da analizzare
        self.proof_queue: List[ProofTree] = []
        self.code_samples_queue: List[str] = []
        
        logger.info(f"🎛️ AutonomousEvolutionController inizializzato")
        logger.info(f"   - Pattern Learning: {'✅' if self.pattern_engine else '❌'}")
        logger.info(f"   - Symbol Evolution: {'✅' if self.evolution_system else '❌'}")
        logger.info(f"   - Cold Start Learning: {'✅' if self.cold_start_system else '❌'}")
    
    async def start_autonomous_evolution(self):
        """Avvia evoluzione autonoma in background."""
        if self.is_running:
            logger.warning("⚠️ Evoluzione autonoma già in esecuzione")
            return
        
        self.is_running = True
        logger.info("🚀 Avviando evoluzione autonoma NEUROGLYPH...")
        
        try:
            # Avvia loop principale
            await self._main_evolution_loop()
        except Exception as e:
            logger.error(f"❌ Errore in evoluzione autonoma: {e}")
        finally:
            self.is_running = False
            logger.info("🛑 Evoluzione autonoma terminata")
    
    async def stop_autonomous_evolution(self):
        """Ferma evoluzione autonoma."""
        logger.info("🛑 Fermando evoluzione autonoma...")
        self.is_running = False
    
    async def _main_evolution_loop(self):
        """Loop principale di evoluzione."""
        while self.is_running:
            try:
                current_time = time.time()
                
                # Controlla se è tempo per pattern learning
                if (self.pattern_engine and 
                    current_time - self.last_learning_run >= self.config.learning_interval_seconds):
                    await self._run_pattern_learning_cycle()
                    self.last_learning_run = current_time
                
                # Controlla se è tempo per symbol evolution
                if (self.evolution_system and 
                    current_time - self.last_evolution_run >= self.config.evolution_interval_seconds):
                    await self._run_symbol_evolution_cycle()
                    self.last_evolution_run = current_time
                
                # Processa queue di proof trees
                if self.proof_queue:
                    await self._process_proof_queue()
                
                # Processa queue di code samples
                if self.code_samples_queue:
                    await self._process_code_queue()
                
                # Sleep prima del prossimo ciclo
                await asyncio.sleep(60)  # Controlla ogni minuto
                
            except Exception as e:
                logger.error(f"❌ Errore nel loop evoluzione: {e}")
                await asyncio.sleep(60)
    
    async def _run_pattern_learning_cycle(self):
        """Esegue ciclo di pattern learning."""
        logger.info("🧠 Avviando ciclo Pattern Learning...")
        
        try:
            # Analizza proof trees accumulati
            if self.proof_queue:
                proof_patterns = self.pattern_engine.analyze_proof_patterns(self.proof_queue)
                self.total_patterns_learned += len(proof_patterns)
                
                # Proponi nuovi simboli
                if proof_patterns:
                    proposals = self.pattern_engine.propose_new_symbols(proof_patterns)
                    
                    # Auto-applica proposte ad alta confidence
                    if self.config.auto_apply_high_confidence:
                        await self._auto_apply_high_confidence_proposals(proposals)
                
                # Pulisci queue
                self.proof_queue.clear()
            
            # Analizza code samples
            if self.code_samples_queue:
                ast_patterns = self.pattern_engine.analyze_ast_patterns(self.code_samples_queue)
                self.total_patterns_learned += len(ast_patterns)
                
                # Pulisci queue
                self.code_samples_queue.clear()
            
            # Identifica gap simbolici
            gaps = self.pattern_engine.identify_symbol_gaps()
            
            # Avvia cold start learning per gap critici
            if self.cold_start_system:
                await self._handle_critical_gaps(gaps)
            
            logger.info(f"✅ Ciclo Pattern Learning completato")
            
        except Exception as e:
            logger.error(f"❌ Errore in pattern learning cycle: {e}")
    
    async def _run_symbol_evolution_cycle(self):
        """Esegue ciclo di symbol evolution."""
        logger.info("🧬 Avviando ciclo Symbol Evolution...")
        
        try:
            # Analizza trend utilizzo
            trends = self.evolution_system.analyze_symbol_trends()
            
            # Identifica opportunità evoluzione
            proposals = self.evolution_system.identify_evolution_opportunities()
            self.total_symbols_evolved += len(proposals)
            
            # Auto-applica proposte sicure
            if self.config.auto_apply_high_confidence:
                safe_proposals = [p for p in proposals 
                                if p.confidence >= self.config.confidence_threshold_auto_apply
                                and p.proposal_type in ['deprecate', 'add']]
                
                for proposal in safe_proposals:
                    await self._apply_evolution_proposal(proposal)
            
            self.evolution_cycles_completed += 1
            
            logger.info(f"✅ Ciclo Symbol Evolution completato")
            logger.info(f"   - Trend analizzati: {sum(len(symbols) for symbols in trends.values())}")
            logger.info(f"   - Proposte generate: {len(proposals)}")
            
        except Exception as e:
            logger.error(f"❌ Errore in symbol evolution cycle: {e}")
    
    async def _process_proof_queue(self):
        """Processa queue di proof trees."""
        if not self.pattern_engine:
            return
        
        # Processa in batch per efficienza
        batch_size = min(50, len(self.proof_queue))
        batch = self.proof_queue[:batch_size]
        
        try:
            # Analizza batch
            patterns = self.pattern_engine.analyze_proof_patterns(batch)
            
            # Rimuovi batch processato
            self.proof_queue = self.proof_queue[batch_size:]
            
            logger.debug(f"📊 Processati {batch_size} proof trees, {len(patterns)} pattern identificati")
            
        except Exception as e:
            logger.error(f"❌ Errore processing proof queue: {e}")
    
    async def _process_code_queue(self):
        """Processa queue di code samples."""
        if not self.pattern_engine:
            return
        
        # Processa in batch
        batch_size = min(100, len(self.code_samples_queue))
        batch = self.code_samples_queue[:batch_size]
        
        try:
            # Analizza batch
            patterns = self.pattern_engine.analyze_ast_patterns(batch)
            
            # Rimuovi batch processato
            self.code_samples_queue = self.code_samples_queue[batch_size:]
            
            logger.debug(f"📊 Processati {batch_size} code samples, {len(patterns)} pattern identificati")
            
        except Exception as e:
            logger.error(f"❌ Errore processing code queue: {e}")
    
    async def _auto_apply_high_confidence_proposals(self, proposals: List[Dict[str, Any]]):
        """Applica automaticamente proposte ad alta confidence."""
        high_confidence = [p for p in proposals 
                          if p['confidence'] >= self.config.confidence_threshold_auto_apply]
        
        for proposal in high_confidence:
            try:
                # Simula applicazione (in un sistema reale, modificherebbe il registry)
                logger.info(f"🔄 Auto-applicando proposta: {proposal['suggested_symbol']} per {proposal['pattern_id']}")
                
                # Qui andrebbe l'integrazione con il registry manager
                # registry_manager.add_symbol(proposal['suggested_symbol'], proposal['metadata'])
                
            except Exception as e:
                logger.error(f"❌ Errore auto-applicando proposta: {e}")
    
    async def _handle_critical_gaps(self, gaps):
        """Gestisce gap critici avviando cold start learning."""
        if not self.cold_start_system:
            return
        
        critical_gaps = [gap for gap in gaps if gap.priority >= 0.8]
        
        for gap in critical_gaps:
            # Controlla se abbiamo già una sessione attiva per questo dominio
            active_domains = [session.domain_name for session in self.cold_start_system.active_sessions.values()]
            
            if gap.domain not in active_domains:
                # Avvia bootstrap per dominio
                bootstrap_config = DomainBootstrap(
                    domain_name=gap.domain,
                    seed_patterns=gap.missing_patterns,
                    expected_symbols=min(gap.frequency, 10),
                    confidence_threshold=0.6,
                    validation_rules=['no_empty', 'has_structure']
                )
                
                session_id = self.cold_start_system.bootstrap_new_domain(bootstrap_config)
                self.total_domains_bootstrapped += 1
                
                logger.info(f"🚀 Avviato bootstrap per dominio critico '{gap.domain}' (sessione: {session_id})")
    
    async def _apply_evolution_proposal(self, proposal: SymbolEvolutionProposal):
        """Applica proposta di evoluzione."""
        try:
            logger.info(f"🔄 Applicando proposta evoluzione: {proposal.proposal_type} per {proposal.target_symbol}")
            
            # Qui andrebbe l'integrazione con il registry manager
            if proposal.proposal_type == "add":
                # registry_manager.add_symbol(proposal.new_symbol, proposal.metadata)
                pass
            elif proposal.proposal_type == "deprecate":
                # registry_manager.deprecate_symbol(proposal.target_symbol)
                pass
            elif proposal.proposal_type == "modify":
                # registry_manager.modify_symbol(proposal.target_symbol, proposal.metadata)
                pass
            
            # Aggiorna status nel database
            # proposal.status = "applied"
            
        except Exception as e:
            logger.error(f"❌ Errore applicando proposta evoluzione: {e}")
    
    def add_proof_for_analysis(self, proof: ProofTree):
        """Aggiunge proof tree alla queue per analisi."""
        self.proof_queue.append(proof)
        
        # Traccia utilizzo simboli se evolution system attivo
        if self.evolution_system:
            for step in proof.steps:
                if step.formula:
                    # Estrai simboli dalla formula e traccia utilizzo
                    symbols = self._extract_symbols_from_formula(step.formula)
                    for symbol in symbols:
                        self.evolution_system.track_symbol_usage(
                            symbol=symbol,
                            context_type="reasoning",
                            domain="logic",
                            confidence=0.8
                        )
    
    def add_code_for_analysis(self, code: str):
        """Aggiunge code sample alla queue per analisi."""
        self.code_samples_queue.append(code)
    
    def _extract_symbols_from_formula(self, formula) -> List[str]:
        """Estrae simboli da una formula logica."""
        # Implementazione semplificata
        formula_str = str(formula)
        # Cerca pattern di simboli NEUROGLYPH
        import re
        symbols = re.findall(r'<NG_[^>]+>', formula_str)
        return symbols
    
    def bootstrap_domain(self, domain_name: str, seed_patterns: List[str], 
                         expected_symbols: int = 5) -> Optional[str]:
        """
        Avvia bootstrap manuale di un dominio.
        
        Args:
            domain_name: Nome del dominio
            seed_patterns: Pattern seed per il dominio
            expected_symbols: Numero simboli attesi
            
        Returns:
            ID sessione se avviata, None altrimenti
        """
        if not self.cold_start_system:
            logger.error("❌ Cold Start Learning non abilitato")
            return None
        
        config = DomainBootstrap(
            domain_name=domain_name,
            seed_patterns=seed_patterns,
            expected_symbols=expected_symbols,
            confidence_threshold=0.6,
            validation_rules=['no_empty', 'has_structure', 'balanced_parens']
        )
        
        session_id = self.cold_start_system.bootstrap_new_domain(config)
        self.total_domains_bootstrapped += 1
        
        return session_id
    
    def get_evolution_status(self) -> Dict[str, Any]:
        """Restituisce status completo dell'evoluzione autonoma."""
        status = {
            'is_running': self.is_running,
            'config': {
                'pattern_learning_enabled': self.config.enable_pattern_learning,
                'symbol_evolution_enabled': self.config.enable_symbol_evolution,
                'cold_start_learning_enabled': self.config.enable_cold_start_learning,
                'learning_interval': self.config.learning_interval_seconds,
                'evolution_interval': self.config.evolution_interval_seconds
            },
            'metrics': {
                'total_patterns_learned': self.total_patterns_learned,
                'total_symbols_evolved': self.total_symbols_evolved,
                'total_domains_bootstrapped': self.total_domains_bootstrapped,
                'evolution_cycles_completed': self.evolution_cycles_completed,
                'proof_queue_size': len(self.proof_queue),
                'code_queue_size': len(self.code_samples_queue)
            },
            'last_runs': {
                'last_learning_run': self.last_learning_run,
                'last_evolution_run': self.last_evolution_run
            }
        }
        
        # Aggiungi statistiche componenti
        if self.pattern_engine:
            status['pattern_engine_stats'] = self.pattern_engine.get_learning_statistics()
        
        if self.evolution_system:
            status['evolution_system_stats'] = self.evolution_system.get_evolution_statistics()
        
        if self.cold_start_system:
            status['cold_start_stats'] = self.cold_start_system.get_learning_statistics()
        
        return status
