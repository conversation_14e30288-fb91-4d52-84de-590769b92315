"""
NEUROGLYPH Symbol Evolution System - Autonomous Symbol Management

Sistema per il monitoraggio dell'utilizzo dei simboli e l'evoluzione
automatica del registry simbolico basata su dati di utilizzo reale.

Fase 7.0 - Autonomous Evolution
"""

import logging
import time
import json
import sqlite3
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import Counter, defaultdict
import statistics

from ..utils.registry_loader import load_registry_fast, registry_loader
from .pattern_learning_engine import PatternCandidate, SymbolGap

logger = logging.getLogger(__name__)


@dataclass
class SymbolUsageStats:
    """Statistiche di utilizzo per un simbolo."""
    symbol: str
    total_usage: int
    recent_usage: int  # Ultimi 7 giorni
    domains_used: Set[str] = field(default_factory=set)
    contexts_used: Set[str] = field(default_factory=set)
    avg_confidence: float = 0.0
    last_used: float = 0.0
    trend: str = "stable"  # growing, stable, declining, unused


@dataclass
class SymbolEvolutionProposal:
    """Proposta di evoluzione simbolica."""
    proposal_id: str
    proposal_type: str  # 'add', 'modify', 'deprecate', 'merge'
    target_symbol: Optional[str]
    new_symbol: Optional[str]
    rationale: str
    confidence: float
    impact_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)


class SymbolEvolutionSystem:
    """
    Sistema di evoluzione automatica dei simboli NEUROGLYPH.
    
    Features:
    - Monitoraggio utilizzo simboli in tempo reale
    - Identificazione simboli sottoutilizzati o ridondanti
    - Proposta automatica di miglioramenti al registry
    - Evoluzione incrementale basata su feedback
    """
    
    def __init__(self, db_path: str = "symbol_evolution.db"):
        """
        Inizializza Symbol Evolution System.
        
        Args:
            db_path: Percorso database per tracking utilizzo
        """
        self.db_path = db_path
        self.db = None
        
        # Registry e statistiche
        self.current_registry = {}
        self.usage_stats: Dict[str, SymbolUsageStats] = {}
        self.evolution_proposals: List[SymbolEvolutionProposal] = []
        
        # Configurazione evoluzione
        self.min_usage_threshold = 5  # Utilizzi minimi per considerare simbolo attivo
        self.deprecation_threshold = 30  # Giorni senza utilizzo per deprecazione
        self.confidence_threshold = 0.7
        self.max_proposals_per_run = 20
        
        # Metriche
        self.symbols_tracked = 0
        self.proposals_generated = 0
        self.evolutions_applied = 0
        
        self._initialize_database()
        self._load_current_registry()
        
        logger.info(f"🧬 SymbolEvolutionSystem inizializzato")
        logger.info(f"   - Simboli tracciati: {len(self.current_registry)}")
        logger.info(f"   - Database: {self.db_path}")
    
    def _initialize_database(self):
        """Inizializza database per tracking utilizzo."""
        self.db = sqlite3.connect(self.db_path, check_same_thread=False)
        
        # Tabella utilizzo simboli
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS symbol_usage (
                usage_id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                context_type TEXT NOT NULL,  -- parsing, encoding, reasoning
                domain TEXT,
                confidence REAL,
                timestamp REAL NOT NULL,
                metadata TEXT  -- JSON
            )
        """)
        
        # Tabella proposte evoluzione
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS evolution_proposals (
                proposal_id TEXT PRIMARY KEY,
                proposal_type TEXT NOT NULL,
                target_symbol TEXT,
                new_symbol TEXT,
                rationale TEXT NOT NULL,
                confidence REAL NOT NULL,
                impact_score REAL NOT NULL,
                metadata TEXT,  -- JSON
                created_at REAL NOT NULL,
                status TEXT DEFAULT 'pending'  -- pending, approved, rejected, applied
            )
        """)
        
        # Tabella trend simboli
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS symbol_trends (
                symbol TEXT PRIMARY KEY,
                total_usage INTEGER DEFAULT 0,
                recent_usage INTEGER DEFAULT 0,
                domains_used TEXT,  -- JSON
                contexts_used TEXT,  -- JSON
                avg_confidence REAL DEFAULT 0.0,
                last_used REAL,
                trend TEXT DEFAULT 'stable',
                updated_at REAL NOT NULL
            )
        """)
        
        self.db.commit()
        logger.debug("✅ Database symbol evolution inizializzato")
    
    def _load_current_registry(self):
        """Carica registry simbolico corrente."""
        try:
            registry = load_registry_fast()
            self.current_registry = registry['symbols']
            self.symbols_tracked = len(self.current_registry)
            
            logger.info(f"✅ Registry caricato: {self.symbols_tracked} simboli")
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento registry: {e}")
            self.current_registry = {}
    
    def track_symbol_usage(self, symbol: str, context_type: str, 
                          domain: Optional[str] = None, 
                          confidence: Optional[float] = None,
                          metadata: Optional[Dict[str, Any]] = None):
        """
        Traccia utilizzo di un simbolo.
        
        Args:
            symbol: Simbolo utilizzato
            context_type: Contesto di utilizzo (parsing, encoding, reasoning)
            domain: Dominio di utilizzo
            confidence: Confidence dell'utilizzo
            metadata: Metadati aggiuntivi
        """
        timestamp = time.time()
        
        # Registra nel database
        self.db.execute("""
            INSERT INTO symbol_usage 
            (symbol, context_type, domain, confidence, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            symbol,
            context_type,
            domain,
            confidence,
            timestamp,
            json.dumps(metadata or {})
        ))
        
        self.db.commit()
        
        # Aggiorna statistiche in memoria
        self._update_usage_stats(symbol, context_type, domain, confidence, timestamp)
    
    def _update_usage_stats(self, symbol: str, context_type: str, 
                           domain: Optional[str], confidence: Optional[float],
                           timestamp: float):
        """Aggiorna statistiche di utilizzo in memoria."""
        if symbol not in self.usage_stats:
            self.usage_stats[symbol] = SymbolUsageStats(
                symbol=symbol,
                total_usage=0,
                recent_usage=0
            )
        
        stats = self.usage_stats[symbol]
        stats.total_usage += 1
        stats.last_used = timestamp
        
        # Utilizzo recente (ultimi 7 giorni)
        week_ago = timestamp - (7 * 24 * 3600)
        if timestamp > week_ago:
            stats.recent_usage += 1
        
        if domain:
            stats.domains_used.add(domain)
        
        stats.contexts_used.add(context_type)
        
        if confidence is not None:
            # Aggiorna media confidence
            if stats.avg_confidence == 0.0:
                stats.avg_confidence = confidence
            else:
                stats.avg_confidence = (stats.avg_confidence + confidence) / 2
    
    def analyze_symbol_trends(self) -> Dict[str, List[str]]:
        """
        Analizza trend di utilizzo dei simboli.
        
        Returns:
            Dizionario con simboli categorizzati per trend
        """
        logger.info("📈 Analizzando trend utilizzo simboli...")
        
        trends = {
            'growing': [],
            'stable': [],
            'declining': [],
            'unused': []
        }
        
        current_time = time.time()
        week_ago = current_time - (7 * 24 * 3600)
        month_ago = current_time - (30 * 24 * 3600)
        
        for symbol in self.current_registry.keys():
            # Carica statistiche dal database
            usage_data = self._get_symbol_usage_from_db(symbol, month_ago)
            
            if not usage_data:
                trends['unused'].append(symbol)
                continue
            
            # Calcola trend
            recent_usage = sum(1 for timestamp in usage_data if timestamp > week_ago)
            older_usage = sum(1 for timestamp in usage_data if week_ago >= timestamp > month_ago)
            
            if recent_usage > older_usage * 1.5:
                trend = 'growing'
            elif recent_usage < older_usage * 0.5:
                trend = 'declining'
            else:
                trend = 'stable'
            
            trends[trend].append(symbol)
            
            # Aggiorna stats in memoria
            if symbol in self.usage_stats:
                self.usage_stats[symbol].trend = trend
        
        logger.info(f"✅ Trend analizzati:")
        for trend_type, symbols in trends.items():
            logger.info(f"   - {trend_type}: {len(symbols)} simboli")
        
        return trends
    
    def _get_symbol_usage_from_db(self, symbol: str, since: float) -> List[float]:
        """Recupera timestamp di utilizzo dal database."""
        cursor = self.db.execute("""
            SELECT timestamp FROM symbol_usage 
            WHERE symbol = ? AND timestamp > ?
            ORDER BY timestamp DESC
        """, (symbol, since))
        
        return [row[0] for row in cursor.fetchall()]
    
    def identify_evolution_opportunities(self) -> List[SymbolEvolutionProposal]:
        """
        Identifica opportunità di evoluzione simbolica.
        
        Returns:
            Lista di proposte di evoluzione
        """
        logger.info("🔍 Identificando opportunità di evoluzione...")
        
        proposals = []
        trends = self.analyze_symbol_trends()
        
        # 1. Simboli da deprecare (non utilizzati)
        for symbol in trends['unused']:
            if self._should_deprecate_symbol(symbol):
                proposal = SymbolEvolutionProposal(
                    proposal_id=f"deprecate_{symbol}_{int(time.time())}",
                    proposal_type="deprecate",
                    target_symbol=symbol,
                    new_symbol=None,
                    rationale=f"Simbolo '{symbol}' non utilizzato da oltre {self.deprecation_threshold} giorni",
                    confidence=0.8,
                    impact_score=0.2,  # Basso impatto
                    metadata={"unused_days": self._get_unused_days(symbol)}
                )
                proposals.append(proposal)
        
        # 2. Simboli da ottimizzare (bassa confidence)
        for symbol, stats in self.usage_stats.items():
            if stats.avg_confidence < self.confidence_threshold and stats.total_usage > 0:
                proposal = SymbolEvolutionProposal(
                    proposal_id=f"optimize_{symbol}_{int(time.time())}",
                    proposal_type="modify",
                    target_symbol=symbol,
                    new_symbol=None,
                    rationale=f"Simbolo '{symbol}' ha confidence bassa ({stats.avg_confidence:.2f})",
                    confidence=0.7,
                    impact_score=0.6,
                    metadata={"current_confidence": stats.avg_confidence, "usage_count": stats.total_usage}
                )
                proposals.append(proposal)
        
        # 3. Nuovi simboli da aggiungere (gap identificati)
        gap_proposals = self._generate_gap_filling_proposals()
        proposals.extend(gap_proposals)
        
        # 4. Simboli da unire (ridondanti)
        merge_proposals = self._identify_merge_opportunities()
        proposals.extend(merge_proposals)
        
        # Ordina per impact score
        proposals.sort(key=lambda p: p.impact_score, reverse=True)
        
        # Limita numero proposte
        proposals = proposals[:self.max_proposals_per_run]
        
        self.evolution_proposals.extend(proposals)
        self.proposals_generated += len(proposals)
        
        # Persisti proposte
        self._persist_proposals(proposals)
        
        logger.info(f"✅ Identificate {len(proposals)} opportunità di evoluzione")
        
        return proposals
    
    def _should_deprecate_symbol(self, symbol: str) -> bool:
        """Verifica se un simbolo dovrebbe essere deprecato."""
        # Controlla se simbolo è critico (non deprecare mai)
        critical_symbols = {'⟨⟩', '◊', '⟲', '⟳', '⟦⟧'}  # Simboli base
        if symbol in critical_symbols:
            return False
        
        # Controlla utilizzo recente
        usage_data = self._get_symbol_usage_from_db(symbol, time.time() - (self.deprecation_threshold * 24 * 3600))
        return len(usage_data) == 0
    
    def _get_unused_days(self, symbol: str) -> int:
        """Calcola giorni dall'ultimo utilizzo."""
        cursor = self.db.execute("""
            SELECT MAX(timestamp) FROM symbol_usage WHERE symbol = ?
        """, (symbol,))
        
        result = cursor.fetchone()
        if result[0]:
            days_unused = (time.time() - result[0]) / (24 * 3600)
            return int(days_unused)
        
        return 999  # Mai utilizzato
    
    def _generate_gap_filling_proposals(self) -> List[SymbolEvolutionProposal]:
        """Genera proposte per riempire gap simbolici."""
        proposals = []
        
        # Domini con bassa copertura
        low_coverage_domains = self._identify_low_coverage_domains()
        
        for domain in low_coverage_domains:
            proposal = SymbolEvolutionProposal(
                proposal_id=f"add_domain_{domain}_{int(time.time())}",
                proposal_type="add",
                target_symbol=None,
                new_symbol=self._suggest_symbol_for_domain(domain),
                rationale=f"Dominio '{domain}' ha copertura simbolica insufficiente",
                confidence=0.6,
                impact_score=0.8,
                metadata={"domain": domain, "current_coverage": self._get_domain_coverage(domain)}
            )
            proposals.append(proposal)
        
        return proposals
    
    def _identify_low_coverage_domains(self) -> List[str]:
        """Identifica domini con bassa copertura simbolica."""
        domain_coverage = defaultdict(int)
        
        for symbol_data in self.current_registry.values():
            domain = symbol_data.get('domain', 'unknown')
            domain_coverage[domain] += 1
        
        # Domini con meno di 10 simboli
        return [domain for domain, count in domain_coverage.items() if count < 10]
    
    def _get_domain_coverage(self, domain: str) -> int:
        """Ottiene numero di simboli per dominio."""
        count = 0
        for symbol_data in self.current_registry.values():
            if symbol_data.get('domain') == domain:
                count += 1
        return count
    
    def _suggest_symbol_for_domain(self, domain: str) -> str:
        """Suggerisce simbolo per dominio."""
        # Pool di simboli per dominio
        domain_symbols = {
            'logic': ['⊢', '⊣', '⊤', '⊥', '⊨'],
            'math': ['∑', '∏', '∫', '∂', '∇'],
            'coding': ['⟨', '⟩', '⟪', '⟫', '⟬'],
            'control_flow': ['◊', '◈', '◉', '◎', '●']
        }
        
        pool = domain_symbols.get(domain, ['◆', '◇', '◈', '◉'])
        used_symbols = set(self.current_registry.keys())
        
        for symbol in pool:
            if symbol not in used_symbols:
                return symbol
        
        return f"⟨{domain[:2]}⟩"  # Fallback
    
    def _identify_merge_opportunities(self) -> List[SymbolEvolutionProposal]:
        """Identifica simboli ridondanti da unire."""
        proposals = []
        
        # Trova simboli con utilizzo simile
        similar_symbols = self._find_similar_usage_patterns()
        
        for symbol_pair in similar_symbols:
            symbol1, symbol2 = symbol_pair
            proposal = SymbolEvolutionProposal(
                proposal_id=f"merge_{symbol1}_{symbol2}_{int(time.time())}",
                proposal_type="merge",
                target_symbol=symbol1,
                new_symbol=symbol2,  # Mantieni il più utilizzato
                rationale=f"Simboli '{symbol1}' e '{symbol2}' hanno pattern di utilizzo simili",
                confidence=0.5,
                impact_score=0.4,
                metadata={"symbols_to_merge": [symbol1, symbol2]}
            )
            proposals.append(proposal)
        
        return proposals
    
    def _find_similar_usage_patterns(self) -> List[Tuple[str, str]]:
        """Trova simboli con pattern di utilizzo simili."""
        # Implementazione semplificata - da migliorare
        similar_pairs = []
        
        symbols_with_stats = [(s, stats) for s, stats in self.usage_stats.items() 
                             if stats.total_usage > 0]
        
        for i, (symbol1, stats1) in enumerate(symbols_with_stats):
            for symbol2, stats2 in symbols_with_stats[i+1:]:
                # Verifica similarità
                if (stats1.domains_used == stats2.domains_used and 
                    abs(stats1.avg_confidence - stats2.avg_confidence) < 0.1):
                    similar_pairs.append((symbol1, symbol2))
        
        return similar_pairs[:5]  # Limita a 5 coppie
    
    def _persist_proposals(self, proposals: List[SymbolEvolutionProposal]):
        """Persiste proposte nel database."""
        for proposal in proposals:
            self.db.execute("""
                INSERT OR REPLACE INTO evolution_proposals
                (proposal_id, proposal_type, target_symbol, new_symbol, 
                 rationale, confidence, impact_score, metadata, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                proposal.proposal_id,
                proposal.proposal_type,
                proposal.target_symbol,
                proposal.new_symbol,
                proposal.rationale,
                proposal.confidence,
                proposal.impact_score,
                json.dumps(proposal.metadata),
                proposal.created_at
            ))
        
        self.db.commit()
    
    def get_evolution_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche del sistema di evoluzione."""
        return {
            'symbols_tracked': self.symbols_tracked,
            'proposals_generated': self.proposals_generated,
            'evolutions_applied': self.evolutions_applied,
            'active_proposals': len(self.evolution_proposals),
            'usage_stats_count': len(self.usage_stats),
            'avg_symbol_usage': statistics.mean([s.total_usage for s in self.usage_stats.values()]) if self.usage_stats else 0,
            'most_used_symbols': sorted(self.usage_stats.items(), key=lambda x: x[1].total_usage, reverse=True)[:5]
        }
