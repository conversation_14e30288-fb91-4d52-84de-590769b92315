"""
NEUROGLYPH Knowledge Graph Builder
Costruzione grafo error→patch→pattern con persistenza SQLite
"""

import sqlite3
import json
import time
import threading
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Tipi di nodi nel knowledge graph."""
    ERROR_TYPE = "error_type"
    PATCH = "patch"
    PATTERN = "pattern"
    CONTEXT = "context"


class EdgeType(Enum):
    """Tipi di edge nel knowledge graph."""
    TRIGGERS = "triggers"          # error_type → patch
    GENERATES = "generates"        # patch → pattern
    SIMILAR_TO = "similar_to"      # pattern → pattern
    CONTEXT_OF = "context_of"      # context → error_type


@dataclass
class KGNode:
    """Nodo del knowledge graph."""
    
    node_id: str
    node_type: NodeType
    properties: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serializza nodo a dict."""
        return {
            'node_id': self.node_id,
            'node_type': self.node_type.value,
            'properties': self.properties,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }


@dataclass
class KGEdge:
    """Edge del knowledge graph."""
    
    edge_id: str
    source_id: str
    target_id: str
    edge_type: EdgeType
    weight: float = 1.0
    properties: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serializza edge a dict."""
        return {
            'edge_id': self.edge_id,
            'source_id': self.source_id,
            'target_id': self.target_id,
            'edge_type': self.edge_type.value,
            'weight': self.weight,
            'properties': self.properties,
            'created_at': self.created_at
        }


class KnowledgeGraphBuilder:
    """
    Knowledge Graph Builder per NEUROGLYPH.
    
    Costruisce e mantiene grafo error→patch→pattern con:
    - Persistenza SQLite per scalabilità
    - Fallback dict/adj-list per prod (no networkx dependency)
    - Thread-safe operations
    - Incremental updates
    """
    
    def __init__(self, db_path: str = "knowledge_graph.db", use_networkx: bool = False):
        """
        Inizializza Knowledge Graph Builder.
        
        Args:
            db_path: Percorso database SQLite
            use_networkx: Usa networkx (dev-mode) o fallback dict (prod)
        """
        self.db_path = db_path
        self.use_networkx = use_networkx
        
        # In-memory graph (fallback)
        self.nodes: Dict[str, KGNode] = {}
        self.edges: Dict[str, KGEdge] = {}
        self.adjacency: Dict[str, Set[str]] = {}  # node_id → set(connected_node_ids)
        
        # NetworkX graph (dev-mode)
        self.nx_graph = None
        if use_networkx:
            try:
                import networkx as nx
                self.nx_graph = nx.MultiDiGraph()
                logger.info("✅ NetworkX enabled for dev-mode")
            except ImportError:
                logger.warning("⚠️ NetworkX not available, using fallback dict")
                self.use_networkx = False
        
        # Threading
        self._lock = threading.RLock()
        
        # Database
        self.db = None
        self._setup_database()
        self._load_graph()
        
        logger.info(f"🧠 KnowledgeGraphBuilder inizializzato")
        logger.info(f"   - Database: {db_path}")
        logger.info(f"   - NetworkX: {self.use_networkx}")
        logger.info(f"   - Nodi caricati: {len(self.nodes)}")
        logger.info(f"   - Edge caricati: {len(self.edges)}")
    
    def _setup_database(self):
        """Setup database con schema knowledge graph."""
        try:
            self.db = sqlite3.connect(
                self.db_path,
                isolation_level=None,  # Autocommit
                check_same_thread=False,
                timeout=30.0
            )
            
            cur = self.db.cursor()
            
            # Schema knowledge graph
            cur.executescript("""
                -- WAL mode per performance
                PRAGMA journal_mode=WAL;
                PRAGMA synchronous=NORMAL;
                PRAGMA cache_size=-10000;
                
                -- Tabella nodi
                CREATE TABLE IF NOT EXISTS kg_nodes(
                    node_id TEXT PRIMARY KEY,
                    node_type TEXT NOT NULL,
                    properties TEXT,
                    created_at REAL NOT NULL,
                    updated_at REAL NOT NULL
                );
                
                -- Tabella edge
                CREATE TABLE IF NOT EXISTS kg_edges(
                    edge_id TEXT PRIMARY KEY,
                    source_id TEXT NOT NULL,
                    target_id TEXT NOT NULL,
                    edge_type TEXT NOT NULL,
                    weight REAL DEFAULT 1.0,
                    properties TEXT,
                    created_at REAL NOT NULL,
                    FOREIGN KEY (source_id) REFERENCES kg_nodes(node_id),
                    FOREIGN KEY (target_id) REFERENCES kg_nodes(node_id)
                );
                
                -- Tabella statistiche
                CREATE TABLE IF NOT EXISTS kg_stats(
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts REAL NOT NULL,
                    total_nodes INTEGER NOT NULL,
                    total_edges INTEGER NOT NULL,
                    node_types TEXT,
                    edge_types TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
                
                -- Indici per performance
                CREATE INDEX IF NOT EXISTS idx_kg_nodes_type
                  ON kg_nodes(node_type);
                  
                CREATE INDEX IF NOT EXISTS idx_kg_edges_source
                  ON kg_edges(source_id);
                  
                CREATE INDEX IF NOT EXISTS idx_kg_edges_target
                  ON kg_edges(target_id);
                  
                CREATE INDEX IF NOT EXISTS idx_kg_edges_type
                  ON kg_edges(edge_type);
                  
                CREATE INDEX IF NOT EXISTS idx_kg_stats_ts
                  ON kg_stats(ts DESC);
            """)
            
            logger.info("✅ Knowledge Graph database setup completato")
            
        except Exception as e:
            logger.error(f"❌ Errore setup KG database: {e}")
            raise
    
    def _load_graph(self):
        """Carica grafo dal database in memoria."""
        try:
            cur = self.db.cursor()
            
            # Carica nodi
            cur.execute("SELECT node_id, node_type, properties, created_at, updated_at FROM kg_nodes")
            for row in cur.fetchall():
                node_id, node_type, props_json, created_at, updated_at = row
                properties = json.loads(props_json) if props_json else {}
                
                node = KGNode(
                    node_id=node_id,
                    node_type=NodeType(node_type),
                    properties=properties,
                    created_at=created_at,
                    updated_at=updated_at
                )
                self.nodes[node_id] = node
                
                # Inizializza adjacency
                if node_id not in self.adjacency:
                    self.adjacency[node_id] = set()
                
                # Aggiungi a NetworkX se disponibile
                if self.nx_graph is not None:
                    self.nx_graph.add_node(node_id, **node.to_dict())
            
            # Carica edge
            cur.execute("SELECT edge_id, source_id, target_id, edge_type, weight, properties, created_at FROM kg_edges")
            for row in cur.fetchall():
                edge_id, source_id, target_id, edge_type, weight, props_json, created_at = row
                properties = json.loads(props_json) if props_json else {}
                
                edge = KGEdge(
                    edge_id=edge_id,
                    source_id=source_id,
                    target_id=target_id,
                    edge_type=EdgeType(edge_type),
                    weight=weight,
                    properties=properties,
                    created_at=created_at
                )
                self.edges[edge_id] = edge
                
                # Aggiorna adjacency
                self.adjacency[source_id].add(target_id)
                if target_id not in self.adjacency:
                    self.adjacency[target_id] = set()
                
                # Aggiungi a NetworkX se disponibile
                if self.nx_graph is not None:
                    self.nx_graph.add_edge(source_id, target_id, key=edge_id, **edge.to_dict())
            
            logger.info(f"✅ Grafo caricato: {len(self.nodes)} nodi, {len(self.edges)} edge")
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento grafo: {e}")
    
    def add_node(self, node: KGNode) -> bool:
        """
        Aggiunge nodo al grafo.
        
        Args:
            node: Nodo da aggiungere
            
        Returns:
            True se aggiunto, False se già esistente
        """
        with self._lock:
            if node.node_id in self.nodes:
                return False
            
            try:
                # Aggiungi al database
                cur = self.db.cursor()
                cur.execute("""
                    INSERT INTO kg_nodes (node_id, node_type, properties, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    node.node_id,
                    node.node_type.value,
                    json.dumps(node.properties),
                    node.created_at,
                    node.updated_at
                ))
                
                # Aggiungi in memoria
                self.nodes[node.node_id] = node
                self.adjacency[node.node_id] = set()
                
                # Aggiungi a NetworkX se disponibile
                if self.nx_graph is not None:
                    self.nx_graph.add_node(node.node_id, **node.to_dict())
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Errore aggiunta nodo {node.node_id}: {e}")
                return False
    
    def add_edge(self, edge: KGEdge) -> bool:
        """
        Aggiunge edge al grafo.
        
        Args:
            edge: Edge da aggiungere
            
        Returns:
            True se aggiunto, False se già esistente o nodi mancanti
        """
        with self._lock:
            if edge.edge_id in self.edges:
                return False
            
            # Verifica che i nodi esistano
            if edge.source_id not in self.nodes or edge.target_id not in self.nodes:
                logger.warning(f"⚠️ Nodi mancanti per edge {edge.edge_id}")
                return False
            
            try:
                # Aggiungi al database
                cur = self.db.cursor()
                cur.execute("""
                    INSERT INTO kg_edges (edge_id, source_id, target_id, edge_type, weight, properties, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    edge.edge_id,
                    edge.source_id,
                    edge.target_id,
                    edge.edge_type.value,
                    edge.weight,
                    json.dumps(edge.properties),
                    edge.created_at
                ))
                
                # Aggiungi in memoria
                self.edges[edge.edge_id] = edge
                self.adjacency[edge.source_id].add(edge.target_id)
                
                # Aggiungi a NetworkX se disponibile
                if self.nx_graph is not None:
                    self.nx_graph.add_edge(edge.source_id, edge.target_id, key=edge.edge_id, **edge.to_dict())
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Errore aggiunta edge {edge.edge_id}: {e}")
                return False

    def get_neighbors(self, node_id: str, edge_type: Optional[EdgeType] = None) -> List[str]:
        """
        Ottiene vicini di un nodo.

        Args:
            node_id: ID del nodo
            edge_type: Tipo di edge da filtrare (opzionale)

        Returns:
            Lista ID nodi vicini
        """
        with self._lock:
            if node_id not in self.adjacency:
                return []

            neighbors = []
            for target_id in self.adjacency[node_id]:
                # Trova edge tra node_id e target_id
                for edge in self.edges.values():
                    if (edge.source_id == node_id and edge.target_id == target_id):
                        if edge_type is None or edge.edge_type == edge_type:
                            neighbors.append(target_id)
                            break

            return neighbors

    def find_path(self, source_id: str, target_id: str, max_depth: int = 3) -> Optional[List[str]]:
        """
        Trova path tra due nodi (BFS).

        Args:
            source_id: Nodo sorgente
            target_id: Nodo target
            max_depth: Profondità massima

        Returns:
            Path come lista di node_id o None se non trovato
        """
        if self.nx_graph is not None:
            # Usa NetworkX se disponibile
            try:
                import networkx as nx
                path = nx.shortest_path(self.nx_graph, source_id, target_id)
                return path if len(path) <= max_depth + 1 else None
            except:
                pass

        # Fallback BFS manuale
        with self._lock:
            if source_id not in self.nodes or target_id not in self.nodes:
                return None

            if source_id == target_id:
                return [source_id]

            queue = [(source_id, [source_id])]
            visited = {source_id}

            while queue:
                current, path = queue.pop(0)

                if len(path) > max_depth + 1:
                    continue

                for neighbor in self.adjacency.get(current, []):
                    if neighbor == target_id:
                        return path + [neighbor]

                    if neighbor not in visited:
                        visited.add(neighbor)
                        queue.append((neighbor, path + [neighbor]))

            return None

    def get_node_stats(self) -> Dict[str, Any]:
        """Statistiche nodi per tipo."""
        with self._lock:
            stats = {}
            for node_type in NodeType:
                count = sum(1 for node in self.nodes.values() if node.node_type == node_type)
                stats[node_type.value] = count
            return stats

    def get_edge_stats(self) -> Dict[str, Any]:
        """Statistiche edge per tipo."""
        with self._lock:
            stats = {}
            for edge_type in EdgeType:
                count = sum(1 for edge in self.edges.values() if edge.edge_type == edge_type)
                stats[edge_type.value] = count
            return stats

    def record_stats(self):
        """Registra statistiche correnti nel database."""
        try:
            cur = self.db.cursor()

            node_stats = self.get_node_stats()
            edge_stats = self.get_edge_stats()

            cur.execute("""
                INSERT INTO kg_stats (ts, total_nodes, total_edges, node_types, edge_types)
                VALUES (?, ?, ?, ?, ?)
            """, (
                time.time(),
                len(self.nodes),
                len(self.edges),
                json.dumps(node_stats),
                json.dumps(edge_stats)
            ))

        except Exception as e:
            logger.error(f"❌ Errore registrazione stats: {e}")

    def create_error_patch_pattern_chain(self, error_type: str, patch_id: str, pattern_id: str) -> bool:
        """
        Crea catena error_type → patch → pattern.

        Args:
            error_type: Tipo di errore
            patch_id: ID della patch
            pattern_id: ID del pattern

        Returns:
            True se creata con successo
        """
        with self._lock:
            try:
                # Crea nodi se non esistono
                error_node = KGNode(
                    node_id=f"error_{error_type}",
                    node_type=NodeType.ERROR_TYPE,
                    properties={"error_type": error_type}
                )
                self.add_node(error_node)

                patch_node = KGNode(
                    node_id=patch_id,
                    node_type=NodeType.PATCH,
                    properties={"patch_id": patch_id}
                )
                self.add_node(patch_node)

                pattern_node = KGNode(
                    node_id=pattern_id,
                    node_type=NodeType.PATTERN,
                    properties={"pattern_id": pattern_id}
                )
                self.add_node(pattern_node)

                # Crea edge
                error_patch_edge = KGEdge(
                    edge_id=f"triggers_{error_type}_{patch_id}",
                    source_id=error_node.node_id,
                    target_id=patch_node.node_id,
                    edge_type=EdgeType.TRIGGERS
                )
                self.add_edge(error_patch_edge)

                patch_pattern_edge = KGEdge(
                    edge_id=f"generates_{patch_id}_{pattern_id}",
                    source_id=patch_node.node_id,
                    target_id=pattern_node.node_id,
                    edge_type=EdgeType.GENERATES
                )
                self.add_edge(patch_pattern_edge)

                return True

            except Exception as e:
                logger.error(f"❌ Errore creazione catena: {e}")
                return False

    def get_summary(self) -> Dict[str, Any]:
        """Riassunto completo del knowledge graph."""
        return {
            "total_nodes": len(self.nodes),
            "total_edges": len(self.edges),
            "node_stats": self.get_node_stats(),
            "edge_stats": self.get_edge_stats(),
            "use_networkx": self.use_networkx,
            "db_path": self.db_path
        }
