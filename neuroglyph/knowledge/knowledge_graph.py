"""
NEUROGLYPH Knowledge Graph - Fase 6.0
Implementazione knowledge graph per reasoning avanzato
"""

import sqlite3
import json
import logging
from dataclasses import dataclass, asdict
from typing import List, Dict, Set, Optional, Tuple, Any
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class KGNode:
    """Nodo del knowledge graph."""
    id: str
    type: str  # 'entity', 'concept', 'theorem', 'definition'
    label: str
    properties: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class KGEdge:
    """Edge del knowledge graph."""
    id: str
    source_id: str
    target_id: str
    relation: str  # 'implies', 'equivalent', 'contradicts', 'supports', 'instance_of'
    weight: float = 1.0
    properties: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class KGFact:
    """Fatto nel knowledge graph."""
    id: str
    subject: str
    predicate: str
    object: str
    confidence: float = 1.0
    source: str = "manual"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class KnowledgeGraph:
    """
    Knowledge Graph per NEUROGLYPH.
    
    Gestisce:
    - Nodi (entità, concetti, teoremi)
    - Edge (relazioni logiche)
    - Fatti (triple subject-predicate-object)
    - Query e lookup per reasoning
    """
    
    def __init__(self, db_path: str = "data/knowledge_graph.db"):
        """
        Inizializza knowledge graph.
        
        Args:
            db_path: Path al database SQLite
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Statistiche
        self.lookup_hits = 0
        self.lookup_misses = 0
        
        # Inizializza database
        self._init_database()
        
        logger.info(f"🧠 KnowledgeGraph inizializzato: {self.db_path}")
    
    def _init_database(self):
        """Inizializza schema database."""
        with sqlite3.connect(self.db_path) as conn:
            # Tabella nodi
            conn.execute("""
                CREATE TABLE IF NOT EXISTS kg_nodes (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    label TEXT NOT NULL,
                    properties TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Tabella edge
            conn.execute("""
                CREATE TABLE IF NOT EXISTS kg_edges (
                    id TEXT PRIMARY KEY,
                    source_id TEXT NOT NULL,
                    target_id TEXT NOT NULL,
                    relation TEXT NOT NULL,
                    weight REAL DEFAULT 1.0,
                    properties TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (source_id) REFERENCES kg_nodes (id),
                    FOREIGN KEY (target_id) REFERENCES kg_nodes (id)
                )
            """)
            
            # Tabella fatti
            conn.execute("""
                CREATE TABLE IF NOT EXISTS kg_facts (
                    id TEXT PRIMARY KEY,
                    subject TEXT NOT NULL,
                    predicate TEXT NOT NULL,
                    object TEXT NOT NULL,
                    confidence REAL DEFAULT 1.0,
                    source TEXT DEFAULT 'manual',
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Indici per performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_nodes_type ON kg_nodes (type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_edges_relation ON kg_edges (relation)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_facts_subject ON kg_facts (subject)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_facts_predicate ON kg_facts (predicate)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_facts_object ON kg_facts (object)")
            
            conn.commit()
    
    def add_node(self, node: KGNode) -> bool:
        """Aggiunge nodo al knowledge graph."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO kg_nodes (id, type, label, properties)
                    VALUES (?, ?, ?, ?)
                """, (
                    node.id,
                    node.type,
                    node.label,
                    json.dumps(node.properties)
                ))
                conn.commit()
                
            logger.debug(f"✅ Nodo aggiunto: {node.id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore aggiunta nodo {node.id}: {e}")
            return False
    
    def add_edge(self, edge: KGEdge) -> bool:
        """Aggiunge edge al knowledge graph."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO kg_edges (id, source_id, target_id, relation, weight, properties)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    edge.id,
                    edge.source_id,
                    edge.target_id,
                    edge.relation,
                    edge.weight,
                    json.dumps(edge.properties)
                ))
                conn.commit()
                
            logger.debug(f"✅ Edge aggiunto: {edge.id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore aggiunta edge {edge.id}: {e}")
            return False
    
    def add_fact(self, fact: KGFact) -> bool:
        """Aggiunge fatto al knowledge graph."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO kg_facts (id, subject, predicate, object, confidence, source, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    fact.id,
                    fact.subject,
                    fact.predicate,
                    fact.object,
                    fact.confidence,
                    fact.source,
                    json.dumps(fact.metadata)
                ))
                conn.commit()
                
            logger.debug(f"✅ Fatto aggiunto: {fact.id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore aggiunta fatto {fact.id}: {e}")
            return False
    
    def lookup_facts(self, subject: str = None, predicate: str = None, object: str = None) -> List[KGFact]:
        """Lookup fatti nel knowledge graph."""
        try:
            conditions = []
            params = []
            
            if subject:
                conditions.append("subject = ?")
                params.append(subject)
            if predicate:
                conditions.append("predicate = ?")
                params.append(predicate)
            if object:
                conditions.append("object = ?")
                params.append(object)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(f"""
                    SELECT * FROM kg_facts WHERE {where_clause}
                    ORDER BY confidence DESC
                """, params)
                
                facts = []
                for row in cursor.fetchall():
                    fact = KGFact(
                        id=row['id'],
                        subject=row['subject'],
                        predicate=row['predicate'],
                        object=row['object'],
                        confidence=row['confidence'],
                        source=row['source'],
                        metadata=json.loads(row['metadata']) if row['metadata'] else {}
                    )
                    facts.append(fact)
                
                if facts:
                    self.lookup_hits += 1
                    logger.debug(f"🎯 Lookup hit: {len(facts)} fatti trovati")
                else:
                    self.lookup_misses += 1
                    logger.debug(f"❌ Lookup miss: nessun fatto trovato")
                
                return facts
                
        except Exception as e:
            logger.error(f"❌ Errore lookup fatti: {e}")
            self.lookup_misses += 1
            return []
    
    def get_implications(self, antecedent: str) -> List[KGFact]:
        """Trova implicazioni con antecedente dato."""
        return self.lookup_facts(subject=antecedent, predicate="implies")
    
    def get_equivalences(self, concept: str) -> List[KGFact]:
        """Trova equivalenze per concetto dato."""
        facts = []
        facts.extend(self.lookup_facts(subject=concept, predicate="equivalent"))
        facts.extend(self.lookup_facts(object=concept, predicate="equivalent"))
        return facts
    
    def get_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche knowledge graph."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM kg_nodes")
                nodes_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM kg_edges")
                edges_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM kg_facts")
                facts_count = cursor.fetchone()[0]
            
            lookup_total = self.lookup_hits + self.lookup_misses
            hit_rate = self.lookup_hits / lookup_total if lookup_total > 0 else 0.0
            
            return {
                'nodes_count': nodes_count,
                'edges_count': edges_count,
                'facts_count': facts_count,
                'lookup_hits': self.lookup_hits,
                'lookup_misses': self.lookup_misses,
                'lookup_hit_rate': hit_rate
            }
            
        except Exception as e:
            logger.error(f"❌ Errore statistiche KG: {e}")
            return {}


if __name__ == '__main__':
    # Test rapido
    kg = KnowledgeGraph("test_kg.db")
    
    # Aggiungi nodi di test
    kg.add_node(KGNode("human", "concept", "Human", {"domain": "philosophy"}))
    kg.add_node(KGNode("mortal", "concept", "Mortal", {"domain": "philosophy"}))
    
    # Aggiungi fatto di test
    kg.add_fact(KGFact("human_mortal", "human", "implies", "mortal", 1.0, "classical_logic"))
    
    # Test lookup
    facts = kg.lookup_facts(subject="human", predicate="implies")
    print(f"Fatti trovati: {len(facts)}")
    
    # Statistiche
    stats = kg.get_statistics()
    print(f"Statistiche KG: {stats}")
