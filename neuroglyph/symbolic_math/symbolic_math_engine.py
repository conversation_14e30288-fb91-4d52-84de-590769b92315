"""
NEUROGLYPH Symbolic Math Engine - SymPy Integration

Motore di calcolo simbolico avanzato per NEUROGLYPH Fase 6.0.
Fornisce capacità di ragionamento matematico simbolico attraverso SymPy.

Features:
- Semplificazione di espressioni simboliche
- Derivazione e integrazione simbolica
- Risoluzione di equazioni e sistemi
- Calcolo di limiti e serie
- Cache intelligente per performance
- Integration con FormalLogicEngine
"""

import logging
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Union, Any
from functools import lru_cache
import json

try:
    import sympy as sp
    from sympy import symbols, sympify, simplify, diff, integrate, solve, limit, series
    from sympy import sin, cos, tan, exp, log, sqrt, pi, E, oo
    SYMPY_AVAILABLE = True
except ImportError:
    SYMPY_AVAILABLE = False
    # Mock sp per evitare errori di import
    class MockSymPy:
        class Basic:
            pass
    sp = MockSymPy()

logger = logging.getLogger(__name__)


@dataclass
class MathFormula:
    """Rappresentazione di una formula matematica."""
    expression: str
    variables: List[str]
    domain: str = "real"
    properties: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}


@dataclass
class MathResult:
    """Risultato di un'operazione matematica simbolica."""
    success: bool
    result: Optional[str] = None
    original_expression: Optional[str] = None
    operation: Optional[str] = None
    variables_used: List[str] = None
    computation_time: float = 0.0
    error_message: Optional[str] = None
    intermediate_steps: List[str] = None
    
    def __post_init__(self):
        if self.variables_used is None:
            self.variables_used = []
        if self.intermediate_steps is None:
            self.intermediate_steps = []


class SymbolicMathEngine:
    """
    Motore di calcolo simbolico per NEUROGLYPH.
    
    Utilizza SymPy per operazioni matematiche avanzate:
    - Algebra simbolica
    - Calcolo differenziale e integrale
    - Risoluzione equazioni
    - Semplificazione espressioni
    """
    
    def __init__(self, enable_cache: bool = True, cache_size: int = 1000):
        """
        Inizializza il motore matematico simbolico.
        
        Args:
            enable_cache: Abilita cache LRU per performance
            cache_size: Dimensione cache (default: 1000)
        """
        if not SYMPY_AVAILABLE:
            raise ImportError("SymPy non disponibile. Installare con: pip install sympy")
        
        self.enable_cache = enable_cache
        self.cache_size = cache_size
        
        # Statistiche
        self.operations_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.total_computation_time = 0.0
        
        # Cache manuale per operazioni complesse
        self._expression_cache: Dict[str, Any] = {}
        
        logger.info(f"🧮 SymbolicMathEngine inizializzato (cache={enable_cache}, size={cache_size})")
    
    def _parse_expression(self, expr_str: str) -> Any:
        """
        Converte stringa in espressione SymPy.
        
        Args:
            expr_str: Espressione matematica come stringa
            
        Returns:
            Espressione SymPy
            
        Raises:
            ValueError: Se l'espressione non è valida
        """
        try:
            # Cache lookup
            if self.enable_cache and expr_str in self._expression_cache:
                self.cache_hits += 1
                return self._expression_cache[expr_str]
            
            # Parse expression
            expr = sympify(expr_str)
            
            # Cache store
            if self.enable_cache:
                self._expression_cache[expr_str] = expr
                self.cache_misses += 1
            
            return expr
            
        except Exception as e:
            raise ValueError(f"Impossibile parsare espressione '{expr_str}': {e}")
    
    def simplify(self, expr_str: str) -> MathResult:
        """
        Semplifica un'espressione matematica.
        
        Args:
            expr_str: Espressione da semplificare
            
        Returns:
            MathResult con espressione semplificata
        """
        start_time = time.time()
        self.operations_count += 1
        
        try:
            expr = self._parse_expression(expr_str)
            simplified = simplify(expr)
            
            computation_time = time.time() - start_time
            self.total_computation_time += computation_time
            
            # Estrai variabili
            variables = [str(var) for var in expr.free_symbols]
            
            return MathResult(
                success=True,
                result=str(simplified),
                original_expression=expr_str,
                operation="simplify",
                variables_used=variables,
                computation_time=computation_time,
                intermediate_steps=[f"Original: {expr_str}", f"Simplified: {simplified}"]
            )
            
        except Exception as e:
            computation_time = time.time() - start_time
            logger.error(f"❌ Errore semplificazione '{expr_str}': {e}")
            
            return MathResult(
                success=False,
                original_expression=expr_str,
                operation="simplify",
                computation_time=computation_time,
                error_message=str(e)
            )
    
    def differentiate(self, expr_str: str, variable: str, order: int = 1) -> MathResult:
        """
        Calcola la derivata di un'espressione.
        
        Args:
            expr_str: Espressione da derivare
            variable: Variabile rispetto cui derivare
            order: Ordine di derivazione (default: 1)
            
        Returns:
            MathResult con derivata
        """
        start_time = time.time()
        self.operations_count += 1
        
        try:
            expr = self._parse_expression(expr_str)
            var = symbols(variable)
            
            derivative = diff(expr, var, order)
            
            computation_time = time.time() - start_time
            self.total_computation_time += computation_time
            
            return MathResult(
                success=True,
                result=str(derivative),
                original_expression=expr_str,
                operation=f"differentiate_order_{order}",
                variables_used=[variable],
                computation_time=computation_time,
                intermediate_steps=[
                    f"Original: {expr_str}",
                    f"d^{order}/d{variable}^{order}: {derivative}"
                ]
            )
            
        except Exception as e:
            computation_time = time.time() - start_time
            logger.error(f"❌ Errore derivazione '{expr_str}' rispetto a '{variable}': {e}")
            
            return MathResult(
                success=False,
                original_expression=expr_str,
                operation=f"differentiate_order_{order}",
                variables_used=[variable],
                computation_time=computation_time,
                error_message=str(e)
            )
    
    def integrate(self, expr_str: str, variable: str, 
                  lower_limit: Optional[str] = None, 
                  upper_limit: Optional[str] = None) -> MathResult:
        """
        Calcola l'integrale di un'espressione.
        
        Args:
            expr_str: Espressione da integrare
            variable: Variabile di integrazione
            lower_limit: Limite inferiore (per integrali definiti)
            upper_limit: Limite superiore (per integrali definiti)
            
        Returns:
            MathResult con integrale
        """
        start_time = time.time()
        self.operations_count += 1
        
        try:
            expr = self._parse_expression(expr_str)
            var = symbols(variable)
            
            if lower_limit is not None and upper_limit is not None:
                # Integrale definito
                lower = sympify(lower_limit)
                upper = sympify(upper_limit)
                integral = integrate(expr, (var, lower, upper))
                operation = f"definite_integral_{lower_limit}_to_{upper_limit}"
            else:
                # Integrale indefinito
                integral = integrate(expr, var)
                operation = "indefinite_integral"
            
            computation_time = time.time() - start_time
            self.total_computation_time += computation_time
            
            return MathResult(
                success=True,
                result=str(integral),
                original_expression=expr_str,
                operation=operation,
                variables_used=[variable],
                computation_time=computation_time,
                intermediate_steps=[
                    f"Original: {expr_str}",
                    f"Integral: {integral}"
                ]
            )
            
        except Exception as e:
            computation_time = time.time() - start_time
            logger.error(f"❌ Errore integrazione '{expr_str}' rispetto a '{variable}': {e}")
            
            return MathResult(
                success=False,
                original_expression=expr_str,
                operation="integrate",
                variables_used=[variable],
                computation_time=computation_time,
                error_message=str(e)
            )

    def solve_equation(self, equation_str: str, variable: str) -> MathResult:
        """
        Risolve un'equazione rispetto a una variabile.

        Args:
            equation_str: Equazione da risolvere (formato: "expr1 = expr2" o "expr")
            variable: Variabile da risolvere

        Returns:
            MathResult con soluzioni
        """
        start_time = time.time()
        self.operations_count += 1

        try:
            var = symbols(variable)

            # Parse equation
            if "=" in equation_str:
                left, right = equation_str.split("=", 1)
                equation = sp.Eq(sympify(left.strip()), sympify(right.strip()))
            else:
                # Assume equation = 0
                equation = sympify(equation_str)

            solutions = solve(equation, var)

            computation_time = time.time() - start_time
            self.total_computation_time += computation_time

            # Converti soluzioni in stringhe
            solution_strings = [str(sol) for sol in solutions]

            return MathResult(
                success=True,
                result=json.dumps(solution_strings),
                original_expression=equation_str,
                operation="solve_equation",
                variables_used=[variable],
                computation_time=computation_time,
                intermediate_steps=[
                    f"Equation: {equation_str}",
                    f"Solutions: {solution_strings}"
                ]
            )

        except Exception as e:
            computation_time = time.time() - start_time
            logger.error(f"❌ Errore risoluzione equazione '{equation_str}': {e}")

            return MathResult(
                success=False,
                original_expression=equation_str,
                operation="solve_equation",
                variables_used=[variable],
                computation_time=computation_time,
                error_message=str(e)
            )

    def expand(self, expr_str: str) -> MathResult:
        """
        Espande un'espressione matematica.

        Args:
            expr_str: Espressione da espandere

        Returns:
            MathResult con espressione espansa
        """
        start_time = time.time()
        self.operations_count += 1

        try:
            expr = self._parse_expression(expr_str)
            expanded = sp.expand(expr)

            computation_time = time.time() - start_time
            self.total_computation_time += computation_time

            variables = [str(var) for var in expr.free_symbols]

            return MathResult(
                success=True,
                result=str(expanded),
                original_expression=expr_str,
                operation="expand",
                variables_used=variables,
                computation_time=computation_time,
                intermediate_steps=[f"Original: {expr_str}", f"Expanded: {expanded}"]
            )

        except Exception as e:
            computation_time = time.time() - start_time
            logger.error(f"❌ Errore espansione '{expr_str}': {e}")

            return MathResult(
                success=False,
                original_expression=expr_str,
                operation="expand",
                computation_time=computation_time,
                error_message=str(e)
            )

    def factor(self, expr_str: str) -> MathResult:
        """
        Fattorizza un'espressione matematica.

        Args:
            expr_str: Espressione da fattorizzare

        Returns:
            MathResult con espressione fattorizzata
        """
        start_time = time.time()
        self.operations_count += 1

        try:
            expr = self._parse_expression(expr_str)
            factored = sp.factor(expr)

            computation_time = time.time() - start_time
            self.total_computation_time += computation_time

            variables = [str(var) for var in expr.free_symbols]

            return MathResult(
                success=True,
                result=str(factored),
                original_expression=expr_str,
                operation="factor",
                variables_used=variables,
                computation_time=computation_time,
                intermediate_steps=[f"Original: {expr_str}", f"Factored: {factored}"]
            )

        except Exception as e:
            computation_time = time.time() - start_time
            logger.error(f"❌ Errore fattorizzazione '{expr_str}': {e}")

            return MathResult(
                success=False,
                original_expression=expr_str,
                operation="factor",
                computation_time=computation_time,
                error_message=str(e)
            )

    def get_statistics(self) -> Dict[str, Any]:
        """
        Restituisce statistiche del motore matematico.

        Returns:
            Dizionario con statistiche
        """
        cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0.0
        avg_computation_time = self.total_computation_time / self.operations_count if self.operations_count > 0 else 0.0

        return {
            "operations_count": self.operations_count,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_rate": cache_hit_rate,
            "total_computation_time": self.total_computation_time,
            "average_computation_time": avg_computation_time,
            "cache_size": len(self._expression_cache),
            "sympy_available": SYMPY_AVAILABLE
        }

    def reset_statistics(self):
        """Reset statistiche e cache."""
        self.operations_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.total_computation_time = 0.0
        self._expression_cache.clear()

        logger.info("📊 Statistiche SymbolicMathEngine resettate")


# Factory function per uso rapido
def compute(expression: str, operation: str = "simplify", **kwargs) -> MathResult:
    """
    Funzione di convenienza per calcoli matematici rapidi.

    Args:
        expression: Espressione matematica
        operation: Tipo di operazione ("simplify", "differentiate", "integrate", "solve", "expand", "factor")
        **kwargs: Parametri aggiuntivi per l'operazione

    Returns:
        MathResult
    """
    engine = SymbolicMathEngine()

    if operation == "simplify":
        return engine.simplify(expression)
    elif operation == "differentiate":
        variable = kwargs.get("variable", "x")
        order = kwargs.get("order", 1)
        return engine.differentiate(expression, variable, order)
    elif operation == "integrate":
        variable = kwargs.get("variable", "x")
        lower = kwargs.get("lower_limit")
        upper = kwargs.get("upper_limit")
        return engine.integrate(expression, variable, lower, upper)
    elif operation == "solve":
        variable = kwargs.get("variable", "x")
        return engine.solve_equation(expression, variable)
    elif operation == "expand":
        return engine.expand(expression)
    elif operation == "factor":
        return engine.factor(expression)
    else:
        raise ValueError(f"Operazione non supportata: {operation}")


if __name__ == '__main__':
    # Test rapido
    engine = SymbolicMathEngine()

    # Test semplificazione
    result = engine.simplify("(x + y)**2")
    print(f"Semplificazione: {result.result}")

    # Test derivazione
    result = engine.differentiate("sin(x)*cos(x)", "x")
    print(f"Derivata: {result.result}")

    # Test risoluzione equazione
    result = engine.solve_equation("x**2 - 5*x + 6 = 0", "x")
    print(f"Soluzioni: {result.result}")

    # Statistiche
    stats = engine.get_statistics()
    print(f"Statistiche: {stats}")
