"""
NEUROGLYPH Math Module - Symbolic Mathematical Reasoning

Questo modulo fornisce capacità di ragionamento matematico simbolico
utilizzando SymPy per calcoli avanzati, derivazioni, integrazioni e
risoluzione di equazioni.

Componenti:
- SymbolicMathEngine: Motore principale per calcoli simbolici
- MathFormula: Strutture dati per formule matematiche
- Integration con FormalLogicEngine per reasoning ibrido
"""

from .symbolic_math_engine import SymbolicMathEngine, MathResult, MathFormula

__all__ = [
    "SymbolicMathEngine",
    "MathResult", 
    "MathFormula"
]
