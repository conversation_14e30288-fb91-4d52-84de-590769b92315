#!/usr/bin/env python3
"""
NEUROGLYPH HumanEval Logic Parser
Parser NL→AST per task di tipo "human evaluator logic"

Obiettivi:
- Convertire prompt HumanEval in specifiche logiche
- Estrarre condizioni, operazioni e output attesi
- Generare AST Python per validazione automatica
- Integrazione con NGReasoner per reasoning simbolico
"""

import re
import ast
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass


@dataclass
class LogicSpecification:
    """Specifica logica estratta da prompt HumanEval."""
    function_name: str
    parameters: List[str]
    return_type: str
    conditions: List[str]
    operations: List[str]
    expected_behavior: str
    test_cases: List[Dict[str, Any]]


class HumanEvalLogicParser:
    """Parser per convertire prompt HumanEval in specifiche logiche."""
    
    def __init__(self):
        # Pattern per riconoscere elementi logici
        self.logic_patterns = {
            'function_def': r'def\s+(\w+)\s*\([^)]*\)\s*->\s*(\w+):',
            'parameters': r'def\s+\w+\s*\(([^)]*)\)',
            'return_statement': r'return\s+(.+)',
            'condition_if': r'if\s+(.+?):',
            'condition_else': r'else:',
            'boolean_logic': r'\b(and|or|not|True|False)\b',
            'comparison': r'([<>=!]+)',
            'arithmetic': r'([+\-*/])',
            'modulo': r'(%)',
            'list_operation': r'\.(append|extend|remove|pop|insert)',
            'string_operation': r'\.(split|join|replace|strip|lower|upper)'
        }
        
        # Template per generazione codice
        self.code_templates = {
            'boolean_check': """
def {function_name}({parameters}):
    return {condition}
""",
            'arithmetic_operation': """
def {function_name}({parameters}):
    result = {operation}
    return result
""",
            'list_processing': """
def {function_name}({parameters}):
    result = []
    for item in {input_list}:
        {processing_logic}
    return result
""",
            'conditional_logic': """
def {function_name}({parameters}):
    if {condition}:
        return {true_value}
    else:
        return {false_value}
"""
        }
    
    def parse(self, prompt: str, task_id: str = "") -> LogicSpecification:
        """Parse un prompt HumanEval in specifica logica."""
        
        # Estrai definizione funzione
        function_name, return_type = self.extract_function_signature(prompt)
        parameters = self.extract_parameters(prompt)
        
        # Analizza il contenuto logico
        conditions = self.extract_conditions(prompt)
        operations = self.extract_operations(prompt)
        expected_behavior = self.extract_expected_behavior(prompt)
        
        # Genera test cases basati sul prompt
        test_cases = self.generate_test_cases(prompt, function_name, task_id)
        
        return LogicSpecification(
            function_name=function_name,
            parameters=parameters,
            return_type=return_type,
            conditions=conditions,
            operations=operations,
            expected_behavior=expected_behavior,
            test_cases=test_cases
        )
    
    def extract_function_signature(self, prompt: str) -> Tuple[str, str]:
        """Estrai nome funzione e tipo di ritorno."""
        match = re.search(self.logic_patterns['function_def'], prompt)
        if match:
            return match.group(1), match.group(2)
        
        # Fallback: cerca pattern alternativi
        if 'def ' in prompt:
            lines = prompt.split('\n')
            for line in lines:
                if line.strip().startswith('def '):
                    # Estrai nome funzione
                    name_match = re.search(r'def\s+(\w+)', line)
                    if name_match:
                        function_name = name_match.group(1)
                        # Determina tipo di ritorno dal contesto
                        if 'bool' in prompt.lower() or 'true' in prompt.lower() or 'false' in prompt.lower():
                            return function_name, 'bool'
                        elif 'list' in prompt.lower() or 'array' in prompt.lower():
                            return function_name, 'List'
                        elif 'int' in prompt.lower() or 'number' in prompt.lower():
                            return function_name, 'int'
                        elif 'float' in prompt.lower():
                            return function_name, 'float'
                        else:
                            return function_name, 'Any'
        
        return 'unknown_function', 'Any'
    
    def extract_parameters(self, prompt: str) -> List[str]:
        """Estrai parametri della funzione."""
        match = re.search(self.logic_patterns['parameters'], prompt)
        if match:
            params_str = match.group(1)
            # Parse parametri
            params = []
            for param in params_str.split(','):
                param = param.strip()
                if param and param != 'self':
                    # Rimuovi type hints
                    param_name = param.split(':')[0].strip()
                    params.append(param_name)
            return params
        
        return ['input']  # Fallback
    
    def extract_conditions(self, prompt: str) -> List[str]:
        """Estrai condizioni logiche dal prompt."""
        conditions = []
        
        # Cerca pattern di condizioni
        if_matches = re.findall(self.logic_patterns['condition_if'], prompt)
        conditions.extend(if_matches)
        
        # Cerca descrizioni testuali di condizioni
        text_conditions = []
        if 'if' in prompt.lower():
            sentences = prompt.split('.')
            for sentence in sentences:
                if 'if' in sentence.lower():
                    text_conditions.append(sentence.strip())
        
        conditions.extend(text_conditions)
        
        return conditions
    
    def extract_operations(self, prompt: str) -> List[str]:
        """Estrai operazioni dal prompt."""
        operations = []
        
        # Cerca operazioni aritmetiche
        if re.search(self.logic_patterns['arithmetic'], prompt):
            operations.append('arithmetic')
        
        # Cerca operazioni modulo
        if re.search(self.logic_patterns['modulo'], prompt):
            operations.append('modulo')
        
        # Cerca operazioni su liste
        if re.search(self.logic_patterns['list_operation'], prompt):
            operations.append('list_manipulation')
        
        # Cerca operazioni su stringhe
        if re.search(self.logic_patterns['string_operation'], prompt):
            operations.append('string_manipulation')
        
        # Cerca logica booleana
        if re.search(self.logic_patterns['boolean_logic'], prompt):
            operations.append('boolean_logic')
        
        # Cerca comparazioni
        if re.search(self.logic_patterns['comparison'], prompt):
            operations.append('comparison')
        
        return operations
    
    def extract_expected_behavior(self, prompt: str) -> str:
        """Estrai comportamento atteso dalla descrizione."""
        # Cerca frasi che descrivono il comportamento
        sentences = prompt.split('.')
        for sentence in sentences:
            sentence = sentence.strip().lower()
            if any(keyword in sentence for keyword in ['return', 'should', 'must', 'will']):
                return sentence
        
        # Fallback: prima frase dopo la definizione
        lines = prompt.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('def ') and i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and not next_line.startswith('"""'):
                    return next_line
        
        return "Function should process input and return result"
    
    def generate_test_cases(self, prompt: str, function_name: str, task_id: str) -> List[Dict[str, Any]]:
        """Genera test cases basati sul prompt e task_id."""
        test_cases = []
        
        # Test cases specifici per task noti
        if 'humaneval_logic_001' in task_id:
            # Task: check if number is even
            test_cases = [
                {'input': (2,), 'expected': True, 'description': 'Even number'},
                {'input': (3,), 'expected': False, 'description': 'Odd number'},
                {'input': (0,), 'expected': True, 'description': 'Zero is even'},
                {'input': (-2,), 'expected': True, 'description': 'Negative even'},
                {'input': (-3,), 'expected': False, 'description': 'Negative odd'}
            ]
        elif 'humaneval_logic_002' in task_id:
            # Task: check if string is palindrome
            test_cases = [
                {'input': ('racecar',), 'expected': True, 'description': 'Simple palindrome'},
                {'input': ('hello',), 'expected': False, 'description': 'Not palindrome'},
                {'input': ('a',), 'expected': True, 'description': 'Single character'},
                {'input': ('',), 'expected': True, 'description': 'Empty string'},
                {'input': ('Aa',), 'expected': False, 'description': 'Case sensitive'}
            ]
        else:
            # Test cases generici basati sul tipo di ritorno
            if 'bool' in prompt.lower():
                test_cases = [
                    {'input': (True,), 'expected': True, 'description': 'True input'},
                    {'input': (False,), 'expected': False, 'description': 'False input'}
                ]
            elif 'list' in prompt.lower():
                test_cases = [
                    {'input': ([1, 2, 3],), 'expected': [1, 2, 3], 'description': 'Simple list'},
                    {'input': ([],), 'expected': [], 'description': 'Empty list'}
                ]
            else:
                test_cases = [
                    {'input': (1,), 'expected': 1, 'description': 'Basic test'},
                    {'input': (0,), 'expected': 0, 'description': 'Zero test'}
                ]
        
        return test_cases
    
    def generate_code(self, spec: LogicSpecification) -> str:
        """Genera codice Python dalla specifica logica."""
        
        # Determina il template appropriato
        if 'boolean_logic' in spec.operations or 'comparison' in spec.operations:
            if 'modulo' in spec.operations:
                # Probabilmente check pari/dispari
                template = self.code_templates['conditional_logic']
                condition = f"{spec.parameters[0]} % 2 == 0"
                return template.format(
                    function_name=spec.function_name,
                    parameters=', '.join(spec.parameters),
                    condition=condition,
                    true_value='True',
                    false_value='False'
                )
            else:
                # Logica booleana generica
                template = self.code_templates['boolean_check']
                condition = "True"  # Placeholder
                return template.format(
                    function_name=spec.function_name,
                    parameters=', '.join(spec.parameters),
                    condition=condition
                )
        
        elif 'arithmetic' in spec.operations:
            template = self.code_templates['arithmetic_operation']
            operation = f"{spec.parameters[0]} + 1"  # Placeholder
            return template.format(
                function_name=spec.function_name,
                parameters=', '.join(spec.parameters),
                operation=operation
            )
        
        elif 'list_manipulation' in spec.operations:
            template = self.code_templates['list_processing']
            return template.format(
                function_name=spec.function_name,
                parameters=', '.join(spec.parameters),
                input_list=spec.parameters[0],
                processing_logic="result.append(item)"
            )
        
        else:
            # Template generico
            return f"""
def {spec.function_name}({', '.join(spec.parameters)}):
    # {spec.expected_behavior}
    return {spec.parameters[0] if spec.parameters else 'None'}
"""
    
    def validate_code(self, code: str, test_cases: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
        """Valida il codice generato sui test cases."""
        try:
            # Compila il codice
            compiled = compile(code, '<string>', 'exec')
            namespace = {}
            exec(compiled, namespace)
            
            # Trova la funzione
            function_name = None
            for name, obj in namespace.items():
                if callable(obj) and not name.startswith('__'):
                    function_name = name
                    break
            
            if not function_name:
                return False, ["No function found in generated code"]
            
            func = namespace[function_name]
            results = []
            
            # Testa su tutti i test cases
            for test_case in test_cases:
                try:
                    input_args = test_case['input']
                    expected = test_case['expected']
                    
                    if isinstance(input_args, tuple):
                        result = func(*input_args)
                    else:
                        result = func(input_args)
                    
                    if result == expected:
                        results.append(f"✅ {test_case['description']}: {result}")
                    else:
                        results.append(f"❌ {test_case['description']}: got {result}, expected {expected}")
                        
                except Exception as e:
                    results.append(f"❌ {test_case['description']}: error {e}")
            
            # Successo se tutti i test passano
            success = all('✅' in result for result in results)
            return success, results
            
        except Exception as e:
            return False, [f"Code compilation error: {e}"]
