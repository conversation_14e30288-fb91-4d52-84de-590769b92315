"""
NEUROGLYPH Cognitive Modules
Moduli per il reasoning simbolico e l'intelligenza cognitiva
"""

# Nessun import diretto - solo lazy loading

# Export list per lazy loading
__all__ = [
    'NGContextPrioritizer',
    'NGMemory',
    'NGReasoner',
    'NGSelfCheck',
    'NGSandbox',
    'NGAdaptivePatcher',
    'NGLearner',
    'NGIntegration'
]

def __getattr__(name):
    """Lazy loading per evitare import circolari."""
    if name == 'NGContextPrioritizer':
        from .context_prioritizer import NGContextPrioritizer
        return NGContextPrioritizer
    elif name == 'NGMemory':
        from .memory import NGMemory
        return NGMemory
    elif name == 'NGReasoner':
        from .reasoner import NGReasoner
        return NGReasoner
    elif name == 'NGSelfCheck':
        from .self_check import NGSelfCheck
        return NGSelfCheck
    elif name == 'NGSandbox':
        from .sandbox import NGSandbox
        return NGSandbox
    elif name == 'NGAdaptivePatcher':
        from .adaptive_patcher import NGAdaptivePatcher
        return NGAdaptivePatcher
    elif name == 'NGLearner':
        from .learner import NGLearner
        return NGLearner
    elif name == 'NGIntegration':
        from .integration import NGIntegration
        return NGIntegration
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
