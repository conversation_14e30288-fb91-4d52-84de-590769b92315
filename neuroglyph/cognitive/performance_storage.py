"""
NEUROGLYPH Performance Storage Engine
SQLite-based high-performance storage per patch-testing con WAL mode.
Target: ≤1ms append, ≤5ms retrieve @ 10k rows
"""

import sqlite3
import json
import time
import threading
from collections import Counter, deque
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class PerformanceStorage:
    """
    Storage engine ad alte prestazioni per NEUROGLYPH patch-testing.
    
    Features:
    - SQLite con WAL mode per concorrenza
    - Batch writing con ring buffer
    - Indici compositi ottimizzati
    - Hot-path API con contatori in memoria
    - Target: ≤1ms append, ≤5ms retrieve @ 10k rows
    """
    
    def __init__(self, db_path: str = "learning.db", batch_size: int = 128, flush_interval: float = 0.2):
        """
        Inizializza storage engine ad alte prestazioni.
        
        Args:
            db_path: Percorso database SQLite
            batch_size: Dimensione buffer per batch writing
            flush_interval: Intervallo flush asincrono (secondi)
        """
        self.db_path = db_path
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        
        # Ring buffer per batch writing
        self.patch_buffer = deque(maxlen=batch_size)
        self.pattern_buffer = deque(maxlen=batch_size)
        self.metrics_buffer = deque(maxlen=batch_size)  # Buffer per patch_metrics
        
        # Hot-path contatori in memoria
        self.stats = Counter()
        
        # Threading per flush asincrono
        self._flush_lock = threading.Lock()
        self._shutdown = False
        
        # Inizializza database
        self.db = None
        self._setup_database()
        self._load_stats()
        
        # Avvia flush timer
        self._start_flush_timer()
        
        logger.info(f"✅ PerformanceStorage inizializzato: {db_path}")
        logger.info(f"   - Batch size: {batch_size}")
        logger.info(f"   - Flush interval: {flush_interval}s")
    
    def _setup_database(self):
        """Setup database con WAL mode e pragma ottimizzati."""
        try:
            # Connessione con WAL mode
            self.db = sqlite3.connect(
                self.db_path, 
                isolation_level=None,  # Autocommit mode
                check_same_thread=False,  # Thread-safe
                timeout=30.0  # Timeout per lock contention
            )
            
            cur = self.db.cursor()
            
            # Pragma ottimizzati per performance
            cur.executescript("""
                -- WAL mode per concorrenza
                PRAGMA journal_mode=WAL;
                
                -- Sincronizzazione ridotta per performance
                PRAGMA synchronous=NORMAL;
                
                -- Cache 10MB in memoria
                PRAGMA cache_size=-10000;
                
                -- Temp tables in memoria
                PRAGMA temp_store=MEMORY;
                
                -- Ottimizzazioni query
                PRAGMA optimize;
                
                -- Tabelle principali
                CREATE TABLE IF NOT EXISTS patch_history(
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts REAL NOT NULL,
                    error_type TEXT NOT NULL,
                    status TEXT NOT NULL,
                    meta TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE TABLE IF NOT EXISTS learned_patterns(
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts REAL NOT NULL,
                    pattern TEXT NOT NULL,
                    metadata TEXT,
                    effectiveness REAL DEFAULT 0.0,
                    usage_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                -- Tabella patch_metrics per telemetria Fase 2.4
                CREATE TABLE IF NOT EXISTS patch_metrics(
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts REAL NOT NULL,
                    latency_ms REAL NOT NULL,
                    cache_hit INTEGER NOT NULL,
                    error_count INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 0.0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                -- Tabelle Fase 3.0 Learning
                CREATE TABLE IF NOT EXISTS kg_stats(
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts REAL NOT NULL,
                    total_nodes INTEGER NOT NULL,
                    total_edges INTEGER NOT NULL,
                    node_types TEXT,
                    edge_types TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS pattern_stats(
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts REAL NOT NULL,
                    last_extracted_ts REAL NOT NULL,
                    extraction_count INTEGER NOT NULL,
                    total_patterns INTEGER NOT NULL,
                    new_patterns INTEGER NOT NULL,
                    updated_patterns INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
                
                -- Indici compositi per performance
                CREATE INDEX IF NOT EXISTS idx_ph_err_ts
                  ON patch_history(error_type, ts DESC);

                CREATE INDEX IF NOT EXISTS idx_ph_status_ts
                  ON patch_history(status, ts DESC);

                CREATE INDEX IF NOT EXISTS idx_lp_pattern
                  ON learned_patterns(pattern);

                CREATE INDEX IF NOT EXISTS idx_lp_effectiveness
                  ON learned_patterns(effectiveness DESC, usage_count DESC);

                -- Indici per patch_metrics
                CREATE INDEX IF NOT EXISTS idx_pm_ts
                  ON patch_metrics(ts DESC);

                CREATE INDEX IF NOT EXISTS idx_pm_cache_hit
                  ON patch_metrics(cache_hit, ts DESC);

                -- Indici per kg_stats
                CREATE INDEX IF NOT EXISTS idx_kg_stats_ts
                  ON kg_stats(ts DESC);

                -- Indici per pattern_stats
                CREATE INDEX IF NOT EXISTS idx_pattern_stats_ts
                  ON pattern_stats(ts DESC);
            """)
            
            logger.info("✅ Database setup completato con WAL mode")
            
        except Exception as e:
            logger.error(f"❌ Errore setup database: {e}")
            raise
    
    def _load_stats(self):
        """Carica statistiche in memoria per hot-path API."""
        try:
            cur = self.db.cursor()
            
            # Carica contatori patch history
            cur.execute("""
                SELECT status, COUNT(*) 
                FROM patch_history 
                GROUP BY status
            """)
            for status, count in cur.fetchall():
                self.stats[f"patch_{status}"] = count
            
            # Carica contatori learned patterns
            cur.execute("SELECT COUNT(*) FROM learned_patterns")
            self.stats["learned_patterns"] = cur.fetchone()[0]
            
            # Carica statistiche temporali
            cur.execute("""
                SELECT COUNT(*) 
                FROM patch_history 
                WHERE ts > ?
            """, (time.time() - 86400,))  # Ultime 24h
            self.stats["patches_24h"] = cur.fetchone()[0]
            
            logger.info(f"✅ Stats caricate: {dict(self.stats)}")
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento stats: {e}")
    
    def append_patch_history(self, error_type: str, status: str, metadata: Dict[str, Any] = None):
        """
        Aggiunge record patch history con batch writing.
        Target: ≤1ms
        """
        ts = time.time()
        meta_json = json.dumps(metadata) if metadata else None
        
        # Aggiunge al buffer
        self.patch_buffer.append((ts, error_type, status, meta_json))
        
        # Aggiorna contatori hot-path
        self.stats[f"patch_{status}"] += 1
        self.stats["patches_24h"] += 1
        
        # Flush se buffer pieno
        if len(self.patch_buffer) >= self.batch_size:
            self._flush_patch_buffer()
    
    def append_learned_pattern(self, pattern: str, metadata: Dict[str, Any] = None, effectiveness: float = 0.0):
        """
        Aggiunge learned pattern con batch writing.
        Target: ≤1ms
        """
        ts = time.time()
        meta_json = json.dumps(metadata) if metadata else None
        
        # Aggiunge al buffer
        self.pattern_buffer.append((ts, pattern, meta_json, effectiveness, 1))
        
        # Aggiorna contatori hot-path
        self.stats["learned_patterns"] += 1
        
        # Flush se buffer pieno
        if len(self.pattern_buffer) >= self.batch_size:
            self._flush_pattern_buffer()

    def record_metric(self, latency_ms: float, cache_hit: int, error_count: int = 0, success_rate: float = 0.0):
        """
        Registra metrica patch per telemetria Fase 2.4.
        Target: ≤0.1ms (hot-path)

        Args:
            latency_ms: Latenza in millisecondi
            cache_hit: 1 se cache hit, 0 se cache miss
            error_count: Numero errori processati
            success_rate: Tasso successo patch
        """
        ts = time.time()

        # Aggiunge al buffer
        self.metrics_buffer.append((ts, latency_ms, cache_hit, error_count, success_rate))

        # Aggiorna contatori hot-path
        self.stats["total_metrics"] += 1
        if cache_hit:
            self.stats["cache_hits"] += 1
        else:
            self.stats["cache_misses"] += 1

        # Flush se buffer pieno
        if len(self.metrics_buffer) >= self.batch_size:
            self._flush_metrics_buffer()
    
    def get_patch_history(self, error_type: str = None, limit: int = 100) -> List[Tuple]:
        """
        Recupera patch history con query ottimizzata.
        Target: ≤5ms
        """
        try:
            cur = self.db.cursor()
            
            if error_type:
                # Query con indice composito
                cur.execute("""
                    SELECT id, ts, error_type, status, meta, created_at
                    FROM patch_history 
                    WHERE error_type = ?
                    ORDER BY ts DESC 
                    LIMIT ?
                """, (error_type, limit))
            else:
                cur.execute("""
                    SELECT id, ts, error_type, status, meta, created_at
                    FROM patch_history 
                    ORDER BY ts DESC 
                    LIMIT ?
                """, (limit,))
            
            return cur.fetchall()
            
        except Exception as e:
            logger.error(f"❌ Errore get_patch_history: {e}")
            return []
    
    def get_learned_patterns(self, pattern: str = None, limit: int = 100) -> List[Tuple]:
        """
        Recupera learned patterns con query ottimizzata.
        Target: ≤5ms
        """
        try:
            cur = self.db.cursor()
            
            if pattern:
                cur.execute("""
                    SELECT id, ts, pattern, metadata, effectiveness, usage_count, created_at
                    FROM learned_patterns 
                    WHERE pattern = ?
                    ORDER BY effectiveness DESC, usage_count DESC
                    LIMIT ?
                """, (pattern, limit))
            else:
                cur.execute("""
                    SELECT id, ts, pattern, metadata, effectiveness, usage_count, created_at
                    FROM learned_patterns 
                    ORDER BY effectiveness DESC, usage_count DESC
                    LIMIT ?
                """, (limit,))
            
            return cur.fetchall()
            
        except Exception as e:
            logger.error(f"❌ Errore get_learned_patterns: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Restituisce statistiche hot-path (O(1)).
        """
        return {
            "total_patches": sum(v for k, v in self.stats.items() if k.startswith("patch_")),
            "successful_patches": self.stats.get("patch_successful", 0),
            "failed_patches": self.stats.get("patch_failed", 0),
            "learned_patterns": self.stats.get("learned_patterns", 0),
            "patches_24h": self.stats.get("patches_24h", 0),
            "success_rate": (
                self.stats.get("patch_successful", 0) / 
                max(1, sum(v for k, v in self.stats.items() if k.startswith("patch_")))
            ),
            "buffer_sizes": {
                "patch_buffer": len(self.patch_buffer),
                "pattern_buffer": len(self.pattern_buffer),
                "metrics_buffer": len(self.metrics_buffer)
            },
            "cache_stats": {
                "total_metrics": self.stats.get("total_metrics", 0),
                "cache_hits": self.stats.get("cache_hits", 0),
                "cache_misses": self.stats.get("cache_misses", 0),
                "cache_hit_rate": (
                    self.stats.get("cache_hits", 0) /
                    max(1, self.stats.get("total_metrics", 1))
                )
            }
        }

    def _flush_patch_buffer(self):
        """Flush batch patch history al database."""
        if not self.patch_buffer:
            return

        with self._flush_lock:
            try:
                cur = self.db.cursor()

                # Batch insert con executemany
                batch_data = list(self.patch_buffer)
                cur.executemany("""
                    INSERT INTO patch_history (ts, error_type, status, meta)
                    VALUES (?, ?, ?, ?)
                """, batch_data)

                self.patch_buffer.clear()
                logger.debug(f"✅ Flushed {len(batch_data)} patch records")

            except Exception as e:
                logger.error(f"❌ Errore flush patch buffer: {e}")

    def _flush_pattern_buffer(self):
        """Flush batch learned patterns al database."""
        if not self.pattern_buffer:
            return

        with self._flush_lock:
            try:
                cur = self.db.cursor()

                # Batch insert con executemany
                batch_data = list(self.pattern_buffer)
                cur.executemany("""
                    INSERT INTO learned_patterns (ts, pattern, metadata, effectiveness, usage_count)
                    VALUES (?, ?, ?, ?, ?)
                """, batch_data)

                self.pattern_buffer.clear()
                logger.debug(f"✅ Flushed {len(batch_data)} pattern records")

            except Exception as e:
                logger.error(f"❌ Errore flush pattern buffer: {e}")

    def _flush_metrics_buffer(self):
        """Flush batch patch metrics al database."""
        if not self.metrics_buffer:
            return

        with self._flush_lock:
            try:
                cur = self.db.cursor()

                # Batch insert con executemany
                batch_data = list(self.metrics_buffer)
                cur.executemany("""
                    INSERT INTO patch_metrics (ts, latency_ms, cache_hit, error_count, success_rate)
                    VALUES (?, ?, ?, ?, ?)
                """, batch_data)

                self.metrics_buffer.clear()
                logger.debug(f"✅ Flushed {len(batch_data)} metric records")

            except Exception as e:
                logger.error(f"❌ Errore flush metrics buffer: {e}")

    def _start_flush_timer(self):
        """Avvia timer per flush asincrono."""
        def flush_worker():
            while not self._shutdown:
                time.sleep(self.flush_interval)
                if not self._shutdown:
                    self._flush_patch_buffer()
                    self._flush_pattern_buffer()
                    self._flush_metrics_buffer()

        flush_thread = threading.Thread(target=flush_worker, daemon=True)
        flush_thread.start()
        logger.debug("✅ Flush timer avviato")

    def force_flush(self):
        """Forza flush immediato di tutti i buffer."""
        self._flush_patch_buffer()
        self._flush_pattern_buffer()
        self._flush_metrics_buffer()

    def shutdown(self):
        """Shutdown graceful con WAL checkpoint."""
        logger.info("🔄 Shutdown PerformanceStorage...")

        self._shutdown = True

        # Flush finale
        self.force_flush()

        # WAL checkpoint per cleanup
        try:
            cur = self.db.cursor()
            cur.execute("PRAGMA wal_checkpoint(TRUNCATE)")
            logger.info("✅ WAL checkpoint completato")
        except Exception as e:
            logger.error(f"❌ Errore WAL checkpoint: {e}")

        # Chiudi connessione
        if self.db:
            self.db.close()
            logger.info("✅ Database chiuso")

    def get_database_info(self) -> Dict[str, Any]:
        """Restituisce informazioni database per diagnostica."""
        try:
            cur = self.db.cursor()

            # Dimensione database
            cur.execute("PRAGMA page_count")
            page_count = cur.fetchone()[0]

            cur.execute("PRAGMA page_size")
            page_size = cur.fetchone()[0]

            db_size_mb = (page_count * page_size) / (1024 * 1024)

            # WAL info
            cur.execute("PRAGMA wal_checkpoint")
            wal_info = cur.fetchone()

            # Conteggi tabelle
            cur.execute("SELECT COUNT(*) FROM patch_history")
            patch_count = cur.fetchone()[0]

            cur.execute("SELECT COUNT(*) FROM learned_patterns")
            pattern_count = cur.fetchone()[0]

            return {
                "db_path": self.db_path,
                "db_size_mb": round(db_size_mb, 2),
                "page_count": page_count,
                "page_size": page_size,
                "wal_info": wal_info,
                "patch_history_count": patch_count,
                "learned_patterns_count": pattern_count,
                "buffer_sizes": {
                    "patch_buffer": len(self.patch_buffer),
                    "pattern_buffer": len(self.pattern_buffer)
                }
            }

        except Exception as e:
            logger.error(f"❌ Errore get_database_info: {e}")
            return {"error": str(e)}
