"""
NEUROGLYPH Integration Module
Controller principale per l'integrazione end-to-end di tutti i moduli cognitivi
"""

import time
import json
import uuid
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

from .context_prioritizer import NGContextPrioritizer
from .memory import NGMemory
from .reasoner import NGReasoner
from .self_check import NGSelfCheck
from .sandbox import NGSandbox
from .adaptive_patcher import NGAdaptivePatcher
from .learner import N<PERSON><PERSON><PERSON><PERSON>, LearnerConfig

from ..core.parser.ng_parser import NGParser
from .data_structures import Parsed<PERSON>rompt, PriorityVector, MemoryContext
from .reasoning_structures import ReasoningGraph
from .validation_structures import ValidationResult
from .sandbox_structures import ExecutionResult
from .patcher_structures import PatchResult


class PipelineStage(Enum):
    """Stadi della pipeline cognitiva."""
    PARSING = "parsing"
    PRIORITIZATION = "prioritization"
    MEMORY_RETRIEVAL = "memory_retrieval"
    REASONING = "reasoning"
    SELF_CHECK = "self_check"
    SANDBOX_EXECUTION = "sandbox_execution"
    ADAPTIVE_PATCHING = "adaptive_patching"
    LEARNING = "learning"
    COMPLETED = "completed"
    FAILED = "failed"


class ExecutionMode(Enum):
    """Modalità di esecuzione."""
    PRODUCTION = "production"
    DEBUG = "debug"
    DRY_RUN = "dry_run"
    BENCHMARK = "benchmark"


@dataclass
class PipelineConfig:
    """Configurazione della pipeline."""
    execution_mode: ExecutionMode = ExecutionMode.PRODUCTION
    
    # Controllo flusso
    enable_self_check: bool = True
    enable_sandbox: bool = True
    enable_adaptive_patching: bool = True
    enable_learning: bool = True
    
    # Timeout e limiti
    max_execution_time: float = 300.0  # 5 minuti
    max_reasoning_depth: int = 10
    max_patch_attempts: int = 3
    
    # Logging e metriche
    enable_detailed_logging: bool = False
    save_execution_trace: bool = True
    collect_metrics: bool = True
    
    # Configurazioni moduli
    learner_config: Optional[LearnerConfig] = None


@dataclass
class ExecutionMetrics:
    """Metriche di esecuzione."""
    execution_id: str
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    
    # Timing per stadio
    stage_timings: Dict[str, float] = field(default_factory=dict)
    stage_start_times: Dict[str, float] = field(default_factory=dict)
    
    # Contatori
    total_stages: int = 0
    successful_stages: int = 0
    failed_stages: int = 0
    
    # Qualità
    reasoning_quality_score: float = 0.0
    validation_score: float = 0.0
    execution_success_rate: float = 0.0
    
    # Risorse
    peak_memory_usage: float = 0.0
    total_cpu_time: float = 0.0
    
    def start_stage(self, stage: PipelineStage):
        """Inizia timing per uno stadio."""
        self.stage_start_times[stage.value] = time.time()
        self.total_stages += 1
    
    def end_stage(self, stage: PipelineStage, success: bool = True):
        """Termina timing per uno stadio."""
        if stage.value in self.stage_start_times:
            duration = time.time() - self.stage_start_times[stage.value]
            self.stage_timings[stage.value] = duration
            
            if success:
                self.successful_stages += 1
            else:
                self.failed_stages += 1
    
    def finalize(self):
        """Finalizza le metriche."""
        self.end_time = time.time()
        if self.total_stages > 0:
            self.execution_success_rate = self.successful_stages / self.total_stages


@dataclass
class PipelineResult:
    """Risultato dell'esecuzione pipeline."""
    execution_id: str
    success: bool
    final_output: Optional[str] = None
    
    # Risultati intermedi
    parsed_prompt: Optional[ParsedPrompt] = None
    priority_vector: Optional[PriorityVector] = None
    memory_context: Optional[MemoryContext] = None
    reasoning_graph: Optional[ReasoningGraph] = None
    validation_result: Optional[ValidationResult] = None
    execution_result: Optional[ExecutionResult] = None
    patch_results: List[PatchResult] = field(default_factory=list)
    learning_results: Optional[Dict[str, Any]] = None
    
    # Metadati
    current_stage: PipelineStage = PipelineStage.PARSING
    error_message: Optional[str] = None
    metrics: Optional[ExecutionMetrics] = None
    
    # Tracciamento
    execution_trace: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class NGIntegration:
    """
    Controller principale per l'integrazione end-to-end di NEUROGLYPH.
    Coordina tutti i moduli cognitivi in una pipeline unificata.
    """
    
    def __init__(self, config: Optional[PipelineConfig] = None):
        """
        Inizializza il controller di integrazione.
        
        Args:
            config: Configurazione della pipeline
        """
        self.config = config or PipelineConfig()
        
        # Inizializza tutti i moduli cognitivi
        print("🚀 Inizializzazione NGIntegration...")
        
        self.parser = NGParser()
        self.prioritizer = NGContextPrioritizer()
        self.memory = NGMemory()
        self.reasoner = NGReasoner()
        self.self_check = NGSelfCheck()
        self.sandbox = NGSandbox()
        self.adaptive_patcher = NGAdaptivePatcher()
        
        # Inizializza learner se abilitato
        if self.config.enable_learning:
            learner_config = self.config.learner_config or LearnerConfig()
            self.learner = NGLearner(learner_config)
        else:
            self.learner = None
        
        # Statistiche
        self.execution_history: List[PipelineResult] = []
        self.total_executions = 0
        self.successful_executions = 0
        
        print("✅ NGIntegration inizializzato")
        print(f"   - Modalità: {self.config.execution_mode.value}")
        print(f"   - Self-check: {'✓' if self.config.enable_self_check else '✗'}")
        print(f"   - Sandbox: {'✓' if self.config.enable_sandbox else '✗'}")
        print(f"   - Adaptive Patching: {'✓' if self.config.enable_adaptive_patching else '✗'}")
        print(f"   - Learning: {'✓' if self.config.enable_learning else '✗'}")
    
    def run(self, source_code: str, context: Optional[Dict[str, Any]] = None) -> PipelineResult:
        """
        Esegue la pipeline completa su codice sorgente.
        
        Args:
            source_code: Codice sorgente da processare
            context: Contesto aggiuntivo per l'esecuzione
            
        Returns:
            Risultato dell'esecuzione pipeline
        """
        execution_id = f"exec_{uuid.uuid4().hex[:8]}"
        metrics = ExecutionMetrics(execution_id=execution_id)
        
        result = PipelineResult(
            execution_id=execution_id,
            success=False,
            metrics=metrics
        )
        
        self.total_executions += 1
        
        try:
            print(f"🔄 Avvio pipeline {execution_id}")
            result.execution_trace.append(f"Pipeline started: {execution_id}")
            
            # 1. PARSING
            result = self._execute_parsing(source_code, result, context)
            if not result.success:
                return result
            
            # 2. PRIORITIZATION
            result = self._execute_prioritization(result)
            if not result.success:
                return result
            
            # 3. MEMORY RETRIEVAL
            result = self._execute_memory_retrieval(result)
            if not result.success:
                return result
            
            # 4. REASONING
            result = self._execute_reasoning(result)
            if not result.success:
                return result
            
            # 5. SELF CHECK (se abilitato)
            if self.config.enable_self_check:
                result = self._execute_self_check(result)
                if not result.success:
                    return result
            
            # 6. SANDBOX EXECUTION (se abilitato)
            if self.config.enable_sandbox:
                result = self._execute_sandbox(result)
                # Continua anche se sandbox fallisce per permettere patching
            
            # 7. ADAPTIVE PATCHING (se abilitato e necessario)
            if self.config.enable_adaptive_patching and result.execution_result and not result.execution_result.success:
                result = self._execute_adaptive_patching(result)
            
            # 8. LEARNING (se abilitato)
            if self.config.enable_learning and self.learner:
                result = self._execute_learning(result)
            
            # Finalizza
            result.current_stage = PipelineStage.COMPLETED
            result.success = True
            self.successful_executions += 1
            
            # Genera output finale
            result.final_output = self._generate_final_output(result)
            
            print(f"✅ Pipeline {execution_id} completata con successo")
            
        except Exception as e:
            result.success = False
            result.current_stage = PipelineStage.FAILED
            result.error_message = f"Pipeline failed: {str(e)}"
            result.execution_trace.append(f"ERROR: {str(e)}")
            print(f"❌ Pipeline {execution_id} fallita: {str(e)}")
        
        finally:
            # Finalizza metriche
            metrics.finalize()
            
            # Salva nella storia
            self.execution_history.append(result)
            
            # Mantieni solo ultime 100 esecuzioni
            if len(self.execution_history) > 100:
                self.execution_history = self.execution_history[-100:]
        
        return result
    
    def _execute_parsing(self, source_code: str, result: PipelineResult,
                        context: Optional[Dict[str, Any]]) -> PipelineResult:
        """Esegue stadio di parsing."""
        result.current_stage = PipelineStage.PARSING
        result.metrics.start_stage(PipelineStage.PARSING)

        try:
            result.parsed_prompt = self.parser.parse(source_code)
            result.execution_trace.append("✓ Parsing completato")
            result.metrics.end_stage(PipelineStage.PARSING, True)
            result.success = True  # Parsing riuscito

        except Exception as e:
            result.success = False
            result.error_message = f"Parsing failed: {str(e)}"
            result.execution_trace.append(f"✗ Parsing failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.PARSING, False)

        return result
    
    def _execute_prioritization(self, result: PipelineResult) -> PipelineResult:
        """Esegue stadio di prioritizzazione."""
        result.current_stage = PipelineStage.PRIORITIZATION
        result.metrics.start_stage(PipelineStage.PRIORITIZATION)

        try:
            result.priority_vector = self.prioritizer.prioritize(result.parsed_prompt)
            result.execution_trace.append("✓ Prioritization completata")
            result.metrics.end_stage(PipelineStage.PRIORITIZATION, True)
            result.success = True  # Prioritization riuscita

        except Exception as e:
            result.success = False
            result.error_message = f"Prioritization failed: {str(e)}"
            result.execution_trace.append(f"✗ Prioritization failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.PRIORITIZATION, False)

        return result
    
    def _execute_memory_retrieval(self, result: PipelineResult) -> PipelineResult:
        """Esegue stadio di recupero memoria."""
        result.current_stage = PipelineStage.MEMORY_RETRIEVAL
        result.metrics.start_stage(PipelineStage.MEMORY_RETRIEVAL)

        try:
            # Usa append per aggiungere alla memoria
            memory_record = self.memory.append(
                prompt=result.parsed_prompt.original_text,
                response="Processing...",  # Non può essere vuoto
                prio_vec=result.priority_vector,
                symbols=[],  # Simboli vuoti per ora (problema di validazione)
                metadata=result.parsed_prompt.metadata
            )

            # Simula memory context per ora
            from .data_structures import MemoryContext
            result.memory_context = MemoryContext(
                symbols=[{"symbol": s, "confidence": 1.0} for s in result.parsed_prompt.symbols],
                examples=[],
                errors=[],
                metadata={"memory_record_id": str(memory_record.id)}
            )

            result.execution_trace.append("✓ Memory retrieval completato")
            result.metrics.end_stage(PipelineStage.MEMORY_RETRIEVAL, True)
            result.success = True  # Memory retrieval riuscito

        except Exception as e:
            result.success = False
            result.error_message = f"Memory retrieval failed: {str(e)}"
            result.execution_trace.append(f"✗ Memory retrieval failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.MEMORY_RETRIEVAL, False)

        return result
    
    def _execute_reasoning(self, result: PipelineResult) -> PipelineResult:
        """Esegue stadio di reasoning."""
        result.current_stage = PipelineStage.REASONING
        result.metrics.start_stage(PipelineStage.REASONING)

        try:
            result.reasoning_graph = self.reasoner.reason(
                result.memory_context,
                result.parsed_prompt
            )
            result.execution_trace.append("✓ Reasoning completato")
            result.metrics.end_stage(PipelineStage.REASONING, True)
            result.success = True  # Reasoning riuscito

        except Exception as e:
            result.success = False
            result.error_message = f"Reasoning failed: {str(e)}"
            result.execution_trace.append(f"✗ Reasoning failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.REASONING, False)

        return result
    
    def _execute_self_check(self, result: PipelineResult) -> PipelineResult:
        """Esegue stadio di self-check."""
        result.current_stage = PipelineStage.SELF_CHECK
        result.metrics.start_stage(PipelineStage.SELF_CHECK)

        try:
            result.validation_result = self.self_check.self_check(result.reasoning_graph)
            result.metrics.reasoning_quality_score = result.validation_result.metrics.overall_quality_score
            result.execution_trace.append("✓ Self-check completato")
            result.metrics.end_stage(PipelineStage.SELF_CHECK, True)
            result.success = True  # Self-check riuscito

        except Exception as e:
            result.success = False
            result.error_message = f"Self-check failed: {str(e)}"
            result.execution_trace.append(f"✗ Self-check failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.SELF_CHECK, False)

        return result
    
    def _execute_sandbox(self, result: PipelineResult) -> PipelineResult:
        """Esegue stadio di sandbox."""
        result.current_stage = PipelineStage.SANDBOX_EXECUTION
        result.metrics.start_stage(PipelineStage.SANDBOX_EXECUTION)
        
        try:
            result.execution_result = self.sandbox.execute_reasoning_graph(result.reasoning_graph)
            result.execution_trace.append("✓ Sandbox execution completato")
            result.metrics.end_stage(PipelineStage.SANDBOX_EXECUTION, result.execution_result.success)
            
        except Exception as e:
            # Non fallisce la pipeline, ma registra l'errore
            result.warnings.append(f"Sandbox execution failed: {str(e)}")
            result.execution_trace.append(f"⚠ Sandbox execution failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.SANDBOX_EXECUTION, False)
        
        return result
    
    def _execute_adaptive_patching(self, result: PipelineResult) -> PipelineResult:
        """Esegue stadio di adaptive patching."""
        result.current_stage = PipelineStage.ADAPTIVE_PATCHING
        result.metrics.start_stage(PipelineStage.ADAPTIVE_PATCHING)
        
        try:
            # Prova a correggere errori di esecuzione
            if result.execution_result and not result.execution_result.success:
                patch_result = self.adaptive_patcher.patch_execution_error(result.execution_result)
                if patch_result:
                    result.patch_results.append(patch_result)
            
            # Prova a correggere errori di validazione
            if result.validation_result and result.validation_result.has_errors:
                validation_patches = self.adaptive_patcher.patch_validation_errors(result.validation_result)
                result.patch_results.extend(validation_patches)
            
            result.execution_trace.append(f"✓ Adaptive patching completato ({len(result.patch_results)} patch)")
            result.metrics.end_stage(PipelineStage.ADAPTIVE_PATCHING, True)
            
        except Exception as e:
            result.warnings.append(f"Adaptive patching failed: {str(e)}")
            result.execution_trace.append(f"⚠ Adaptive patching failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.ADAPTIVE_PATCHING, False)
        
        return result
    
    def _execute_learning(self, result: PipelineResult) -> PipelineResult:
        """Esegue stadio di learning."""
        result.current_stage = PipelineStage.LEARNING
        result.metrics.start_stage(PipelineStage.LEARNING)
        
        try:
            if result.patch_results:
                result.learning_results = self.learner.learn(result.patch_results)
                result.execution_trace.append("✓ Learning completato")
            else:
                result.execution_trace.append("⚠ Learning skipped (no patch results)")
            
            result.metrics.end_stage(PipelineStage.LEARNING, True)
            
        except Exception as e:
            result.warnings.append(f"Learning failed: {str(e)}")
            result.execution_trace.append(f"⚠ Learning failed: {str(e)}")
            result.metrics.end_stage(PipelineStage.LEARNING, False)
        
        return result
    
    def _generate_final_output(self, result: PipelineResult) -> str:
        """Genera output finale della pipeline."""
        if result.execution_result and result.execution_result.success:
            return result.execution_result.result or "Execution completed successfully"
        elif result.patch_results and any(p.success for p in result.patch_results):
            successful_patches = [p for p in result.patch_results if p.success]
            return f"Code corrected with {len(successful_patches)} successful patches"
        elif result.reasoning_graph:
            return f"Reasoning completed with {len(result.reasoning_graph.all_facts)} facts and {len(result.reasoning_graph.paths)} paths"
        else:
            return "Processing completed"
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche della pipeline."""
        if self.total_executions == 0:
            return {"message": "No executions yet"}
        
        success_rate = self.successful_executions / self.total_executions
        
        # Calcola timing medi per stadio
        stage_timings = {}
        if self.execution_history:
            for stage in PipelineStage:
                timings = [
                    r.metrics.stage_timings.get(stage.value, 0)
                    for r in self.execution_history
                    if r.metrics and stage.value in r.metrics.stage_timings
                ]
                if timings:
                    stage_timings[stage.value] = {
                        "average": sum(timings) / len(timings),
                        "min": min(timings),
                        "max": max(timings)
                    }
        
        return {
            "total_executions": self.total_executions,
            "successful_executions": self.successful_executions,
            "success_rate": success_rate,
            "stage_timings": stage_timings,
            "recent_executions": len([
                r for r in self.execution_history
                if r.metrics and time.time() - r.metrics.start_time < 3600
            ])
        }
