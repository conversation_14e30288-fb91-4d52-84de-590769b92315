"""
NEUROGLYPH Learner Structures
Strutture dati per il modulo di apprendimento meta-cognitivo
"""

import time
import json
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum


class LearningMode(Enum):
    """Modalità di apprendimento."""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"
    EXPERIMENTAL = "experimental"


class PatternType(Enum):
    """Tipo di pattern appreso."""
    ERROR_CORRECTION = "error_correction"
    OPTIMIZATION = "optimization"
    SEMANTIC_ENHANCEMENT = "semantic_enhancement"
    LOGICAL_INFERENCE = "logical_inference"
    SYMBOLIC_MAPPING = "symbolic_mapping"


class ConfidenceLevel(Enum):
    """Livello di confidenza del pattern."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class KnowledgeNode:
    """Nodo nel grafo di conoscenza."""
    node_id: str
    node_type: str  # "error_pattern", "solution_pattern", "concept", "symbol"
    content: Dict[str, Any]
    embeddings: Optional[List[float]] = None
    creation_time: float = field(default_factory=time.time)
    last_accessed: float = field(default_factory=time.time)
    access_count: int = 0
    confidence: float = 0.5
    
    def update_access(self):
        """Aggiorna statistiche di accesso."""
        self.last_accessed = time.time()
        self.access_count += 1


@dataclass
class KnowledgeEdge:
    """Arco nel grafo di conoscenza."""
    edge_id: str
    source_node_id: str
    target_node_id: str
    relationship_type: str  # "causes", "solves", "similar_to", "generalizes"
    weight: float = 1.0
    confidence: float = 0.5
    evidence_count: int = 1
    creation_time: float = field(default_factory=time.time)
    
    def strengthen(self, amount: float = 0.1):
        """Rinforza la connessione."""
        self.weight = min(1.0, self.weight + amount)
        self.evidence_count += 1
        self.confidence = min(1.0, self.confidence + amount * 0.5)


@dataclass
class MetaPattern:
    """Pattern meta-cognitivo appreso."""
    pattern_id: str
    pattern_name: str
    pattern_type: PatternType
    
    # Struttura del pattern
    trigger_conditions: List[str]  # Condizioni che attivano il pattern
    action_sequence: List[str]     # Sequenza di azioni da eseguire
    expected_outcomes: List[str]   # Risultati attesi
    
    # Metriche di performance
    success_rate: float = 0.0
    total_applications: int = 0
    successful_applications: int = 0
    average_improvement: float = 0.0
    
    # Metadati
    confidence_level: ConfidenceLevel = ConfidenceLevel.LOW
    complexity_score: float = 0.5
    generalization_level: float = 0.0  # Quanto è generalizzabile
    
    # Tracciamento temporale
    creation_time: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    
    # Relazioni
    parent_patterns: Set[str] = field(default_factory=set)
    child_patterns: Set[str] = field(default_factory=set)
    similar_patterns: Set[str] = field(default_factory=set)
    
    def update_success_rate(self):
        """Aggiorna il tasso di successo."""
        if self.total_applications > 0:
            self.success_rate = self.successful_applications / self.total_applications
            
            # Aggiorna livello di confidenza basato su successo e utilizzo
            if self.success_rate >= 0.9 and self.total_applications >= 10:
                self.confidence_level = ConfidenceLevel.CRITICAL
            elif self.success_rate >= 0.7 and self.total_applications >= 5:
                self.confidence_level = ConfidenceLevel.HIGH
            elif self.success_rate >= 0.5 and self.total_applications >= 3:
                self.confidence_level = ConfidenceLevel.MEDIUM
            else:
                self.confidence_level = ConfidenceLevel.LOW
    
    def record_application(self, success: bool, improvement: float = 0.0):
        """Registra un'applicazione del pattern."""
        self.total_applications += 1
        if success:
            self.successful_applications += 1
            
            # Aggiorna miglioramento medio
            if self.successful_applications > 1:
                self.average_improvement = (
                    (self.average_improvement * (self.successful_applications - 1) + improvement) /
                    self.successful_applications
                )
            else:
                self.average_improvement = improvement
        
        self.last_used = time.time()
        self.update_success_rate()


@dataclass
class LearningSession:
    """Sessione di apprendimento."""
    session_id: str
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    
    # Dati della sessione
    patterns_discovered: List[str] = field(default_factory=list)
    patterns_refined: List[str] = field(default_factory=list)
    knowledge_nodes_added: int = 0
    knowledge_edges_added: int = 0
    
    # Metriche
    total_errors_processed: int = 0
    successful_corrections: int = 0
    improvement_score: float = 0.0
    
    # Contesto
    learning_mode: LearningMode = LearningMode.BALANCED
    focus_areas: List[str] = field(default_factory=list)
    
    def finalize(self):
        """Finalizza la sessione."""
        self.end_time = time.time()
        if self.total_errors_processed > 0:
            self.improvement_score = self.successful_corrections / self.total_errors_processed


@dataclass
class LearnerConfig:
    """Configurazione del learner."""
    learning_mode: LearningMode = LearningMode.BALANCED
    
    # Soglie di apprendimento
    min_pattern_confidence: float = 0.3
    min_pattern_applications: int = 3
    max_patterns_per_session: int = 50
    
    # Parametri di generalizzazione
    similarity_threshold: float = 0.8
    generalization_threshold: float = 0.7
    
    # Gestione memoria
    max_knowledge_nodes: int = 10000
    max_knowledge_edges: int = 50000
    memory_cleanup_interval: int = 3600  # secondi
    
    # Persistenza
    save_knowledge_graph: bool = True
    knowledge_graph_file: str = "ng_knowledge_graph.json"
    save_patterns: bool = True
    patterns_file: str = "ng_meta_patterns.json"
    
    # Performance
    enable_embeddings: bool = True
    embedding_dimension: int = 384
    batch_processing: bool = True
    parallel_processing: bool = False


@dataclass
class LearnerState:
    """Stato del learner."""
    # Statistiche generali
    total_sessions: int = 0
    total_patterns_learned: int = 0
    total_knowledge_nodes: int = 0
    total_knowledge_edges: int = 0
    
    # Performance
    overall_learning_rate: float = 0.0
    average_pattern_quality: float = 0.0
    knowledge_graph_density: float = 0.0
    
    # Stato corrente
    current_session: Optional[LearningSession] = None
    active_patterns: List[str] = field(default_factory=list)
    
    # Tracciamento temporale
    last_learning_session: Optional[float] = None
    last_knowledge_update: Optional[float] = None
    last_cleanup: Optional[float] = None
    
    # Modalità adattiva
    adaptive_learning_rate: float = 0.1
    exploration_rate: float = 0.2
    
    def update_statistics(self, patterns: List[MetaPattern], nodes_count: int, edges_count: int):
        """Aggiorna le statistiche."""
        self.total_patterns_learned = len(patterns)
        self.total_knowledge_nodes = nodes_count
        self.total_knowledge_edges = edges_count
        
        # Calcola qualità media pattern
        if patterns:
            self.average_pattern_quality = sum(p.success_rate for p in patterns) / len(patterns)
        
        # Calcola densità grafo
        if nodes_count > 1:
            max_edges = nodes_count * (nodes_count - 1) / 2
            self.knowledge_graph_density = edges_count / max_edges if max_edges > 0 else 0.0
        
        self.last_knowledge_update = time.time()


@dataclass
class LearningInsight:
    """Insight appreso dal sistema."""
    insight_id: str
    insight_type: str  # "pattern_discovery", "optimization", "generalization"
    description: str
    
    # Evidenza
    supporting_evidence: List[str]
    confidence_score: float
    impact_score: float  # Quanto impatto ha avuto
    
    # Applicabilità
    applicable_contexts: List[str]
    prerequisites: List[str]
    
    # Tracciamento
    discovery_time: float = field(default_factory=time.time)
    validation_count: int = 0
    application_count: int = 0
    
    def validate(self):
        """Valida l'insight."""
        self.validation_count += 1
        # Aumenta confidenza con validazioni multiple
        self.confidence_score = min(1.0, self.confidence_score + 0.1)
    
    def apply(self, impact: float):
        """Registra applicazione dell'insight."""
        self.application_count += 1
        # Aggiorna impatto medio
        if self.application_count > 1:
            self.impact_score = (
                (self.impact_score * (self.application_count - 1) + impact) /
                self.application_count
            )
        else:
            self.impact_score = impact
