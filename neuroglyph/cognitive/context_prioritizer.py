"""
NEUROGLYPH Context Prioritizer
Modulo per la prioritizzazione intelligente del contesto basata su urgenza, rischio e dominio
"""

import re
import time
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

from .data_structures import (
    ParsedPrompt, PriorityVector, DomainType, 
    UrgencyLevel, RiskLevel
)


class NGContextPrioritizer:
    """
    Context Prioritizer per NEUROGLYPH.
    Classifica prompt in base a urgenza, rischio e dominio semantico.
    """
    
    def __init__(self):
        """Inizializza il Context Prioritizer."""
        self.urgency_patterns = self._load_urgency_patterns()
        self.risk_patterns = self._load_risk_patterns()
        self.domain_patterns = self._load_domain_patterns()
        self.urgency_keywords = self._load_urgency_keywords()
        self.risk_keywords = self._load_risk_keywords()
        
        print("🎯 NGContextPrioritizer inizializzato")
        print(f"   - Pattern urgenza: {len(self.urgency_patterns)}")
        print(f"   - Pattern rischio: {len(self.risk_patterns)}")
        print(f"   - Pattern dominio: {len(self.domain_patterns)}")
    
    def prioritize(self, parsed_prompt: ParsedPrompt) -> PriorityVector:
        """
        Prioritizza un prompt parsato.
        
        Args:
            parsed_prompt: Prompt già parsato simbolicamente
            
        Returns:
            PriorityVector con urgenza, rischio e dominio
        """
        start_time = time.time()
        
        # 1. Classifica urgenza
        urgency, urgency_reasoning = self._classify_urgency(parsed_prompt)
        
        # 2. Valuta rischio
        risk, risk_reasoning = self._assess_risk(parsed_prompt)
        
        # 3. Mappa dominio
        domain, domain_reasoning = self._map_domain(parsed_prompt)
        
        # 4. Calcola confidenza
        confidence = self._calculate_confidence(urgency, risk, domain, parsed_prompt)
        
        # 5. Combina reasoning
        combined_reasoning = f"Urgency: {urgency_reasoning}. Risk: {risk_reasoning}. Domain: {domain_reasoning}"
        
        processing_time = time.time() - start_time
        
        priority_vector = PriorityVector(
            urgency=urgency,
            risk=risk,
            domain=domain,
            confidence=confidence,
            reasoning=combined_reasoning,
            metadata={
                "processing_time": processing_time,
                "prompt_length": len(parsed_prompt.original_text),
                "tokens_count": len(parsed_prompt.tokens),
                "symbols_count": len(parsed_prompt.symbols)
            }
        )
        
        return priority_vector
    
    def _classify_urgency(self, parsed_prompt: ParsedPrompt) -> Tuple[float, str]:
        """Classifica il livello di urgenza."""
        text = parsed_prompt.original_text.lower()
        urgency_score = 0.0
        reasons = []
        
        # Pattern regex per urgenza
        for pattern, score, description in self.urgency_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                urgency_score = max(urgency_score, score)
                reasons.append(f"{description} (score: {score})")
        
        # Keywords urgenza
        for keyword, score in self.urgency_keywords.items():
            if keyword in text:
                urgency_score = max(urgency_score, score)
                reasons.append(f"keyword '{keyword}' (score: {score})")
        
        # Analisi simboli per urgenza
        symbol_urgency = self._analyze_symbol_urgency(parsed_prompt.symbols)
        if symbol_urgency > 0:
            urgency_score = max(urgency_score, symbol_urgency)
            reasons.append(f"symbolic analysis (score: {symbol_urgency})")
        
        # Analisi intenti per urgenza
        intent_urgency = self._analyze_intent_urgency(parsed_prompt.intents)
        if intent_urgency > 0:
            urgency_score = max(urgency_score, intent_urgency)
            reasons.append(f"intent analysis (score: {intent_urgency})")
        
        reasoning = "; ".join(reasons) if reasons else "no urgency indicators found"
        return min(urgency_score, 1.0), reasoning
    
    def _assess_risk(self, parsed_prompt: ParsedPrompt) -> Tuple[float, str]:
        """Valuta il livello di rischio."""
        text = parsed_prompt.original_text.lower()
        risk_score = 0.0
        reasons = []
        
        # Pattern regex per rischio
        for pattern, score, description in self.risk_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                risk_score = max(risk_score, score)
                reasons.append(f"{description} (score: {score})")
        
        # Keywords rischio
        for keyword, score in self.risk_keywords.items():
            if keyword in text:
                risk_score = max(risk_score, score)
                reasons.append(f"keyword '{keyword}' (score: {score})")
        
        # Analisi simboli per rischio
        symbol_risk = self._analyze_symbol_risk(parsed_prompt.symbols)
        if symbol_risk > 0:
            risk_score = max(risk_score, symbol_risk)
            reasons.append(f"symbolic analysis (score: {symbol_risk})")
        
        reasoning = "; ".join(reasons) if reasons else "no risk indicators found"
        return min(risk_score, 1.0), reasoning
    
    def _map_domain(self, parsed_prompt: ParsedPrompt) -> Tuple[DomainType, str]:
        """Mappa il dominio semantico."""
        text = parsed_prompt.original_text.lower()
        domain_scores = {}
        reasons = []
        
        # Pattern regex per domini
        for domain, patterns in self.domain_patterns.items():
            domain_score = 0.0
            domain_reasons = []
            
            for pattern, score, description in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    domain_score = max(domain_score, score)
                    domain_reasons.append(f"{description} (score: {score})")
            
            if domain_score > 0:
                domain_scores[domain] = domain_score
                reasons.extend([f"{domain}: {r}" for r in domain_reasons])
        
        # Analisi simboli per dominio
        symbol_domains = self._analyze_symbol_domains(parsed_prompt.symbols)
        for domain, score in symbol_domains.items():
            if score > domain_scores.get(domain, 0):
                domain_scores[domain] = score
                reasons.append(f"{domain}: symbolic analysis (score: {score})")
        
        # Seleziona dominio con score più alto
        if domain_scores:
            best_domain = max(domain_scores.items(), key=lambda x: x[1])
            domain_type = DomainType(best_domain[0])
            reasoning = "; ".join([r for r in reasons if r.startswith(best_domain[0])])
        else:
            domain_type = DomainType.GENERAL
            reasoning = "no specific domain indicators found, defaulting to general"
        
        return domain_type, reasoning
    
    def _calculate_confidence(self, urgency: float, risk: float, 
                            domain: DomainType, parsed_prompt: ParsedPrompt) -> float:
        """Calcola la confidenza nella classificazione."""
        confidence = 1.0
        
        # Riduce confidenza per prompt molto corti
        if len(parsed_prompt.original_text) < 10:
            confidence *= 0.7
        
        # Riduce confidenza per prompt senza simboli
        if not parsed_prompt.symbols:
            confidence *= 0.8
        
        # Riduce confidenza per classificazioni ambigue
        if 0.4 <= urgency <= 0.6:  # Zona ambigua
            confidence *= 0.9
        
        if 0.4 <= risk <= 0.6:  # Zona ambigua
            confidence *= 0.9
        
        # Aumenta confidenza per classificazioni chiare
        if urgency >= 0.8 or urgency <= 0.2:
            confidence *= 1.1
        
        if risk >= 0.8 or risk <= 0.2:
            confidence *= 1.1
        
        return min(confidence, 1.0)
    
    def _analyze_symbol_urgency(self, symbols: List[str]) -> float:
        """Analizza urgenza dai simboli."""
        urgency_symbols = {
            "⟐": 0.7,  # Try/except (gestione errori urgente)
            "◊": 0.5,   # If (decisioni urgenti)
            "⟲": 0.3,   # For (iterazioni possono essere urgenti)
        }
        
        max_urgency = 0.0
        for symbol in symbols:
            for urgency_symbol, score in urgency_symbols.items():
                if urgency_symbol in symbol:
                    max_urgency = max(max_urgency, score)
        
        return max_urgency
    
    def _analyze_symbol_risk(self, symbols: List[str]) -> float:
        """Analizza rischio dai simboli."""
        risk_symbols = {
            "⟐": 0.6,  # Try/except (operazioni rischiose)
            "⎋": 0.4,   # With (file operations)
            "⊶⎋": 0.5,  # AsyncWith (async operations)
        }
        
        max_risk = 0.0
        for symbol in symbols:
            for risk_symbol, score in risk_symbols.items():
                if risk_symbol in symbol:
                    max_risk = max(max_risk, score)
        
        return max_risk
    
    def _analyze_symbol_domains(self, symbols: List[str]) -> Dict[str, float]:
        """Analizza domini dai simboli."""
        domain_symbols = {
            "code": {
                "⟨⟩": 0.8,  # Functions
                "⟡": 0.7,   # Classes
                "◊": 0.6,   # If statements
            },
            "data": {
                "⟲": 0.7,   # For loops (data processing)
                "⩺": 0.6,   # Generators
            },
            "system": {
                "⎋": 0.8,   # With statements (file/resource management)
                "⟐": 0.7,   # Try/except (error handling)
            },
            "text": {
                "⌦": 0.8,   # F-strings
            }
        }
        
        domain_scores = {}
        for domain, symbols_dict in domain_symbols.items():
            max_score = 0.0
            for symbol in symbols:
                for domain_symbol, score in symbols_dict.items():
                    if domain_symbol in symbol:
                        max_score = max(max_score, score)
            if max_score > 0:
                domain_scores[domain] = max_score
        
        return domain_scores
    
    def _analyze_intent_urgency(self, intents: List[str]) -> float:
        """Analizza urgenza dagli intenti."""
        urgent_intents = {
            "error": 0.9,
            "fix": 0.8,
            "urgent": 1.0,
            "critical": 1.0,
            "emergency": 1.0,
            "asap": 0.9,
            "immediately": 0.9
        }
        
        max_urgency = 0.0
        for intent in intents:
            intent_lower = intent.lower()
            for urgent_intent, score in urgent_intents.items():
                if urgent_intent in intent_lower:
                    max_urgency = max(max_urgency, score)
        
        return max_urgency
    
    def _load_urgency_patterns(self) -> List[Tuple[str, float, str]]:
        """Carica pattern regex per urgenza."""
        return [
            (r'\b(urgent|asap|immediately|critical|emergency)\b', 1.0, "urgent keywords"),
            (r'\b(quick|fast|soon|now)\b', 0.7, "time pressure keywords"),
            (r'\b(fix|repair|solve|debug)\b', 0.6, "problem-solving keywords"),
            (r'\b(error|bug|issue|problem)\b', 0.8, "error-related keywords"),
            (r'\b(deadline|due|expire)\b', 0.7, "deadline keywords"),
            (r'[!]{2,}', 0.6, "multiple exclamation marks"),
            (r'\b(help|stuck|broken)\b', 0.5, "help-seeking keywords"),
        ]
    
    def _load_risk_patterns(self) -> List[Tuple[str, float, str]]:
        """Carica pattern regex per rischio."""
        return [
            (r'\b(delete|remove|drop|destroy)\b', 0.9, "destructive operations"),
            (r'\b(sudo|admin|root|privilege)\b', 0.8, "elevated privileges"),
            (r'\b(password|secret|key|token)\b', 0.7, "sensitive data"),
            (r'\b(execute|run|eval|exec)\b', 0.6, "code execution"),
            (r'\b(network|socket|connection)\b', 0.5, "network operations"),
            (r'\b(file|write|save|create)\b', 0.4, "file operations"),
            (r'\b(database|sql|query)\b', 0.5, "database operations"),
            (r'\b(system|os|shell|command)\b', 0.6, "system operations"),
        ]
    
    def _load_domain_patterns(self) -> Dict[str, List[Tuple[str, float, str]]]:
        """Carica pattern regex per domini."""
        return {
            "code": [
                (r'\b(function|class|method|variable)\b', 0.8, "code structure"),
                (r'\b(python|javascript|java|c\+\+)\b', 0.7, "programming languages"),
                (r'\b(algorithm|logic|implementation)\b', 0.6, "algorithmic concepts"),
            ],
            "data": [
                (r'\b(data|dataset|csv|json|xml)\b', 0.8, "data formats"),
                (r'\b(analyze|process|transform|filter)\b', 0.7, "data operations"),
                (r'\b(statistics|analysis|visualization)\b', 0.6, "data analysis"),
            ],
            "system": [
                (r'\b(server|service|daemon|process)\b', 0.8, "system services"),
                (r'\b(memory|cpu|disk|performance)\b', 0.7, "system resources"),
                (r'\b(install|configure|setup)\b', 0.6, "system configuration"),
            ],
            "security": [
                (r'\b(security|vulnerability|attack|threat)\b', 0.9, "security concepts"),
                (r'\b(encrypt|decrypt|hash|signature)\b', 0.8, "cryptographic operations"),
                (r'\b(authentication|authorization|access)\b', 0.7, "access control"),
            ],
            "network": [
                (r'\b(http|https|api|rest|socket)\b', 0.8, "network protocols"),
                (r'\b(request|response|client|server)\b', 0.7, "network communication"),
                (r'\b(url|endpoint|route|path)\b', 0.6, "network addressing"),
            ],
            "math": [
                (r'\b(calculate|compute|formula|equation)\b', 0.8, "mathematical operations"),
                (r'\b(matrix|vector|algebra|geometry)\b', 0.7, "mathematical concepts"),
                (r'\b(statistics|probability|optimization)\b', 0.6, "mathematical analysis"),
            ]
        }
    
    def _load_urgency_keywords(self) -> Dict[str, float]:
        """Carica keywords per urgenza."""
        return {
            "urgent": 1.0,
            "asap": 1.0,
            "immediately": 1.0,
            "critical": 1.0,
            "emergency": 1.0,
            "quick": 0.7,
            "fast": 0.7,
            "soon": 0.6,
            "now": 0.8,
            "help": 0.5,
            "stuck": 0.6,
            "broken": 0.7,
            "error": 0.8,
            "bug": 0.7,
            "issue": 0.6,
            "problem": 0.6,
            "fix": 0.7,
            "repair": 0.6,
            "solve": 0.5,
            "debug": 0.6
        }
    
    def _load_risk_keywords(self) -> Dict[str, float]:
        """Carica keywords per rischio."""
        return {
            "delete": 0.9,
            "remove": 0.8,
            "drop": 0.9,
            "destroy": 1.0,
            "sudo": 0.8,
            "admin": 0.7,
            "root": 0.9,
            "privilege": 0.7,
            "password": 0.7,
            "secret": 0.8,
            "key": 0.6,
            "token": 0.6,
            "execute": 0.6,
            "run": 0.5,
            "eval": 0.8,
            "exec": 0.8,
            "system": 0.6,
            "shell": 0.7,
            "command": 0.5,
            "network": 0.4,
            "socket": 0.5,
            "connection": 0.4
        }
