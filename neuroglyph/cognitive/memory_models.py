"""
NEUROGLYPH Memory Models
Modelli dati per il sistema di memoria a lungo termine
"""

import json
import time
import math
from datetime import datetime, timezone
from typing import Dict, List, Set, Optional, Any, Union
from uuid import UUID, uuid4
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, validator


class MemoryRecord(BaseModel):
    """
    Record di memoria per NEUROGLYPH.
    Contiene prompt, response, priority vector e metadati.
    """
    
    # Identificatori
    id: UUID = Field(default_factory=uuid4)
    
    # Contenuto principale
    prompt: str = Field(..., min_length=1, max_length=10000)
    response: str = Field(..., min_length=1, max_length=50000)
    
    # Vettore di priorità (da NG_CONTEXT_PRIORITIZER)
    prio_vec: Dict[str, Any] = Field(...)
    
    # Simboli NEUROGLYPH
    symbols: List[str] = Field(default_factory=list)
    
    # Tags semantici
    tags: Set[str] = Field(default_factory=set)
    
    # Metadati temporali
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_used: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Statistiche utilizzo
    hits: int = Field(default=0, ge=0)
    
    # Score calcolato (per decay e ranking)
    score: float = Field(default=1.0, ge=0.0, le=1.0)
    initial_score: float = Field(default=1.0, ge=0.0, le=1.0)
    
    # Metadati aggiuntivi
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        """Configurazione Pydantic."""
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat(),
            set: list
        }
        
        schema_extra = {
            "example": {
                "prompt": "Fix critical security bug immediately",
                "response": "def fix_security_bug():\n    # Implementation here\n    pass",
                "prio_vec": {
                    "urgency": 1.0,
                    "risk": 0.8,
                    "domain": "security",
                    "confidence": 0.95
                },
                "symbols": ["⟨⟩§func_0§", "⟐§try_1§"],
                "tags": {"security", "bugfix", "critical"},
                "hits": 5,
                "score": 0.85
            }
        }
    
    @validator('prio_vec')
    def validate_prio_vec(cls, v):
        """Valida il vettore di priorità."""
        required_keys = {'urgency', 'risk', 'domain', 'confidence'}
        if not all(key in v for key in required_keys):
            raise ValueError(f"prio_vec must contain keys: {required_keys}")

        # Valida range valori numerici
        for key in ['urgency', 'risk', 'confidence']:
            if not 0.0 <= v[key] <= 1.0:
                raise ValueError(f"{key} must be between 0.0 and 1.0")

        # Valida domain (può essere stringa)
        if not isinstance(v['domain'], str):
            raise ValueError("domain must be a string")

        return v
    
    @validator('symbols')
    def validate_symbols(cls, v):
        """Valida i simboli NEUROGLYPH (versione permissiva per MVP)."""
        for symbol in v:
            if not isinstance(symbol, str):
                raise ValueError(f"Symbol must be string: {symbol}")
        # Per ora accettiamo tutti i simboli stringa per compatibilità MVP
        return v
    
    def update_usage(self):
        """Aggiorna statistiche di utilizzo."""
        self.hits += 1
        self.last_used = datetime.now(timezone.utc)
    
    def calculate_decay_score(self, decay_lambda: float = 0.001) -> float:
        """
        Calcola score con decay esponenziale.
        
        Args:
            decay_lambda: Parametro di decay (default: 0.001 s⁻¹)
            
        Returns:
            Score decaduto
        """
        now = datetime.now(timezone.utc)
        age_seconds = (now - self.created_at).total_seconds()
        
        # Score base con decay esponenziale
        decay_factor = math.exp(-decay_lambda * age_seconds)
        
        # Bonus per riutilizzo (logaritmico per evitare esplosione)
        reuse_bonus = math.log(1 + self.hits) * 0.1
        
        # Score finale
        decayed_score = (self.initial_score * decay_factor) + reuse_bonus
        
        return min(decayed_score, 1.0)
    
    def update_score(self, decay_lambda: float = 0.001):
        """Aggiorna il score con decay."""
        self.score = self.calculate_decay_score(decay_lambda)
    
    @property
    def age_seconds(self) -> float:
        """Età del record in secondi."""
        now = datetime.now(timezone.utc)
        return (now - self.created_at).total_seconds()
    
    @property
    def priority_score(self) -> float:
        """Score di priorità combinato."""
        pv = self.prio_vec
        return (pv['urgency'] * 0.6 + 
                (1.0 - pv['risk']) * 0.3 + 
                pv['confidence'] * 0.1)
    
    @property
    def domain(self) -> str:
        """Dominio semantico."""
        return self.prio_vec.get('domain', 'general')
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte in dizionario per serializzazione."""
        return {
            'id': str(self.id),
            'prompt': self.prompt,
            'response': self.response,
            'prio_vec': self.prio_vec,
            'symbols': self.symbols,
            'tags': list(self.tags),
            'created_at': self.created_at.isoformat(),
            'last_used': self.last_used.isoformat(),
            'hits': self.hits,
            'score': self.score,
            'initial_score': self.initial_score,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryRecord':
        """Crea da dizionario."""
        # Converte stringhe datetime
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if isinstance(data.get('last_used'), str):
            data['last_used'] = datetime.fromisoformat(data['last_used'])
        
        # Converte UUID
        if isinstance(data.get('id'), str):
            data['id'] = UUID(data['id'])
        
        # Converte tags
        if isinstance(data.get('tags'), list):
            data['tags'] = set(data['tags'])
        
        return cls(**data)


@dataclass
class MemorySearchQuery:
    """Query per ricerca in memoria."""
    
    # Testo di ricerca
    text: Optional[str] = None
    
    # Vettore di priorità target
    target_prio_vec: Optional[Dict[str, float]] = None
    
    # Filtri
    domains: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    symbols: Optional[List[str]] = None
    min_score: float = 0.0
    max_age_seconds: Optional[float] = None
    
    # Parametri risultati
    limit: int = 5
    offset: int = 0
    
    # Pesi per scoring
    weight_prio: float = 0.5
    weight_text: float = 0.3
    weight_reuse: float = 0.2


@dataclass
class MemorySearchResult:
    """Risultato di ricerca in memoria."""
    
    record: MemoryRecord
    relevance_score: float
    match_reasons: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Valida score."""
        self.relevance_score = max(0.0, min(1.0, self.relevance_score))


@dataclass
class MemoryStats:
    """Statistiche del sistema di memoria."""
    
    total_records: int = 0
    total_size_bytes: int = 0
    avg_score: float = 0.0
    oldest_record_age: float = 0.0
    most_used_hits: int = 0
    
    # Statistiche per dominio
    domain_counts: Dict[str, int] = field(default_factory=dict)
    
    # Statistiche temporali
    records_last_hour: int = 0
    records_last_day: int = 0
    records_last_week: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte in dizionario."""
        return {
            'total_records': self.total_records,
            'total_size_bytes': self.total_size_bytes,
            'avg_score': self.avg_score,
            'oldest_record_age': self.oldest_record_age,
            'most_used_hits': self.most_used_hits,
            'domain_counts': self.domain_counts,
            'records_last_hour': self.records_last_hour,
            'records_last_day': self.records_last_day,
            'records_last_week': self.records_last_week
        }


class MemoryConfig(BaseModel):
    """Configurazione per NGMemory."""
    
    # Database
    db_path: str = ":memory:"
    max_records: int = 100000
    
    # Decay parameters
    decay_lambda: float = 0.001  # s⁻¹
    decay_update_interval: int = 3600  # seconds
    
    # Eviction policy
    eviction_policy: str = "lru_low_score"  # lru, lru_low_score, score_only
    eviction_batch_size: int = 1000
    
    # Search parameters
    default_search_limit: int = 5
    min_relevance_threshold: float = 0.1
    
    # Performance
    enable_fts: bool = True
    enable_rtree: bool = True
    vacuum_interval: int = 86400  # seconds
    
    # Snapshot
    snapshot_enabled: bool = True
    snapshot_interval: int = 86400  # seconds
    snapshot_compression: str = "zstd"  # zstd, lz4, gzip
    
    class Config:
        """Configurazione Pydantic."""
        schema_extra = {
            "example": {
                "db_path": "neuroglyph_memory.db",
                "max_records": 50000,
                "decay_lambda": 0.0005,
                "eviction_policy": "lru_low_score",
                "default_search_limit": 3,
                "snapshot_enabled": True
            }
        }
