"""
NEUROGLYPH Cognitive Decoder

Decoder cognitivo per NEUROGLYPH che estende le capacità di decodifica
con generazione di codice da proof trees e reasoning graphs.

Integra:
- Template Engine per code generation
- Proof Tree analysis
- Reasoning Graph interpretation
- Code synthesis da symbolic reasoning
"""

import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from pathlib import Path

from ..code_templates.template_engine import TemplateEngine
from ..logic.proof_tree import ProofTree, ProofStep

logger = logging.getLogger(__name__)


@dataclass
class CodeGenerationResult:
    """Risultato di generazione codice da proof tree."""
    success: bool
    generated_code: Optional[str] = None
    template_used: Optional[str] = None
    proof_analysis: Dict[str, Any] = None
    error_message: Optional[str] = None
    generation_time: float = 0.0
    
    def __post_init__(self):
        if self.proof_analysis is None:
            self.proof_analysis = {}


@dataclass
class DecodingContext:
    """Contesto per decodifica cognitiva."""
    reasoning_depth: int = 0
    symbolic_complexity: float = 0.0
    domain_hints: List[str] = None
    performance_constraints: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.domain_hints is None:
            self.domain_hints = []
        if self.performance_constraints is None:
            self.performance_constraints = {}


class NGDecoder:
    """
    Decoder cognitivo NEUROGLYPH per generazione intelligente di codice.
    
    Features:
    - Analisi proof trees per identificare pattern algoritmici
    - Generazione codice da template basata su reasoning
    - Integrazione con FormalLogicEngine e SymbolicMathEngine
    - Context-aware code synthesis
    """
    
    def __init__(self, template_dir: Optional[str] = None, enable_caching: bool = True):
        """
        Inizializza NGDecoder.
        
        Args:
            template_dir: Directory template personalizzata
            enable_caching: Abilita cache per performance
        """
        # Inizializza Template Engine
        if template_dir is None:
            # Default: usa template nella directory code_templates
            template_dir = Path(__file__).parent.parent / "code_templates"
        
        self.template_engine = TemplateEngine(template_dir=str(template_dir))
        self.enable_caching = enable_caching
        
        # Cache per proof analysis
        self._proof_cache: Dict[str, Any] = {}
        
        # Statistiche
        self.generations_count = 0
        self.successful_generations = 0
        self.cache_hits = 0
        
        # Pattern recognition per algoritmi comuni
        self._algorithm_patterns = {
            "gcd": ["greatest_common_divisor", "euclid", "gcd"],
            "fibonacci": ["fibonacci", "fib", "sequence"],
            "factorial": ["factorial", "fact", "n!"],
            "prime": ["prime", "primality", "is_prime"],
            "sort": ["sort", "order", "arrange"],
            "search": ["search", "find", "lookup", "binary_search"]
        }
        
        logger.info(f"🧠 NGDecoder inizializzato con {len(self.template_engine.templates)} template")
    
    def analyze_proof_tree(self, proof: ProofTree) -> Dict[str, Any]:
        """
        Analizza un proof tree per identificare pattern algoritmici.
        
        Args:
            proof: ProofTree da analizzare
            
        Returns:
            Dizionario con analisi del proof
        """
        analysis = {
            "depth": 0,
            "steps_count": 0,
            "algorithm_hints": [],
            "mathematical_operations": [],
            "logical_patterns": [],
            "code_template_candidates": []
        }
        
        if not proof or not proof.steps:
            return analysis
        
        # Analizza profondità e step count
        analysis["depth"] = self._calculate_proof_depth(proof)
        analysis["steps_count"] = len(proof.steps)
        
        # Cerca pattern algoritmici nei step
        for step in proof.steps:
            self._analyze_proof_step(step, analysis)
        
        # Identifica template candidati
        analysis["code_template_candidates"] = self._identify_template_candidates(analysis)
        
        return analysis
    
    def _calculate_proof_depth(self, proof: ProofTree) -> int:
        """Calcola la profondità massima del proof tree."""
        if not proof.steps:
            return 0
        
        max_depth = 0
        for step in proof.steps:
            if hasattr(step, 'sub_proofs') and step.sub_proofs:
                for sub_proof in step.sub_proofs:
                    depth = 1 + self._calculate_proof_depth(sub_proof)
                    max_depth = max(max_depth, depth)
        
        return max_depth
    
    def _analyze_proof_step(self, step: ProofStep, analysis: Dict[str, Any]):
        """Analizza un singolo step del proof per pattern."""
        if not step.formula:
            return

        conclusion_str = str(step.formula).lower()
        
        # Cerca hint algoritmici
        for algorithm, keywords in self._algorithm_patterns.items():
            for keyword in keywords:
                if keyword in conclusion_str:
                    if algorithm not in analysis["algorithm_hints"]:
                        analysis["algorithm_hints"].append(algorithm)
        
        # Identifica operazioni matematiche
        math_ops = ["divide", "multiply", "modulo", "remainder", "factor"]
        for op in math_ops:
            if op in conclusion_str:
                if op not in analysis["mathematical_operations"]:
                    analysis["mathematical_operations"].append(op)
        
        # Identifica pattern logici
        logical_patterns = ["recursive", "iterative", "base_case", "induction"]
        for pattern in logical_patterns:
            if pattern in conclusion_str:
                if pattern not in analysis["logical_patterns"]:
                    analysis["logical_patterns"].append(pattern)
    
    def _identify_template_candidates(self, analysis: Dict[str, Any]) -> List[str]:
        """Identifica template candidati basati sull'analisi."""
        candidates = []
        
        # Mapping da algorithm hints a template
        hint_to_template = {
            "gcd": "euclid_gcd",
            "fibonacci": "fibonacci_recursive" if "recursive" in analysis["logical_patterns"] else "fibonacci_iterative",
            "factorial": "factorial_recursive",
            "prime": "prime_check",
            "sort": "quicksort",  # Default, potrebbe essere raffinato
            "search": "binary_search"
        }
        
        for hint in analysis["algorithm_hints"]:
            template_id = hint_to_template.get(hint)
            if template_id and template_id not in candidates:
                candidates.append(template_id)
        
        return candidates
    
    def generate_code_from_proof(self, proof: ProofTree, 
                                context: Optional[DecodingContext] = None,
                                preferred_template: Optional[str] = None) -> CodeGenerationResult:
        """
        Genera codice Python da un proof tree.
        
        Args:
            proof: ProofTree contenente il reasoning
            context: Contesto di decodifica
            preferred_template: Template preferito (override automatico)
            
        Returns:
            CodeGenerationResult con codice generato
        """
        import time
        start_time = time.time()
        self.generations_count += 1
        
        try:
            # Analizza proof tree
            analysis = self.analyze_proof_tree(proof)
            
            # Determina template da usare
            template_id = self._select_template(analysis, preferred_template)
            
            if not template_id:
                return CodeGenerationResult(
                    success=False,
                    proof_analysis=analysis,
                    error_message="Nessun template appropriato trovato per il proof tree",
                    generation_time=time.time() - start_time
                )
            
            # Genera codice
            generated_code = self.template_engine.render(template_id)
            
            self.successful_generations += 1
            
            return CodeGenerationResult(
                success=True,
                generated_code=generated_code,
                template_used=template_id,
                proof_analysis=analysis,
                generation_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"❌ Errore generazione codice da proof: {e}")
            
            return CodeGenerationResult(
                success=False,
                error_message=str(e),
                generation_time=time.time() - start_time
            )
    
    def _select_template(self, analysis: Dict[str, Any], 
                        preferred_template: Optional[str] = None) -> Optional[str]:
        """Seleziona il template più appropriato."""
        if preferred_template and preferred_template in self.template_engine.templates:
            return preferred_template
        
        candidates = analysis.get("code_template_candidates", [])
        
        if not candidates:
            return None
        
        # Seleziona il primo candidato (potrebbe essere raffinato con scoring)
        return candidates[0]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche del decoder."""
        success_rate = (self.successful_generations / self.generations_count 
                       if self.generations_count > 0 else 0.0)
        
        return {
            "generations_count": self.generations_count,
            "successful_generations": self.successful_generations,
            "success_rate": success_rate,
            "cache_hits": self.cache_hits,
            "available_templates": len(self.template_engine.templates),
            "template_categories": self.template_engine.get_categories()
        }
