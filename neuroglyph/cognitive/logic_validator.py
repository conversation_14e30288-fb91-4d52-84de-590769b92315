"""
NEUROGLYPH Logic Validator
Validatore logico per ReasoningGraph e logical inference rules
"""

import time
from typing import List, Optional, Dict, Any, Set, Tuple

from .validation_structures import (
    ValidationError, ValidationErrorType, ValidationSeverity, 
    ValidationLevel, ValidationConfig
)
from .reasoning_structures import (
    ReasoningGraph, LogicalFact, ReasoningStep, ReasoningPath,
    LogicalOperator, FactType
)


class LogicValidator:
    """
    Validatore logico per NEUROGLYPH.
    Verifica correttezza delle inferenze logiche e rileva circolarità.
    """
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        """
        Inizializza il validatore logico.
        
        Args:
            config: Configurazione validazione
        """
        self.config = config or ValidationConfig()
        self.errors = []
        
        # Regole di inferenza valide
        self.inference_rules = self._load_inference_rules()
        
        print("🔍 LogicValidator inizializzato")
        print(f"   - Detect circular inference: {self.config.detect_circular_inference}")
        print(f"   - Validate operator application: {self.config.validate_operator_application}")
        print(f"   - Check contradiction handling: {self.config.check_contradiction_handling}")
        print(f"   - Inference rules loaded: {len(self.inference_rules)}")
    
    def validate_logic(self, graph: ReasoningGraph) -> List[ValidationError]:
        """
        Valida logica del ReasoningGraph.
        
        Args:
            graph: Grafo di reasoning da validare
            
        Returns:
            Lista di errori logici
        """
        self.errors = []
        start_time = time.time()
        
        try:
            # Valida applicazione operatori
            if self.config.validate_operator_application:
                self._validate_operator_application(graph)
            
            # Rileva inferenze circolari
            if self.config.detect_circular_inference:
                self._detect_circular_inference(graph)
            
            # Valida gestione contraddizioni
            if self.config.check_contradiction_handling:
                self._validate_contradiction_handling(graph)
            
            # Valida confidence propagation
            self._validate_confidence_propagation(graph)
            
            # Valida depth consistency
            self._validate_depth_consistency(graph)
            
        except Exception as e:
            error = ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.CRITICAL,
                level=ValidationLevel.LOGIC,
                message=f"Logic validation failed: {str(e)}",
                context={"exception": str(e)}
            )
            self.errors.append(error)
        
        validation_time = time.time() - start_time
        
        print(f"🔍 Logic validation completed in {validation_time:.3f}s")
        print(f"   - Errors found: {len(self.errors)}")
        
        return self.errors
    
    def _validate_operator_application(self, graph: ReasoningGraph):
        """Valida applicazione corretta degli operatori logici."""
        for path in graph.paths:
            for step in path.steps:
                rule_name = step.rule_name
                operator = step.operation
                
                # Verifica che la regola sia supportata
                if rule_name not in self.inference_rules:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                        severity=ValidationSeverity.ERROR,
                        level=ValidationLevel.LOGIC,
                        message=f"Unknown inference rule: {rule_name}",
                        step_id=step.step_id,
                        path_id=path.path_id,
                        context={"rule_name": rule_name}
                    ))
                    continue
                
                # Valida applicazione specifica per regola
                if rule_name == "modus_ponens":
                    self._validate_modus_ponens(step, path)
                elif rule_name == "modus_tollens":
                    self._validate_modus_tollens(step, path)
                elif rule_name == "conjunction_introduction":
                    self._validate_conjunction_introduction(step, path)
                elif rule_name == "conjunction_elimination":
                    self._validate_conjunction_elimination(step, path)
                elif rule_name == "contradiction_detection":
                    self._validate_contradiction_detection(step, path)
    
    def _validate_modus_ponens(self, step: ReasoningStep, path: ReasoningPath):
        """Valida applicazione Modus Ponens: A, A⇒B → B."""
        if len(step.input_facts) != 2:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Modus Ponens requires exactly 2 input facts, got {len(step.input_facts)}",
                step_id=step.step_id,
                path_id=path.path_id
            ))
            return
        
        fact1, fact2 = step.input_facts
        
        # Identifica quale è l'implicazione
        implication = None
        antecedent_fact = None
        
        if "⇒" in fact1.statement or " implies " in fact1.statement.lower():
            implication = fact1
            antecedent_fact = fact2
        elif "⇒" in fact2.statement or " implies " in fact2.statement.lower():
            implication = fact2
            antecedent_fact = fact1
        else:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message="Modus Ponens requires one implication (A⇒B)",
                step_id=step.step_id,
                path_id=path.path_id,
                context={"fact1": fact1.statement, "fact2": fact2.statement}
            ))
            return
        
        # Estrai antecedente dall'implicazione
        if "⇒" in implication.statement:
            antecedent_in_impl = implication.statement.split("⇒")[0].strip()
        else:
            antecedent_in_impl = implication.statement.lower().split(" implies ")[0].strip()
        
        # Verifica che l'antecedente corrisponda
        if antecedent_fact.statement.strip() != antecedent_in_impl:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Modus Ponens antecedent mismatch: '{antecedent_fact.statement}' vs '{antecedent_in_impl}'",
                step_id=step.step_id,
                path_id=path.path_id,
                context={
                    "antecedent_fact": antecedent_fact.statement,
                    "antecedent_in_implication": antecedent_in_impl
                }
            ))
    
    def _validate_modus_tollens(self, step: ReasoningStep, path: ReasoningPath):
        """Valida applicazione Modus Tollens: A⇒B, ¬B → ¬A."""
        if len(step.input_facts) != 2:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Modus Tollens requires exactly 2 input facts, got {len(step.input_facts)}",
                step_id=step.step_id,
                path_id=path.path_id
            ))
            return
        
        fact1, fact2 = step.input_facts
        
        # Identifica implicazione e negazione del conseguente
        implication = None
        negated_consequent = None
        
        if "⇒" in fact1.statement:
            implication = fact1
            negated_consequent = fact2
        elif "⇒" in fact2.statement:
            implication = fact2
            negated_consequent = fact1
        else:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message="Modus Tollens requires one implication (A⇒B)",
                step_id=step.step_id,
                path_id=path.path_id
            ))
            return
        
        # Verifica che il secondo fatto sia una negazione
        if not (negated_consequent.statement.startswith("¬") or 
                negated_consequent.truth_value == False):
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message="Modus Tollens requires negation of consequent (¬B)",
                step_id=step.step_id,
                path_id=path.path_id,
                context={"negated_fact": negated_consequent.statement}
            ))
    
    def _validate_conjunction_introduction(self, step: ReasoningStep, path: ReasoningPath):
        """Valida introduzione congiunzione: A, B → A∧B."""
        if len(step.input_facts) != 2:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Conjunction introduction requires exactly 2 input facts, got {len(step.input_facts)}",
                step_id=step.step_id,
                path_id=path.path_id
            ))
            return
        
        # Verifica che entrambi i fatti siano veri
        for fact in step.input_facts:
            if fact.truth_value == False:
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                    severity=ValidationSeverity.WARNING,
                    level=ValidationLevel.LOGIC,
                    message="Conjunction introduction with false fact",
                    step_id=step.step_id,
                    path_id=path.path_id,
                    context={"false_fact": fact.statement}
                ))
        
        # Verifica output
        if len(step.output_facts) != 1:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Conjunction introduction should produce exactly 1 output fact, got {len(step.output_facts)}",
                step_id=step.step_id,
                path_id=path.path_id
            ))
            return
        
        output_fact = step.output_facts[0]
        if "∧" not in output_fact.statement:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message="Conjunction introduction output should contain ∧",
                step_id=step.step_id,
                path_id=path.path_id,
                context={"output": output_fact.statement}
            ))
    
    def _validate_conjunction_elimination(self, step: ReasoningStep, path: ReasoningPath):
        """Valida eliminazione congiunzione: A∧B → A, B."""
        if len(step.input_facts) != 1:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Conjunction elimination requires exactly 1 input fact, got {len(step.input_facts)}",
                step_id=step.step_id,
                path_id=path.path_id
            ))
            return
        
        input_fact = step.input_facts[0]
        if "∧" not in input_fact.statement:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message="Conjunction elimination requires conjunction (A∧B)",
                step_id=step.step_id,
                path_id=path.path_id,
                context={"input": input_fact.statement}
            ))
            return
        
        # Verifica output
        if len(step.output_facts) != 2:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Conjunction elimination should produce exactly 2 output facts, got {len(step.output_facts)}",
                step_id=step.step_id,
                path_id=path.path_id
            ))
    
    def _validate_contradiction_detection(self, step: ReasoningStep, path: ReasoningPath):
        """Valida rilevamento contraddizioni: A, ¬A → ⊥."""
        if len(step.input_facts) != 2:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message=f"Contradiction detection requires exactly 2 input facts, got {len(step.input_facts)}",
                step_id=step.step_id,
                path_id=path.path_id
            ))
            return
        
        fact1, fact2 = step.input_facts
        
        # Verifica che i fatti si contraddicano
        if not fact1.contradicts(fact2):
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.INVALID_OPERATOR_APPLICATION,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.LOGIC,
                message="Contradiction detection applied to non-contradictory facts",
                step_id=step.step_id,
                path_id=path.path_id,
                context={"fact1": fact1.statement, "fact2": fact2.statement}
            ))
    
    def _detect_circular_inference(self, graph: ReasoningGraph):
        """Rileva inferenze circolari nel grafo."""
        # Costruisci grafo delle dipendenze
        dependencies = {}  # fact_id -> set of parent_fact_ids
        
        for fact in graph.all_facts:
            dependencies[fact.fact_id] = set(fact.derived_from)
        
        # Rileva cicli usando DFS
        visited = set()
        rec_stack = set()
        
        def has_cycle(node_id: str, path: List[str]) -> bool:
            if node_id in rec_stack:
                # Trovato ciclo
                cycle_start = path.index(node_id)
                cycle = path[cycle_start:] + [node_id]
                
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.CIRCULAR_INFERENCE,
                    severity=ValidationSeverity.CRITICAL,
                    level=ValidationLevel.LOGIC,
                    message=f"Circular inference detected: {' -> '.join(cycle)}",
                    node_id=node_id,
                    context={"cycle": cycle}
                ))
                return True
            
            if node_id in visited:
                return False
            
            visited.add(node_id)
            rec_stack.add(node_id)
            
            # Visita dipendenze
            for parent_id in dependencies.get(node_id, set()):
                if has_cycle(parent_id, path + [node_id]):
                    return True
            
            rec_stack.remove(node_id)
            return False
        
        # Controlla ogni nodo
        for fact_id in dependencies:
            if fact_id not in visited:
                has_cycle(fact_id, [])
    
    def _validate_contradiction_handling(self, graph: ReasoningGraph):
        """Valida gestione delle contraddizioni."""
        # Verifica che le contraddizioni rilevate siano gestite
        for fact1_id, fact2_id in graph.contradictions:
            # Cerca se esiste un step che gestisce questa contraddizione
            handled = False
            
            for path in graph.paths:
                for step in path.steps:
                    if (step.rule_name == "contradiction_detection" and
                        len(step.input_facts) == 2):
                        input_ids = {fact.fact_id for fact in step.input_facts}
                        if {fact1_id, fact2_id} == input_ids:
                            handled = True
                            break
                if handled:
                    break
            
            if not handled:
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.UNHANDLED_CONTRADICTION,
                    severity=ValidationSeverity.WARNING,
                    level=ValidationLevel.LOGIC,
                    message=f"Unhandled contradiction between facts: {fact1_id}, {fact2_id}",
                    context={"fact1_id": fact1_id, "fact2_id": fact2_id},
                    suggestions=["Add contradiction detection step"]
                ))
    
    def _validate_confidence_propagation(self, graph: ReasoningGraph):
        """Valida propagazione della confidence."""
        for path in graph.paths:
            for step in path.steps:
                if step.input_facts and step.output_facts:
                    # Confidence output non dovrebbe essere maggiore del minimo input
                    min_input_confidence = min(fact.confidence for fact in step.input_facts)
                    
                    for output_fact in step.output_facts:
                        if output_fact.confidence > min_input_confidence:
                            self.errors.append(ValidationError(
                                error_type=ValidationErrorType.CONFIDENCE_UNDERFLOW,
                                severity=ValidationSeverity.WARNING,
                                level=ValidationLevel.LOGIC,
                                message=f"Output confidence ({output_fact.confidence:.3f}) exceeds minimum input confidence ({min_input_confidence:.3f})",
                                step_id=step.step_id,
                                node_id=output_fact.fact_id,
                                context={
                                    "output_confidence": output_fact.confidence,
                                    "min_input_confidence": min_input_confidence
                                }
                            ))
                        
                        # Verifica soglia minima
                        if output_fact.confidence < self.config.min_confidence_threshold:
                            self.errors.append(ValidationError(
                                error_type=ValidationErrorType.CONFIDENCE_UNDERFLOW,
                                severity=ValidationSeverity.WARNING,
                                level=ValidationLevel.LOGIC,
                                message=f"Output confidence below threshold: {output_fact.confidence:.3f} < {self.config.min_confidence_threshold}",
                                step_id=step.step_id,
                                node_id=output_fact.fact_id,
                                context={
                                    "confidence": output_fact.confidence,
                                    "threshold": self.config.min_confidence_threshold
                                }
                            ))
    
    def _validate_depth_consistency(self, graph: ReasoningGraph):
        """Valida consistenza della profondità."""
        for path in graph.paths:
            if path.depth > self.config.max_depth_allowed:
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.EXCESSIVE_DEPTH,
                    severity=ValidationSeverity.WARNING,
                    level=ValidationLevel.LOGIC,
                    message=f"Path depth ({path.depth}) exceeds maximum allowed ({self.config.max_depth_allowed})",
                    path_id=path.path_id,
                    context={
                        "path_depth": path.depth,
                        "max_allowed": self.config.max_depth_allowed
                    }
                ))
    
    def _load_inference_rules(self) -> Dict[str, str]:
        """Carica regole di inferenza supportate."""
        return {
            "modus_ponens": "A, A⇒B → B",
            "modus_tollens": "A⇒B, ¬B → ¬A", 
            "conjunction_introduction": "A, B → A∧B",
            "conjunction_elimination": "A∧B → A, B",
            "contradiction_detection": "A, ¬A → ⊥",
            "analogical_reasoning": "Pattern matching",
            "disjunctive_syllogism": "A∨B, ¬A → B",
            "hypothetical_syllogism": "A⇒B, B⇒C → A⇒C"
        }
