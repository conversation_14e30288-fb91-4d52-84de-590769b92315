"""
NEUROGLYPH Security Monitor
Monitor di sicurezza per sandbox execution
"""

import ast
import sys
import time
import threading
from typing import Dict, List, Optional, Any, Set
from contextlib import contextmanager

from .sandbox_structures import (
    SecurityViolation, SandboxConfig, SecurityLevel,
    ResourceLimits, ResourceUsage
)


class SecurityMonitor:
    """
    Monitor di sicurezza per l'esecuzione del codice.
    Rileva e blocca operazioni pericolose.
    """
    
    def __init__(self, config: Optional[SandboxConfig] = None):
        """
        Inizializza il monitor di sicurezza.
        
        Args:
            config: Configurazione sandbox
        """
        self.config = config or SandboxConfig()
        self.violations = []
        self.resource_usage = ResourceUsage()
        self.start_time = None
        self.monitoring = False
        
        print("🔒 SecurityMonitor inizializzato")
        print(f"   - Security level: {self.config.default_security_level}")
        print(f"   - Allowed modules: {len(self.config.allowed_modules)}")
        print(f"   - Blocked modules: {len(self.config.blocked_modules)}")
    
    def analyze_code_security(self, code: str, security_level: SecurityLevel) -> List[SecurityViolation]:
        """
        Analizza il codice per rilevare potenziali problemi di sicurezza.
        
        Args:
            code: Codice da analizzare
            security_level: Livello di sicurezza
            
        Returns:
            Lista di violazioni rilevate
        """
        violations = []
        
        try:
            # Parse AST
            tree = ast.parse(code)
            
            # Analizza nodi AST
            for node in ast.walk(tree):
                node_violations = self._analyze_ast_node(node, security_level)
                violations.extend(node_violations)
            
        except SyntaxError as e:
            violations.append(SecurityViolation(
                violation_type="syntax_error",
                severity="medium",
                message=f"Syntax error in code: {str(e)}",
                line_number=e.lineno,
                code_line=e.text
            ))
        
        return violations
    
    def _analyze_ast_node(self, node: ast.AST, security_level: SecurityLevel) -> List[SecurityViolation]:
        """Analizza un singolo nodo AST."""
        violations = []
        
        # Import statements
        if isinstance(node, (ast.Import, ast.ImportFrom)):
            violations.extend(self._check_import_security(node, security_level))
        
        # Function calls
        elif isinstance(node, ast.Call):
            violations.extend(self._check_call_security(node, security_level))
        
        # Attribute access
        elif isinstance(node, ast.Attribute):
            violations.extend(self._check_attribute_security(node, security_level))
        
        # Name access
        elif isinstance(node, ast.Name):
            violations.extend(self._check_name_security(node, security_level))
        
        # Exec/eval
        elif isinstance(node, ast.Expr) and isinstance(node.value, ast.Call):
            if isinstance(node.value.func, ast.Name):
                if node.value.func.id in ['exec', 'eval', 'compile']:
                    violations.append(SecurityViolation(
                        violation_type="dangerous_builtin",
                        severity="critical",
                        message=f"Use of dangerous builtin: {node.value.func.id}",
                        operation=node.value.func.id,
                        line_number=getattr(node, 'lineno', None)
                    ))
        
        return violations
    
    def _check_import_security(self, node: ast.AST, security_level: SecurityLevel) -> List[SecurityViolation]:
        """Verifica sicurezza degli import."""
        violations = []
        
        if isinstance(node, ast.Import):
            for alias in node.names:
                module_name = alias.name
                if self._is_module_blocked(module_name, security_level):
                    violations.append(SecurityViolation(
                        violation_type="blocked_import",
                        severity="high",
                        message=f"Import of blocked module: {module_name}",
                        operation=f"import {module_name}",
                        line_number=getattr(node, 'lineno', None)
                    ))
        
        elif isinstance(node, ast.ImportFrom):
            module_name = node.module or ""
            if self._is_module_blocked(module_name, security_level):
                violations.append(SecurityViolation(
                    violation_type="blocked_import",
                    severity="high",
                    message=f"Import from blocked module: {module_name}",
                    operation=f"from {module_name} import ...",
                    line_number=getattr(node, 'lineno', None)
                ))
        
        return violations
    
    def _check_call_security(self, node: ast.Call, security_level: SecurityLevel) -> List[SecurityViolation]:
        """Verifica sicurezza delle chiamate a funzione."""
        violations = []
        
        # Identifica nome funzione
        func_name = None
        if isinstance(node.func, ast.Name):
            func_name = node.func.id
        elif isinstance(node.func, ast.Attribute):
            func_name = node.func.attr
        
        if func_name:
            # Verifica builtin bloccati
            if func_name in self.config.blocked_builtins:
                violations.append(SecurityViolation(
                    violation_type="blocked_builtin",
                    severity="high",
                    message=f"Call to blocked builtin: {func_name}",
                    operation=func_name,
                    line_number=getattr(node, 'lineno', None)
                ))
            
            # Verifica operazioni pericolose
            dangerous_functions = {
                'open': 'file_access',
                'input': 'user_input',
                'print': 'output' if security_level == SecurityLevel.MAXIMUM else None,
                '__import__': 'dynamic_import',
                'getattr': 'reflection',
                'setattr': 'reflection',
                'delattr': 'reflection'
            }
            
            if func_name in dangerous_functions:
                violation_type = dangerous_functions[func_name]
                if violation_type:  # None significa consentito
                    severity = "critical" if func_name in ['__import__', 'exec', 'eval'] else "medium"
                    violations.append(SecurityViolation(
                        violation_type=violation_type,
                        severity=severity,
                        message=f"Call to potentially dangerous function: {func_name}",
                        operation=func_name,
                        line_number=getattr(node, 'lineno', None)
                    ))
        
        return violations
    
    def _check_attribute_security(self, node: ast.Attribute, security_level: SecurityLevel) -> List[SecurityViolation]:
        """Verifica sicurezza dell'accesso agli attributi."""
        violations = []
        
        # Attributi pericolosi
        dangerous_attrs = {
            '__globals__': 'globals_access',
            '__locals__': 'locals_access',
            '__dict__': 'dict_access',
            '__class__': 'class_access',
            '__bases__': 'inheritance_access',
            '__subclasses__': 'subclass_access'
        }
        
        if node.attr in dangerous_attrs:
            violations.append(SecurityViolation(
                violation_type=dangerous_attrs[node.attr],
                severity="high",
                message=f"Access to dangerous attribute: {node.attr}",
                operation=f"obj.{node.attr}",
                line_number=getattr(node, 'lineno', None)
            ))
        
        return violations
    
    def _check_name_security(self, node: ast.Name, security_level: SecurityLevel) -> List[SecurityViolation]:
        """Verifica sicurezza dell'accesso ai nomi."""
        violations = []
        
        # Nomi pericolosi
        dangerous_names = {
            '__builtins__': 'builtins_access',
            '__import__': 'import_access',
            'globals': 'globals_function',
            'locals': 'locals_function',
            'vars': 'vars_function'
        }
        
        if isinstance(node.ctx, ast.Load) and node.id in dangerous_names:
            violations.append(SecurityViolation(
                violation_type=dangerous_names[node.id],
                severity="high",
                message=f"Access to dangerous name: {node.id}",
                operation=node.id,
                line_number=getattr(node, 'lineno', None)
            ))
        
        return violations
    
    def _is_module_blocked(self, module_name: str, security_level: SecurityLevel) -> bool:
        """Verifica se un modulo è bloccato."""
        # Moduli sempre bloccati
        if module_name in self.config.blocked_modules:
            return True
        
        # Moduli consentiti esplicitamente
        if module_name in self.config.allowed_modules:
            return False
        
        # Logica basata su security level
        if security_level == SecurityLevel.MAXIMUM:
            # Solo moduli esplicitamente consentiti
            return module_name not in self.config.allowed_modules
        elif security_level == SecurityLevel.HIGH:
            # Blocca moduli potenzialmente pericolosi
            dangerous_patterns = ['os', 'sys', 'subprocess', 'socket', 'urllib']
            return any(pattern in module_name for pattern in dangerous_patterns)
        
        return False
    
    def create_safe_globals(self, security_level: SecurityLevel) -> Dict[str, Any]:
        """Crea dizionario globals sicuro."""
        safe_globals = {}
        
        # Builtin sicuri
        import builtins
        for name in self.config.allowed_builtins:
            if hasattr(builtins, name):
                safe_globals[name] = getattr(builtins, name)
        
        # Moduli sicuri
        for module_name in self.config.allowed_modules:
            if not self._is_module_blocked(module_name, security_level):
                try:
                    module = __import__(module_name)
                    safe_globals[module_name] = module
                except ImportError:
                    pass
        
        # Aggiungi __builtins__ limitato
        safe_builtins = {}
        for name in self.config.allowed_builtins:
            if hasattr(builtins, name):
                safe_builtins[name] = getattr(builtins, name)
        
        safe_globals['__builtins__'] = safe_builtins
        
        return safe_globals
    
    @contextmanager
    def monitor_execution(self, limits: ResourceLimits):
        """Context manager per monitorare l'esecuzione."""
        self.start_time = time.time()
        self.monitoring = True
        self.resource_usage = ResourceUsage()
        
        # Setup monitoring thread
        monitor_thread = threading.Thread(
            target=self._monitor_resources,
            args=(limits,),
            daemon=True
        )
        monitor_thread.start()
        
        try:
            yield self
        finally:
            self.monitoring = False
            if self.start_time:
                self.resource_usage.execution_time = time.time() - self.start_time
    
    def _monitor_resources(self, limits: ResourceLimits):
        """Monitora l'utilizzo delle risorse."""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
        except ImportError:
            # psutil non disponibile, usa monitoring semplificato
            process = None
        
        while self.monitoring:
            try:
                if process:
                    # Memoria
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    self.resource_usage.peak_memory_mb = max(
                        self.resource_usage.peak_memory_mb, memory_mb
                    )

                    # CPU time
                    cpu_times = process.cpu_times()
                    self.resource_usage.cpu_time = cpu_times.user + cpu_times.system
                
                # Verifica limiti
                if self.start_time:
                    elapsed = time.time() - self.start_time
                    if elapsed > limits.max_execution_time:
                        self.violations.append(SecurityViolation(
                            violation_type="timeout",
                            severity="critical",
                            message=f"Execution timeout: {elapsed:.2f}s > {limits.max_execution_time}s"
                        ))
                        break

                if process:
                    memory_mb = self.resource_usage.peak_memory_mb
                    if memory_mb > limits.max_memory_mb:
                        self.violations.append(SecurityViolation(
                            violation_type="memory_limit",
                            severity="critical",
                            message=f"Memory limit exceeded: {memory_mb:.1f}MB > {limits.max_memory_mb}MB"
                        ))
                        break
                
                time.sleep(0.1)  # Check every 100ms

            except Exception:
                # Ignore monitoring errors (including psutil errors)
                if process is None:
                    # Se non abbiamo psutil, monitora solo il tempo
                    if self.start_time:
                        elapsed = time.time() - self.start_time
                        if elapsed > limits.max_execution_time:
                            break
                pass
    
    def add_violation(self, violation: SecurityViolation):
        """Aggiunge una violazione di sicurezza."""
        self.violations.append(violation)
    
    def get_violations(self) -> List[SecurityViolation]:
        """Ottiene tutte le violazioni rilevate."""
        return self.violations.copy()
    
    def has_critical_violations(self) -> bool:
        """Verifica se ci sono violazioni critiche."""
        return any(v.severity == "critical" for v in self.violations)
    
    def clear_violations(self):
        """Pulisce le violazioni."""
        self.violations.clear()
