"""
NEUROGLYPH Error Cache
Cache LRU con TTL per risultati patch idempotenti
"""

import time
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import OrderedDict

from .patcher_structures import PatchResult


@dataclass
class CacheEntry:
    """Entry della cache con TTL."""
    
    value: List[PatchResult]
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)
    
    def is_expired(self, ttl_seconds: float) -> bool:
        """Verifica se l'entry è scaduta."""
        return (time.time() - self.timestamp) > ttl_seconds
    
    def touch(self):
        """Aggiorna timestamp di accesso."""
        self.last_access = time.time()
        self.access_count += 1


class ErrorCache:
    """
    Cache LRU con TTL per risultati patch.
    
    Ottimizzazioni:
    - LRU per gestione memoria
    - TTL per invalidazione automatica
    - Thread-safe per accesso concorrente
    - Metriche per monitoring
    """
    
    def __init__(self, max_size: int = 2000, ttl_seconds: float = 30.0):
        """
        Inizializza cache.
        
        Args:
            max_size: Dimensione massima cache
            ttl_seconds: Time-to-live in secondi
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        
        # Cache storage (thread-safe OrderedDict)
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        
        # Metriche
        self.hits = 0
        self.misses = 0
        self.evictions = 0
        self.expired_entries = 0
        
        # Cleanup automatico
        self._last_cleanup = time.time()
        self._cleanup_interval = 60.0  # Cleanup ogni 60s
    
    def get(self, digest: str) -> Optional[List[PatchResult]]:
        """
        Recupera risultati dalla cache.
        
        Args:
            digest: Digest del ValidationResult
            
        Returns:
            Lista PatchResult o None se non trovato/scaduto
        """
        with self._lock:
            # Cleanup periodico
            self._maybe_cleanup()
            
            entry = self._cache.get(digest)
            if entry is None:
                self.misses += 1
                return None
            
            # Verifica TTL
            if entry.is_expired(self.ttl_seconds):
                del self._cache[digest]
                self.expired_entries += 1
                self.misses += 1
                return None
            
            # Hit - sposta in fondo (LRU)
            entry.touch()
            self._cache.move_to_end(digest)
            self.hits += 1
            
            return entry.value
    
    def put(self, digest: str, results: List[PatchResult]):
        """
        Inserisce risultati in cache.
        
        Args:
            digest: Digest del ValidationResult
            results: Lista PatchResult da cachare
        """
        with self._lock:
            # Cleanup se necessario
            self._maybe_cleanup()
            
            # Crea entry
            entry = CacheEntry(
                value=results,
                timestamp=time.time()
            )
            
            # Inserisci/aggiorna
            if digest in self._cache:
                del self._cache[digest]
            
            self._cache[digest] = entry
            self._cache.move_to_end(digest)
            
            # Evict se necessario
            while len(self._cache) > self.max_size:
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]
                self.evictions += 1
    
    def invalidate(self, digest: str):
        """Invalida entry specifica."""
        with self._lock:
            if digest in self._cache:
                del self._cache[digest]
    
    def clear(self):
        """Svuota cache."""
        with self._lock:
            self._cache.clear()
            self.hits = 0
            self.misses = 0
            self.evictions = 0
            self.expired_entries = 0
    
    def _maybe_cleanup(self):
        """Cleanup periodico delle entry scadute."""
        now = time.time()
        if (now - self._last_cleanup) < self._cleanup_interval:
            return
        
        self._last_cleanup = now
        expired_keys = []
        
        for key, entry in self._cache.items():
            if entry.is_expired(self.ttl_seconds):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            self.expired_entries += 1
    
    @property
    def hit_rate(self) -> float:
        """Calcola hit rate."""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def size(self) -> int:
        """Dimensione corrente cache."""
        return len(self._cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """Statistiche cache."""
        return {
            'size': self.size,
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': self.hit_rate,
            'evictions': self.evictions,
            'expired_entries': self.expired_entries,
            'ttl_seconds': self.ttl_seconds
        }


# Cache globale singleton
_global_cache: Optional[ErrorCache] = None


def get_error_cache() -> ErrorCache:
    """Ottieni cache globale (singleton)."""
    global _global_cache
    if _global_cache is None:
        _global_cache = ErrorCache()
    return _global_cache


def reset_error_cache():
    """Reset cache globale (per testing)."""
    global _global_cache
    _global_cache = None
