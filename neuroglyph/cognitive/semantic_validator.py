"""
NEUROGLYPH Semantic Validator
Validatore semantico per ReasoningGraph e logical consistency
"""

import time
from typing import List, Optional, Dict, Any, Set

from .validation_structures import (
    ValidationError, ValidationErrorType, ValidationSeverity, 
    ValidationLevel, ValidationConfig
)
from .reasoning_structures import (
    ReasoningGraph, LogicalFact, ReasoningStep, FactType
)


class SemanticValidator:
    """
    Validatore semantico per NEUROGLYPH.
    Verifica coerenza semantica e giustificazione dei fatti derivati.
    """
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        """
        Inizializza il validatore semantico.
        
        Args:
            config: Configurazione validazione
        """
        self.config = config or ValidationConfig()
        self.errors = []
        
        print("🔍 SemanticValidator inizializzato")
        print(f"   - Check premise existence: {self.config.check_premise_existence}")
        print(f"   - Validate fact justification: {self.config.validate_fact_justification}")
        print(f"   - Check type consistency: {self.config.check_type_consistency}")
    
    def validate_semantic(self, graph: ReasoningGraph) -> List[ValidationError]:
        """
        Valida semantica del ReasoningGraph.
        
        Args:
            graph: Grafo di reasoning da validare
            
        Returns:
            Lista di errori semantici
        """
        self.errors = []
        start_time = time.time()
        
        try:
            # Valida esistenza premesse
            if self.config.check_premise_existence:
                self._validate_premise_existence(graph)
            
            # Valida giustificazione fatti
            if self.config.validate_fact_justification:
                self._validate_fact_justification(graph)
            
            # Valida consistenza tipi
            if self.config.check_type_consistency:
                self._validate_type_consistency(graph)
            
            # Valida riferimenti simbolici
            self._validate_symbolic_references(graph)
            
            # Valida coerenza predicati
            self._validate_predicate_coherence(graph)
            
        except Exception as e:
            error = ValidationError(
                error_type=ValidationErrorType.TYPE_MISMATCH,
                severity=ValidationSeverity.CRITICAL,
                level=ValidationLevel.SEMANTIC,
                message=f"Semantic validation failed: {str(e)}",
                context={"exception": str(e)}
            )
            self.errors.append(error)
        
        validation_time = time.time() - start_time
        
        print(f"🔍 Semantic validation completed in {validation_time:.3f}s")
        print(f"   - Errors found: {len(self.errors)}")
        
        return self.errors
    
    def _validate_premise_existence(self, graph: ReasoningGraph):
        """Valida che tutte le premesse esistano."""
        # Crea mappa ID -> fatto per lookup veloce
        fact_map = {fact.fact_id: fact for fact in graph.all_facts}
        
        # Verifica ogni fatto derivato
        for fact in graph.all_facts:
            if fact.fact_type == FactType.DERIVED:
                # Verifica che tutti i parent esistano
                for parent_id in fact.derived_from:
                    if parent_id not in fact_map:
                        self.errors.append(ValidationError(
                            error_type=ValidationErrorType.MISSING_PREMISES,
                            severity=ValidationSeverity.CRITICAL,
                            level=ValidationLevel.SEMANTIC,
                            message=f"Derived fact references non-existent parent: {parent_id}",
                            node_id=fact.fact_id,
                            context={
                                "missing_parent": parent_id,
                                "derived_fact": fact.statement
                            },
                            suggestions=["Ensure all parent facts exist before derivation"]
                        ))
                
                # Verifica che abbia almeno un parent (eccetto assunzioni)
                if not fact.derived_from and fact.fact_type != FactType.ASSUMPTION:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.UNJUSTIFIED_FACTS,
                        severity=ValidationSeverity.ERROR,
                        level=ValidationLevel.SEMANTIC,
                        message="Derived fact has no parent facts",
                        node_id=fact.fact_id,
                        context={"fact": fact.statement},
                        suggestions=["Add derivation source or change fact type"]
                    ))
    
    def _validate_fact_justification(self, graph: ReasoningGraph):
        """Valida giustificazione dei fatti derivati."""
        # Verifica ogni step di reasoning
        for path in graph.paths:
            for step in path.steps:
                # Verifica che input facts esistano
                input_fact_ids = {fact.fact_id for fact in step.input_facts}
                available_fact_ids = {fact.fact_id for fact in path.current_facts}
                
                missing_inputs = input_fact_ids - available_fact_ids
                if missing_inputs:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.MISSING_PREMISES,
                        severity=ValidationSeverity.ERROR,
                        level=ValidationLevel.SEMANTIC,
                        message=f"ReasoningStep references unavailable input facts: {missing_inputs}",
                        step_id=step.step_id,
                        path_id=path.path_id,
                        context={"missing_inputs": list(missing_inputs)}
                    ))
                
                # Verifica che output facts siano giustificati
                for output_fact in step.output_facts:
                    if output_fact.derivation_rule != step.rule_name:
                        self.errors.append(ValidationError(
                            error_type=ValidationErrorType.UNJUSTIFIED_FACTS,
                            severity=ValidationSeverity.WARNING,
                            level=ValidationLevel.SEMANTIC,
                            message=f"Output fact derivation rule mismatch: {output_fact.derivation_rule} vs {step.rule_name}",
                            step_id=step.step_id,
                            node_id=output_fact.fact_id,
                            context={
                                "fact_rule": output_fact.derivation_rule,
                                "step_rule": step.rule_name
                            }
                        ))
                    
                    # Verifica che derived_from corrisponda agli input
                    expected_parents = {fact.fact_id for fact in step.input_facts}
                    actual_parents = set(output_fact.derived_from)
                    
                    if expected_parents != actual_parents:
                        self.errors.append(ValidationError(
                            error_type=ValidationErrorType.UNJUSTIFIED_FACTS,
                            severity=ValidationSeverity.WARNING,
                            level=ValidationLevel.SEMANTIC,
                            message="Output fact parent references don't match step inputs",
                            step_id=step.step_id,
                            node_id=output_fact.fact_id,
                            context={
                                "expected_parents": list(expected_parents),
                                "actual_parents": list(actual_parents)
                            }
                        ))
    
    def _validate_type_consistency(self, graph: ReasoningGraph):
        """Valida consistenza dei tipi di fatti."""
        # Verifica transizioni di tipo valide
        valid_transitions = {
            FactType.PREMISE: {FactType.DERIVED, FactType.CONCLUSION},
            FactType.ASSUMPTION: {FactType.DERIVED, FactType.HYPOTHESIS},
            FactType.OBSERVATION: {FactType.DERIVED, FactType.HYPOTHESIS},
            FactType.DERIVED: {FactType.DERIVED, FactType.CONCLUSION},
            FactType.HYPOTHESIS: {FactType.DERIVED, FactType.CONCLUSION, FactType.CONTRADICTION}
        }
        
        # Crea mappa ID -> fatto
        fact_map = {fact.fact_id: fact for fact in graph.all_facts}
        
        for fact in graph.all_facts:
            if fact.fact_type == FactType.DERIVED:
                # Verifica che i parent abbiano tipi compatibili
                for parent_id in fact.derived_from:
                    if parent_id in fact_map:
                        parent_fact = fact_map[parent_id]
                        
                        if (parent_fact.fact_type in valid_transitions and
                            fact.fact_type not in valid_transitions[parent_fact.fact_type]):
                            
                            self.errors.append(ValidationError(
                                error_type=ValidationErrorType.TYPE_MISMATCH,
                                severity=ValidationSeverity.WARNING,
                                level=ValidationLevel.SEMANTIC,
                                message=f"Invalid fact type transition: {parent_fact.fact_type} -> {fact.fact_type}",
                                node_id=fact.fact_id,
                                context={
                                    "parent_type": parent_fact.fact_type.value,
                                    "child_type": fact.fact_type.value,
                                    "parent_id": parent_id
                                }
                            ))
    
    def _validate_symbolic_references(self, graph: ReasoningGraph):
        """Valida riferimenti simbolici nei fatti."""
        # Estrai tutti i simboli referenziati
        referenced_symbols = set()
        
        for fact in graph.all_facts:
            # Cerca simboli NEUROGLYPH nel statement
            import re
            symbols = re.findall(r'[⟨⟩◊⟲⟐⎋⟡⌦⩺⊶]§\w+§', fact.statement)
            referenced_symbols.update(symbols)
            
            # Aggiungi simboli espliciti
            referenced_symbols.update(fact.arguments)
        
        # Verifica che i simboli siano validi (pattern NEUROGLYPH)
        for symbol in referenced_symbols:
            if not self._is_valid_neuroglyph_symbol(symbol):
                # Trova il fatto che contiene questo simbolo
                containing_facts = [
                    fact for fact in graph.all_facts 
                    if symbol in fact.statement or symbol in fact.arguments
                ]
                
                for fact in containing_facts:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.INVALID_REFERENCES,
                        severity=ValidationSeverity.WARNING,
                        level=ValidationLevel.SEMANTIC,
                        message=f"Invalid NEUROGLYPH symbol reference: {symbol}",
                        node_id=fact.fact_id,
                        context={"invalid_symbol": symbol},
                        suggestions=["Use valid NEUROGLYPH symbol format: ⟨⟩§name§"]
                    ))
    
    def _validate_predicate_coherence(self, graph: ReasoningGraph):
        """Valida coerenza dei predicati."""
        # Raggruppa fatti per predicato
        predicate_groups = {}
        
        for fact in graph.all_facts:
            predicate = fact.predicate
            if predicate not in predicate_groups:
                predicate_groups[predicate] = []
            predicate_groups[predicate].append(fact)
        
        # Verifica coerenza all'interno di ogni gruppo
        for predicate, facts in predicate_groups.items():
            if len(facts) > 1:
                # Verifica che fatti con stesso predicato non si contraddicano
                for i, fact1 in enumerate(facts):
                    for fact2 in facts[i+1:]:
                        if self._are_semantically_contradictory(fact1, fact2):
                            self.errors.append(ValidationError(
                                error_type=ValidationErrorType.INVALID_REFERENCES,
                                severity=ValidationSeverity.ERROR,
                                level=ValidationLevel.SEMANTIC,
                                message=f"Semantic contradiction in predicate '{predicate}': {fact1.statement} vs {fact2.statement}",
                                node_id=fact1.fact_id,
                                context={
                                    "predicate": predicate,
                                    "fact1": fact1.statement,
                                    "fact2": fact2.statement,
                                    "fact2_id": fact2.fact_id
                                }
                            ))
    
    def _is_valid_neuroglyph_symbol(self, symbol: str) -> bool:
        """Verifica se un simbolo è un simbolo NEUROGLYPH valido."""
        import re
        # Pattern per simboli NEUROGLYPH: simbolo§nome§
        pattern = r'^[⟨⟩◊⟲⟐⎋⟡⌦⩺⊶]§\w+§$'
        return bool(re.match(pattern, symbol))
    
    def _are_semantically_contradictory(self, fact1: LogicalFact, fact2: LogicalFact) -> bool:
        """Verifica se due fatti sono semanticamente contraddittori."""
        # Verifica contraddizione diretta
        if fact1.contradicts(fact2):
            return True
        
        # Verifica contraddizioni semantiche per predicati comuni
        semantic_opposites = {
            "secure": "insecure",
            "safe": "dangerous", 
            "valid": "invalid",
            "working": "broken",
            "exists": "missing",
            "true": "false",
            "possible": "impossible"
        }
        
        pred1 = fact1.predicate.lower()
        pred2 = fact2.predicate.lower()
        
        # Verifica se sono opposti semantici
        if (pred1 in semantic_opposites and semantic_opposites[pred1] == pred2) or \
           (pred2 in semantic_opposites and semantic_opposites[pred2] == pred1):
            # Verifica che abbiano stessi argomenti
            if fact1.arguments == fact2.arguments:
                return True
        
        return False
