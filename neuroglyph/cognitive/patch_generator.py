"""
NEUROGLYPH Patch Generator
Generatore di patch per auto-correzione
"""

import re
import ast
import time
from typing import List, Dict, Any, Optional, Tuple

from .patcher_structures import (
    ErrorAnalysis, PatchCandidate, PatchType, PatcherConfig,
    ErrorType, LearningPattern
)
from .performance_optimizer import PerformanceOptimizer
from ..monitoring import get_metrics_collector

# FASE 3: Mappatura esplicita ErrorPattern → Strategie
STRATEGY_MAP = {
    # Import Errors
    "import_error": [
        ("try_except_import", 0.9),
        ("add_import_stub", 0.7),
        ("comment_out_import", 0.6)
    ],

    # Syntax Errors
    "syntax_missing_colon": [
        ("fix_missing_colon", 0.9)
    ],
    "syntax_unclosed_paren": [
        ("fix_close_paren", 0.8)
    ],
    "syntax_indentation": [
        ("fix_indentation", 0.8)
    ],
    "syntax_invalid": [
        ("fix_syntax_general", 0.7)
    ],
    "syntax_general": [
        ("fix_syntax_general", 0.6)
    ],

    # <PERSON><PERSON><PERSON>rrors
    "undefined_variable": [
        ("add_name_stub", 0.8),
        ("wrap_try_except", 0.9),
        ("remove_offending_call", 0.6)
    ],
    "undefined_attribute": [
        ("safe_attribute_access", 0.8),
        ("wrap_try_except", 0.7)
    ],
    "type_mismatch": [
        ("type_conversion", 0.7),
        ("wrap_try_except", 0.6)
    ],

    # Runtime General
    "runtime_general": [
        ("try_except_import", 0.8),  # Molti runtime_general sono import errors
        ("wrap_try_except", 0.6),
        ("remove_offending_call", 0.5)
    ],

    # FASE 4.0.1: Pattern Aggiuntivi per Copertura Completa

    # Syntax Errors Estesi
    "syntax_unclosed_bracket": [
        ("fix_close_bracket", 0.9),
        ("fix_syntax_general", 0.7)
    ],
    "syntax_unclosed_quote": [
        ("fix_close_quote", 0.9),
        ("fix_syntax_general", 0.7)
    ],
    "syntax_unexpected_token": [
        ("fix_unexpected_token", 0.8),
        ("fix_syntax_general", 0.6)
    ],

    # Runtime Errors Estesi
    "undefined_function": [
        ("add_function_stub", 0.9),
        ("wrap_try_except", 0.8),
        ("remove_function_call", 0.7)
    ],
    "index_error": [
        ("add_bounds_check", 0.9),
        ("safe_list_access", 0.8),
        ("wrap_try_except", 0.7)
    ],
    "key_error": [
        ("safe_dict_access", 0.9),
        ("add_key_check", 0.8),
        ("wrap_try_except", 0.7)
    ],
    "value_error": [
        ("add_value_validation", 0.8),
        ("wrap_try_except", 0.7),
        ("use_default_value", 0.6)
    ],
    "zero_division_error": [
        ("add_division_check", 0.9),
        ("wrap_try_except", 0.8)
    ],
    "attribute_error": [
        ("safe_attribute_access", 0.9),
        ("add_attribute_check", 0.8),
        ("wrap_try_except", 0.7)
    ],

    # Performance Errors
    "timeout_error": [
        ("optimize_algorithm", 0.8),
        ("increase_timeout", 0.7),
        ("add_progress_tracking", 0.6)
    ],
    "memory_error": [
        ("optimize_memory_usage", 0.8),
        ("use_generators", 0.7),
        ("batch_processing", 0.6)
    ],
    "recursion_error": [
        ("add_recursion_limit", 0.9),
        ("convert_to_iterative", 0.8),
        ("optimize_recursion", 0.7)
    ],

    # Logic Errors
    "logical_contradiction": [
        ("resolve_contradiction", 0.7),
        ("add_logical_check", 0.6),
        ("simplify_logic", 0.5)
    ],
    "missing_premises": [
        ("add_missing_premises", 0.8),
        ("strengthen_assumptions", 0.7)
    ],
    "circular_reasoning": [
        ("break_circular_logic", 0.8),
        ("restructure_reasoning", 0.7)
    ],

    # Security Errors
    "security_vulnerability": [
        ("apply_security_patch", 0.9),
        ("sanitize_input", 0.8),
        ("add_validation", 0.7)
    ],
    "injection_attack": [
        ("sanitize_input", 0.9),
        ("use_parameterized_queries", 0.8),
        ("add_input_validation", 0.7)
    ],

    # Import Errors Estesi
    "circular_import": [
        ("break_circular_import", 0.8),
        ("lazy_import", 0.7),
        ("restructure_imports", 0.6)
    ],
    "missing_dependency": [
        ("install_dependency", 0.9),
        ("add_import_stub", 0.8),
        ("comment_out_import", 0.6)
    ],

    # Validation Errors
    "validation_error": [
        ("add_input_validation", 0.8),
        ("fix_validation_logic", 0.7),
        ("wrap_try_except", 0.6)
    ],
    "schema_mismatch": [
        ("fix_schema", 0.8),
        ("add_schema_validation", 0.7),
        ("use_flexible_schema", 0.6)
    ],

    # Fallback per pattern sconosciuti
    "default": [
        ("wrap_try_except", 0.5),
        ("remove_offending_call", 0.4),
        ("add_error_handling", 0.3)
    ]
}


class PatchGenerator:
    """
    Generatore di patch per NEUROGLYPH.
    Genera candidati patch basati su analisi errori e pattern appresi.
    """
    
    def __init__(self, config: Optional[PatcherConfig] = None):
        """
        Inizializza il generatore di patch.
        
        Args:
            config: Configurazione patcher
        """
        self.config = config or PatcherConfig()
        self.patch_strategies = self._load_patch_strategies()
        self.learned_patterns = []

        # FASE 4.0.2: Performance Optimizer
        self.performance_optimizer = PerformanceOptimizer()

        # FASE 4.0.3: Metrics Collector
        self.metrics_collector = get_metrics_collector()

        self._init_state()

        print("🔧 PatchGenerator inizializzato")
        print(f"   - Patch strategies: {len(self.patch_strategies)}")
        print(f"   - Max candidates: {self.config.max_patch_candidates}")
        print(f"   - Min confidence: {self.config.min_confidence_threshold}")

        # FASE 4.0.1: Statistiche STRATEGY_MAP estesa
        self._print_strategy_map_stats()

    def _init_state(self):
        """FASE 3: Inizializza stato interno per evitare caching tra test."""
        self._seen_error_patterns = set()
        self._generated_patches_cache = {}
        self._last_error_pattern = None
        self._patch_generation_count = 0
        print("🔄 PatchGenerator state initialized/reset")

    def reset_state(self):
        """FASE 3: Reset esplicito dello stato per evitare caching."""
        print("🧹 Resetting PatchGenerator state...")
        self._init_state()

    def _print_strategy_map_stats(self):
        """FASE 4.0.1: Stampa statistiche della STRATEGY_MAP estesa."""
        total_patterns = len(STRATEGY_MAP)
        total_strategies = sum(len(strategies) for strategies in STRATEGY_MAP.values())
        unique_strategies = len(set(strategy for strategies in STRATEGY_MAP.values()
                                  for strategy, _ in strategies))

        print(f"📊 STRATEGY_MAP Stats:")
        print(f"   - Total patterns: {total_patterns}")
        print(f"   - Total strategy mappings: {total_strategies}")
        print(f"   - Unique strategies: {unique_strategies}")
        print(f"   - Coverage: {total_patterns - 1} error patterns + 1 default")  # -1 per escludere default

    def generate_patches_optimized(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """
        FASE 4.0.2: Generazione patch ottimizzata per performance <10ms.

        Args:
            error_analysis: Analisi dell'errore

        Returns:
            Lista di candidati patch ottimizzata
        """
        return self.performance_optimizer.optimize_patch_generation(
            error_analysis,
            self.generate_patches
        )

    def get_performance_metrics(self) -> Dict[str, Any]:
        """FASE 4.0.2: Ottieni metriche performance."""
        return self.performance_optimizer.get_performance_report()
    
    def generate_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """
        Genera candidati patch per un errore.

        Args:
            error_analysis: Analisi dell'errore

        Returns:
            Lista di candidati patch
        """
        import time
        start_time = time.perf_counter()

        candidates = []

        # FASE 3: Tracking dello stato per debug caching
        self._patch_generation_count += 1
        current_pattern = getattr(error_analysis, 'error_pattern', 'NO_PATTERN')
        print(f"🔍 Generate patches call #{self._patch_generation_count} for pattern: {current_pattern}")
        print(f"   Previous pattern: {self._last_error_pattern}")
        print(f"   Seen patterns: {self._seen_error_patterns}")

        # Aggiorna stato
        self._seen_error_patterns.add(current_pattern)
        self._last_error_pattern = current_pattern

        # FASE 3: Priorità alle patch pattern-based
        print(f"🔍 Calling _generate_pattern_based_patches for pattern: {current_pattern}")
        pattern_patches = self._generate_pattern_based_patches(error_analysis)
        candidates.extend(pattern_patches)
        print(f"📊 Pattern-based patches generated: {len(pattern_patches)}")

        # Se non abbiamo patch pattern-based di alta qualità, usa type-based come fallback
        if not pattern_patches or not any(c.confidence >= 0.7 for c in pattern_patches):
            print("🔄 Pattern-based patches insufficient, using type-based fallback")
            print(f"   Pattern patches: {len(pattern_patches)}, High confidence: {any(c.confidence >= 0.7 for c in pattern_patches) if pattern_patches else False}")
            type_patches = self._generate_type_based_patches(error_analysis)
            candidates.extend(type_patches)
        else:
            print(f"✅ Using {len(pattern_patches)} pattern-based patches")

        # Genera patch basate su pattern appresi (sempre)
        learned_patches = self._generate_learned_patches(error_analysis)
        candidates.extend(learned_patches)
        
        # Filtra e ordina candidati
        filtered_candidates = self._filter_candidates(candidates)
        sorted_candidates = self._sort_candidates(filtered_candidates)
        
        # Limita numero candidati
        final_candidates = sorted_candidates[:self.config.max_patch_candidates]

        # FASE 4.0.3: Record metrics
        end_time = time.perf_counter()
        latency_ms = (end_time - start_time) * 1000
        success = len(final_candidates) > 0

        self.metrics_collector.record_patch_attempt(
            pattern=current_pattern,
            success=success,
            latency_ms=latency_ms
        )

        return final_candidates
    
    def _generate_pattern_based_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 3: Genera patch usando mappatura esplicita ErrorPattern → Strategie."""
        candidates = []
        pattern = getattr(error_analysis, 'error_pattern', 'default')

        # Ottieni strategie specifiche per questo pattern
        strategies = STRATEGY_MAP.get(pattern, STRATEGY_MAP['default'])

        print(f"🎯 Pattern: {pattern}, Strategie: {[s[0] for s in strategies]}")

        # Debug: verifica se il pattern è nella mappa
        if pattern not in STRATEGY_MAP:
            print(f"⚠️  Pattern '{pattern}' not found in STRATEGY_MAP, using default")
            print(f"   Available patterns: {list(STRATEGY_MAP.keys())}")

        # Genera patch usando solo le strategie mappate
        for strategy_name, base_confidence in strategies:
            patch_candidates = self._generate_patches_for_strategy(
                error_analysis, strategy_name, base_confidence
            )
            candidates.extend(patch_candidates)

            # Se abbiamo trovato candidati con alta confidence, fermiamoci
            if patch_candidates and any(c.confidence >= 0.8 for c in patch_candidates):
                print(f"✅ High-confidence patch found with strategy: {strategy_name}")
                break

        return candidates

    def get_strategies_for_pattern(self, error_pattern: str) -> List[Tuple[str, float]]:
        """
        FASE 3: Metodo centralizzato per ottenere strategie da pattern.

        Args:
            error_pattern: Pattern dell'errore

        Returns:
            Lista di tuple (strategy_name, confidence)
        """
        strategies = STRATEGY_MAP.get(error_pattern, STRATEGY_MAP['default'])
        print(f"🎯 Pattern '{error_pattern}' → Strategie: {[s[0] for s in strategies]}")
        return strategies

    def _generate_patches_for_strategy(self, error_analysis: ErrorAnalysis,
                                     strategy_name: str, base_confidence: float) -> List[PatchCandidate]:
        """FASE 3: Genera patch per una strategia specifica."""
        candidates = []

        # Crea patch candidate per la strategia specifica
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Apply strategy: {strategy_name}",
            patch_strategy=strategy_name,
            confidence=base_confidence,
            risk_level="low"
        )

        # Aggiungi metadata se disponibile
        if hasattr(error_analysis, 'error_message'):
            # Estrai informazioni specifiche dal messaggio di errore
            if strategy_name in ["add_name_stub", "wrap_try_except"]:
                var_match = re.search(r"name '(\w+)' is not defined", error_analysis.error_message)
                if var_match:
                    candidate.metadata = {"variable_name": var_match.group(1)}

            elif strategy_name == "safe_attribute_access":
                attr_match = re.search(r"'(\w+)' object has no attribute '(\w+)'", error_analysis.error_message)
                if attr_match:
                    candidate.metadata = {
                        "object_type": attr_match.group(1),
                        "attribute_name": attr_match.group(2)
                    }

            elif strategy_name in ["try_except_import", "add_import_stub", "comment_out_import"]:
                module_name = self._extract_module_name(error_analysis.error_message)
                candidate.metadata = {"module_name": module_name}

        candidates.append(candidate)
        return candidates

    def _generate_legacy_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 3: Fallback a metodi legacy quando le strategie specifiche falliscono."""
        print(f"🔄 Using legacy fallback for error type: {error_analysis.error_type}")
        return self._generate_type_based_patches(error_analysis)

    def _generate_type_based_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su tipo errore."""
        candidates = []
        error_type = error_analysis.error_type
        
        if error_type == ErrorType.SYNTAX_ERROR:
            candidates.extend(self._generate_syntax_patches(error_analysis))
        elif error_type == ErrorType.RUNTIME_ERROR:
            candidates.extend(self._generate_runtime_patches(error_analysis))
        elif error_type == ErrorType.LOGIC_ERROR:
            candidates.extend(self._generate_logic_patches(error_analysis))
        elif error_type == ErrorType.PERFORMANCE_ERROR:
            candidates.extend(self._generate_performance_patches(error_analysis))
        elif error_type == ErrorType.SECURITY_ERROR:
            candidates.extend(self._generate_security_patches(error_analysis))
        elif error_type == ErrorType.VALIDATION_ERROR:
            candidates.extend(self._generate_validation_patches(error_analysis))

        return candidates
    
    def _generate_syntax_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 3: Genera patch per errori di sintassi usando STRATEGY_MAP."""
        print(f"🔄 _generate_syntax_patches called - redirecting to STRATEGY_MAP")

        # Usa sempre la mappatura esplicita
        error_pattern = getattr(error_analysis, 'error_pattern', 'syntax_general')
        strategies = self.get_strategies_for_pattern(error_pattern)

        candidates = []
        for strategy_name, base_confidence in strategies:
            patch_candidates = self._generate_patches_for_strategy(
                error_analysis, strategy_name, base_confidence
            )
            candidates.extend(patch_candidates)

            # Se abbiamo trovato candidati con alta confidence, fermiamoci
            if patch_candidates and any(c.confidence >= 0.8 for c in patch_candidates):
                print(f"✅ High-confidence syntax patch found with strategy: {strategy_name}")
                break

        return candidates
    
    def _generate_runtime_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 3: Genera patch per errori di runtime usando STRATEGY_MAP."""
        print(f"🔄 _generate_runtime_patches called - redirecting to STRATEGY_MAP")

        # Usa sempre la mappatura esplicita
        error_pattern = getattr(error_analysis, 'error_pattern', 'runtime_general')
        strategies = self.get_strategies_for_pattern(error_pattern)

        candidates = []
        for strategy_name, base_confidence in strategies:
            patch_candidates = self._generate_patches_for_strategy(
                error_analysis, strategy_name, base_confidence
            )
            candidates.extend(patch_candidates)

            # Se abbiamo trovato candidati con alta confidence, fermiamoci
            if patch_candidates and any(c.confidence >= 0.8 for c in patch_candidates):
                print(f"✅ High-confidence runtime patch found with strategy: {strategy_name}")
                break

        return candidates

    def _generate_legacy_runtime_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Strategie legacy per errori di runtime (per compatibilità)."""
        candidates = []
        message = error_analysis.error_message

        # Logica legacy esistente per compatibilità
        if "is not defined" in message or "NameError" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Fix undefined variable (legacy)",
                patch_strategy="name_error_fix",
                confidence=0.6,
                risk_level="medium"
            )
            candidates.append(candidate)

        return candidates

    def _is_import_error(self, message: str) -> bool:
        """Verifica se è un errore di import."""
        message_lower = message.lower()
        return any(pattern in message_lower for pattern in [
            "no module named",
            "modulenotfounderror",
            "importerror",
            "cannot import name"
        ])

    def _generate_undefined_variable_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 2: Genera patch per variabili non definite."""
        candidates = []
        message = error_analysis.error_message

        # Estrai nome variabile
        var_match = re.search(r"name '(\w+)' is not defined", message)
        var_name = var_match.group(1) if var_match else "unknown_var"

        # Strategia 1: Add name stub
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Add stub for variable '{var_name}'",
            patch_strategy="add_name_stub",
            confidence=0.8,
            risk_level="low",
            metadata={"variable_name": var_name}
        )
        candidates.append(candidate)

        # Strategia 2: Wrap try/except
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Wrap in try/except for '{var_name}'",
            patch_strategy="wrap_try_except",
            confidence=0.9,
            risk_level="low",
            metadata={"variable_name": var_name}
        )
        candidates.append(candidate)

        return candidates

    def _generate_undefined_attribute_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 2: Genera patch per attributi non definiti."""
        candidates = []
        message = error_analysis.error_message

        # Estrai oggetto e attributo
        attr_match = re.search(r"'(\w+)' object has no attribute '(\w+)'", message)
        if attr_match:
            obj_type, attr_name = attr_match.groups()
        else:
            obj_type, attr_name = "object", "attribute"

        # Strategia: Safe attribute access
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Add safe access for attribute '{attr_name}'",
            patch_strategy="safe_attribute_access",
            confidence=0.8,
            risk_level="low",
            metadata={"object_type": obj_type, "attribute_name": attr_name}
        )
        candidates.append(candidate)

        return candidates

    def _generate_type_mismatch_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """FASE 2: Genera patch per errori di tipo."""
        candidates = []

        # Strategia: Type conversion
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description="Add type conversion",
            patch_strategy="type_conversion",
            confidence=0.7,
            risk_level="medium"
        )
        candidates.append(candidate)

        return candidates

    def _generate_import_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch specifiche per errori di import."""
        candidates = []
        message = error_analysis.error_message

        # Estrai nome modulo dall'errore
        module_name = self._extract_module_name(message)

        # Strategia 1: Comment out import
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Comment out import for '{module_name}'",
            patch_strategy="comment_out_import",
            confidence=0.8,
            risk_level="low",
            metadata={"module_name": module_name}
        )
        candidates.append(candidate)

        # Strategia 2: Add stub module
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Add stub for module '{module_name}'",
            patch_strategy="add_import_stub",
            confidence=0.7,
            risk_level="medium",
            metadata={"module_name": module_name}
        )
        candidates.append(candidate)

        # Strategia 3: Try/except import
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Wrap import in try/except for '{module_name}'",
            patch_strategy="try_except_import",
            confidence=0.9,
            risk_level="low",
            metadata={"module_name": module_name}
        )
        candidates.append(candidate)

        return candidates

    def _extract_module_name(self, error_message: str) -> str:
        """Estrae il nome del modulo dall'errore di import."""
        # Pattern per "No module named 'xyz'"
        import_pattern = r"No module named ['\"]([^'\"]+)['\"]"
        match = re.search(import_pattern, error_message, re.IGNORECASE)
        if match:
            return match.group(1)

        # Pattern per "cannot import name 'xyz'"
        name_pattern = r"cannot import name ['\"]([^'\"]+)['\"]"
        match = re.search(name_pattern, error_message, re.IGNORECASE)
        if match:
            return match.group(1)

        # Fallback: cerca parole dopo "module"
        words = error_message.split()
        for i, word in enumerate(words):
            if word.lower() == "module" and i + 2 < len(words):
                return words[i + 2].strip("'\"")

        return "unknown_module"

    def _generate_logic_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori logici."""
        candidates = []
        
        if "contradiction" in error_analysis.error_message.lower():
            candidate = PatchCandidate(
                patch_type=PatchType.LOGIC_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Resolve logical contradiction",
                patch_strategy="contradiction_resolution",
                confidence=0.5,
                risk_level="high"
            )
            candidates.append(candidate)
        
        if "low quality reasoning" in error_analysis.error_message.lower():
            candidate = PatchCandidate(
                patch_type=PatchType.ALGORITHM_REPLACEMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Improve reasoning quality",
                patch_strategy="reasoning_enhancement",
                confidence=0.4,
                risk_level="medium"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_performance_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di performance."""
        candidates = []
        
        if "timeout" in error_analysis.error_message.lower():
            # Patch: ottimizzazione algoritmo
            candidate = PatchCandidate(
                patch_type=PatchType.PERFORMANCE_OPTIMIZATION,
                target_error_id=error_analysis.error_id,
                patch_description="Optimize algorithm for better performance",
                patch_strategy="algorithm_optimization",
                confidence=0.6,
                risk_level="medium"
            )
            candidates.append(candidate)
            
            # Patch: aumento timeout
            candidate = PatchCandidate(
                patch_type=PatchType.PARAMETER_ADJUSTMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Increase timeout limit",
                patch_strategy="timeout_adjustment",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_security_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di sicurezza."""
        candidates = []
        
        candidate = PatchCandidate(
            patch_type=PatchType.SECURITY_HARDENING,
            target_error_id=error_analysis.error_id,
            patch_description="Apply security hardening",
            patch_strategy="security_enhancement",
            confidence=0.9,
            risk_level="low"
        )
        candidates.append(candidate)
        
        return candidates

    def _generate_validation_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di validazione."""
        candidates = []
        message = error_analysis.error_message.lower()
        pattern = error_analysis.error_pattern

        # Patch per missing premises
        if "missing" in message and "premise" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.LOGIC_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Add missing logical premises",
                patch_strategy="premise_addition",
                confidence=0.6,
                risk_level="medium"
            )
            candidates.append(candidate)

        # Patch per logical dependency errors
        if pattern == "logical_dependency_error":
            candidate = PatchCandidate(
                patch_type=PatchType.LOGIC_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Resolve logical dependency issue",
                patch_strategy="dependency_resolution",
                confidence=0.7,
                risk_level="medium"
            )
            candidates.append(candidate)

        # Patch per invalid references
        if "reference" in message and "invalid" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Fix invalid reference",
                patch_strategy="reference_correction",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)

        # Patch per circular inference
        if "circular" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.ALGORITHM_REPLACEMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Break circular inference",
                patch_strategy="circularity_breaking",
                confidence=0.5,
                risk_level="high"
            )
            candidates.append(candidate)

        return candidates

    def _generate_learned_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su pattern appresi."""
        candidates = []
        
        # Cerca pattern simili nei pattern appresi
        for pattern in self.learned_patterns:
            if self._pattern_matches(pattern, error_analysis):
                candidate = self._create_patch_from_learned_pattern(
                    error_analysis, pattern
                )
                if candidate:
                    candidates.append(candidate)
        
        return candidates
    
    def _create_patch_from_template(self, error_analysis: ErrorAnalysis, 
                                   template: Dict[str, Any], 
                                   strategy: Dict[str, Any]) -> Optional[PatchCandidate]:
        """Crea patch da template."""
        candidate = PatchCandidate(
            patch_type=PatchType(template.get('type', 'code_fix')),
            target_error_id=error_analysis.error_id,
            patch_description=template.get('description', ''),
            patch_strategy=template.get('strategy', ''),
            confidence=template.get('confidence', 0.5),
            risk_level=template.get('risk_level', 'medium')
        )
        
        # Personalizza patch basata su contesto errore
        candidate = self._customize_patch(candidate, error_analysis)
        
        return candidate
    
    def _create_patch_from_learned_pattern(self, error_analysis: ErrorAnalysis,
                                          pattern: LearningPattern) -> Optional[PatchCandidate]:
        """Crea patch da pattern appreso."""
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Apply learned pattern: {pattern.pattern_name}",
            patch_strategy="learned_pattern",
            confidence=pattern.effectiveness_score,
            risk_level="medium"
        )
        
        candidate.metadata = {
            "learned_pattern_id": pattern.pattern_id,
            "pattern_effectiveness": pattern.effectiveness_score
        }
        
        return candidate
    
    def _customize_patch(self, candidate: PatchCandidate, 
                        error_analysis: ErrorAnalysis) -> PatchCandidate:
        """Personalizza patch basata su contesto errore."""
        # Aggiusta confidence basata su severità errore
        if error_analysis.severity == "critical":
            candidate.confidence *= 1.2  # Aumenta confidence per errori critici
        elif error_analysis.severity == "low":
            candidate.confidence *= 0.8  # Diminuisci per errori minori
        
        # Aggiusta risk level
        if error_analysis.impact_score > 0.8:
            if candidate.risk_level == "low":
                candidate.risk_level = "medium"
            elif candidate.risk_level == "medium":
                candidate.risk_level = "high"
        
        # Clamp confidence
        candidate.confidence = max(0.0, min(1.0, candidate.confidence))
        
        return candidate
    
    def _filter_candidates(self, candidates: List[PatchCandidate]) -> List[PatchCandidate]:
        """Filtra candidati patch."""
        filtered = []
        
        for candidate in candidates:
            # Filtra per confidence minima
            if candidate.confidence < self.config.min_confidence_threshold:
                continue
            
            # Filtra per risk level massimo
            risk_levels = ["low", "medium", "high", "critical"]
            max_risk_index = risk_levels.index(self.config.max_risk_level)
            candidate_risk_index = risk_levels.index(candidate.risk_level)
            
            if candidate_risk_index > max_risk_index:
                continue
            
            filtered.append(candidate)
        
        return filtered
    
    def _sort_candidates(self, candidates: List[PatchCandidate]) -> List[PatchCandidate]:
        """Ordina candidati per qualità."""
        def patch_score(candidate: PatchCandidate) -> float:
            # Score basato su confidence e risk
            risk_penalty = {
                "low": 0.0,
                "medium": 0.1,
                "high": 0.3,
                "critical": 0.5
            }
            
            penalty = risk_penalty.get(candidate.risk_level, 0.2)
            return candidate.confidence - penalty
        
        return sorted(candidates, key=patch_score, reverse=True)
    
    def _pattern_matches(self, pattern: LearningPattern, 
                        error_analysis: ErrorAnalysis) -> bool:
        """Verifica se un pattern appreso corrisponde all'errore."""
        # Matching semplice basato su pattern dell'errore
        return pattern.error_pattern == error_analysis.error_pattern
    
    def _load_patch_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Carica strategie di patch."""
        return {
            "undefined_variable": {
                "patches": [
                    {
                        "type": "code_fix",
                        "description": "Initialize variable with default value",
                        "strategy": "variable_init",
                        "confidence": 0.7,
                        "risk_level": "medium"
                    }
                ]
            },
            "missing_attribute": {
                "patches": [
                    {
                        "type": "code_fix", 
                        "description": "Add attribute check",
                        "strategy": "attribute_guard",
                        "confidence": 0.8,
                        "risk_level": "low"
                    }
                ]
            },
            "syntax_invalid": {
                "patches": [
                    {
                        "type": "code_fix",
                        "description": "Fix syntax error",
                        "strategy": "syntax_repair",
                        "confidence": 0.9,
                        "risk_level": "low"
                    }
                ]
            },
            "logical_dependency_error": {
                "patches": [
                    {
                        "type": "logic_fix",
                        "description": "Resolve logical dependency issue",
                        "strategy": "dependency_resolution",
                        "confidence": 0.7,
                        "risk_level": "medium"
                    },
                    {
                        "type": "logic_fix",
                        "description": "Add missing premises",
                        "strategy": "premise_addition",
                        "confidence": 0.6,
                        "risk_level": "medium"
                    }
                ]
            }
        }
    
    def add_learned_pattern(self, pattern: LearningPattern):
        """Aggiunge pattern appreso."""
        self.learned_patterns.append(pattern)
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche di generazione."""
        return {
            "strategies_available": len(self.patch_strategies),
            "learned_patterns": len(self.learned_patterns),
            "max_candidates": self.config.max_patch_candidates,
            "min_confidence": self.config.min_confidence_threshold
        }
