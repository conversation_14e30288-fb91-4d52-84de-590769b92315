"""
NEUROGLYPH Syntax Validator
Validatore sintattico per ReasoningGraph e AST structures
"""

import ast
import re
import time
from typing import List, Optional, Dict, Any, Tuple

from .validation_structures import (
    ValidationError, ValidationErrorType, ValidationSeverity, 
    ValidationLevel, ValidationConfig
)
from .reasoning_structures import ReasoningGraph, LogicalFact, ReasoningStep


class SyntaxValidator:
    """
    Validatore sintattico per NEUROGLYPH.
    Verifica correttezza sintattica di ReasoningGraph e AST structures.
    """
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        """
        Inizializza il validatore sintattico.
        
        Args:
            config: Configurazione validazione
        """
        self.config = config or ValidationConfig()
        self.errors = []
        
        print("🔍 SyntaxValidator inizializzato")
        print(f"   - AST validation: {self.config.enable_ast_validation}")
        print(f"   - Fix missing locations: {self.config.fix_missing_locations}")
        print(f"   - Check indentation: {self.config.check_indentation}")
    
    def validate_syntax(self, graph: ReasoningGraph) -> List[ValidationError]:
        """
        Valida sintassi del ReasoningGraph.
        
        Args:
            graph: Grafo di reasoning da validare
            
        Returns:
            Lista di errori sintattici
        """
        self.errors = []
        start_time = time.time()
        
        try:
            # Valida struttura grafo
            self._validate_graph_structure(graph)
            
            # Valida fatti logici
            self._validate_logical_facts(graph.all_facts)
            
            # Valida steps di reasoning
            self._validate_reasoning_steps(graph)
            
            # Valida percorsi
            self._validate_reasoning_paths(graph.paths)
            
            # Valida AST se abilitato
            if self.config.enable_ast_validation:
                self._validate_ast_structures(graph)
            
        except Exception as e:
            error = ValidationError(
                error_type=ValidationErrorType.AST_PARSE_ERROR,
                severity=ValidationSeverity.CRITICAL,
                level=ValidationLevel.SYNTAX,
                message=f"Syntax validation failed: {str(e)}",
                context={"exception": str(e)}
            )
            self.errors.append(error)
        
        validation_time = time.time() - start_time
        
        print(f"🔍 Syntax validation completed in {validation_time:.3f}s")
        print(f"   - Errors found: {len(self.errors)}")
        
        return self.errors
    
    def _validate_graph_structure(self, graph: ReasoningGraph):
        """Valida struttura base del grafo."""
        # Verifica ID grafo
        if not graph.graph_id:
            self.errors.append(ValidationError(
                error_type=ValidationErrorType.MISSING_LOCATIONS,
                severity=ValidationSeverity.ERROR,
                level=ValidationLevel.SYNTAX,
                message="Graph missing ID",
                suggestions=["Ensure graph has valid UUID"]
            ))
        
        # Verifica percorsi attivi
        for active_path_id in graph.active_paths:
            if not any(path.path_id == active_path_id for path in graph.paths):
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.ORPHAN_NODES,
                    severity=ValidationSeverity.ERROR,
                    level=ValidationLevel.SYNTAX,
                    message=f"Active path {active_path_id} not found in paths",
                    path_id=active_path_id,
                    suggestions=["Remove invalid path ID from active_paths"]
                ))
        
        # Verifica contraddizioni
        for fact1_id, fact2_id in graph.contradictions:
            fact1_exists = any(f.fact_id == fact1_id for f in graph.all_facts)
            fact2_exists = any(f.fact_id == fact2_id for f in graph.all_facts)
            
            if not fact1_exists or not fact2_exists:
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.ORPHAN_NODES,
                    severity=ValidationSeverity.ERROR,
                    level=ValidationLevel.SYNTAX,
                    message=f"Contradiction references non-existent facts: {fact1_id}, {fact2_id}",
                    context={"fact1_exists": fact1_exists, "fact2_exists": fact2_exists}
                ))
    
    def _validate_logical_facts(self, facts: List[LogicalFact]):
        """Valida fatti logici."""
        for fact in facts:
            # Verifica ID
            if not fact.fact_id:
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.MISSING_LOCATIONS,
                    severity=ValidationSeverity.ERROR,
                    level=ValidationLevel.SYNTAX,
                    message="LogicalFact missing ID",
                    node_id=getattr(fact, 'fact_id', 'unknown')
                ))
            
            # Verifica statement
            if not fact.statement or not fact.statement.strip():
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.AST_PARSE_ERROR,
                    severity=ValidationSeverity.CRITICAL,
                    level=ValidationLevel.SYNTAX,
                    message="LogicalFact has empty statement",
                    node_id=fact.fact_id
                ))
            
            # Verifica predicate
            if not fact.predicate or not fact.predicate.strip():
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.MISSING_LOCATIONS,
                    severity=ValidationSeverity.WARNING,
                    level=ValidationLevel.SYNTAX,
                    message="LogicalFact has empty predicate",
                    node_id=fact.fact_id,
                    suggestions=["Extract predicate from statement"]
                ))
            
            # Verifica confidence range
            if not (0.0 <= fact.confidence <= 1.0):
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.TYPE_MISMATCH,
                    severity=ValidationSeverity.ERROR,
                    level=ValidationLevel.SYNTAX,
                    message=f"LogicalFact confidence out of range: {fact.confidence}",
                    node_id=fact.fact_id,
                    context={"confidence": fact.confidence},
                    suggestions=["Ensure confidence is between 0.0 and 1.0"]
                ))
            
            # Verifica derived_from references
            for parent_id in fact.derived_from:
                if not parent_id:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.ORPHAN_NODES,
                        severity=ValidationSeverity.WARNING,
                        level=ValidationLevel.SYNTAX,
                        message="LogicalFact has empty parent reference",
                        node_id=fact.fact_id
                    ))
    
    def _validate_reasoning_steps(self, graph: ReasoningGraph):
        """Valida steps di reasoning."""
        for path in graph.paths:
            for step in path.steps:
                # Verifica ID
                if not step.step_id:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.MISSING_LOCATIONS,
                        severity=ValidationSeverity.ERROR,
                        level=ValidationLevel.SYNTAX,
                        message="ReasoningStep missing ID",
                        step_id=getattr(step, 'step_id', 'unknown'),
                        path_id=path.path_id
                    ))
                
                # Verifica operation
                if not step.operation:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.AST_PARSE_ERROR,
                        severity=ValidationSeverity.CRITICAL,
                        level=ValidationLevel.SYNTAX,
                        message="ReasoningStep missing operation",
                        step_id=step.step_id,
                        path_id=path.path_id
                    ))
                
                # Verifica confidence
                if not (0.0 <= step.confidence <= 1.0):
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.TYPE_MISMATCH,
                        severity=ValidationSeverity.ERROR,
                        level=ValidationLevel.SYNTAX,
                        message=f"ReasoningStep confidence out of range: {step.confidence}",
                        step_id=step.step_id,
                        path_id=path.path_id,
                        context={"confidence": step.confidence}
                    ))
                
                # Verifica input/output facts
                if not step.input_facts and not step.output_facts:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.ORPHAN_NODES,
                        severity=ValidationSeverity.WARNING,
                        level=ValidationLevel.SYNTAX,
                        message="ReasoningStep has no input or output facts",
                        step_id=step.step_id,
                        path_id=path.path_id,
                        suggestions=["Ensure step has valid input and output facts"]
                    ))
    
    def _validate_reasoning_paths(self, paths: List):
        """Valida percorsi di reasoning."""
        for path in paths:
            # Verifica ID
            if not path.path_id:
                self.errors.append(ValidationError(
                    error_type=ValidationErrorType.MISSING_LOCATIONS,
                    severity=ValidationSeverity.ERROR,
                    level=ValidationLevel.SYNTAX,
                    message="ReasoningPath missing ID",
                    path_id=getattr(path, 'path_id', 'unknown')
                ))
            
            # Verifica scores range
            for score_name, score_value in [
                ("confidence_score", path.confidence_score),
                ("plausibility_score", path.plausibility_score),
                ("coherence_score", path.coherence_score)
            ]:
                if not (0.0 <= score_value <= 1.0):
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.TYPE_MISMATCH,
                        severity=ValidationSeverity.ERROR,
                        level=ValidationLevel.SYNTAX,
                        message=f"ReasoningPath {score_name} out of range: {score_value}",
                        path_id=path.path_id,
                        context={score_name: score_value}
                    ))
            
            # Verifica step numbering
            for i, step in enumerate(path.steps):
                expected_number = i + 1
                if step.step_number != expected_number:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.MISSING_LOCATIONS,
                        severity=ValidationSeverity.WARNING,
                        level=ValidationLevel.SYNTAX,
                        message=f"ReasoningStep number mismatch: expected {expected_number}, got {step.step_number}",
                        step_id=step.step_id,
                        path_id=path.path_id,
                        suggestions=["Renumber steps sequentially"]
                    ))
    
    def _validate_ast_structures(self, graph: ReasoningGraph):
        """Valida strutture AST se presenti."""
        # Cerca statements che potrebbero essere codice Python
        code_statements = []
        
        for fact in graph.all_facts:
            if self._looks_like_python_code(fact.statement):
                code_statements.append((fact, fact.statement))
        
        # Valida ogni statement di codice
        for fact, code in code_statements:
            try:
                # Prova a parsare come espressione
                ast.parse(code, mode='eval')
            except SyntaxError:
                try:
                    # Prova a parsare come statement
                    parsed = ast.parse(code, mode='exec')
                    
                    # Fix missing locations se abilitato
                    if self.config.fix_missing_locations:
                        ast.fix_missing_locations(parsed)
                    
                    # Verifica indentazione se abilitato
                    if self.config.check_indentation:
                        self._check_indentation(code, fact)
                        
                except SyntaxError as e:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.AST_PARSE_ERROR,
                        severity=ValidationSeverity.ERROR,
                        level=ValidationLevel.SYNTAX,
                        message=f"AST parse error in fact statement: {str(e)}",
                        node_id=fact.fact_id,
                        line_number=e.lineno,
                        context={"syntax_error": str(e), "code": code}
                    ))
                except Exception as e:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.AST_PARSE_ERROR,
                        severity=ValidationSeverity.WARNING,
                        level=ValidationLevel.SYNTAX,
                        message=f"AST validation error: {str(e)}",
                        node_id=fact.fact_id,
                        context={"error": str(e), "code": code}
                    ))
    
    def _looks_like_python_code(self, statement: str) -> bool:
        """Verifica se uno statement sembra codice Python."""
        # Pattern che indicano codice Python
        code_patterns = [
            r'\bdef\s+\w+\s*\(',           # function definition
            r'\bclass\s+\w+\s*[\(:]',      # class definition
            r'\bif\s+.+:',                 # if statement
            r'\bfor\s+\w+\s+in\s+',        # for loop
            r'\bwhile\s+.+:',              # while loop
            r'\btry\s*:',                  # try block
            r'\bwith\s+.+:',               # with statement
            r'\bimport\s+\w+',             # import statement
            r'\bfrom\s+\w+\s+import',      # from import
            r'=\s*\[.*\]',                 # list assignment
            r'=\s*\{.*\}',                 # dict assignment
            r'\w+\s*\(.*\)\s*:',           # function call with colon
        ]
        
        for pattern in code_patterns:
            if re.search(pattern, statement):
                return True
        
        return False
    
    def _check_indentation(self, code: str, fact: LogicalFact):
        """Verifica indentazione del codice."""
        lines = code.split('\n')
        
        for i, line in enumerate(lines, 1):
            if line.strip():  # Ignora linee vuote
                # Verifica che l'indentazione sia consistente (multipli di 4 spazi)
                leading_spaces = len(line) - len(line.lstrip())
                if leading_spaces % 4 != 0:
                    self.errors.append(ValidationError(
                        error_type=ValidationErrorType.INDENTATION_ERROR,
                        severity=ValidationSeverity.WARNING,
                        level=ValidationLevel.SYNTAX,
                        message=f"Inconsistent indentation: {leading_spaces} spaces",
                        node_id=fact.fact_id,
                        line_number=i,
                        context={"line": line, "leading_spaces": leading_spaces},
                        suggestions=["Use 4 spaces for indentation"]
                    ))
