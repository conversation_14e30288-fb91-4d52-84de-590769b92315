"""
NEUROGLYPH Logical Operators
Motore degli operatori logici per reasoning simbolico
"""

import re
import time
from typing import List, Optional, Tuple, Dict, Any, Set
from dataclasses import dataclass

from .reasoning_structures import (
    LogicalFact, ReasoningStep, LogicalOperator, 
    ReasoningType, FactType
)


class LogicalOperators:
    """
    Motore degli operatori logici per NEUROGLYPH.
    Implementa regole di inferenza simbolica.
    """
    
    def __init__(self):
        """Inizializza il motore logico."""
        self.inference_rules = self._load_inference_rules()
        self.pattern_matchers = self._load_pattern_matchers()
        
        print("🧠 LogicalOperators inizializzato")
        print(f"   - Regole di inferenza: {len(self.inference_rules)}")
        print(f"   - Pattern matchers: {len(self.pattern_matchers)}")
    
    def deduce(self, facts: List[LogicalFact]) -> List[ReasoningStep]:
        """
        Applica deduzione logica sui fatti.
        
        Args:
            facts: Lista di fatti logici
            
        Returns:
            Lista di steps di reasoning con nuovi fatti derivati
        """
        steps = []
        
        # Applica tutte le regole di inferenza
        for rule_name, rule_func in self.inference_rules.items():
            try:
                new_steps = rule_func(facts)
                steps.extend(new_steps)
            except Exception as e:
                print(f"⚠️ Error in rule {rule_name}: {e}")
        
        return steps
    
    def modus_ponens(self, facts: List[LogicalFact]) -> List[ReasoningStep]:
        """
        Modus Ponens: Se A e A⇒B, allora B.
        
        Args:
            facts: Lista di fatti
            
        Returns:
            Steps di reasoning derivati
        """
        steps = []
        
        # Trova implicazioni (A ⇒ B)
        implications = [f for f in facts if "⇒" in f.statement or " implies " in f.statement.lower()]
        
        for impl in implications:
            # Estrai antecedente e conseguente
            if "⇒" in impl.statement:
                parts = impl.statement.split("⇒")
            else:
                parts = impl.statement.lower().split(" implies ")
            
            if len(parts) != 2:
                continue
            
            antecedent = parts[0].strip()
            consequent = parts[1].strip()
            
            # Cerca antecedente nei fatti
            for fact in facts:
                if fact.statement.strip() == antecedent and fact.truth_value != False:
                    # Applica Modus Ponens
                    new_fact = LogicalFact(
                        statement=consequent,
                        predicate=self._extract_predicate(consequent),
                        arguments=self._extract_arguments(consequent),
                        fact_type=FactType.DERIVED,
                        confidence=min(fact.confidence, impl.confidence),
                        truth_value=True,
                        derived_from=[fact.fact_id, impl.fact_id],
                        derivation_rule="modus_ponens",
                        derivation_operator=LogicalOperator.DEDUCE
                    )
                    
                    step = ReasoningStep(
                        operation=LogicalOperator.DEDUCE,
                        reasoning_type=ReasoningType.DEDUCTIVE,
                        rule_name="modus_ponens",
                        input_facts=[fact, impl],
                        output_facts=[new_fact],
                        reasoning_text=f"From '{fact.statement}' and '{impl.statement}', deduce '{new_fact.statement}'",
                        confidence=new_fact.confidence
                    )
                    
                    steps.append(step)
        
        return steps
    
    def modus_tollens(self, facts: List[LogicalFact]) -> List[ReasoningStep]:
        """
        Modus Tollens: Se A⇒B e ¬B, allora ¬A.
        
        Args:
            facts: Lista di fatti
            
        Returns:
            Steps di reasoning derivati
        """
        steps = []
        
        # Trova implicazioni (A ⇒ B)
        implications = [f for f in facts if "⇒" in f.statement or " implies " in f.statement.lower()]
        
        for impl in implications:
            # Estrai antecedente e conseguente
            if "⇒" in impl.statement:
                parts = impl.statement.split("⇒")
            else:
                parts = impl.statement.lower().split(" implies ")
            
            if len(parts) != 2:
                continue
            
            antecedent = parts[0].strip()
            consequent = parts[1].strip()
            
            # Cerca negazione del conseguente
            for fact in facts:
                if (fact.statement.strip() == f"¬{consequent}" or 
                    fact.statement.strip() == f"not {consequent}" or
                    (fact.statement.strip() == consequent and fact.truth_value == False)):
                    
                    # Applica Modus Tollens
                    new_fact = LogicalFact(
                        statement=f"¬{antecedent}",
                        predicate=self._extract_predicate(antecedent),
                        arguments=self._extract_arguments(antecedent),
                        fact_type=FactType.DERIVED,
                        confidence=min(fact.confidence, impl.confidence),
                        truth_value=False,
                        derived_from=[fact.fact_id, impl.fact_id],
                        derivation_rule="modus_tollens",
                        derivation_operator=LogicalOperator.DEDUCE
                    )
                    
                    step = ReasoningStep(
                        operation=LogicalOperator.DEDUCE,
                        reasoning_type=ReasoningType.DEDUCTIVE,
                        rule_name="modus_tollens",
                        input_facts=[fact, impl],
                        output_facts=[new_fact],
                        reasoning_text=f"From '{impl.statement}' and '{fact.statement}', deduce '{new_fact.statement}'",
                        confidence=new_fact.confidence
                    )
                    
                    steps.append(step)
        
        return steps
    
    def conjunction_introduction(self, facts: List[LogicalFact]) -> List[ReasoningStep]:
        """
        Introduzione Congiunzione: Se A e B, allora A∧B.
        
        Args:
            facts: Lista di fatti
            
        Returns:
            Steps di reasoning derivati
        """
        steps = []
        
        # Combina coppie di fatti
        for i, fact1 in enumerate(facts):
            for fact2 in facts[i+1:]:
                if (fact1.truth_value != False and fact2.truth_value != False and
                    fact1.fact_type != FactType.CONTRADICTION and 
                    fact2.fact_type != FactType.CONTRADICTION):
                    
                    # Crea congiunzione
                    new_fact = LogicalFact(
                        statement=f"{fact1.statement} ∧ {fact2.statement}",
                        predicate="conjunction",
                        arguments=[fact1.statement, fact2.statement],
                        fact_type=FactType.DERIVED,
                        confidence=min(fact1.confidence, fact2.confidence),
                        truth_value=True,
                        derived_from=[fact1.fact_id, fact2.fact_id],
                        derivation_rule="conjunction_introduction",
                        derivation_operator=LogicalOperator.AND
                    )
                    
                    step = ReasoningStep(
                        operation=LogicalOperator.AND,
                        reasoning_type=ReasoningType.DEDUCTIVE,
                        rule_name="conjunction_introduction",
                        input_facts=[fact1, fact2],
                        output_facts=[new_fact],
                        reasoning_text=f"From '{fact1.statement}' and '{fact2.statement}', form conjunction",
                        confidence=new_fact.confidence
                    )
                    
                    steps.append(step)
        
        return steps
    
    def conjunction_elimination(self, facts: List[LogicalFact]) -> List[ReasoningStep]:
        """
        Eliminazione Congiunzione: Se A∧B, allora A e B.
        
        Args:
            facts: Lista di fatti
            
        Returns:
            Steps di reasoning derivati
        """
        steps = []
        
        for fact in facts:
            if "∧" in fact.statement and fact.truth_value != False:
                # Dividi congiunzione
                parts = fact.statement.split("∧")
                if len(parts) == 2:
                    left = parts[0].strip()
                    right = parts[1].strip()
                    
                    # Crea fatti separati
                    left_fact = LogicalFact(
                        statement=left,
                        predicate=self._extract_predicate(left),
                        arguments=self._extract_arguments(left),
                        fact_type=FactType.DERIVED,
                        confidence=fact.confidence,
                        truth_value=True,
                        derived_from=[fact.fact_id],
                        derivation_rule="conjunction_elimination",
                        derivation_operator=LogicalOperator.AND
                    )
                    
                    right_fact = LogicalFact(
                        statement=right,
                        predicate=self._extract_predicate(right),
                        arguments=self._extract_arguments(right),
                        fact_type=FactType.DERIVED,
                        confidence=fact.confidence,
                        truth_value=True,
                        derived_from=[fact.fact_id],
                        derivation_rule="conjunction_elimination",
                        derivation_operator=LogicalOperator.AND
                    )
                    
                    step = ReasoningStep(
                        operation=LogicalOperator.AND,
                        reasoning_type=ReasoningType.DEDUCTIVE,
                        rule_name="conjunction_elimination",
                        input_facts=[fact],
                        output_facts=[left_fact, right_fact],
                        reasoning_text=f"From '{fact.statement}', extract '{left}' and '{right}'",
                        confidence=fact.confidence
                    )
                    
                    steps.append(step)
        
        return steps
    
    def contradiction_detection(self, facts: List[LogicalFact]) -> List[ReasoningStep]:
        """
        Rilevamento Contraddizioni: Se A e ¬A, allora ⊥.
        
        Args:
            facts: Lista di fatti
            
        Returns:
            Steps di reasoning con contraddizioni rilevate
        """
        steps = []
        
        for i, fact1 in enumerate(facts):
            for fact2 in facts[i+1:]:
                if fact1.contradicts(fact2):
                    # Crea contraddizione
                    contradiction_fact = LogicalFact(
                        statement=f"⊥ ({fact1.statement} contradicts {fact2.statement})",
                        predicate="contradiction",
                        arguments=[fact1.statement, fact2.statement],
                        fact_type=FactType.CONTRADICTION,
                        confidence=min(fact1.confidence, fact2.confidence),
                        truth_value=False,
                        derived_from=[fact1.fact_id, fact2.fact_id],
                        derivation_rule="contradiction_detection",
                        derivation_operator=LogicalOperator.CONTRADICT
                    )
                    
                    step = ReasoningStep(
                        operation=LogicalOperator.CONTRADICT,
                        reasoning_type=ReasoningType.DEDUCTIVE,
                        rule_name="contradiction_detection",
                        input_facts=[fact1, fact2],
                        output_facts=[contradiction_fact],
                        reasoning_text=f"Contradiction detected between '{fact1.statement}' and '{fact2.statement}'",
                        confidence=contradiction_fact.confidence,
                        is_valid=False,
                        validation_errors=[f"Contradiction: {fact1.statement} vs {fact2.statement}"]
                    )
                    
                    steps.append(step)
        
        return steps
    
    def _extract_predicate(self, statement: str) -> str:
        """Estrae il predicato principale da una statement."""
        # Rimuovi negazioni
        clean_statement = statement.replace("¬", "").replace("not ", "").strip()
        
        # Pattern per predicati comuni
        patterns = [
            r'^(\w+)\(',           # function(args)
            r'^(\w+)\s+is\s+',     # X is Y
            r'^(\w+)\s+has\s+',    # X has Y
            r'^(\w+)\s+can\s+',    # X can Y
            r'^(\w+)',             # Prima parola
        ]
        
        for pattern in patterns:
            match = re.search(pattern, clean_statement, re.IGNORECASE)
            if match:
                return match.group(1).lower()
        
        return "unknown"
    
    def _extract_arguments(self, statement: str) -> List[str]:
        """Estrae gli argomenti da una statement."""
        # Pattern per argomenti in parentesi
        paren_match = re.search(r'\(([^)]+)\)', statement)
        if paren_match:
            args_str = paren_match.group(1)
            return [arg.strip() for arg in args_str.split(',')]
        
        # Pattern per soggetto-predicato-oggetto
        words = statement.split()
        if len(words) >= 3:
            return [words[0], words[-1]]  # Primo e ultimo come argomenti
        
        return []
    
    def _load_inference_rules(self) -> Dict[str, callable]:
        """Carica le regole di inferenza."""
        return {
            "modus_ponens": self.modus_ponens,
            "modus_tollens": self.modus_tollens,
            "conjunction_introduction": self.conjunction_introduction,
            "conjunction_elimination": self.conjunction_elimination,
            "contradiction_detection": self.contradiction_detection,
        }
    
    def _load_pattern_matchers(self) -> Dict[str, str]:
        """Carica pattern matchers per riconoscimento strutture logiche."""
        return {
            "implication": r'(.+?)\s*(?:⇒|implies)\s*(.+)',
            "conjunction": r'(.+?)\s*∧\s*(.+)',
            "disjunction": r'(.+?)\s*∨\s*(.+)',
            "negation": r'¬(.+)|not\s+(.+)',
            "equivalence": r'(.+?)\s*⇔\s*(.+)',
            "quantifier_exists": r'∃\s*(\w+)\s*(.+)',
            "quantifier_forall": r'∀\s*(\w+)\s*(.+)',
        }
