"""
NEUROGLYPH Sandbox Structures
Strutture dati per esecuzione sicura del codice
"""

import time
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timezone


class ExecutionStatus(Enum):
    """Stati di esecuzione del sandbox."""
    PENDING = "pending"           # In attesa di esecuzione
    RUNNING = "running"           # In esecuzione
    COMPLETED = "completed"       # Completato con successo
    FAILED = "failed"            # Fallito con errore
    TIMEOUT = "timeout"          # Timeout raggiunto
    KILLED = "killed"            # Terminato forzatamente
    BLOCKED = "blocked"          # Bloccato per sicurezza


class SecurityLevel(Enum):
    """Livelli di sicurezza del sandbox."""
    MINIMAL = "minimal"          # Sicurezza minima
    STANDARD = "standard"        # Sicurezza standard
    HIGH = "high"               # Sicurezza alta
    MAXIMUM = "maximum"         # Sicurezza massima


class ExecutionMode(Enum):
    """Modalità di esecuzione."""
    EVAL = "eval"               # Valutazione espressione
    EXEC = "exec"               # Esecuzione statement
    COMPILE = "compile"         # Solo compilazione
    VALIDATE = "validate"       # Solo validazione


class ResourceType(Enum):
    """Tipi di risorse monitorate."""
    CPU_TIME = "cpu_time"       # Tempo CPU
    MEMORY = "memory"           # Memoria RAM
    DISK_IO = "disk_io"         # I/O disco
    NETWORK = "network"         # Accesso rete
    FILE_SYSTEM = "file_system" # Accesso filesystem


@dataclass
class ResourceLimits:
    """Limiti delle risorse per l'esecuzione."""
    
    # Tempo
    max_execution_time: float = 5.0      # secondi
    max_cpu_time: float = 3.0            # secondi CPU
    
    # Memoria
    max_memory_mb: int = 100              # MB
    max_stack_depth: int = 100            # profondità stack
    
    # I/O
    max_output_size: int = 10240          # bytes (10KB)
    max_file_operations: int = 0          # numero operazioni file
    
    # Rete
    allow_network: bool = False           # accesso rete
    allow_imports: bool = True            # import moduli
    
    # Filesystem
    allow_file_read: bool = False         # lettura file
    allow_file_write: bool = False        # scrittura file
    
    # Sicurezza
    allow_exec: bool = False              # exec() e eval()
    allow_subprocess: bool = False        # subprocess
    allow_dangerous_builtins: bool = False # __import__, open, etc.


@dataclass
class ExecutionContext:
    """Contesto di esecuzione del sandbox."""
    
    # Codice
    code: str                             # Codice da eseguire
    mode: ExecutionMode = ExecutionMode.EXEC
    
    # Ambiente
    globals_dict: Dict[str, Any] = field(default_factory=dict)
    locals_dict: Dict[str, Any] = field(default_factory=dict)
    
    # Input/Output
    stdin_data: str = ""                  # Input per stdin
    capture_stdout: bool = True           # Cattura stdout
    capture_stderr: bool = True           # Cattura stderr
    
    # Sicurezza
    security_level: SecurityLevel = SecurityLevel.STANDARD
    resource_limits: ResourceLimits = field(default_factory=ResourceLimits)
    
    # Metadati
    execution_id: str = field(default_factory=lambda: str(time.time()))
    description: str = ""
    tags: List[str] = field(default_factory=list)


@dataclass
class ResourceUsage:
    """Utilizzo delle risorse durante l'esecuzione."""
    
    # Tempo
    execution_time: float = 0.0           # Tempo totale esecuzione
    cpu_time: float = 0.0                 # Tempo CPU utilizzato
    
    # Memoria
    peak_memory_mb: float = 0.0           # Picco memoria utilizzata
    final_memory_mb: float = 0.0          # Memoria finale
    
    # I/O
    stdout_size: int = 0                  # Dimensione output stdout
    stderr_size: int = 0                  # Dimensione output stderr
    file_operations: int = 0              # Numero operazioni file
    
    # Chiamate
    function_calls: int = 0               # Numero chiamate funzione
    builtin_calls: int = 0                # Numero chiamate builtin
    import_calls: int = 0                 # Numero import
    
    # Sicurezza
    security_violations: int = 0          # Violazioni sicurezza
    blocked_operations: List[str] = field(default_factory=list)


@dataclass
class ExecutionResult:
    """Risultato dell'esecuzione nel sandbox."""
    
    # Stato
    status: ExecutionStatus = ExecutionStatus.PENDING
    success: bool = False
    
    # Output
    result: Any = None                    # Risultato dell'esecuzione
    stdout: str = ""                      # Output stdout
    stderr: str = ""                      # Output stderr
    
    # Errori
    error_type: Optional[str] = None      # Tipo errore
    error_message: str = ""               # Messaggio errore
    traceback: str = ""                   # Traceback completo
    
    # Risorse
    resource_usage: ResourceUsage = field(default_factory=ResourceUsage)
    
    # Temporali
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    end_time: Optional[datetime] = None
    
    # Metadati
    execution_context: Optional[ExecutionContext] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def finalize(self):
        """Finalizza il risultato dell'esecuzione."""
        self.end_time = datetime.now(timezone.utc)
        if self.start_time and self.end_time:
            self.resource_usage.execution_time = (
                self.end_time - self.start_time
            ).total_seconds()
        
        # Determina successo
        self.success = (
            self.status == ExecutionStatus.COMPLETED and
            self.error_type is None
        )
    
    @property
    def duration(self) -> float:
        """Durata dell'esecuzione in secondi."""
        return self.resource_usage.execution_time
    
    @property
    def has_output(self) -> bool:
        """Verifica se ha prodotto output."""
        return bool(self.stdout or self.stderr or self.result is not None)
    
    @property
    def exceeded_limits(self) -> bool:
        """Verifica se ha superato i limiti delle risorse."""
        if not self.execution_context:
            return False
        
        limits = self.execution_context.resource_limits
        usage = self.resource_usage
        
        return (
            usage.execution_time > limits.max_execution_time or
            usage.cpu_time > limits.max_cpu_time or
            usage.peak_memory_mb > limits.max_memory_mb or
            usage.stdout_size > limits.max_output_size or
            usage.file_operations > limits.max_file_operations
        )


@dataclass
class SandboxConfig:
    """Configurazione del sandbox."""
    
    # Sicurezza di default
    default_security_level: SecurityLevel = SecurityLevel.STANDARD
    default_resource_limits: ResourceLimits = field(default_factory=ResourceLimits)
    
    # Moduli consentiti
    allowed_modules: List[str] = field(default_factory=lambda: [
        "math", "random", "datetime", "json", "re", "string",
        "collections", "itertools", "functools", "operator"
    ])
    
    # Moduli bloccati
    blocked_modules: List[str] = field(default_factory=lambda: [
        "os", "sys", "subprocess", "socket", "urllib", "requests",
        "shutil", "glob", "tempfile", "pickle", "marshal"
    ])
    
    # Builtin consentiti
    allowed_builtins: List[str] = field(default_factory=lambda: [
        "abs", "all", "any", "bin", "bool", "chr", "dict", "dir",
        "enumerate", "filter", "float", "format", "hex", "int",
        "len", "list", "map", "max", "min", "oct", "ord", "pow",
        "range", "reversed", "round", "set", "sorted", "str",
        "sum", "tuple", "type", "zip", "print"
    ])
    
    # Builtin bloccati
    blocked_builtins: List[str] = field(default_factory=lambda: [
        "__import__", "compile", "eval", "exec", "globals", "locals",
        "open", "input", "print", "vars", "dir", "getattr", "setattr",
        "delattr", "hasattr"
    ])
    
    # Timeout
    default_timeout: float = 5.0          # secondi
    max_timeout: float = 30.0             # secondi massimi
    
    # Memoria
    default_memory_limit: int = 100       # MB
    max_memory_limit: int = 500           # MB massimi
    
    # Output
    max_output_lines: int = 1000          # linee output massime
    max_traceback_lines: int = 50         # linee traceback massime
    
    # Logging
    enable_execution_logging: bool = True
    log_resource_usage: bool = True
    log_security_violations: bool = True


@dataclass
class SecurityViolation:
    """Violazione di sicurezza rilevata."""
    
    # Identificazione
    violation_type: str                   # Tipo violazione
    severity: str                         # Severità (low, medium, high, critical)
    
    # Descrizione
    message: str                          # Messaggio descrittivo
    operation: str = ""                   # Operazione tentata
    
    # Contesto
    code_line: Optional[str] = None       # Linea di codice
    line_number: Optional[int] = None     # Numero linea
    function_name: Optional[str] = None   # Nome funzione
    
    # Azione
    action_taken: str = "blocked"         # Azione intrapresa
    
    # Temporali
    detected_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)
