"""
NEUROGLYPH Reasoning Structures
Strutture dati e operatori per il reasoning simbolico
"""

import time
import uuid
from enum import Enum
from typing import Dict, List, Set, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timezone


class LogicalOperator(Enum):
    """Operatori logici simbolici."""
    DEDUCE = "⊢"          # Deduzione (entailment)
    AND = "∧"             # Congiunzione
    OR = "∨"              # Disgiunzione  
    NOT = "¬"             # Negazione
    IMPLIES = "⇒"         # Implicazione
    EQUIVALENT = "⇔"      # Equivalenza
    EXISTS = "∃"          # Quantificatore esistenziale
    FORALL = "∀"          # Quantificatore universale
    ASSUME = "⊨"          # Assunzione
    CONTRADICT = "⊥"      # Contraddizione


class ReasoningType(Enum):
    """Tipi di reasoning."""
    DEDUCTIVE = "deductive"           # Deduttivo (da generale a specifico)
    INDUCTIVE = "inductive"           # Induttivo (da specifico a generale)
    ABDUCTIVE = "abductive"           # Abduttivo (migliore spiegazione)
    ANALOGICAL = "analogical"         # Per analogia
    CAUSAL = "causal"                 # Causale
    TEMPORAL = "temporal"             # Temporale
    SPATIAL = "spatial"               # Spaziale
    PROBABILISTIC = "probabilistic"   # Probabilistico


class FactType(Enum):
    """Tipi di fatti nel reasoning."""
    PREMISE = "premise"               # Premessa iniziale
    ASSUMPTION = "assumption"         # Assunzione
    DERIVED = "derived"               # Derivato da reasoning
    OBSERVATION = "observation"       # Osservazione empirica
    HYPOTHESIS = "hypothesis"         # Ipotesi
    CONCLUSION = "conclusion"         # Conclusione finale
    CONTRADICTION = "contradiction"   # Contraddizione rilevata


@dataclass
class LogicalFact:
    """Fatto logico nel reasoning."""

    # Contenuto (parametri richiesti)
    statement: str                    # Affermazione logica
    predicate: str                    # Predicato principale

    # Identificatori (parametri opzionali)
    fact_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    arguments: List[str] = field(default_factory=list)
    
    # Metadati logici
    fact_type: FactType = FactType.PREMISE
    confidence: float = 1.0           # 0.0-1.0
    truth_value: Optional[bool] = None
    
    # Derivazione
    derived_from: List[str] = field(default_factory=list)  # IDs fatti padre
    derivation_rule: Optional[str] = None
    derivation_operator: Optional[LogicalOperator] = None
    
    # Temporali
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_used: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validazione post-inizializzazione."""
        self.confidence = max(0.0, min(1.0, self.confidence))
    
    @property
    def is_negation(self) -> bool:
        """Verifica se è una negazione."""
        return self.statement.startswith("¬") or "not " in self.statement.lower()
    
    @property
    def base_statement(self) -> str:
        """Statement senza negazione."""
        if self.statement.startswith("¬"):
            return self.statement[1:].strip()
        elif self.statement.lower().startswith("not "):
            return self.statement[4:].strip()
        return self.statement
    
    def negate(self) -> 'LogicalFact':
        """Crea la negazione di questo fatto."""
        if self.is_negation:
            # Doppia negazione = affermazione
            new_statement = self.base_statement
        else:
            new_statement = f"¬{self.statement}"
        
        return LogicalFact(
            statement=new_statement,
            predicate=self.predicate,
            arguments=self.arguments.copy(),
            fact_type=self.fact_type,
            confidence=self.confidence,
            truth_value=not self.truth_value if self.truth_value is not None else None,
            metadata=self.metadata.copy()
        )
    
    def contradicts(self, other: 'LogicalFact') -> bool:
        """Verifica se contraddice un altro fatto."""
        # Stesso predicato e argomenti ma valori di verità opposti
        if (self.predicate == other.predicate and 
            self.arguments == other.arguments):
            
            # Uno è negazione dell'altro
            if self.is_negation != other.is_negation:
                return self.base_statement == other.base_statement
            
            # Valori di verità espliciti opposti
            if (self.truth_value is not None and 
                other.truth_value is not None):
                return self.truth_value != other.truth_value
        
        return False


@dataclass
class ReasoningStep:
    """Singolo step di reasoning."""

    # Operazione (parametri richiesti)
    operation: LogicalOperator

    # Identificatori (parametri opzionali)
    step_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    step_number: int = 0
    reasoning_type: ReasoningType = ReasoningType.DEDUCTIVE
    rule_name: str = ""
    
    # Input/Output
    input_facts: List[LogicalFact] = field(default_factory=list)
    output_facts: List[LogicalFact] = field(default_factory=list)
    
    # Reasoning
    reasoning_text: str = ""
    confidence: float = 1.0
    
    # Validazione
    is_valid: bool = True
    validation_errors: List[str] = field(default_factory=list)
    
    # Temporali
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    execution_time: float = 0.0
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validazione post-inizializzazione."""
        self.confidence = max(0.0, min(1.0, self.confidence))
    
    @property
    def input_fact_ids(self) -> List[str]:
        """IDs dei fatti di input."""
        return [fact.fact_id for fact in self.input_facts]
    
    @property
    def output_fact_ids(self) -> List[str]:
        """IDs dei fatti di output."""
        return [fact.fact_id for fact in self.output_facts]


@dataclass
class ReasoningPath:
    """Percorso di reasoning (branch nel Tree-of-Thought)."""
    
    # Identificatori
    path_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    parent_path_id: Optional[str] = None
    
    # Steps
    steps: List[ReasoningStep] = field(default_factory=list)
    
    # Stato
    is_active: bool = True
    is_complete: bool = False
    has_contradiction: bool = False
    
    # Scoring
    confidence_score: float = 1.0
    plausibility_score: float = 1.0
    coherence_score: float = 1.0
    
    # Fatti correnti
    current_facts: List[LogicalFact] = field(default_factory=list)
    
    # Temporali
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_step(self, step: ReasoningStep):
        """Aggiunge step al percorso."""
        step.step_number = len(self.steps) + 1
        self.steps.append(step)
        
        # Aggiorna fatti correnti
        for fact in step.output_facts:
            if fact not in self.current_facts:
                self.current_facts.append(fact)
        
        # Aggiorna scores
        self._update_scores()
        self.last_updated = datetime.now(timezone.utc)
    
    def _update_scores(self):
        """Aggiorna scores del percorso."""
        if not self.steps:
            return
        
        # Confidence = media confidence degli steps
        total_confidence = sum(step.confidence for step in self.steps)
        self.confidence_score = total_confidence / len(self.steps)
        
        # Plausibility = penalizza percorsi lunghi
        length_penalty = max(0.1, 1.0 - (len(self.steps) * 0.1))
        self.plausibility_score = self.confidence_score * length_penalty
        
        # Coherence = penalizza contraddizioni
        contradiction_penalty = 0.5 if self.has_contradiction else 1.0
        self.coherence_score = self.plausibility_score * contradiction_penalty
    
    @property
    def depth(self) -> int:
        """Profondità del percorso."""
        return len(self.steps)
    
    @property
    def overall_score(self) -> float:
        """Score complessivo del percorso."""
        return (self.confidence_score * 0.4 + 
                self.plausibility_score * 0.3 + 
                self.coherence_score * 0.3)


@dataclass
class ReasoningGraph:
    """Grafo di reasoning Tree-of-Thought."""
    
    # Identificatori
    graph_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    
    # Percorsi
    paths: List[ReasoningPath] = field(default_factory=list)
    active_paths: List[str] = field(default_factory=list)  # IDs percorsi attivi
    
    # Fatti globali
    initial_facts: List[LogicalFact] = field(default_factory=list)
    all_facts: List[LogicalFact] = field(default_factory=list)
    contradictions: List[Tuple[str, str]] = field(default_factory=list)  # Coppie fact IDs
    
    # Configurazione
    max_depth: int = 8
    max_paths: int = 5
    min_confidence: float = 0.1
    
    # Stato
    is_complete: bool = False
    best_path_id: Optional[str] = None
    
    # Temporali
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_initial_facts(self, facts: List[LogicalFact]):
        """Aggiunge fatti iniziali."""
        self.initial_facts.extend(facts)
        self.all_facts.extend(facts)
        
        # Crea percorso iniziale
        initial_path = ReasoningPath()
        initial_path.current_facts = facts.copy()
        self.paths.append(initial_path)
        self.active_paths.append(initial_path.path_id)
    
    def add_path(self, path: ReasoningPath):
        """Aggiunge percorso al grafo."""
        self.paths.append(path)
        if path.is_active:
            self.active_paths.append(path.path_id)
        
        # Aggiorna fatti globali
        for fact in path.current_facts:
            if fact not in self.all_facts:
                self.all_facts.append(fact)
        
        self.last_updated = datetime.now(timezone.utc)
    
    def get_path(self, path_id: str) -> Optional[ReasoningPath]:
        """Ottiene percorso per ID."""
        for path in self.paths:
            if path.path_id == path_id:
                return path
        return None
    
    def get_active_paths(self) -> List[ReasoningPath]:
        """Ottiene percorsi attivi."""
        return [path for path in self.paths if path.path_id in self.active_paths]
    
    def prune_paths(self):
        """Pota percorsi con score basso."""
        active_paths = self.get_active_paths()
        
        # Ordina per score
        active_paths.sort(key=lambda p: p.overall_score, reverse=True)
        
        # Mantieni solo i migliori
        keep_paths = active_paths[:self.max_paths]
        
        # Aggiorna active_paths
        self.active_paths = [p.path_id for p in keep_paths]
        
        # Disattiva gli altri
        for path in self.paths:
            if path.path_id not in self.active_paths:
                path.is_active = False
    
    def detect_contradictions(self):
        """Rileva contraddizioni tra fatti."""
        self.contradictions.clear()
        
        for i, fact1 in enumerate(self.all_facts):
            for j, fact2 in enumerate(self.all_facts[i+1:], i+1):
                if fact1.contradicts(fact2):
                    self.contradictions.append((fact1.fact_id, fact2.fact_id))
        
        # Marca percorsi con contraddizioni
        for path in self.paths:
            path.has_contradiction = False
            for fact in path.current_facts:
                for contradiction in self.contradictions:
                    if fact.fact_id in contradiction:
                        path.has_contradiction = True
                        break
    
    def get_best_path(self) -> Optional[ReasoningPath]:
        """Ottiene il percorso migliore."""
        if not self.paths:
            return None
        
        best_path = max(self.paths, key=lambda p: p.overall_score)
        self.best_path_id = best_path.path_id
        return best_path
    
    @property
    def total_facts(self) -> int:
        """Numero totale di fatti."""
        return len(self.all_facts)
    
    @property
    def total_steps(self) -> int:
        """Numero totale di steps."""
        return sum(len(path.steps) for path in self.paths)
    
    @property
    def max_depth_reached(self) -> int:
        """Profondità massima raggiunta."""
        return max((path.depth for path in self.paths), default=0)
    
    @property
    def has_contradictions(self) -> bool:
        """Verifica se ci sono contraddizioni."""
        return len(self.contradictions) > 0
