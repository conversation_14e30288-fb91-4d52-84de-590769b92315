"""
FASE 4.0.2 - Performance Optimizer per NEUROGLYPH Patch Engine

Ottimizzazioni per raggiungere target <10ms patch generation:
- Cache strategie compilate
- Batch processing
- Lazy loading
- Memory pooling
"""

import time
import functools
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque

from .patcher_structures import ErrorAnalysis, PatchCandidate


@dataclass
class PerformanceMetrics:
    """Metriche di performance per monitoring."""
    total_calls: int = 0
    total_time_ms: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    avg_latency_ms: float = 0.0
    p95_latency_ms: float = 0.0
    
    def update(self, latency_ms: float, cache_hit: bool = False):
        """Aggiorna metriche con nuova misurazione."""
        self.total_calls += 1
        self.total_time_ms += latency_ms
        self.avg_latency_ms = self.total_time_ms / self.total_calls
        
        if cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1


class StrategyCache:
    """Cache LRU per strategie compilate."""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, List[Tuple[str, float]]] = {}
        self.access_order = deque()
        
    def get(self, pattern: str) -> Optional[List[Tuple[str, float]]]:
        """Ottieni strategie dal cache."""
        if pattern in self.cache:
            # Sposta in fondo per LRU
            self.access_order.remove(pattern)
            self.access_order.append(pattern)
            return self.cache[pattern]
        return None
    
    def put(self, pattern: str, strategies: List[Tuple[str, float]]):
        """Inserisci strategie nel cache."""
        if len(self.cache) >= self.max_size:
            # Rimuovi il meno recentemente usato
            oldest = self.access_order.popleft()
            del self.cache[oldest]
        
        self.cache[pattern] = strategies
        self.access_order.append(pattern)
    
    def clear(self):
        """Svuota cache."""
        self.cache.clear()
        self.access_order.clear()


class BatchProcessor:
    """Processore batch per multiple error analyses."""
    
    def __init__(self, batch_size: int = 10):
        self.batch_size = batch_size
        self.pending_batch: List[ErrorAnalysis] = []
        
    def add_to_batch(self, error_analysis: ErrorAnalysis) -> bool:
        """Aggiunge analisi al batch. Ritorna True se batch è pronto."""
        self.pending_batch.append(error_analysis)
        return len(self.pending_batch) >= self.batch_size
    
    def process_batch(self, processor_func) -> List[List[PatchCandidate]]:
        """Processa batch corrente."""
        if not self.pending_batch:
            return []
        
        results = []
        for analysis in self.pending_batch:
            results.append(processor_func(analysis))
        
        self.pending_batch.clear()
        return results
    
    def flush(self, processor_func) -> List[List[PatchCandidate]]:
        """Forza processing del batch anche se non completo."""
        return self.process_batch(processor_func)


class PerformanceOptimizer:
    """
    FASE 4.0.2: Ottimizzatore performance per PatchGenerator.
    
    Target: <10ms patch generation
    """
    
    def __init__(self):
        self.strategy_cache = StrategyCache(max_size=1000)
        self.batch_processor = BatchProcessor(batch_size=10)
        self.metrics = PerformanceMetrics()
        self.pattern_regex_cache: Dict[str, Any] = {}
        self.template_cache: Dict[str, Dict] = {}
        
        # Precompila regex patterns comuni
        self._precompile_patterns()
        
    def _precompile_patterns(self):
        """Precompila regex patterns per performance."""
        import re
        
        common_patterns = {
            'variable_name': re.compile(r"name '(\w+)' is not defined"),
            'attribute_error': re.compile(r"'(\w+)' object has no attribute '(\w+)'"),
            'module_name': re.compile(r"No module named ['\"]([^'\"]+)['\"]"),
            'import_name': re.compile(r"cannot import name ['\"]([^'\"]+)['\"]"),
            'division_by_zero': re.compile(r"division by zero"),
            'index_out_of_range': re.compile(r"list index out of range"),
            'key_error': re.compile(r"KeyError.*'(\w+)'"),
        }
        
        self.pattern_regex_cache.update(common_patterns)
        print(f"🚀 Precompiled {len(common_patterns)} regex patterns for performance")
    
    @functools.lru_cache(maxsize=1000)
    def get_cached_strategies(self, pattern: str) -> Tuple[Tuple[str, float], ...]:
        """Cache LRU per strategie. Usa tuple per hashability."""
        from .patch_generator import STRATEGY_MAP
        strategies = STRATEGY_MAP.get(pattern, STRATEGY_MAP['default'])
        return tuple(strategies)
    
    def extract_metadata_fast(self, error_message: str, strategy_name: str) -> Dict[str, Any]:
        """Estrazione metadata ottimizzata con regex precompilate."""
        metadata = {}
        
        if strategy_name in ["add_name_stub", "wrap_try_except"]:
            if 'variable_name' in self.pattern_regex_cache:
                match = self.pattern_regex_cache['variable_name'].search(error_message)
                if match:
                    metadata["variable_name"] = match.group(1)
        
        elif strategy_name == "safe_attribute_access":
            if 'attribute_error' in self.pattern_regex_cache:
                match = self.pattern_regex_cache['attribute_error'].search(error_message)
                if match:
                    metadata.update({
                        "object_type": match.group(1),
                        "attribute_name": match.group(2)
                    })
        
        elif strategy_name in ["try_except_import", "add_import_stub", "comment_out_import"]:
            # Prova prima module_name, poi import_name
            for pattern_name in ['module_name', 'import_name']:
                if pattern_name in self.pattern_regex_cache:
                    match = self.pattern_regex_cache[pattern_name].search(error_message)
                    if match:
                        metadata["module_name"] = match.group(1)
                        break
        
        return metadata
    
    def time_function(self, func):
        """Decorator per misurare performance."""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            result = func(*args, **kwargs)
            end_time = time.perf_counter()
            
            latency_ms = (end_time - start_time) * 1000
            self.metrics.update(latency_ms)
            
            return result
        return wrapper
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Genera report performance dettagliato."""
        cache_hit_rate = (self.metrics.cache_hits / 
                         max(1, self.metrics.cache_hits + self.metrics.cache_misses)) * 100
        
        return {
            "total_calls": self.metrics.total_calls,
            "avg_latency_ms": round(self.metrics.avg_latency_ms, 2),
            "total_time_ms": round(self.metrics.total_time_ms, 2),
            "cache_hit_rate": round(cache_hit_rate, 1),
            "cache_size": len(self.strategy_cache.cache),
            "target_met": self.metrics.avg_latency_ms < 10.0,
            "performance_grade": self._calculate_performance_grade()
        }
    
    def _calculate_performance_grade(self) -> str:
        """Calcola grade performance basato su latency."""
        avg_latency = self.metrics.avg_latency_ms
        
        if avg_latency < 5.0:
            return "A+ (Excellent)"
        elif avg_latency < 10.0:
            return "A (Target Met)"
        elif avg_latency < 20.0:
            return "B (Good)"
        elif avg_latency < 50.0:
            return "C (Acceptable)"
        else:
            return "D (Needs Improvement)"
    
    def optimize_patch_generation(self, error_analysis: ErrorAnalysis, 
                                 base_generator_func) -> List[PatchCandidate]:
        """
        Ottimizza generazione patch con cache e batch processing.
        
        Args:
            error_analysis: Analisi errore
            base_generator_func: Funzione base di generazione
            
        Returns:
            Lista patch candidates ottimizzata
        """
        start_time = time.perf_counter()
        
        try:
            # Usa cache per strategie
            pattern = getattr(error_analysis, 'error_pattern', 'default')
            cached_strategies = self.get_cached_strategies(pattern)
            
            # Genera candidates ottimizzati
            candidates = []
            for strategy_name, base_confidence in cached_strategies:
                # Metadata extraction ottimizzata
                metadata = self.extract_metadata_fast(
                    error_analysis.error_message, strategy_name
                )
                
                # Crea candidate
                candidate = PatchCandidate(
                    patch_type="CODE_FIX",
                    target_error_id=error_analysis.error_id,
                    patch_description=f"Apply strategy: {strategy_name}",
                    patch_strategy=strategy_name,
                    confidence=base_confidence,
                    risk_level="low"
                )
                
                if metadata:
                    candidate.metadata = metadata
                
                candidates.append(candidate)
                
                # Early exit per alta confidence
                if base_confidence >= 0.8:
                    break
            
            return candidates
            
        finally:
            # Registra metriche
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            self.metrics.update(latency_ms, cache_hit=True)
