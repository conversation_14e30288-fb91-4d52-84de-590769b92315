"""
NEUROGLYPH Self Check
Modulo di validazione end-to-end per quality control del reasoning
"""

import time
from typing import List, Optional, Dict, Any

from .validation_structures import (
    ValidationResult, ValidationError, ValidationMetrics, ValidationConfig,
    ValidationLevel, ValidationSeverity, ValidationErrorType
)
from .reasoning_structures import ReasoningGraph, LogicalFact, ReasoningPath
from .syntax_validator import SyntaxValidator
from .semantic_validator import SemanticValidator
from .logic_validator import LogicValidator


class NGSelfCheck:
    """
    Self-Check Engine per NEUROGLYPH.
    Coordina validazione sintattica, semantica e logica del reasoning.
    """
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        """
        Inizializza NGSelfCheck.
        
        Args:
            config: Configurazione validazione
        """
        self.config = config or ValidationConfig()
        
        # Inizializza validatori
        self.syntax_validator = SyntaxValidator(self.config)
        self.semantic_validator = SemanticValidator(self.config)
        self.logic_validator = LogicValidator(self.config)
        
        print("🔍 NGSelfCheck inizializzato")
        print(f"   - Syntax validation: {self.config.enable_ast_validation}")
        print(f"   - Semantic validation: {self.config.check_premise_existence}")
        print(f"   - Logic validation: {self.config.validate_operator_application}")
        print(f"   - Quality threshold: {self.config.min_quality_score}")
    
    def self_check(self, graph: ReasoningGraph) -> ValidationResult:
        """
        Esegue validazione completa del ReasoningGraph.
        
        Args:
            graph: Grafo di reasoning da validare
            
        Returns:
            Risultato completo della validazione
        """
        result = ValidationResult()
        start_time = time.time()
        
        print(f"🔍 Starting self-check validation")
        print(f"   - Graph ID: {graph.graph_id}")
        print(f"   - Total facts: {graph.total_facts}")
        print(f"   - Total paths: {len(graph.paths)}")
        print(f"   - Total steps: {graph.total_steps}")
        
        try:
            # Validazione sintattica
            print("   🔍 Running syntax validation...")
            syntax_errors = self.syntax_validator.validate_syntax(graph)
            for error in syntax_errors:
                result.add_error(error)
            
            # Validazione semantica
            print("   🔍 Running semantic validation...")
            semantic_errors = self.semantic_validator.validate_semantic(graph)
            for error in semantic_errors:
                result.add_error(error)
            
            # Validazione logica
            print("   🔍 Running logic validation...")
            logic_errors = self.logic_validator.validate_logic(graph)
            for error in logic_errors:
                result.add_error(error)
            
            # Calcola metriche di qualità
            print("   📊 Calculating quality metrics...")
            result.metrics = self._calculate_metrics(graph, result)
            
            # Finalizza risultato
            result.finalize()
            
        except Exception as e:
            error = ValidationError(
                error_type=ValidationErrorType.AST_PARSE_ERROR,
                severity=ValidationSeverity.CRITICAL,
                level=ValidationLevel.SYNTAX,
                message=f"Self-check validation failed: {str(e)}",
                context={"exception": str(e)}
            )
            result.add_error(error)
            result.finalize()
        
        validation_time = time.time() - start_time
        
        print(f"🔍 Self-check completed in {validation_time:.3f}s")
        print(f"   - Total errors: {result.total_errors}")
        print(f"   - Critical errors: {result.critical_errors}")
        print(f"   - Overall quality: {result.metrics.overall_quality_score:.3f}")
        print(f"   - Validation passed: {result.validation_passed}")
        
        return result
    
    def validate_syntax(self, graph: ReasoningGraph) -> bool:
        """
        Validazione sintattica rapida.
        
        Args:
            graph: Grafo da validare
            
        Returns:
            True se sintassi valida
        """
        errors = self.syntax_validator.validate_syntax(graph)
        critical_errors = [e for e in errors if e.severity == ValidationSeverity.CRITICAL]
        return len(critical_errors) == 0
    
    def validate_semantic(self, graph: ReasoningGraph) -> bool:
        """
        Validazione semantica rapida.
        
        Args:
            graph: Grafo da validare
            
        Returns:
            True se semantica valida
        """
        errors = self.semantic_validator.validate_semantic(graph)
        critical_errors = [e for e in errors if e.severity == ValidationSeverity.CRITICAL]
        return len(critical_errors) == 0
    
    def validate_logic(self, graph: ReasoningGraph) -> bool:
        """
        Validazione logica rapida.
        
        Args:
            graph: Grafo da validare
            
        Returns:
            True se logica valida
        """
        errors = self.logic_validator.validate_logic(graph)
        critical_errors = [e for e in errors if e.severity == ValidationSeverity.CRITICAL]
        return len(critical_errors) == 0
    
    def _calculate_metrics(self, graph: ReasoningGraph, result: ValidationResult) -> ValidationMetrics:
        """Calcola metriche di qualità."""
        metrics = ValidationMetrics()
        
        # Coverage metrics
        if graph.initial_facts:
            # Calcola coverage: % fatti iniziali riutilizzati
            initial_fact_ids = {fact.fact_id for fact in graph.initial_facts}
            used_fact_ids = set()
            
            for path in graph.paths:
                for step in path.steps:
                    for input_fact in step.input_facts:
                        used_fact_ids.add(input_fact.fact_id)
            
            used_initial_facts = initial_fact_ids & used_fact_ids
            metrics.coverage_percentage = (len(used_initial_facts) / len(initial_fact_ids)) * 100.0
            
            # Premise utilization
            premise_facts = [f for f in graph.initial_facts if f.fact_type.value == "premise"]
            if premise_facts:
                used_premises = sum(1 for f in premise_facts if f.fact_id in used_fact_ids)
                metrics.premise_utilization = (used_premises / len(premise_facts)) * 100.0
            
            # Derived facts ratio
            derived_facts = [f for f in graph.all_facts if f.fact_type.value == "derived"]
            metrics.derived_facts_ratio = len(derived_facts) / len(graph.initial_facts)
        
        # Depth consistency
        if graph.paths:
            metrics.max_depth_reached = max(path.depth for path in graph.paths)
            metrics.avg_path_depth = sum(path.depth for path in graph.paths) / len(graph.paths)
            
            # Depth consistency score (penalizza varianza eccessiva)
            depths = [path.depth for path in graph.paths]
            if len(depths) > 1:
                avg_depth = sum(depths) / len(depths)
                variance = sum((d - avg_depth) ** 2 for d in depths) / len(depths)
                metrics.depth_consistency_score = max(0.0, 1.0 - (variance / 10.0))
        
        # Confidence coherence
        all_confidences = []
        for fact in graph.all_facts:
            all_confidences.append(fact.confidence)
        for path in graph.paths:
            for step in path.steps:
                all_confidences.append(step.confidence)
        
        if all_confidences:
            metrics.min_confidence = min(all_confidences)
            metrics.avg_confidence = sum(all_confidences) / len(all_confidences)
            
            # Confidence decay rate
            if len(all_confidences) > 1:
                sorted_conf = sorted(all_confidences, reverse=True)
                decay_rate = (sorted_conf[0] - sorted_conf[-1]) / len(sorted_conf)
                metrics.confidence_decay_rate = decay_rate
                
                # Coherence score (penalizza decay eccessivo)
                metrics.confidence_coherence_score = max(0.0, 1.0 - decay_rate)
        
        # Logic metrics
        for path in graph.paths:
            for step in path.steps:
                if step.is_valid:
                    metrics.valid_inferences += 1
                else:
                    metrics.invalid_inferences += 1
        
        metrics.contradictions_detected = len(graph.contradictions)
        
        # Circular references (da logic validator)
        circular_errors = [e for e in result.logic_errors 
                          if e.error_type.value == "circular_inference"]
        metrics.circular_references = len(circular_errors)
        
        # Quality scores (già calcolati in ValidationResult.finalize())
        # Qui possiamo aggiungere calcoli aggiuntivi se necessario
        
        return metrics
    
    def generate_report(self, result: ValidationResult) -> str:
        """
        Genera report dettagliato della validazione.
        
        Args:
            result: Risultato validazione
            
        Returns:
            Report testuale
        """
        lines = []
        lines.append("🔍 NEUROGLYPH SELF-CHECK VALIDATION REPORT")
        lines.append("=" * 80)
        
        # Summary
        lines.append(f"📊 VALIDATION SUMMARY:")
        lines.append(f"   - Overall Status: {'✅ PASSED' if result.validation_passed else '❌ FAILED'}")
        lines.append(f"   - Quality Score: {result.metrics.overall_quality_score:.3f}")
        lines.append(f"   - Duration: {result.validation_duration:.3f}s")
        lines.append(f"   - Total Errors: {result.total_errors}")
        lines.append(f"   - Critical Errors: {result.critical_errors}")
        
        # Error breakdown
        lines.append(f"\n📋 ERROR BREAKDOWN:")
        error_summary = result.error_summary
        lines.append(f"   - Syntax Errors: {error_summary['syntax']}")
        lines.append(f"   - Semantic Errors: {error_summary['semantic']}")
        lines.append(f"   - Logic Errors: {error_summary['logic']}")
        lines.append(f"   - Quality Warnings: {error_summary['quality']}")
        
        # Quality metrics
        lines.append(f"\n📈 QUALITY METRICS:")
        m = result.metrics
        lines.append(f"   - Coverage: {m.coverage_percentage:.1f}%")
        lines.append(f"   - Premise Utilization: {m.premise_utilization:.1f}%")
        lines.append(f"   - Avg Confidence: {m.avg_confidence:.3f}")
        lines.append(f"   - Max Depth: {m.max_depth_reached}")
        lines.append(f"   - Valid Inferences: {m.valid_inferences}")
        lines.append(f"   - Contradictions: {m.contradictions_detected}")
        
        # Detailed errors (if any)
        if result.total_errors > 0:
            lines.append(f"\n🚨 DETAILED ERRORS:")
            
            # Critical errors first
            critical_errors = result.get_errors_by_severity(ValidationSeverity.CRITICAL)
            if critical_errors:
                lines.append(f"\n   🔴 CRITICAL ERRORS ({len(critical_errors)}):")
                for i, error in enumerate(critical_errors[:5], 1):  # Limit to 5
                    lines.append(f"      {i}. {error.message}")
                    if error.node_id:
                        lines.append(f"         Node: {error.node_id}")
                    if error.suggestions:
                        lines.append(f"         Suggestion: {error.suggestions[0]}")
            
            # Regular errors
            regular_errors = result.get_errors_by_severity(ValidationSeverity.ERROR)
            if regular_errors:
                lines.append(f"\n   🟡 ERRORS ({len(regular_errors)}):")
                for i, error in enumerate(regular_errors[:3], 1):  # Limit to 3
                    lines.append(f"      {i}. {error.message}")
        
        # Recommendations
        lines.append(f"\n💡 RECOMMENDATIONS:")
        if result.validation_passed:
            lines.append(f"   ✅ Graph validation passed - ready for production use")
            if result.metrics.overall_quality_score < 0.8:
                lines.append(f"   📈 Consider improving quality score (current: {result.metrics.overall_quality_score:.3f})")
        else:
            lines.append(f"   ❌ Fix critical errors before proceeding")
            if result.critical_errors > 0:
                lines.append(f"   🔧 Address {result.critical_errors} critical errors first")
            if result.metrics.coverage_percentage < 50:
                lines.append(f"   📊 Improve fact coverage (current: {result.metrics.coverage_percentage:.1f}%)")
        
        return "\n".join(lines)
    
    def is_graph_valid(self, graph: ReasoningGraph) -> bool:
        """
        Verifica rapida se un grafo è valido.
        
        Args:
            graph: Grafo da verificare
            
        Returns:
            True se valido
        """
        result = self.self_check(graph)
        return result.validation_passed and result.metrics.overall_quality_score >= self.config.min_quality_score
