"""
NEUROGLYPH Cognitive Data Structures
Strutture dati per il reasoning simbolico e l'intelligenza cognitiva
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union, Tuple
from enum import Enum
import time
import uuid


class UrgencyLevel(Enum):
    """Livelli di urgenza per la prioritizzazione."""
    CRITICAL = 1.0      # Emergenza critica
    HIGH = 0.8          # Alta priorità
    MEDIUM = 0.5        # Priorità media
    LOW = 0.2           # Bassa priorità
    MINIMAL = 0.0       # Priorità minima


class RiskLevel(Enum):
    """Livelli di rischio per la valutazione sicurezza."""
    DANGEROUS = 1.0     # Operazione pericolosa
    HIGH_RISK = 0.8     # Alto rischio
    MEDIUM_RISK = 0.5   # Rischio medio
    LOW_RISK = 0.2      # Basso rischio
    SAFE = 0.0          # Operazione sicura


class DomainType(Enum):
    """Domini semantici per la classificazione."""
    SYSTEM = "system"           # Operazioni di sistema
    DATA = "data"              # Manipolazione dati
    LOGIC = "logic"            # Ragionamento logico
    MATH = "math"              # Operazioni matematiche
    TEXT = "text"              # Elaborazione testo
    CODE = "code"              # Generazione/analisi codice
    SECURITY = "security"      # Operazioni sicurezza
    NETWORK = "network"        # Operazioni rete
    FILE = "file"              # Operazioni file
    GENERAL = "general"        # Dominio generale


@dataclass
class ParsedPrompt:
    """Risultato del parsing simbolico del prompt."""
    original_text: str
    tokens: List[str]
    segments: List[Dict[str, Any]]
    intents: List[str]
    symbols: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)
    parsing_timestamp: float = field(default_factory=time.time)
    parsing_id: str = field(default_factory=lambda: str(uuid.uuid4()))


@dataclass
class PriorityVector:
    """Vettore di priorità multi-dimensionale."""
    urgency: float              # 0.0-1.0
    risk: float                 # 0.0-1.0  
    domain: DomainType
    confidence: float = 1.0     # Confidenza nella classificazione
    reasoning: str = ""         # Spiegazione del reasoning
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    
    def __post_init__(self):
        """Validazione valori."""
        self.urgency = max(0.0, min(1.0, self.urgency))
        self.risk = max(0.0, min(1.0, self.risk))
        self.confidence = max(0.0, min(1.0, self.confidence))
    
    @property
    def priority_score(self) -> float:
        """Calcola score priorità combinato."""
        # Formula: urgency * 0.6 + (1-risk) * 0.3 + confidence * 0.1
        return (self.urgency * 0.6 + 
                (1.0 - self.risk) * 0.3 + 
                self.confidence * 0.1)
    
    @property
    def urgency_level(self) -> UrgencyLevel:
        """Converte urgency in enum."""
        if self.urgency >= 0.9: return UrgencyLevel.CRITICAL
        elif self.urgency >= 0.7: return UrgencyLevel.HIGH
        elif self.urgency >= 0.4: return UrgencyLevel.MEDIUM
        elif self.urgency >= 0.1: return UrgencyLevel.LOW
        else: return UrgencyLevel.MINIMAL
    
    @property
    def risk_level(self) -> RiskLevel:
        """Converte risk in enum."""
        if self.risk >= 0.9: return RiskLevel.DANGEROUS
        elif self.risk >= 0.7: return RiskLevel.HIGH_RISK
        elif self.risk >= 0.4: return RiskLevel.MEDIUM_RISK
        elif self.risk >= 0.1: return RiskLevel.LOW_RISK
        else: return RiskLevel.SAFE


@dataclass
class MemoryContext:
    """Contesto di memoria per il reasoning."""
    symbols: List[Dict[str, Any]] = field(default_factory=list)
    episodes: List[Dict[str, Any]] = field(default_factory=list)
    errors: List[Dict[str, Any]] = field(default_factory=list)
    patterns: List[Dict[str, Any]] = field(default_factory=list)
    examples: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    retrieval_timestamp: float = field(default_factory=time.time)
    
    @property
    def total_items(self) -> int:
        """Numero totale di elementi in memoria."""
        return (len(self.symbols) + len(self.episodes) + 
                len(self.errors) + len(self.patterns) + len(self.examples))


@dataclass
class ReasoningStep:
    """Singolo step di ragionamento."""
    step_id: str
    operation: str              # Tipo operazione (deduce, assume, verify, etc.)
    input_facts: List[str]
    output_facts: List[str]
    reasoning: str
    confidence: float
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ReasoningGraph:
    """Grafo di ragionamento multi-hop."""
    graph_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    steps: List[ReasoningStep] = field(default_factory=list)
    facts: List[str] = field(default_factory=list)
    assumptions: List[str] = field(default_factory=list)
    conclusions: List[str] = field(default_factory=list)
    contradictions: List[str] = field(default_factory=list)
    confidence_score: float = 1.0
    reasoning_depth: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    creation_timestamp: float = field(default_factory=time.time)
    
    def add_step(self, step: ReasoningStep):
        """Aggiunge step di ragionamento."""
        self.steps.append(step)
        self.reasoning_depth = len(self.steps)
        # Aggiorna confidence score (media pesata)
        if self.steps:
            total_confidence = sum(s.confidence for s in self.steps)
            self.confidence_score = total_confidence / len(self.steps)
    
    def add_fact(self, fact: str):
        """Aggiunge fatto al grafo."""
        if fact not in self.facts:
            self.facts.append(fact)
    
    def add_conclusion(self, conclusion: str):
        """Aggiunge conclusione al grafo."""
        if conclusion not in self.conclusions:
            self.conclusions.append(conclusion)
    
    def add_contradiction(self, contradiction: str):
        """Aggiunge contraddizione rilevata."""
        if contradiction not in self.contradictions:
            self.contradictions.append(contradiction)
            # Riduce confidence score per contraddizioni
            self.confidence_score *= 0.8


@dataclass
class CheckResult:
    """Risultato del self-check."""
    syntax_valid: bool
    semantic_valid: bool
    logic_valid: bool
    quality_score: float        # 0.0-1.0
    issues: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    check_timestamp: float = field(default_factory=time.time)
    
    @property
    def overall_valid(self) -> bool:
        """Validità complessiva."""
        return self.syntax_valid and self.semantic_valid and self.logic_valid
    
    @property
    def confidence_level(self) -> str:
        """Livello di confidenza basato su quality_score."""
        if self.quality_score >= 0.9: return "HIGH"
        elif self.quality_score >= 0.7: return "MEDIUM"
        elif self.quality_score >= 0.5: return "LOW"
        else: return "VERY_LOW"


@dataclass
class ExecutionResult:
    """Risultato dell'esecuzione in sandbox."""
    success: bool
    output: str
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    memory_usage: int = 0
    security_issues: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_timestamp: float = field(default_factory=time.time)


@dataclass
class PatchResult:
    """Risultato del patching adattivo."""
    original_code: str
    patched_code: str
    patches_applied: List[str]
    improvement_score: float    # 0.0-1.0
    reasoning: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    patch_timestamp: float = field(default_factory=time.time)


@dataclass
class LearningExperience:
    """Esperienza di apprendimento."""
    input_context: Dict[str, Any]
    output_result: Dict[str, Any]
    success: bool
    experience_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    feedback: Optional[str] = None
    patterns_learned: List[str] = field(default_factory=list)
    knowledge_gained: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    learning_timestamp: float = field(default_factory=time.time)


@dataclass
class CognitiveState:
    """Stato cognitivo completo del sistema."""
    parsed_prompt: Optional[ParsedPrompt] = None
    priority_vector: Optional[PriorityVector] = None
    memory_context: Optional[MemoryContext] = None
    reasoning_graph: Optional[ReasoningGraph] = None
    check_result: Optional[CheckResult] = None
    execution_result: Optional[ExecutionResult] = None
    patch_result: Optional[PatchResult] = None
    learning_experiences: List[LearningExperience] = field(default_factory=list)
    
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    creation_timestamp: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def update_timestamp(self):
        """Aggiorna timestamp ultimo update."""
        self.last_update = time.time()
    
    @property
    def processing_time(self) -> float:
        """Tempo totale di processing."""
        return self.last_update - self.creation_timestamp
    
    @property
    def is_complete(self) -> bool:
        """Verifica se lo stato cognitivo è completo."""
        return all([
            self.parsed_prompt is not None,
            self.priority_vector is not None,
            self.memory_context is not None,
            self.reasoning_graph is not None
        ])
