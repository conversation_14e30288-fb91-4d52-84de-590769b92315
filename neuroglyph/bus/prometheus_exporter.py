"""
NEUROGLYPH Prometheus Exporter
Endpoint /metrics per observability
"""

import time
import asyncio
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict
from aiohttp import web
import json

logger = logging.getLogger(__name__)


@dataclass
class MetricValue:
    """Valore metrica con timestamp."""
    
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)


class PrometheusExporter:
    """
    Prometheus Exporter per NEUROGLYPH.
    
    Features:
    - Endpoint /metrics in formato Prometheus
    - Counters, Gauges, Histograms
    - Labels per dimensioni multiple
    - Integration con EventBus
    """
    
    def __init__(self, port: int = 8080):
        """
        Inizializza Prometheus exporter.
        
        Args:
            port: Porta per endpoint HTTP
        """
        self.port = port
        
        # Metriche
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, MetricValue] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        
        # Server HTTP
        self.app = None
        self.runner = None
        self.site = None
        
        # Stato
        self.is_running = False
        
        logger.info(f"📊 PrometheusExporter inizializzato su porta {port}")
    
    async def start(self):
        """Avvia server HTTP."""
        if self.is_running:
            return
        
        # Setup aiohttp app
        self.app = web.Application()
        self.app.router.add_get('/metrics', self._metrics_handler)
        self.app.router.add_get('/health', self._health_handler)
        
        # Avvia server
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(self.runner, 'localhost', self.port)
        await self.site.start()
        
        self.is_running = True
        logger.info(f"🚀 Prometheus exporter avviato su http://localhost:{self.port}/metrics")
    
    async def stop(self):
        """Ferma server HTTP."""
        if not self.is_running:
            return
        
        if self.site:
            await self.site.stop()
        
        if self.runner:
            await self.runner.cleanup()
        
        self.is_running = False
        logger.info("🛑 Prometheus exporter fermato")
    
    async def _metrics_handler(self, request):
        """Handler per endpoint /metrics."""
        try:
            metrics_text = self._format_prometheus_metrics()
            return web.Response(
                text=metrics_text,
                content_type='text/plain; version=0.0.4; charset=utf-8'
            )
        except Exception as e:
            logger.error(f"❌ Errore generazione metriche: {e}")
            return web.Response(text="Error generating metrics", status=500)
    
    async def _health_handler(self, request):
        """Handler per endpoint /health."""
        health_data = {
            'status': 'healthy' if self.is_running else 'unhealthy',
            'timestamp': time.time(),
            'metrics_count': len(self.counters) + len(self.gauges) + len(self.histograms)
        }
        return web.json_response(health_data)
    
    def _format_prometheus_metrics(self) -> str:
        """Formatta metriche in formato Prometheus."""
        lines = []
        
        # Header
        lines.append("# NEUROGLYPH Metrics")
        lines.append(f"# Generated at {time.time()}")
        lines.append("")
        
        # Counters
        for metric_name, value in self.counters.items():
            lines.append(f"# TYPE {metric_name} counter")
            lines.append(f"{metric_name} {value}")
            lines.append("")
        
        # Gauges
        for metric_name, metric_value in self.gauges.items():
            lines.append(f"# TYPE {metric_name} gauge")
            
            if metric_value.labels:
                labels_str = ','.join(f'{k}="{v}"' for k, v in metric_value.labels.items())
                lines.append(f"{metric_name}{{{labels_str}}} {metric_value.value}")
            else:
                lines.append(f"{metric_name} {metric_value.value}")
            lines.append("")
        
        # Histograms (semplificati)
        for metric_name, values in self.histograms.items():
            if not values:
                continue
                
            lines.append(f"# TYPE {metric_name} histogram")
            
            # Bucket counts (semplificato)
            buckets = [0.001, 0.01, 0.1, 1.0, 10.0, float('inf')]
            cumulative_count = 0
            
            for bucket in buckets:
                count = sum(1 for v in values if v <= bucket)
                cumulative_count = count
                bucket_str = '+Inf' if bucket == float('inf') else str(bucket)
                lines.append(f'{metric_name}_bucket{{le="{bucket_str}"}} {cumulative_count}')
            
            lines.append(f"{metric_name}_count {len(values)}")
            lines.append(f"{metric_name}_sum {sum(values)}")
            lines.append("")
        
        return '\n'.join(lines)
    
    def increment_counter(self, name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """Incrementa counter."""
        metric_key = self._make_metric_key(name, labels)
        self.counters[metric_key] += value
        
        logger.debug(f"📈 Counter {metric_key}: +{value}")
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Imposta gauge."""
        metric_key = self._make_metric_key(name, labels)
        self.gauges[metric_key] = MetricValue(value=value, labels=labels or {})
        
        logger.debug(f"📊 Gauge {metric_key}: {value}")
    
    def observe_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Osserva valore histogram."""
        metric_key = self._make_metric_key(name, labels)
        self.histograms[metric_key].append(value)
        
        # Mantieni solo ultimi 1000 valori per memoria
        if len(self.histograms[metric_key]) > 1000:
            self.histograms[metric_key] = self.histograms[metric_key][-1000:]
        
        logger.debug(f"📉 Histogram {metric_key}: {value}")
    
    def _make_metric_key(self, name: str, labels: Optional[Dict[str, str]] = None) -> str:
        """Crea chiave metrica con labels."""
        if not labels:
            return name
        
        labels_str = '_'.join(f"{k}_{v}" for k, v in sorted(labels.items()))
        return f"{name}_{labels_str}"
    
    def record_pipeline_metrics(self, pipeline_metrics: Dict[str, Any]):
        """Registra metriche pipeline."""
        try:
            # Pipeline-level metrics
            pipeline_name = pipeline_metrics.get('pipeline_name', 'unknown')
            execution_count = pipeline_metrics.get('execution_count', 0)
            is_running = pipeline_metrics.get('is_running', False)
            
            self.set_gauge('neuroglyph_pipeline_executions_total', execution_count, 
                          {'pipeline': pipeline_name})
            self.set_gauge('neuroglyph_pipeline_running', 1.0 if is_running else 0.0,
                          {'pipeline': pipeline_name})
            
            # Stage-level metrics
            stages = pipeline_metrics.get('stages', {})
            for stage_id, stage_metrics in stages.items():
                labels = {'stage': stage_id, 'pipeline': pipeline_name}
                
                self.set_gauge('neuroglyph_stage_executions_total', 
                              stage_metrics.get('executions', 0), labels)
                self.set_gauge('neuroglyph_stage_success_rate',
                              stage_metrics.get('success_rate', 0.0), labels)
                self.set_gauge('neuroglyph_stage_avg_duration_seconds',
                              stage_metrics.get('avg_duration', 0.0), labels)
                
                # Status gauge
                status = stage_metrics.get('status', 'unknown')
                status_value = {
                    'pending': 0, 'running': 1, 'completed': 2, 'failed': 3, 'skipped': 4
                }.get(status, -1)
                self.set_gauge('neuroglyph_stage_status', status_value, labels)
            
        except Exception as e:
            logger.error(f"❌ Errore registrazione metriche pipeline: {e}")
    
    def record_event_bus_metrics(self, bus_metrics: Dict[str, Any]):
        """Registra metriche event bus."""
        try:
            self.set_gauge('neuroglyph_bus_messages_published_total',
                          bus_metrics.get('messages_published', 0))
            self.set_gauge('neuroglyph_bus_messages_processed_total',
                          bus_metrics.get('messages_processed', 0))
            self.set_gauge('neuroglyph_bus_messages_failed_total',
                          bus_metrics.get('messages_failed', 0))
            self.set_gauge('neuroglyph_bus_active_topics',
                          bus_metrics.get('active_topics', 0))
            self.set_gauge('neuroglyph_bus_active_subscribers',
                          bus_metrics.get('active_subscribers', 0))
            
            # Queue sizes per topic
            queue_sizes = bus_metrics.get('queue_sizes', {})
            for topic, size in queue_sizes.items():
                self.set_gauge('neuroglyph_bus_queue_size', size, {'topic': topic})
                
        except Exception as e:
            logger.error(f"❌ Errore registrazione metriche bus: {e}")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Ottiene riassunto metriche."""
        return {
            'counters_count': len(self.counters),
            'gauges_count': len(self.gauges),
            'histograms_count': len(self.histograms),
            'is_running': self.is_running,
            'port': self.port
        }
