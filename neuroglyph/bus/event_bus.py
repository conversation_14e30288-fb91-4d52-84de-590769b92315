"""
NEUROGLYPH Event Bus
Pub/Sub in-process con asyncio.Queue + topic filtering
"""

import asyncio
import time
import json
import logging
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from collections import defaultdict
import weakref

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class EventMessage:
    """Messaggio evento immutabile."""
    
    topic: str
    payload: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    message_id: str = field(default_factory=lambda: f"msg_{int(time.time() * 1000000)}")
    source: Optional[str] = None
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Serializza a dict."""
        return {
            'topic': self.topic,
            'payload': self.payload,
            'timestamp': self.timestamp,
            'message_id': self.message_id,
            'source': self.source,
            'correlation_id': self.correlation_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EventMessage':
        """Deserializza da dict."""
        return cls(
            topic=data['topic'],
            payload=data['payload'],
            timestamp=data.get('timestamp', time.time()),
            message_id=data.get('message_id', f"msg_{int(time.time() * 1000000)}"),
            source=data.get('source'),
            correlation_id=data.get('correlation_id')
        )


class EventHandler(ABC):
    """Handler base per eventi."""
    
    @abstractmethod
    async def handle(self, message: EventMessage) -> bool:
        """
        Gestisce evento.
        
        Args:
            message: Messaggio evento
            
        Returns:
            True se gestito con successo
        """
        pass


class EventBus:
    """
    Event Bus in-process con asyncio.Queue.
    
    Features:
    - Topic-based routing
    - Multiple subscribers per topic
    - Async message handling
    - Error handling e retry
    - Metrics collection
    - Placeholder per Kafka/Rabbit
    """
    
    def __init__(self, max_queue_size: int = 1000):
        """
        Inizializza event bus.
        
        Args:
            max_queue_size: Dimensione massima code
        """
        self.max_queue_size = max_queue_size
        
        # Subscribers per topic
        self.subscribers: Dict[str, Set[EventHandler]] = defaultdict(set)
        
        # Code per topic
        self.topic_queues: Dict[str, asyncio.Queue] = {}
        
        # Worker tasks
        self.worker_tasks: Dict[str, asyncio.Task] = {}
        
        # Metriche
        self.metrics = {
            'messages_published': 0,
            'messages_processed': 0,
            'messages_failed': 0,
            'active_topics': 0,
            'active_subscribers': 0
        }
        
        # Stato
        self.is_running = False
        
        logger.info("📡 EventBus inizializzato")
    
    async def start(self):
        """Avvia event bus."""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("🚀 EventBus avviato")
    
    async def stop(self):
        """Ferma event bus."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancella worker tasks
        for task in self.worker_tasks.values():
            task.cancel()
        
        # Aspetta completamento
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks.values(), return_exceptions=True)
        
        self.worker_tasks.clear()
        
        logger.info("🛑 EventBus fermato")
    
    async def publish(self, topic: str, payload: Dict[str, Any], 
                     source: Optional[str] = None, 
                     correlation_id: Optional[str] = None) -> bool:
        """
        Pubblica messaggio su topic.
        
        Args:
            topic: Topic destinazione
            payload: Payload messaggio
            source: Sorgente messaggio
            correlation_id: ID correlazione
            
        Returns:
            True se pubblicato con successo
        """
        if not self.is_running:
            logger.warning("⚠️ EventBus non avviato")
            return False
        
        try:
            message = EventMessage(
                topic=topic,
                payload=payload,
                source=source,
                correlation_id=correlation_id
            )
            
            # Crea coda se non esiste
            if topic not in self.topic_queues:
                await self._create_topic_queue(topic)
            
            # Pubblica su coda
            queue = self.topic_queues[topic]
            await queue.put(message)
            
            self.metrics['messages_published'] += 1
            
            logger.debug(f"📤 Pubblicato su {topic}: {message.message_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore pubblicazione su {topic}: {e}")
            return False
    
    def subscribe(self, topic: str, handler: EventHandler):
        """
        Sottoscrive handler a topic.
        
        Args:
            topic: Topic da sottoscrivere
            handler: Handler eventi
        """
        self.subscribers[topic].add(handler)
        self.metrics['active_subscribers'] = sum(len(handlers) for handlers in self.subscribers.values())
        
        logger.debug(f"📥 Sottoscritto {handler.__class__.__name__} a {topic}")
        
        # Crea coda se non esiste
        if self.is_running and topic not in self.topic_queues:
            asyncio.create_task(self._create_topic_queue(topic))
    
    def unsubscribe(self, topic: str, handler: EventHandler):
        """
        Rimuove sottoscrizione handler.
        
        Args:
            topic: Topic da cui rimuovere
            handler: Handler da rimuovere
        """
        if topic in self.subscribers:
            self.subscribers[topic].discard(handler)
            if not self.subscribers[topic]:
                del self.subscribers[topic]
        
        self.metrics['active_subscribers'] = sum(len(handlers) for handlers in self.subscribers.values())
        
        logger.debug(f"📤 Rimossa sottoscrizione {handler.__class__.__name__} da {topic}")
    
    async def _create_topic_queue(self, topic: str):
        """Crea coda e worker per topic."""
        if topic in self.topic_queues:
            return
        
        # Crea coda
        queue = asyncio.Queue(maxsize=self.max_queue_size)
        self.topic_queues[topic] = queue
        
        # Avvia worker
        worker_task = asyncio.create_task(self._topic_worker(topic, queue))
        self.worker_tasks[topic] = worker_task
        
        self.metrics['active_topics'] = len(self.topic_queues)
        
        logger.debug(f"🔧 Creata coda per topic: {topic}")
    
    async def _topic_worker(self, topic: str, queue: asyncio.Queue):
        """Worker per processare messaggi di un topic."""
        logger.debug(f"👷 Worker avviato per topic: {topic}")
        
        try:
            while self.is_running:
                try:
                    # Aspetta messaggio con timeout
                    message = await asyncio.wait_for(queue.get(), timeout=1.0)
                    
                    # Processa con tutti gli handlers
                    await self._process_message(topic, message)
                    
                except asyncio.TimeoutError:
                    # Timeout normale - continua
                    continue
                except Exception as e:
                    logger.error(f"❌ Errore worker {topic}: {e}")
                    
        except asyncio.CancelledError:
            logger.debug(f"🛑 Worker {topic} cancellato")
        
        logger.debug(f"👷 Worker terminato per topic: {topic}")
    
    async def _process_message(self, topic: str, message: EventMessage):
        """Processa messaggio con tutti gli handlers."""
        handlers = self.subscribers.get(topic, set())
        
        if not handlers:
            logger.debug(f"⚠️ Nessun handler per topic: {topic}")
            return
        
        logger.debug(f"🔄 Processando {message.message_id} con {len(handlers)} handlers")
        
        # Processa con tutti gli handlers
        for handler in handlers:
            try:
                success = await handler.handle(message)
                if success:
                    self.metrics['messages_processed'] += 1
                else:
                    self.metrics['messages_failed'] += 1
                    
            except Exception as e:
                logger.error(f"❌ Handler {handler.__class__.__name__} fallito: {e}")
                self.metrics['messages_failed'] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Ottiene metriche event bus."""
        return {
            **self.metrics,
            'queue_sizes': {
                topic: queue.qsize() 
                for topic, queue in self.topic_queues.items()
            },
            'is_running': self.is_running
        }
    
    def get_topics(self) -> List[str]:
        """Ottiene lista topic attivi."""
        return list(self.topic_queues.keys())
    
    def get_subscribers_count(self, topic: str) -> int:
        """Ottiene numero subscribers per topic."""
        return len(self.subscribers.get(topic, set()))


# Event Bus globale singleton
_global_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """Ottieni event bus globale."""
    global _global_bus
    if _global_bus is None:
        _global_bus = EventBus()
    return _global_bus


def reset_event_bus():
    """Reset event bus globale (per testing)."""
    global _global_bus
    if _global_bus and _global_bus.is_running:
        asyncio.create_task(_global_bus.stop())
    _global_bus = None
