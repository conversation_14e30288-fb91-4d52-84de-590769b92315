"""
NEUROGLYPH Symbolic Unification
Unificazione simbolica con variabili logiche
"""

import logging
from typing import Dict, Optional, Set, List, Tuple
from dataclasses import dataclass

from .formula import Formula, Variable, Term, Constant, Predicate, Conjunction, Disjunction, Implication, Negation

logger = logging.getLogger(__name__)


@dataclass
class Substitution:
    """Sostituzione simbolica."""
    
    mappings: Dict[Variable, Term]
    
    def __init__(self, mappings: Optional[Dict[Variable, Term]] = None):
        self.mappings = mappings or {}
    
    def __getitem__(self, var: Variable) -> Term:
        return self.mappings[var]
    
    def __setitem__(self, var: Variable, term: Term):
        self.mappings[var] = term
    
    def __contains__(self, var: Variable) -> bool:
        return var in self.mappings
    
    def __len__(self) -> int:
        return len(self.mappings)
    
    def __iter__(self):
        return iter(self.mappings)
    
    def items(self):
        return self.mappings.items()
    
    def keys(self):
        return self.mappings.keys()
    
    def values(self):
        return self.mappings.values()
    
    def copy(self) -> 'Substitution':
        """Copia sostituzione."""
        return Substitution(self.mappings.copy())
    
    def compose(self, other: 'Substitution') -> 'Substitution':
        """Compone due sostituzioni."""
        result = Substitution()
        
        # Applica other a tutti i valori di self
        for var, term in self.mappings.items():
            result[var] = self._apply_to_term(term, other)
        
        # Aggiungi mappings di other non in self
        for var, term in other.mappings.items():
            if var not in result:
                result[var] = term
        
        return result
    
    def _apply_to_term(self, term: Term, substitution: 'Substitution') -> Term:
        """Applica sostituzione a termine."""
        if isinstance(term, Variable) and term in substitution:
            return substitution[term]
        return term
    
    def __str__(self) -> str:
        if not self.mappings:
            return "{}"
        items = [f"{var} → {term}" for var, term in self.mappings.items()]
        return "{" + ", ".join(items) + "}"


class SymbolicUnifier:
    """
    Unificatore simbolico con occurs check.
    
    Features:
    - Unificazione di termini e formule
    - Occurs check per prevenire strutture infinite
    - Composizione sostituzioni
    - Support per predicati complessi
    """
    
    def __init__(self, occurs_check: bool = True):
        """
        Inizializza unificatore.
        
        Args:
            occurs_check: Abilita occurs check
        """
        self.occurs_check = occurs_check
        self.unifications_attempted = 0
        self.unifications_successful = 0
        
        logger.debug(f"🔗 SymbolicUnifier inizializzato (occurs_check={occurs_check})")
    
    def unify_terms(self, term1: Term, term2: Term) -> Optional[Substitution]:
        """
        Unifica due termini.
        
        Args:
            term1: Primo termine
            term2: Secondo termine
            
        Returns:
            Substitution se unificabili, None altrimenti
        """
        self.unifications_attempted += 1
        
        logger.debug(f"🔗 Unify terms: {term1} ≈ {term2}")
        
        # Caso 1: Termini identici
        if term1 == term2:
            logger.debug("✅ Termini identici")
            self.unifications_successful += 1
            return Substitution()
        
        # Caso 2: term1 è variabile
        if isinstance(term1, Variable):
            if self.occurs_check and self._occurs_check(term1, term2):
                logger.debug(f"❌ Occurs check fallito: {term1} in {term2}")
                return None
            
            logger.debug(f"✅ Unify variable: {term1} → {term2}")
            self.unifications_successful += 1
            return Substitution({term1: term2})
        
        # Caso 3: term2 è variabile
        if isinstance(term2, Variable):
            if self.occurs_check and self._occurs_check(term2, term1):
                logger.debug(f"❌ Occurs check fallito: {term2} in {term1}")
                return None
            
            logger.debug(f"✅ Unify variable: {term2} → {term1}")
            self.unifications_successful += 1
            return Substitution({term2: term1})
        
        # Caso 4: Entrambi costanti diverse
        if isinstance(term1, Constant) and isinstance(term2, Constant):
            logger.debug(f"❌ Costanti diverse: {term1} ≠ {term2}")
            return None
        
        logger.debug(f"❌ Termini non unificabili: {term1}, {term2}")
        return None
    
    def unify_formulas(self, formula1: Formula, formula2: Formula) -> Optional[Substitution]:
        """
        Unifica due formule.
        
        Args:
            formula1: Prima formula
            formula2: Seconda formula
            
        Returns:
            Substitution se unificabili, None altrimenti
        """
        self.unifications_attempted += 1
        
        logger.debug(f"🔗 Unify formulas: {formula1} ≈ {formula2}")
        
        # Caso 1: Formule identiche
        if formula1 == formula2:
            logger.debug("✅ Formule identiche")
            self.unifications_successful += 1
            return Substitution()
        
        # Caso 2: Entrambi predicati
        if isinstance(formula1, Predicate) and isinstance(formula2, Predicate):
            return self._unify_predicates(formula1, formula2)
        
        # Caso 3: Entrambe congiunzioni
        if isinstance(formula1, Conjunction) and isinstance(formula2, Conjunction):
            return self._unify_conjunctions(formula1, formula2)
        
        # Caso 4: Entrambe disgiunzioni
        if isinstance(formula1, Disjunction) and isinstance(formula2, Disjunction):
            return self._unify_disjunctions(formula1, formula2)
        
        # Caso 5: Entrambe implicazioni
        if isinstance(formula1, Implication) and isinstance(formula2, Implication):
            return self._unify_implications(formula1, formula2)
        
        # Caso 6: Entrambe negazioni
        if isinstance(formula1, Negation) and isinstance(formula2, Negation):
            return self.unify_formulas(formula1.formula, formula2.formula)
        
        logger.debug(f"❌ Formule non unificabili: {type(formula1)} vs {type(formula2)}")
        return None
    
    def _unify_predicates(self, pred1: Predicate, pred2: Predicate) -> Optional[Substitution]:
        """Unifica due predicati."""
        # Nome deve essere uguale
        if pred1.name != pred2.name:
            logger.debug(f"❌ Nomi predicati diversi: {pred1.name} ≠ {pred2.name}")
            return None
        
        # Numero argomenti deve essere uguale
        if len(pred1.args) != len(pred2.args):
            logger.debug(f"❌ Arità diversa: {len(pred1.args)} ≠ {len(pred2.args)}")
            return None
        
        # Unifica argomenti uno per uno
        substitution = Substitution()
        
        for arg1, arg2 in zip(pred1.args, pred2.args):
            arg_sub = self.unify_terms(arg1, arg2)
            if arg_sub is None:
                logger.debug(f"❌ Argomenti non unificabili: {arg1} ≠ {arg2}")
                return None
            
            # Componi sostituzioni
            substitution = substitution.compose(arg_sub)
        
        logger.debug(f"✅ Predicati unificati: {substitution}")
        self.unifications_successful += 1
        return substitution
    
    def _unify_conjunctions(self, conj1: Conjunction, conj2: Conjunction) -> Optional[Substitution]:
        """Unifica due congiunzioni."""
        # Unifica left con left
        left_sub = self.unify_formulas(conj1.left, conj2.left)
        if left_sub is None:
            return None
        
        # Unifica right con right
        right_sub = self.unify_formulas(conj1.right, conj2.right)
        if right_sub is None:
            return None
        
        # Componi sostituzioni
        result = left_sub.compose(right_sub)
        
        logger.debug(f"✅ Congiunzioni unificate: {result}")
        self.unifications_successful += 1
        return result
    
    def _unify_disjunctions(self, disj1: Disjunction, disj2: Disjunction) -> Optional[Substitution]:
        """Unifica due disgiunzioni."""
        # Unifica left con left
        left_sub = self.unify_formulas(disj1.left, disj2.left)
        if left_sub is None:
            return None
        
        # Unifica right con right
        right_sub = self.unify_formulas(disj1.right, disj2.right)
        if right_sub is None:
            return None
        
        # Componi sostituzioni
        result = left_sub.compose(right_sub)
        
        logger.debug(f"✅ Disgiunzioni unificate: {result}")
        self.unifications_successful += 1
        return result
    
    def _unify_implications(self, impl1: Implication, impl2: Implication) -> Optional[Substitution]:
        """Unifica due implicazioni."""
        # Unifica antecedenti
        ant_sub = self.unify_formulas(impl1.antecedent, impl2.antecedent)
        if ant_sub is None:
            return None
        
        # Unifica conseguenti
        cons_sub = self.unify_formulas(impl1.consequent, impl2.consequent)
        if cons_sub is None:
            return None
        
        # Componi sostituzioni
        result = ant_sub.compose(cons_sub)
        
        logger.debug(f"✅ Implicazioni unificate: {result}")
        self.unifications_successful += 1
        return result
    
    def _occurs_check(self, var: Variable, term: Term) -> bool:
        """
        Occurs check: verifica se var occorre in term.
        
        Previene strutture infinite come x = f(x).
        """
        if var == term:
            return True
        
        # Per ora, check semplice solo su variabili
        # TODO: Estendere per strutture complesse
        return False
    
    def get_statistics(self) -> Dict[str, any]:
        """Ottiene statistiche unificatore."""
        success_rate = (self.unifications_successful / self.unifications_attempted 
                       if self.unifications_attempted > 0 else 0.0)
        
        return {
            'unifications_attempted': self.unifications_attempted,
            'unifications_successful': self.unifications_successful,
            'success_rate': success_rate,
            'occurs_check': self.occurs_check
        }
    
    def reset_statistics(self):
        """Reset statistiche."""
        self.unifications_attempted = 0
        self.unifications_successful = 0
