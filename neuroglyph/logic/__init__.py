"""
NEUROGLYPH Formal Logic Engine
PoC stand-alone con SymPy logic + boolean.py
"""

from .formula import Formula, Predicate, Variable, Constant
from .proof_tree import ProofTree, ProofStep, ProofRule
from .logic_engine import FormalLogicEngine
from .unify import SymbolicUnifier, Substitution

__all__ = [
    'Formula',
    'Predicate', 
    'Variable',
    'Constant',
    'ProofTree',
    'ProofStep',
    'ProofRule',
    'FormalLogicEngine',
    'SymbolicUnifier',
    'Substitution'
]
