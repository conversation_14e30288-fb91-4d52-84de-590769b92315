"""
NEUROGLYPH Formula Representation
Strutture dati per formule logiche simboliche
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Set, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import hashlib


class FormulaType(Enum):
    """Tipi di formula logica."""
    PREDICATE = "predicate"
    VARIABLE = "variable"
    CONSTANT = "constant"
    CONJUNCTION = "and"
    DISJUNCTION = "or"
    IMPLICATION = "implies"
    NEGATION = "not"
    UNIVERSAL = "forall"
    EXISTENTIAL = "exists"


@dataclass(frozen=True)
class Term:
    """Termine logico base."""
    name: str
    type: str = "term"
    
    def __str__(self) -> str:
        return self.name
    
    def __hash__(self) -> int:
        return hash((self.name, self.type))


@dataclass(frozen=True)
class Variable(Term):
    """Variabile logica."""
    type: str = "variable"
    
    def __post_init__(self):
        # Variabili iniziano con minuscola
        if not self.name[0].islower():
            object.__setattr__(self, 'name', self.name.lower())


@dataclass(frozen=True)
class Constant(Term):
    """Costante logica."""
    type: str = "constant"
    
    def __post_init__(self):
        # Costanti iniziano con maiuscola
        if not self.name[0].isupper():
            object.__setattr__(self, 'name', self.name.capitalize())


class Formula(ABC):
    """Formula logica astratta."""
    
    @abstractmethod
    def __str__(self) -> str:
        """Rappresentazione stringa."""
        pass
    
    @abstractmethod
    def get_variables(self) -> Set[Variable]:
        """Ottiene variabili libere."""
        pass
    
    @abstractmethod
    def get_constants(self) -> Set[Constant]:
        """Ottiene costanti."""
        pass
    
    @abstractmethod
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Formula':
        """Applica sostituzione."""
        pass
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Serializza a dict."""
        pass
    
    def __hash__(self) -> int:
        """Hash per set/dict."""
        return hash(str(self))
    
    def __eq__(self, other) -> bool:
        """Uguaglianza strutturale."""
        return isinstance(other, Formula) and str(self) == str(other)


@dataclass(frozen=True)
class Predicate(Formula):
    """Predicato logico P(t1, t2, ...)."""
    
    name: str
    args: List[Term]
    
    def __str__(self) -> str:
        if not self.args:
            return self.name
        args_str = ', '.join(str(arg) for arg in self.args)
        return f"{self.name}({args_str})"
    
    def get_variables(self) -> Set[Variable]:
        return {arg for arg in self.args if isinstance(arg, Variable)}
    
    def get_constants(self) -> Set[Constant]:
        return {arg for arg in self.args if isinstance(arg, Constant)}
    
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Predicate':
        new_args = []
        for arg in self.args:
            if isinstance(arg, Variable) and arg in substitution:
                new_args.append(substitution[arg])
            else:
                new_args.append(arg)
        return Predicate(self.name, new_args)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': 'predicate',
            'name': self.name,
            'args': [{'name': arg.name, 'type': arg.type} for arg in self.args]
        }


@dataclass(frozen=True)
class Conjunction(Formula):
    """Congiunzione A ∧ B."""
    
    left: Formula
    right: Formula
    
    def __str__(self) -> str:
        return f"({self.left} ∧ {self.right})"
    
    def get_variables(self) -> Set[Variable]:
        return self.left.get_variables() | self.right.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.left.get_constants() | self.right.get_constants()
    
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Conjunction':
        return Conjunction(
            self.left.substitute(substitution),
            self.right.substitute(substitution)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': 'conjunction',
            'left': self.left.to_dict(),
            'right': self.right.to_dict()
        }


@dataclass(frozen=True)
class Disjunction(Formula):
    """Disgiunzione A ∨ B."""
    
    left: Formula
    right: Formula
    
    def __str__(self) -> str:
        return f"({self.left} ∨ {self.right})"
    
    def get_variables(self) -> Set[Variable]:
        return self.left.get_variables() | self.right.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.left.get_constants() | self.right.get_constants()
    
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Disjunction':
        return Disjunction(
            self.left.substitute(substitution),
            self.right.substitute(substitution)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': 'disjunction',
            'left': self.left.to_dict(),
            'right': self.right.to_dict()
        }


@dataclass(frozen=True)
class Implication(Formula):
    """Implicazione A → B."""
    
    antecedent: Formula
    consequent: Formula
    
    def __str__(self) -> str:
        return f"({self.antecedent} → {self.consequent})"
    
    def get_variables(self) -> Set[Variable]:
        return self.antecedent.get_variables() | self.consequent.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.antecedent.get_constants() | self.consequent.get_constants()
    
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Implication':
        return Implication(
            self.antecedent.substitute(substitution),
            self.consequent.substitute(substitution)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': 'implication',
            'antecedent': self.antecedent.to_dict(),
            'consequent': self.consequent.to_dict()
        }


@dataclass(frozen=True)
class Negation(Formula):
    """Negazione ¬A."""
    
    formula: Formula
    
    def __str__(self) -> str:
        return f"¬{self.formula}"
    
    def get_variables(self) -> Set[Variable]:
        return self.formula.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.formula.get_constants()
    
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Negation':
        return Negation(self.formula.substitute(substitution))
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': 'negation',
            'formula': self.formula.to_dict()
        }


@dataclass(frozen=True)
class Universal(Formula):
    """Quantificatore universale ∀x P(x)."""
    
    variable: Variable
    formula: Formula
    
    def __str__(self) -> str:
        return f"∀{self.variable} {self.formula}"
    
    def get_variables(self) -> Set[Variable]:
        # Variabile quantificata non è libera
        free_vars = self.formula.get_variables()
        free_vars.discard(self.variable)
        return free_vars
    
    def get_constants(self) -> Set[Constant]:
        return self.formula.get_constants()
    
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Universal':
        # Non sostituire variabile quantificata
        filtered_sub = {k: v for k, v in substitution.items() if k != self.variable}
        return Universal(self.variable, self.formula.substitute(filtered_sub))
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': 'universal',
            'variable': {'name': self.variable.name, 'type': self.variable.type},
            'formula': self.formula.to_dict()
        }


@dataclass(frozen=True)
class Existential(Formula):
    """Quantificatore esistenziale ∃x P(x)."""
    
    variable: Variable
    formula: Formula
    
    def __str__(self) -> str:
        return f"∃{self.variable} {self.formula}"
    
    def get_variables(self) -> Set[Variable]:
        # Variabile quantificata non è libera
        free_vars = self.formula.get_variables()
        free_vars.discard(self.variable)
        return free_vars
    
    def get_constants(self) -> Set[Constant]:
        return self.formula.get_constants()
    
    def substitute(self, substitution: Dict[Variable, Term]) -> 'Existential':
        # Non sostituire variabile quantificata
        filtered_sub = {k: v for k, v in substitution.items() if k != self.variable}
        return Existential(self.variable, self.formula.substitute(filtered_sub))
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': 'existential',
            'variable': {'name': self.variable.name, 'type': self.variable.type},
            'formula': self.formula.to_dict()
        }


def parse_formula(formula_str: str) -> Formula:
    """
    Parser semplice per formule logiche.

    Esempi:
    - "P(x)" → Predicate("P", [Variable("x")])
    - "P(x) ∧ Q(y)" → Conjunction(...)
    - "∀x P(x)" → Universal(...)
    """
    # TODO: Implementare parser completo
    # Per ora, factory method semplice

    formula_str = formula_str.strip()

    # Predicato semplice
    if '(' in formula_str and ')' in formula_str:
        name = formula_str[:formula_str.index('(')]
        args_str = formula_str[formula_str.index('(')+1:formula_str.rindex(')')]

        if args_str.strip():
            args = []
            for arg in args_str.split(','):
                arg = arg.strip()
                if arg[0].islower():
                    args.append(Variable(arg))
                else:
                    args.append(Constant(arg))
        else:
            args = []

        return Predicate(name, args)

    # Predicato senza argomenti
    return Predicate(formula_str, [])


# Factory functions per costruzione rapida
def P(name: str, *args) -> Predicate:
    """Factory per predicati."""
    terms = []
    for arg in args:
        if isinstance(arg, str):
            if arg[0].islower():
                terms.append(Variable(arg))
            else:
                terms.append(Constant(arg))
        else:
            terms.append(arg)
    return Predicate(name, terms)


def And(left: Formula, right: Formula) -> Conjunction:
    """Factory per congiunzione."""
    return Conjunction(left, right)


def Or(left: Formula, right: Formula) -> Disjunction:
    """Factory per disgiunzione."""
    return Disjunction(left, right)


def Implies(antecedent: Formula, consequent: Formula) -> Implication:
    """Factory per implicazione."""
    return Implication(antecedent, consequent)


def Not(formula: Formula) -> Negation:
    """Factory per negazione."""
    return Negation(formula)


def ForAll(var: Union[str, Variable], formula: Formula) -> Universal:
    """Factory per quantificatore universale."""
    if isinstance(var, str):
        var = Variable(var)
    return Universal(var, formula)


def Exists(var: Union[str, Variable], formula: Formula) -> Existential:
    """Factory per quantificatore esistenziale."""
    if isinstance(var, str):
        var = Variable(var)
    return Existential(var, formula)
