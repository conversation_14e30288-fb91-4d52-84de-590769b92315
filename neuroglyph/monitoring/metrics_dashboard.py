"""
FASE 4.0.3 - Metrics Dashboard per NEUROGLYPH Patch Engine

Dashboard real-time per monitorare:
- Success rates
- Performance metrics  
- Error patterns
- Alert system
"""

import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
import sqlite3


@dataclass
class MetricSnapshot:
    """Snapshot di metriche in un momento specifico."""
    timestamp: datetime
    success_rate: float
    avg_latency_ms: float
    total_patches: int
    error_count: int
    top_patterns: List[str]
    performance_grade: str


@dataclass
class AlertRule:
    """Regola per alert automatici."""
    name: str
    metric: str  # 'success_rate', 'latency', 'error_rate'
    threshold: float
    operator: str  # 'lt', 'gt', 'eq'
    enabled: bool = True
    cooldown_minutes: int = 5


class MetricsCollector:
    """Collettore metriche real-time."""
    
    def __init__(self, db_path: str = "metrics.db"):
        self.db_path = db_path
        self.metrics_buffer = deque(maxlen=1000)  # Buffer circolare
        self.pattern_stats = defaultdict(lambda: {"count": 0, "success": 0, "total_latency": 0.0})
        self.alert_rules = []
        self.last_alerts = {}
        
        # Inizializza database
        self._init_database()
        
        # Thread per flush periodico
        self.flush_thread = threading.Thread(target=self._periodic_flush, daemon=True)
        self.flush_thread.start()
    
    def _init_database(self):
        """Inizializza database SQLite per persistenza."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS metrics_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    success_rate REAL NOT NULL,
                    avg_latency_ms REAL NOT NULL,
                    total_patches INTEGER NOT NULL,
                    error_count INTEGER NOT NULL,
                    top_patterns TEXT NOT NULL,
                    performance_grade TEXT NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS pattern_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    pattern TEXT NOT NULL,
                    count INTEGER NOT NULL,
                    success_count INTEGER NOT NULL,
                    avg_latency_ms REAL NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    rule_name TEXT NOT NULL,
                    metric TEXT NOT NULL,
                    value REAL NOT NULL,
                    threshold REAL NOT NULL,
                    message TEXT NOT NULL
                )
            """)
    
    def record_patch_attempt(self, pattern: str, success: bool, latency_ms: float):
        """Registra tentativo di patch."""
        timestamp = datetime.now()
        
        # Aggiorna buffer
        self.metrics_buffer.append({
            "timestamp": timestamp,
            "pattern": pattern,
            "success": success,
            "latency_ms": latency_ms
        })
        
        # Aggiorna statistiche pattern
        stats = self.pattern_stats[pattern]
        stats["count"] += 1
        if success:
            stats["success"] += 1
        stats["total_latency"] += latency_ms
        
        # Controlla alert
        self._check_alerts()
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Ottieni metriche correnti."""
        if not self.metrics_buffer:
            return self._empty_metrics()
        
        # Calcola metriche da buffer
        recent_data = list(self.metrics_buffer)[-100:]  # Ultimi 100 eventi
        
        total_attempts = len(recent_data)
        successful_attempts = sum(1 for d in recent_data if d["success"])
        success_rate = (successful_attempts / total_attempts) * 100 if total_attempts > 0 else 0
        
        latencies = [d["latency_ms"] for d in recent_data]
        avg_latency = sum(latencies) / len(latencies) if latencies else 0
        
        # Top patterns
        pattern_counts = defaultdict(int)
        for d in recent_data:
            pattern_counts[d["pattern"]] += 1
        
        top_patterns = sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        top_patterns = [pattern for pattern, _ in top_patterns]
        
        # Performance grade
        performance_grade = self._calculate_performance_grade(avg_latency, success_rate)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "success_rate": round(success_rate, 2),
            "avg_latency_ms": round(avg_latency, 3),
            "total_patches": total_attempts,
            "error_count": total_attempts - successful_attempts,
            "top_patterns": top_patterns,
            "performance_grade": performance_grade,
            "pattern_breakdown": self._get_pattern_breakdown()
        }
    
    def _get_pattern_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """Ottieni breakdown per pattern."""
        breakdown = {}
        
        for pattern, stats in self.pattern_stats.items():
            if stats["count"] > 0:
                success_rate = (stats["success"] / stats["count"]) * 100
                avg_latency = stats["total_latency"] / stats["count"]
                
                breakdown[pattern] = {
                    "count": stats["count"],
                    "success_rate": round(success_rate, 2),
                    "avg_latency_ms": round(avg_latency, 3)
                }
        
        return breakdown
    
    def _calculate_performance_grade(self, avg_latency: float, success_rate: float) -> str:
        """Calcola grade performance."""
        if success_rate >= 95 and avg_latency < 5:
            return "A+ (Excellent)"
        elif success_rate >= 90 and avg_latency < 10:
            return "A (Great)"
        elif success_rate >= 80 and avg_latency < 20:
            return "B (Good)"
        elif success_rate >= 70 and avg_latency < 50:
            return "C (Acceptable)"
        else:
            return "D (Needs Improvement)"
    
    def _empty_metrics(self) -> Dict[str, Any]:
        """Metriche vuote per inizializzazione."""
        return {
            "timestamp": datetime.now().isoformat(),
            "success_rate": 0.0,
            "avg_latency_ms": 0.0,
            "total_patches": 0,
            "error_count": 0,
            "top_patterns": [],
            "performance_grade": "N/A",
            "pattern_breakdown": {}
        }
    
    def add_alert_rule(self, rule: AlertRule):
        """Aggiungi regola di alert."""
        self.alert_rules.append(rule)
        print(f"📢 Alert rule added: {rule.name}")
    
    def _check_alerts(self):
        """Controlla regole di alert."""
        current_metrics = self.get_current_metrics()
        now = datetime.now()
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # Controlla cooldown
            last_alert_time = self.last_alerts.get(rule.name)
            if last_alert_time and (now - last_alert_time).total_seconds() < rule.cooldown_minutes * 60:
                continue
            
            # Ottieni valore metrica
            metric_value = current_metrics.get(rule.metric, 0)
            
            # Controlla condizione
            triggered = False
            if rule.operator == "lt" and metric_value < rule.threshold:
                triggered = True
            elif rule.operator == "gt" and metric_value > rule.threshold:
                triggered = True
            elif rule.operator == "eq" and abs(metric_value - rule.threshold) < 0.01:
                triggered = True
            
            if triggered:
                self._trigger_alert(rule, metric_value, current_metrics)
                self.last_alerts[rule.name] = now
    
    def _trigger_alert(self, rule: AlertRule, value: float, metrics: Dict[str, Any]):
        """Trigger alert."""
        message = f"🚨 ALERT: {rule.name} - {rule.metric}={value} {rule.operator} {rule.threshold}"
        
        print(f"\n{message}")
        print(f"   Current metrics: {json.dumps(metrics, indent=2)}")
        
        # Salva alert nel database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO alerts (timestamp, rule_name, metric, value, threshold, message)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                datetime.now().isoformat(),
                rule.name,
                rule.metric,
                value,
                rule.threshold,
                message
            ))
    
    def _periodic_flush(self):
        """Flush periodico metriche su database."""
        while True:
            try:
                time.sleep(60)  # Flush ogni minuto
                self._flush_to_database()
            except Exception as e:
                print(f"Error in periodic flush: {e}")
    
    def _flush_to_database(self):
        """Flush metriche correnti su database."""
        metrics = self.get_current_metrics()
        
        with sqlite3.connect(self.db_path) as conn:
            # Salva snapshot
            conn.execute("""
                INSERT INTO metrics_snapshots 
                (timestamp, success_rate, avg_latency_ms, total_patches, error_count, top_patterns, performance_grade)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                metrics["timestamp"],
                metrics["success_rate"],
                metrics["avg_latency_ms"],
                metrics["total_patches"],
                metrics["error_count"],
                json.dumps(metrics["top_patterns"]),
                metrics["performance_grade"]
            ))
            
            # Salva pattern metrics
            for pattern, stats in metrics["pattern_breakdown"].items():
                conn.execute("""
                    INSERT INTO pattern_metrics
                    (timestamp, pattern, count, success_count, avg_latency_ms)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    metrics["timestamp"],
                    pattern,
                    stats["count"],
                    int(stats["count"] * stats["success_rate"] / 100),
                    stats["avg_latency_ms"]
                ))


class DashboardServer:
    """Server dashboard per visualizzazione web."""
    
    def __init__(self, metrics_collector: MetricsCollector, port: int = 8080):
        self.metrics_collector = metrics_collector
        self.port = port
    
    def generate_html_dashboard(self) -> str:
        """Genera dashboard HTML."""
        metrics = self.metrics_collector.get_current_metrics()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>NEUROGLYPH Patch Engine - Dashboard</title>
            <meta http-equiv="refresh" content="5">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .metric-card {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .metric-value {{ font-size: 2em; font-weight: bold; color: #3498db; }}
                .metric-label {{ color: #7f8c8d; margin-bottom: 10px; }}
                .success {{ color: #27ae60; }}
                .warning {{ color: #f39c12; }}
                .error {{ color: #e74c3c; }}
                .pattern-list {{ list-style: none; padding: 0; }}
                .pattern-item {{ padding: 8px; margin: 4px 0; background: #ecf0f1; border-radius: 4px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 NEUROGLYPH Patch Engine Dashboard</h1>
                    <p>Real-time monitoring - Last updated: {metrics['timestamp']}</p>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Success Rate</div>
                        <div class="metric-value {'success' if metrics['success_rate'] >= 90 else 'warning' if metrics['success_rate'] >= 70 else 'error'}">{metrics['success_rate']}%</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-label">Average Latency</div>
                        <div class="metric-value {'success' if metrics['avg_latency_ms'] < 10 else 'warning' if metrics['avg_latency_ms'] < 50 else 'error'}">{metrics['avg_latency_ms']}ms</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-label">Total Patches</div>
                        <div class="metric-value">{metrics['total_patches']}</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-label">Performance Grade</div>
                        <div class="metric-value">{metrics['performance_grade']}</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-label">Top Patterns</div>
                        <ul class="pattern-list">
                            {''.join(f'<li class="pattern-item">{pattern}</li>' for pattern in metrics['top_patterns'][:5])}
                        </ul>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-label">Pattern Breakdown</div>
                        <div style="max-height: 200px; overflow-y: auto;">
                            {''.join(f'<div class="pattern-item">{pattern}: {stats["success_rate"]}% ({stats["avg_latency_ms"]}ms)</div>' 
                                   for pattern, stats in metrics['pattern_breakdown'].items())}
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def start_server(self):
        """Avvia server dashboard."""
        try:
            from http.server import HTTPServer, BaseHTTPRequestHandler
            
            class DashboardHandler(BaseHTTPRequestHandler):
                def do_GET(self):
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    
                    html = self.server.dashboard_server.generate_html_dashboard()
                    self.wfile.write(html.encode())
                
                def log_message(self, format, *args):
                    pass  # Silenzia log HTTP
            
            server = HTTPServer(('localhost', self.port), DashboardHandler)
            server.dashboard_server = self
            
            print(f"🌐 Dashboard server started at http://localhost:{self.port}")
            server.serve_forever()
            
        except ImportError:
            print("⚠️ HTTP server not available. Dashboard HTML can be generated manually.")
        except Exception as e:
            print(f"❌ Error starting dashboard server: {e}")


# Istanza globale per facilità d'uso
_global_metrics_collector = None

def get_metrics_collector() -> MetricsCollector:
    """Ottieni collector globale."""
    global _global_metrics_collector
    if _global_metrics_collector is None:
        _global_metrics_collector = MetricsCollector()
        
        # Aggiungi alert rules di default
        _global_metrics_collector.add_alert_rule(
            AlertRule("Low Success Rate", "success_rate", 70.0, "lt")
        )
        _global_metrics_collector.add_alert_rule(
            AlertRule("High Latency", "avg_latency_ms", 50.0, "gt")
        )
        
    return _global_metrics_collector
