#!/usr/bin/env python3
"""
Debug script per testare la generazione di patch
"""

import ast
from neuroglyph.cognitive.validation_structures import ValidationResult, ValidationError, ValidationLevel, ValidationSeverity, ValidationErrorType
from neuroglyph.cognitive.error_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from neuroglyph.cognitive.patch_generator import Patch<PERSON>enerator

def test_patch_generation():
    """Test debug per generazione patch."""
    print("🔍 DEBUG: Test Patch Generation")
    
    # Crea codice con errore di sintassi
    code_with_syntax_error = """
def test_function()  # Missing colon
    return 42
"""

    print(f"📝 Codice con errore di sintassi:")
    print(code_with_syntax_error)

    # Test syntax error
    test_error_type(code_with_syntax_error, "syntax")

    # Crea codice con errore semantico
    code_with_semantic_error = """
x = 5
undefined_function_call()
print(x)
"""

    print(f"\n📝 Codice con errore semantico:")
    print(code_with_semantic_error)

    # Test semantic error
    test_error_type(code_with_semantic_error, "semantic")

def test_error_type(code_with_error, error_type):
    print(f"\n🔍 DEBUG: Test {error_type.upper()} Error")

    # Crea ValidationResult
    result = ValidationResult()

    # Test compilazione per catturare errore
    try:
        compile(code_with_error, '<string>', 'exec')
        print("✅ Compilazione riuscita (nessun errore sintattico)")
    except Exception as e:
        print(f"❌ Errore di compilazione: {e}")
        val_error = ValidationError(
            error_type=ValidationErrorType.AST_PARSE_ERROR,
            severity=ValidationSeverity.ERROR,
            level=ValidationLevel.SYNTAX,
            message=str(e)
        )
        result.add_error(val_error)

    # Test esecuzione per catturare errore semantico
    try:
        exec(code_with_error)
        print("✅ Esecuzione riuscita")
    except Exception as e:
        print(f"❌ Errore di esecuzione: {e}")
        val_error = ValidationError(
            error_type=ValidationErrorType.MISSING_PREMISES,
            severity=ValidationSeverity.ERROR,
            level=ValidationLevel.SEMANTIC,
            message=str(e)
        )
        result.add_error(val_error)

    result.finalize()

    print(f"\n📊 ValidationResult:")
    print(f"   - Has errors: {result.has_errors}")
    print(f"   - Total errors: {result.total_errors}")
    print(f"   - Syntax errors: {len(result.syntax_errors)}")
    print(f"   - Semantic errors: {len(result.semantic_errors)}")

    # Test ErrorAnalyzer
    print(f"\n🔍 Test ErrorAnalyzer:")
    analyzer = ErrorAnalyzer()
    error_analyses = analyzer.analyze_validation_error(result)

    print(f"   - Analyses generated: {len(error_analyses)}")
    for i, analysis in enumerate(error_analyses):
        print(f"   - Analysis {i+1}:")
        print(f"     * Error type: {analysis.error_type}")
        print(f"     * Error message: {analysis.error_message}")
        print(f"     * Error pattern: {analysis.error_pattern}")
        print(f"     * Root cause: {analysis.root_cause}")
        print(f"     * Severity: {analysis.severity}")

    # Test PatchGenerator
    print(f"\n🔧 Test PatchGenerator:")
    generator = PatchGenerator()

    for i, analysis in enumerate(error_analyses):
        print(f"   - Generating patches for analysis {i+1}:")
        candidates = generator.generate_patches(analysis)
        print(f"     * Candidates generated: {len(candidates)}")

        for j, candidate in enumerate(candidates):
            print(f"     * Candidate {j+1}:")
            print(f"       - Description: {candidate.patch_description}")
            print(f"       - Strategy: {candidate.patch_strategy}")
            print(f"       - Confidence: {candidate.confidence}")
            print(f"       - Risk level: {candidate.risk_level}")
            print(f"       - Metadata: {candidate.metadata}")

if __name__ == "__main__":
    test_patch_generation()
