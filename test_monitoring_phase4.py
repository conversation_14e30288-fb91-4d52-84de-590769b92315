#!/usr/bin/env python3
"""
FASE 4.0.3 - Monitoring Dashboard Testing per NEUROGLYPH Patch Engine

Test per verificare il sistema di monitoring real-time.
"""

import time
import threading
from typing import List

from neuroglyph.cognitive.patch_generator import PatchGenerator
from neuroglyph.cognitive.patcher_structures import ErrorAnalysis
from neuroglyph.monitoring import get_metrics_collector, DashboardServer, AlertRule


def create_test_scenarios() -> List[ErrorAnalysis]:
    """Crea scenari di test per monitoring."""
    return [
        # Scenario 1: Import errors (alta success rate)
        ErrorAnalysis(
            error_id="monitor_test_1",
            error_type="import",
            error_message="ModuleNotFoundError: No module named 'numpy'",
            error_pattern="import_error",
            error_context="import numpy as np",
            severity="high"
        ),
        
        # Scenario 2: Syntax errors (alta success rate)
        ErrorAnalysis(
            error_id="monitor_test_2",
            error_type="syntax",
            error_message="SyntaxError: invalid syntax",
            error_pattern="syntax_invalid",
            error_context="if x == 5",
            severity="high"
        ),
        
        # Scenario 3: Runtime errors (media success rate)
        ErrorAnalysis(
            error_id="monitor_test_3",
            error_type="runtime",
            error_message="NameError: name 'undefined_var' is not defined",
            error_pattern="undefined_variable",
            error_context="print(undefined_var)",
            severity="medium"
        ),
        
        # Scenario 4: Pattern sconosciuto (bassa success rate)
        ErrorAnalysis(
            error_id="monitor_test_4",
            error_type="unknown",
            error_message="WeirdError: Something very strange happened",
            error_pattern="unknown_weird_pattern",
            error_context="mysterious_function()",
            severity="critical"
        ),
    ]


def simulate_patch_workload(generator: PatchGenerator, scenarios: List[ErrorAnalysis], 
                           duration_seconds: int = 30):
    """Simula carico di lavoro per testing monitoring."""
    print(f"🚀 Starting workload simulation for {duration_seconds} seconds...")
    
    start_time = time.time()
    iteration = 0
    
    while time.time() - start_time < duration_seconds:
        iteration += 1
        
        # Seleziona scenario random
        scenario = scenarios[iteration % len(scenarios)]
        
        # Reset state
        generator.reset_state()
        
        # Genera patches (questo registrerà automaticamente le metriche)
        patches = generator.generate_patches(scenario)
        
        # Simula processing time variabile
        if iteration % 10 == 0:
            time.sleep(0.1)  # Simula operazione più lenta
        else:
            time.sleep(0.01)  # Operazione normale
        
        if iteration % 50 == 0:
            print(f"   Completed {iteration} patch operations...")
    
    print(f"✅ Workload simulation completed: {iteration} operations in {duration_seconds}s")


def test_alert_system(metrics_collector):
    """Test sistema di alert."""
    print("\n🚨 Testing Alert System...")
    
    # Aggiungi regole di test
    metrics_collector.add_alert_rule(
        AlertRule("Test Low Success Rate", "success_rate", 50.0, "lt", cooldown_minutes=1)
    )
    
    metrics_collector.add_alert_rule(
        AlertRule("Test High Latency", "avg_latency_ms", 100.0, "gt", cooldown_minutes=1)
    )
    
    print("✅ Alert rules configured")


def print_real_time_metrics(metrics_collector, duration_seconds: int = 30):
    """Stampa metriche real-time."""
    print(f"\n📊 Real-time metrics monitoring for {duration_seconds} seconds...")
    print("=" * 80)
    
    start_time = time.time()
    
    while time.time() - start_time < duration_seconds:
        metrics = metrics_collector.get_current_metrics()
        
        # Clear screen (semplice)
        print("\033[H\033[J", end="")
        
        print("🎯 NEUROGLYPH Patch Engine - Real-Time Dashboard")
        print("=" * 60)
        print(f"Timestamp: {metrics['timestamp']}")
        print(f"Success Rate: {metrics['success_rate']}%")
        print(f"Avg Latency: {metrics['avg_latency_ms']}ms")
        print(f"Total Patches: {metrics['total_patches']}")
        print(f"Error Count: {metrics['error_count']}")
        print(f"Performance Grade: {metrics['performance_grade']}")
        print(f"Top Patterns: {', '.join(metrics['top_patterns'][:3])}")
        
        if metrics['pattern_breakdown']:
            print("\nPattern Breakdown:")
            for pattern, stats in list(metrics['pattern_breakdown'].items())[:5]:
                print(f"  {pattern}: {stats['success_rate']}% ({stats['avg_latency_ms']}ms)")
        
        print("\n" + "=" * 60)
        print("Press Ctrl+C to stop monitoring...")
        
        time.sleep(2)


def test_dashboard_generation(metrics_collector):
    """Test generazione dashboard HTML."""
    print("\n🌐 Testing Dashboard HTML Generation...")
    
    dashboard_server = DashboardServer(metrics_collector)
    html = dashboard_server.generate_html_dashboard()
    
    # Salva HTML per ispezione
    with open("dashboard_test.html", "w") as f:
        f.write(html)
    
    print("✅ Dashboard HTML generated and saved to 'dashboard_test.html'")
    print("   You can open this file in a browser to see the dashboard")


def main():
    """Main monitoring testing function."""
    print("🚀 NEUROGLYPH Patch Engine - Monitoring System Test")
    print("=" * 70)
    
    # Inizializza componenti
    print("🔧 Initializing components...")
    generator = PatchGenerator()
    metrics_collector = get_metrics_collector()
    scenarios = create_test_scenarios()
    
    # Test alert system
    test_alert_system(metrics_collector)
    
    # Avvia simulazione workload in background
    print("\n⚡ Starting background workload simulation...")
    workload_thread = threading.Thread(
        target=simulate_patch_workload,
        args=(generator, scenarios, 60),  # 60 secondi di simulazione
        daemon=True
    )
    workload_thread.start()
    
    # Aspetta un po' per accumulare dati
    print("⏳ Waiting for initial data accumulation...")
    time.sleep(5)
    
    # Test dashboard generation
    test_dashboard_generation(metrics_collector)
    
    # Mostra metriche real-time
    try:
        print_real_time_metrics(metrics_collector, duration_seconds=30)
    except KeyboardInterrupt:
        print("\n\n⏹️ Monitoring stopped by user")
    
    # Report finale
    print("\n📊 Final Metrics Report:")
    print("=" * 50)
    final_metrics = metrics_collector.get_current_metrics()
    
    print(f"Total Operations: {final_metrics['total_patches']}")
    print(f"Final Success Rate: {final_metrics['success_rate']}%")
    print(f"Final Avg Latency: {final_metrics['avg_latency_ms']}ms")
    print(f"Performance Grade: {final_metrics['performance_grade']}")
    
    # Valutazione
    if final_metrics['success_rate'] >= 80 and final_metrics['avg_latency_ms'] < 50:
        print("\n✅ MONITORING TEST PASSED!")
        print("   System is performing well and monitoring is working correctly.")
    elif final_metrics['success_rate'] >= 60:
        print("\n⚠️ MONITORING TEST PARTIAL SUCCESS")
        print("   System is working but may need optimization.")
    else:
        print("\n❌ MONITORING TEST FAILED")
        print("   System performance is below acceptable thresholds.")
    
    print(f"\n🎉 Monitoring test completed!")
    print(f"   Dashboard HTML saved to: dashboard_test.html")
    print(f"   Metrics database saved to: metrics.db")


if __name__ == "__main__":
    main()
