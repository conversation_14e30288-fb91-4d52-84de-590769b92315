{"summary": {"total_tests": 5, "encoding_successes": 5, "decoding_successes": 0, "round_trip_successes": 0, "ast_equivalences": 0, "encoding_success_rate": 1.0, "decoding_success_rate": 0.0, "round_trip_success_rate": 0.0, "ast_equivalence_rate": 0.0, "avg_encoding_time": 0.0042276081999997215, "avg_decoding_time": 0.003736874800000045, "avg_compression_ratio": 0.21590921324568374, "avg_symbols_count": 66.0}, "compression_comparison": {"neuroglyph_ratio": 0.21590921324568374, "gzip_ratio": 0.3700331232415922, "brotli_ratio": 0.3090684037766557, "vs_gzip_factor": 1.713836652355036, "vs_brotli_factor": 1.431473901139021}, "file_size_analysis": {"small_files": {"count": 2, "avg_compression": 0.45490464518142326, "ast_equivalence_rate": 0.0}, "medium_files": {"count": 3, "avg_compression": 0.05657892528852402, "ast_equivalence_rate": 0.0}, "large_files": {"count": 0, "avg_compression": 0, "ast_equivalence_rate": 0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_ua_xgev1/scikit-learn-main/sklearn/utils/optimize.py", "file_size": 12298, "lines_count": 390, "encoding_success": true, "encoding_time": 0.008737082999999757, "compressed_size": 904, "compression_ratio": 0.07350788746137583, "symbols_count": 102, "decoding_success": false, "decoding_time": 0.006041624999999939, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": 1423, "gzip_size": 3941, "brotli_size": 3430, "gzip_ratio": 0.3204586111562856, "brotli_ratio": 0.27890713937225564, "error_message": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_ua_xgev1/scikit-learn-main/sklearn/utils/_missing.py", "file_size": 1479, "lines_count": 69, "encoding_success": true, "encoding_time": 0.0022691669999996833, "compressed_size": 548, "compression_ratio": 0.3705206220419202, "symbols_count": 68, "decoding_success": false, "decoding_time": 0.002443833000000062, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": 95, "gzip_size": 664, "brotli_size": 536, "gzip_ratio": 0.4489519945909398, "brotli_ratio": 0.36240703177822853, "error_message": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_ua_xgev1/scikit-learn-main/sklearn/utils/_arpack.py", "file_size": 1209, "lines_count": 34, "encoding_success": true, "encoding_time": 0.0016634999999993738, "compressed_size": 652, "compression_ratio": 0.5392886683209264, "symbols_count": 74, "decoding_success": false, "decoding_time": 0.0046102079999998935, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": 47, "gzip_size": 552, "brotli_size": 441, "gzip_ratio": 0.456575682382134, "brotli_ratio": 0.36476426799007444, "error_message": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_ua_xgev1/scikit-learn-main/sklearn/utils/_chunking.py", "file_size": 5438, "lines_count": 179, "encoding_success": true, "encoding_time": 0.0036442499999997935, "compressed_size": 370, "compression_ratio": 0.0680397204854726, "symbols_count": 40, "decoding_success": false, "decoding_time": 0.0029677079999999023, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": 475, "gzip_size": 1682, "brotli_size": 1449, "gzip_ratio": 0.3093048915042295, "brotli_ratio": 0.2664582567120265, "error_message": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_encoder_test_ua_xgev1/scikit-learn-main/sklearn/utils/fixes.py", "file_size": 13977, "lines_count": 395, "encoding_success": true, "encoding_time": 0.004824041000000001, "compressed_size": 394, "compression_ratio": 0.02818916791872362, "symbols_count": 46, "decoding_success": false, "decoding_time": 0.0026210000000004285, "round_trip_success": false, "ast_equivalent": false, "ast_diff_count": 2185, "gzip_size": 4401, "brotli_size": 3813, "gzip_ratio": 0.3148744365743722, "brotli_ratio": 0.27280532303069327, "error_message": null}]}