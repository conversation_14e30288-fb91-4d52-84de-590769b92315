#!/usr/bin/env python3
"""
Test di integrazione end-to-end per NEUROGLYPH Pipeline
Verifica il funzionamento completo della pipeline cognitiva
"""

import sys
import time
from pathlib import Path

# Aggiungi il path del progetto
sys.path.insert(0, str(Path(__file__).parent))

from neuroglyph.cognitive import NGIntegration
from neuroglyph.cognitive.integration import PipelineConfig, ExecutionMode
from neuroglyph.cognitive.learner_structures import LearnerConfig, LearningMode


def test_simple_code_execution():
    """Test esecuzione codice semplice."""
    print("🧪 Test 1: Esecuzione codice semplice")
    print("-" * 50)
    
    # Codice di test semplice
    source_code = """
def greet(name):
    return f"Hello, {name}!"

result = greet("NEUROGLYPH")
print(result)
"""
    
    # Configurazione base
    config = PipelineConfig(
        execution_mode=ExecutionMode.DEBUG,
        enable_detailed_logging=True
    )
    
    # Esegui pipeline
    pipeline = NGIntegration(config)
    result = pipeline.run(source_code)
    
    # Verifica risultati
    print(f"✅ Success: {result.success}")
    print(f"📊 Stage: {result.current_stage.value}")
    print(f"📤 Output: {result.final_output}")
    
    if result.metrics:
        total_time = result.metrics.end_time - result.metrics.start_time if result.metrics.end_time else 0
        print(f"⏱️  Time: {total_time:.3f}s")
    
    print(f"📋 Trace: {len(result.execution_trace)} steps")
    for trace in result.execution_trace:
        print(f"  • {trace}")
    
    return result.success


def test_error_correction():
    """Test correzione errori con adaptive patcher."""
    print("\n🧪 Test 2: Correzione errori")
    print("-" * 50)
    
    # Codice con errore intenzionale
    source_code = """
def calculate_area(radius):
    # Errore: math non importato
    return math.pi * radius ** 2

result = calculate_area(5)
print(f"Area: {result}")
"""
    
    # Configurazione con patching abilitato
    config = PipelineConfig(
        execution_mode=ExecutionMode.DEBUG,
        enable_adaptive_patching=True,
        enable_detailed_logging=True
    )
    
    # Esegui pipeline
    pipeline = NGIntegration(config)
    result = pipeline.run(source_code)
    
    # Verifica risultati
    print(f"✅ Success: {result.success}")
    print(f"📊 Stage: {result.current_stage.value}")
    print(f"🔧 Patches: {len(result.patch_results)}")
    
    for i, patch in enumerate(result.patch_results, 1):
        status = "✓" if patch.success else "✗"
        print(f"  {i}. {status} {patch.patch_id}: {patch.error_message or 'Success'}")
    
    if result.execution_result:
        print(f"🏃 Execution: {'Success' if result.execution_result.success else 'Failed'}")
        if result.execution_result.error_message:
            print(f"❌ Error: {result.execution_result.error_message}")
    
    return len(result.patch_results) > 0


def test_learning_integration():
    """Test integrazione con learning module."""
    print("\n🧪 Test 3: Learning Integration")
    print("-" * 50)
    
    # Configurazione con learning abilitato
    learner_config = LearnerConfig(
        learning_mode=LearningMode.AGGRESSIVE,
        min_pattern_confidence=0.1,
        save_patterns=False  # Non salvare durante test
    )
    
    config = PipelineConfig(
        execution_mode=ExecutionMode.DEBUG,
        enable_learning=True,
        enable_adaptive_patching=True,
        learner_config=learner_config
    )
    
    # Codici di test multipli per generare pattern
    test_codes = [
        """
# Test 1: NameError
def test1():
    return undefined_variable
result1 = test1()
""",
        """
# Test 2: ImportError
def test2():
    return os.path.join("a", "b")
result2 = test2()
""",
        """
# Test 3: AttributeError
def test3():
    x = "hello"
    return x.nonexistent_method()
result3 = test3()
"""
    ]
    
    pipeline = NGIntegration(config)
    
    # Esegui pipeline su codici multipli
    all_results = []
    for i, code in enumerate(test_codes, 1):
        print(f"\n  Esecuzione {i}/3...")
        result = pipeline.run(code)
        all_results.append(result)
        
        if result.learning_results:
            print(f"  🧠 Learning: {result.learning_results}")
    
    # Verifica che il learning abbia funzionato
    learning_successful = any(r.learning_results for r in all_results)
    print(f"\n✅ Learning Active: {learning_successful}")
    
    # Statistiche pipeline
    stats = pipeline.get_pipeline_statistics()
    print(f"📊 Pipeline Stats:")
    print(f"  • Total executions: {stats['total_executions']}")
    print(f"  • Success rate: {stats['success_rate']:.1%}")
    
    return learning_successful


def test_dry_run_mode():
    """Test modalità dry-run."""
    print("\n🧪 Test 4: Dry Run Mode")
    print("-" * 50)
    
    source_code = """
def complex_function():
    import time
    time.sleep(1)  # Simula operazione lunga
    return "Complex result"

result = complex_function()
print(result)
"""
    
    # Configurazione dry-run
    config = PipelineConfig(
        execution_mode=ExecutionMode.DRY_RUN,
        enable_sandbox=False,  # Dry run non esegue sandbox
        enable_detailed_logging=True
    )
    
    # Esegui pipeline
    start_time = time.time()
    pipeline = NGIntegration(config)
    result = pipeline.run(source_code)
    execution_time = time.time() - start_time
    
    # Verifica che sia veloce (dry run)
    print(f"✅ Success: {result.success}")
    print(f"⏱️  Time: {execution_time:.3f}s (should be fast)")
    print(f"📊 Stage: {result.current_stage.value}")
    print(f"🏃 Sandbox executed: {result.execution_result is not None}")
    
    # Dry run dovrebbe essere veloce e non eseguire sandbox
    return execution_time < 1.0 and result.execution_result is None


def test_full_pipeline_with_all_modules():
    """Test pipeline completa con tutti i moduli abilitati."""
    print("\n🧪 Test 5: Pipeline Completa")
    print("-" * 50)
    
    source_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Calcola alcuni numeri di Fibonacci
results = []
for i in range(5):
    fib = fibonacci(i)
    results.append(fib)
    print(f"fib({i}) = {fib}")

print(f"Results: {results}")
"""
    
    # Configurazione completa
    learner_config = LearnerConfig(
        learning_mode=LearningMode.BALANCED,
        save_patterns=False
    )
    
    config = PipelineConfig(
        execution_mode=ExecutionMode.PRODUCTION,
        enable_self_check=True,
        enable_sandbox=True,
        enable_adaptive_patching=True,
        enable_learning=True,
        enable_detailed_logging=True,
        learner_config=learner_config
    )
    
    # Esegui pipeline
    pipeline = NGIntegration(config)
    result = pipeline.run(source_code)
    
    # Verifica tutti gli stadi
    stages_completed = []
    if result.metrics:
        stages_completed = list(result.metrics.stage_timings.keys())
    
    print(f"✅ Success: {result.success}")
    print(f"📊 Stages completed: {len(stages_completed)}")
    for stage in stages_completed:
        timing = result.metrics.stage_timings[stage]
        print(f"  • {stage}: {timing:.3f}s")
    
    print(f"📤 Final output length: {len(result.final_output) if result.final_output else 0}")
    
    # Pipeline completa dovrebbe completare tutti gli stadi principali
    expected_stages = ["parsing", "prioritization", "memory_retrieval", "reasoning"]
    stages_present = all(stage in stages_completed for stage in expected_stages)
    
    return result.success and stages_present


def main():
    """Esegue tutti i test di integrazione."""
    print("🚀 NEUROGLYPH Integration Pipeline Tests")
    print("=" * 60)
    
    tests = [
        ("Simple Code Execution", test_simple_code_execution),
        ("Error Correction", test_error_correction),
        ("Learning Integration", test_learning_integration),
        ("Dry Run Mode", test_dry_run_mode),
        ("Full Pipeline", test_full_pipeline_with_all_modules)
    ]
    
    results = []
    start_time = time.time()
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success, None))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False, str(e)))
    
    total_time = time.time() - start_time
    
    # Riepilogo risultati
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for test_name, success, error in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"      Error: {error}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({passed/total:.1%})")
    print(f"⏱️  Total time: {total_time:.3f}s")
    
    if passed == total:
        print("\n🎉 All tests passed! NEUROGLYPH Pipeline is working correctly.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. Check the output above for details.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
