#!/usr/bin/env python3
"""
Test di debug per identificare dove si blocca NEUROGLYPH
"""

import sys
from pathlib import Path

# Aggiungi il path del progetto
sys.path.insert(0, str(Path(__file__).parent))

def test_step_by_step():
    """Test passo per passo per identificare il blocco."""
    print("🔍 Debug Step-by-Step")
    print("-" * 30)
    
    try:
        print("1. Import NGIntegration...")
        from neuroglyph.cognitive import NGIntegration
        print("✅ Import riuscito")
        
        print("2. Import PipelineConfig...")
        from neuroglyph.cognitive.integration import PipelineConfig, ExecutionMode
        print("✅ Import riuscito")
        
        print("3. Creazione config...")
        config = PipelineConfig(
            execution_mode=ExecutionMode.DRY_RUN,
            enable_self_check=False,
            enable_sandbox=False,
            enable_adaptive_patching=False,
            enable_learning=False
        )
        print("✅ Config creata")
        
        print("4. Inizializzazione NGIntegration...")
        print("   Questo potrebbe richiedere tempo...")
        pipeline = NGIntegration(config)
        print("✅ NGIntegration inizializzato")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_only():
    """Test solo del modulo memory."""
    print("\n🔍 Test Memory Only")
    print("-" * 30)
    
    try:
        print("1. Import NGMemory...")
        from neuroglyph.cognitive import NGMemory
        print("✅ Import riuscito")
        
        print("2. Inizializzazione NGMemory...")
        memory = NGMemory()
        print("✅ NGMemory inizializzato")
        
        print("3. Test append...")
        from neuroglyph.cognitive.data_structures import PriorityVector, DomainType

        prio_vec = PriorityVector(
            urgency=0.5,
            risk=0.2,
            domain=DomainType.CODE,
            confidence=0.8
        )
        
        record = memory.append(
            prompt="test prompt",
            response="test response",
            prio_vec=prio_vec,
            symbols=["<NG_TEST>"],
            metadata={"test": True}
        )
        print(f"✅ Record creato: {record.record_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_learner_only():
    """Test solo del modulo learner."""
    print("\n🔍 Test Learner Only")
    print("-" * 30)
    
    try:
        print("1. Import NGLearner...")
        from neuroglyph.cognitive import NGLearner
        print("✅ Import riuscito")
        
        print("2. Import LearnerConfig...")
        from neuroglyph.cognitive.learner_structures import LearnerConfig, LearningMode
        print("✅ Import riuscito")
        
        print("3. Creazione config...")
        config = LearnerConfig(
            learning_mode=LearningMode.BALANCED,
            save_patterns=False
        )
        print("✅ Config creata")
        
        print("4. Inizializzazione NGLearner...")
        learner = NGLearner(config)
        print("✅ NGLearner inizializzato")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parser_memory_integration():
    """Test integrazione parser + memory."""
    print("\n🔍 Test Parser + Memory Integration")
    print("-" * 30)
    
    try:
        print("1. Import parser...")
        from neuroglyph.core.parser.ng_parser import NGParser
        print("✅ Parser importato")
        
        print("2. Import memory...")
        from neuroglyph.cognitive import NGMemory
        print("✅ Memory importato")
        
        print("3. Inizializzazione...")
        parser = NGParser()
        memory = NGMemory()
        print("✅ Moduli inizializzati")
        
        print("4. Test parsing...")
        parsed = parser.parse("print('hello')")
        print(f"✅ Parsing: {len(parsed.tokens)} tokens, {len(parsed.symbols)} simboli")
        
        print("5. Test memory append...")
        from neuroglyph.cognitive.data_structures import PriorityVector, DomainType

        prio_vec = PriorityVector(
            urgency=0.5,
            risk=0.2,
            domain=DomainType.CODE,
            confidence=0.8
        )
        
        record = memory.append(
            prompt=parsed.original_text,
            response="",
            prio_vec=prio_vec,
            symbols=parsed.symbols,
            metadata=parsed.metadata
        )
        print(f"✅ Memory record: {record.record_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Esegue tutti i test di debug."""
    print("🚀 NEUROGLYPH Debug Tests")
    print("=" * 40)
    
    tests = [
        ("Memory Only", test_memory_only),
        ("Learner Only", test_learner_only),
        ("Parser + Memory", test_parser_memory_integration),
        ("Step by Step", test_step_by_step)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 {test_name}")
            success = test_func()
            results.append((test_name, success, None))
            if not success:
                print(f"❌ {test_name} fallito - interrompo qui")
                break
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False, str(e)))
            break
    
    # Riepilogo
    print("\n" + "=" * 40)
    print("📊 RISULTATI DEBUG")
    print("=" * 40)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for test_name, success, error in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"      Error: {error}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Tutti i test di debug sono passati!")
        return 0
    else:
        print(f"\n⚠️  Debug interrotto al test: {results[-1][0]}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
