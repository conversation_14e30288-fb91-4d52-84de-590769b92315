#!/usr/bin/env python3
"""
Test manuale per verificare il fix di sintassi
"""

from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
from neuroglyph.cognitive.patcher_structures import PatchCandidate, PatchType

def test_syntax_fix():
    """Test manuale del fix di sintassi."""
    print("🔧 Test Syntax Fix")
    
    # Codice con errore di sintassi
    broken_code = """def test_function()  # Missing colon
    return 42
"""
    
    print(f"📝 Codice originale:")
    print(broken_code)
    
    # Test compilazione
    try:
        compile(broken_code, '<string>', 'exec')
        print("✅ Compilazione riuscita")
    except Exception as e:
        print(f"❌ Errore di compilazione: {e}")
    
    # Crea patch candidate
    candidate = PatchCandidate(
        patch_type=PatchType.CODE_FIX,
        target_error_id="test_error",
        patch_description="Fix syntax error",
        patch_strategy="syntax_invalid",
        confidence=0.9,
        risk_level="low"
    )
    
    # Applica patch
    patcher = NGAdaptivePatcher()
    fixed_code = patcher._generate_patched_code(candidate, broken_code)
    
    print(f"\n📝 Codice dopo patch:")
    print(fixed_code)
    
    # Test compilazione del codice patchato
    try:
        compile(fixed_code, '<string>', 'exec')
        print("✅ Compilazione del codice patchato riuscita!")
        return True
    except Exception as e:
        print(f"❌ Errore di compilazione del codice patchato: {e}")
        return False

if __name__ == "__main__":
    success = test_syntax_fix()
    print(f"\n🎯 Risultato: {'SUCCESS' if success else 'FAILED'}")
