#!/usr/bin/env python3
"""
Test manuale per verificare il fix semantico
"""

from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
from neuroglyph.cognitive.patcher_structures import PatchCandidate, PatchType

def test_semantic_fix():
    """Test manuale del fix semantico."""
    print("🔧 Test Semantic Fix")
    
    # Codice con errore semantico
    broken_code = """x = 5
undefined_function_call()
print(x)
"""
    
    print(f"📝 Codice originale:")
    print(broken_code)
    
    # Test esecuzione
    try:
        exec(broken_code)
        print("✅ Esecuzione riuscita")
    except Exception as e:
        print(f"❌ Errore di esecuzione: {e}")
    
    # Crea patch candidate
    candidate = PatchCandidate(
        patch_type=PatchType.CODE_FIX,
        target_error_id="test_error",
        patch_description="Remove undefined function call",
        patch_strategy="remove_undefined_call",
        confidence=0.9,
        risk_level="low"
    )
    candidate.metadata = {"function_name": "undefined_function_call"}
    
    # Applica patch
    patcher = NGAdaptivePatcher()
    fixed_code = patcher._generate_patched_code(candidate, broken_code)
    
    print(f"\n📝 Codice dopo patch:")
    print(fixed_code)
    
    # Test esecuzione del codice patchato
    try:
        exec(fixed_code)
        print("✅ Esecuzione del codice patchato riuscita!")
        return True
    except Exception as e:
        print(f"❌ Errore di esecuzione del codice patchato: {e}")
        return False

if __name__ == "__main__":
    success = test_semantic_fix()
    print(f"\n🎯 Risultato: {'SUCCESS' if success else 'FAILED'}")
