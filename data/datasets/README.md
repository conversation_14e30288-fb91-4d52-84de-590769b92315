# NEUROGLYPH Datasets

Questa directory contiene i dataset per training e evaluation di NEUROGLYPH.

## Struttura

```
data/datasets/
├── raw/                    # Dataset originali non processati
│   ├── logiqa/            # LogiQA dataset
│   ├── gsm8k/             # GSM8K mathematical reasoning
│   ├── humaneval/         # HumanEval code generation
│   ├── arc/               # ARC commonsense reasoning
│   └── hellaswag/         # HellaSwag reading comprehension
├── processed/             # Dataset processati e normalizzati
│   ├── logiqa_processed.jsonl
│   ├── gsm8k_processed.jsonl
│   ├── humaneval_processed.jsonl
│   ├── arc_processed.jsonl
│   └── hellaswag_processed.jsonl
├── symbolic/              # Dataset con annotazioni simboliche NEUROGLYPH
│   ├── logiqa_symbolic.jsonl
│   ├── gsm8k_symbolic.jsonl
│   └── humaneval_symbolic.jsonl
├── splits/                # Train/dev/test splits stratificati
│   ├── train/
│   ├── dev/
│   └── test/
└── quality_reports/       # Report di qualità e validazione
    ├── data_quality_report.json
    ├── annotation_agreement.json
    └── coverage_analysis.json
```

## Quality Gates per Dataset

### 1. Data Quality
- ✅ **Completezza**: Tutti i campi richiesti presenti
- ✅ **Consistenza**: Formati uniformi tra dataset
- ✅ **Validità**: Risposte corrette verificate
- ✅ **Unicità**: Nessun duplicato

### 2. Annotation Quality
- ✅ **Inter-annotator Agreement**: Cohen's κ > 0.8
- ✅ **Symbolic Coverage**: ≥90% simboli NEUROGLYPH utilizzati
- ✅ **Reasoning Depth**: 3-8 step multi-hop per sample

### 3. Coverage Analysis
- ✅ **Domain Coverage**: Tutti i domini rappresentati
- ✅ **Difficulty Distribution**: Easy/Medium/Hard bilanciati
- ✅ **Error Pattern Coverage**: Errori comuni inclusi

## Utilizzo

```python
from neuroglyph.evaluation.benchmarks import LogiQALoader

# Carica dataset processato
loader = LogiQALoader()
samples = loader.load_samples("data/datasets/processed/logiqa_processed.jsonl")

# Carica con annotazioni simboliche
samples = loader.load_samples("data/datasets/symbolic/logiqa_symbolic.jsonl")
```

## Licenze

- LogiQA: MIT License
- GSM8K: MIT License  
- HumanEval: MIT License
- ARC: Apache 2.0
- HellaSwag: MIT License

Tutti i dataset sono utilizzati per ricerca accademica in conformità alle rispettive licenze.
