{"initial_examples": 1000, "target_examples": 5000, "generated_examples": 3200, "final_examples": 4200, "symbol_coverage": {"∧": 800, "∀": 500, "∃": 500, "⇒": 500, "⊢": 800, "∫": 300, "🔄": 200, "⤴": 200, "🏛️": 200, "⚡": 200, "⟧": 200, "⟦": 200}, "validation_results": [{"type": "final", "validation_rate": 1.0, "quality_scores": {"structural_validity": 1.0, "symbol_registry_compliance": 1.0, "difficulty_coherence": 1.0, "logical_validity": 1.0, "premise_complexity": 1.0, "conclusion_clarity": 0.6607619047619048, "natural_quality": 0.9, "overall_quality": 0.9864172335600907, "mathematical_correctness": 1.0, "problem_complexity": 0.5656666666666668, "solution_clarity": 0.9043333333333333, "syntax_validity": 1.0, "symbolic_coherence": 1.0, "symbol_mapping": 1.0, "category_validation": 0.8}}], "generated_at": "2025-06-07T13:14:11.178894"}