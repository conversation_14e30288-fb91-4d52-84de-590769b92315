{"verification_summary": {"verification_passed": false, "timestamp": "2025-06-07T14:25:29.124680", "total_tests": 500}, "metrics": {"parseability_rate": 1.0, "ast_equivalence_rate": 0.288, "average_fidelity": 1.0, "successful_parses": 500, "ast_equivalent_count": 144}, "assessment": {"critical_failures": ["FAILED: AST Equivalence Rate"], "recommendations": ["CRITICAL: AST roundtrip verification failed", "DO NOT proceed with fine-tuning until fixed", "Improve symbolic encoding/decoding pipeline"]}, "sample_results": {"success_examples": [{"example_id": "math_74f1b8ac", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_975579bf", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_ba1b0931", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_a0e387f3", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_31204e4b", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_6e6b124a", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_1a889fa9", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "code_73859a82", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_bbf3f9e2", "fidelity_score": 1.0, "ast_equivalent": true}, {"example_id": "math_5d8fb3d6", "fidelity_score": 1.0, "ast_equivalent": true}], "failed_examples": [{"example_id": "logical_1f889ab2", "error_details": null, "fidelity_score": 0.0}, {"example_id": "complex_propositional_logic_1533", "error_details": null, "fidelity_score": 0.0}, {"example_id": "reverse_quantifier_reverse_1002", "error_details": null, "fidelity_score": 0.0}, {"example_id": "logical_68f5c154", "error_details": null, "fidelity_score": 0.0}, {"example_id": "structure_∈_155", "error_details": null, "fidelity_score": 0.0}, {"example_id": "structure_⊆_67", "error_details": null, "fidelity_score": 0.0}, {"example_id": "structure_⊇_8", "error_details": null, "fidelity_score": 0.0}, {"example_id": "logical_d681d64c", "error_details": null, "fidelity_score": 0.0}, {"example_id": "complex_set_theory_1847", "error_details": null, "fidelity_score": 0.0}, {"example_id": "multistep_95358", "error_details": null, "fidelity_score": 0.0}]}}