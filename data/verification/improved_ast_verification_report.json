{"verification_summary": {"verification_passed": true, "improvement_achieved": true, "timestamp": "2025-06-07T14:36:49.575901", "total_tests": 500}, "metrics": {"success_rate": 1.0, "fidelity_rate": 1.0, "ast_equivalence_rate": 0.0, "average_fidelity": 1.0, "successful_tests": 500, "high_fidelity_tests": 500, "ast_equivalent_tests": 0}, "assessment": {"recommendations": ["EXCELLENT: Improved system shows significant progress", "READY: Semantic fidelity meets requirements", "NOTE: AST equivalence still needs work (acceptable for now)"]}, "sample_results": [{"example_id": "logical_56c63ff1", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "structure_∅_99", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "math_db9c608c", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "math_6c0264da", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "reverse_logical_reverse_729", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "math_eb7a2e77", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "complex_function_theory_369", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "logical_22554529", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "math_a13b51ba", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "math_aa81d620", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "logical_72f6bdda", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "story_universal_uniqueness_116", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "math_2490c50b", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "code_678a7a8b", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "structure_∈_12", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "multistep_73710", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "logical_7d30b35b", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "complex_mathematical_logic_610", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "code_b1d5b177", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}, {"example_id": "complex_mathematical_logic_513", "success": true, "ast_equivalent": false, "semantic_fidelity": 1.0, "preservation_successful": true}]}