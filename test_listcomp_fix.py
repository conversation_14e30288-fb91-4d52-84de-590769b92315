#!/usr/bin/env python3

from neuroglyph.core.encoder.encoder import NGEncoder

# Test PATCH 2: Distribuzione statement intelligente (quicksort)
code = """
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
""".strip()

print("🔍 TEST PATCH 2: DISTRIBUZIONE STATEMENT INTELLIGENTE")
print("=" * 50)

try:
    encoder = NGEncoder()
    result = encoder.round_trip_test(code, encoding_level=5)
    
    print("\n📝 CODICE ORIGINALE:")
    print(code)
    
    print("\n🔧 SIMBOLI GENERATI:")
    print(result.encoding_result.compressed_symbols)
    
    print("\n🔄 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    
    print("\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Semantica equivalente: {'✅' if result.semantic_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
except Exception as e:
    print(f"❌ ERRORE: {e}")
    import traceback
    traceback.print_exc()
