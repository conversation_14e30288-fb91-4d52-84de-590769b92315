#!/usr/bin/env python3
"""
FASE 4.1 - AST Round-Trip Validation Testing per NEUROGLYPH

Test completo del round-trip AST:
- Corpus collection
- AST round-trip validation  
- Performance benchmarking
- Compression comparison
"""

import time
from pathlib import Path

from neuroglyph.validation.ast_roundtrip_validator import ASTRoundTripValidator
from neuroglyph.validation.corpus_collector import CorpusCollector, create_sample_corpus


def test_sample_corpus():
    """Test rapido su corpus di esempio."""
    print("🚀 FASE 4.1.1 - Testing Sample Corpus")
    print("=" * 60)
    
    # Crea corpus di esempio
    sample_dir = create_sample_corpus()
    
    # Inizializza validator
    validator = ASTRoundTripValidator()
    
    # Valida corpus
    metrics = validator.validate_corpus(sample_dir, max_files=10)
    
    # Mostra risultati
    print("\n📊 Sample Corpus Results:")
    summary = metrics.get_summary()
    for key, value in summary.items():
        print(f"   {key}: {value}")
    
    # Genera report
    report_path = Path("ast_roundtrip_sample_report.json")
    validator.generate_report(report_path)
    
    # Valutazione
    success_rate = metrics.round_trip_success_rate
    ast_rate = metrics.ast_equivalence_rate
    
    print(f"\n🎯 Sample Corpus Evaluation:")
    print(f"   Round-trip Success: {success_rate}%")
    print(f"   AST Equivalence: {ast_rate}%")
    
    if success_rate >= 90 and ast_rate >= 90:
        print("   ✅ SAMPLE TEST PASSED!")
        return True
    else:
        print("   ⚠️ SAMPLE TEST NEEDS IMPROVEMENT")
        return False


def test_real_world_corpus():
    """Test su corpus real-world."""
    print("\n🚀 FASE 4.1.2 - Real-World Corpus Collection & Testing")
    print("=" * 60)
    
    # Inizializza collector
    collector = CorpusCollector()
    
    # Raccoglie corpus (versione ridotta per test)
    print("📁 Collecting real-world corpus...")
    try:
        stats = collector.collect_all_sources(max_total_files=50)  # Ridotto per test
        print(f"✅ Corpus collected: {stats['total_files']} files")
    except Exception as e:
        print(f"⚠️ Corpus collection failed: {e}")
        print("   Using fallback: creating edge cases only")
        collector.create_edge_cases_corpus()
        stats = {"total_files": 4}
    
    # Crea edge cases
    edge_count = collector.create_edge_cases_corpus()
    
    # Salva manifest
    collector.save_corpus_manifest()
    
    # Valida corpus real-world
    validator = ASTRoundTripValidator()
    
    if collector.corpus_dir.exists():
        print(f"\n🔍 Validating corpus: {collector.corpus_dir}")
        metrics = validator.validate_corpus(collector.corpus_dir, max_files=100)
        
        # Mostra risultati
        print("\n📊 Real-World Corpus Results:")
        summary = metrics.get_summary()
        for key, value in summary.items():
            print(f"   {key}: {value}")
        
        # Genera report dettagliato
        report_path = Path("ast_roundtrip_realworld_report.json")
        validator.generate_report(report_path)
        
        return metrics
    else:
        print("⚠️ No real-world corpus available for testing")
        return None


def test_compression_benchmarks():
    """Test benchmark compression vs Gzip/Brotli."""
    print("\n🚀 FASE 4.1.3 - Compression Benchmarking")
    print("=" * 60)
    
    # Usa sample corpus per benchmark
    sample_dir = Path("sample_corpus")
    if not sample_dir.exists():
        create_sample_corpus()
    
    validator = ASTRoundTripValidator()
    
    # Benchmark ogni file
    benchmark_results = []
    for file_path in sample_dir.glob("*.py"):
        print(f"📊 Benchmarking: {file_path.name}")
        
        try:
            result = validator.benchmark_compression(file_path)
            benchmark_results.append(result)
            
            print(f"   Original size: {result['original_size']} bytes")
            print(f"   Gzip ratio: {result['gzip_ratio']}%")
            print(f"   Brotli ratio: {result['brotli_ratio']}%")
            print(f"   NEUROGLYPH ratio: {result['neuroglyph_ratio']}%")
            print(f"   AST preserved: {result['neuroglyph_maintains_ast']}")
            
        except Exception as e:
            print(f"   ❌ Benchmark failed: {e}")
    
    # Calcola medie
    if benchmark_results:
        avg_gzip = sum(r['gzip_ratio'] for r in benchmark_results) / len(benchmark_results)
        avg_brotli = sum(r['brotli_ratio'] for r in benchmark_results) / len(benchmark_results)
        avg_neuroglyph = sum(r['neuroglyph_ratio'] for r in benchmark_results) / len(benchmark_results)
        ast_preserved_count = sum(1 for r in benchmark_results if r['neuroglyph_maintains_ast'])
        
        print(f"\n📈 Compression Benchmark Summary:")
        print(f"   Average Gzip ratio: {avg_gzip:.2f}%")
        print(f"   Average Brotli ratio: {avg_brotli:.2f}%")
        print(f"   Average NEUROGLYPH ratio: {avg_neuroglyph:.2f}%")
        print(f"   AST preservation rate: {(ast_preserved_count/len(benchmark_results)*100):.1f}%")
        
        return {
            "avg_gzip_ratio": avg_gzip,
            "avg_brotli_ratio": avg_brotli,
            "avg_neuroglyph_ratio": avg_neuroglyph,
            "ast_preservation_rate": (ast_preserved_count/len(benchmark_results)*100)
        }
    
    return None


def run_comprehensive_validation():
    """Esegue validazione completa Fase 4.1."""
    print("🎯 NEUROGLYPH FASE 4.1 - AST ROUND-TRIP VALIDATION")
    print("=" * 70)
    print("Obiettivi:")
    print("  - Round-trip AST ≥90% su corpus real-world")
    print("  - AST Equivalenza ≥95%")
    print("  - Benchmark compression vs Gzip/Brotli")
    print("  - Automazione testing e reporting")
    print("=" * 70)
    
    start_time = time.time()
    results = {}
    
    # Test 1: Sample corpus
    sample_success = test_sample_corpus()
    results["sample_test_passed"] = sample_success
    
    # Test 2: Real-world corpus
    realworld_metrics = test_real_world_corpus()
    if realworld_metrics:
        results["realworld_round_trip_rate"] = realworld_metrics.round_trip_success_rate
        results["realworld_ast_equivalence_rate"] = realworld_metrics.ast_equivalence_rate
    
    # Test 3: Compression benchmarks
    compression_results = test_compression_benchmarks()
    if compression_results:
        results.update(compression_results)
    
    # Valutazione finale
    total_time = time.time() - start_time
    
    print(f"\n🏁 FASE 4.1 VALIDATION COMPLETED in {total_time:.2f}s")
    print("=" * 70)
    
    # Criteri di successo
    success_criteria = {
        "Sample test passed": results.get("sample_test_passed", False),
        "Real-world round-trip ≥90%": results.get("realworld_round_trip_rate", 0) >= 90,
        "Real-world AST equiv ≥95%": results.get("realworld_ast_equivalence_rate", 0) >= 95,
        "AST preservation ≥90%": results.get("ast_preservation_rate", 0) >= 90
    }
    
    print("📊 Success Criteria Evaluation:")
    passed_criteria = 0
    for criterion, passed in success_criteria.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {criterion}: {status}")
        if passed:
            passed_criteria += 1
    
    overall_success = passed_criteria >= 3  # Almeno 3/4 criteri
    
    if overall_success:
        print(f"\n🎉 FASE 4.1 VALIDATION SUCCESSFUL!")
        print(f"   Passed {passed_criteria}/4 success criteria")
        print(f"   AST Round-Trip system is PRODUCTION-READY!")
    else:
        print(f"\n⚠️ FASE 4.1 VALIDATION NEEDS IMPROVEMENT")
        print(f"   Passed {passed_criteria}/4 success criteria")
        print(f"   Review failed criteria and optimize system")
    
    # Salva risultati finali
    import json
    final_results = {
        "phase": "4.1_AST_RoundTrip_Validation",
        "timestamp": time.time(),
        "total_time_seconds": total_time,
        "success_criteria": success_criteria,
        "passed_criteria": passed_criteria,
        "overall_success": overall_success,
        "detailed_results": results
    }
    
    with open("phase41_validation_results.json", "w") as f:
        json.dump(final_results, f, indent=2)
    
    print(f"\n📋 Detailed results saved to: phase41_validation_results.json")
    
    return overall_success


def main():
    """Main function per testing Fase 4.1."""
    try:
        success = run_comprehensive_validation()
        exit_code = 0 if success else 1
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)


if __name__ == "__main__":
    main()
