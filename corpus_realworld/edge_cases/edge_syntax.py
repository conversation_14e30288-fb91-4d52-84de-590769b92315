# Edge case Python syntax
async def async_function():
    async with some_context() as ctx:
        async for item in async_iterator():
            yield item

@decorator_with_args(arg1="value")
@another_decorator
class EdgeCaseClass:
    def __init__(self, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs
    
    def method_with_annotations(self, x: int, y: str = "default") -> bool:
        return isinstance(x, int) and isinstance(y, str)