#!/usr/bin/env python3

from neuroglyph.core.encoder.encoder import NGEncoder

# Codice list comprehension dal test
list_comp_code = """
def process_data():
    filtered = [x for x in items if x > 0]
    return [x**2 for x in filtered]
""".strip()

print("🔍 ANALISI PROBLEMA LIST COMPREHENSION")
print("=" * 50)

encoder = NGEncoder()
result = encoder.round_trip_test(list_comp_code, encoding_level=3)

print("\n📝 CODICE ORIGINALE:")
print(list_comp_code)

print("\n🔧 SIMBOLI GENERATI:")
print(result.encoding_result.compressed_symbols)

print("\n🔄 CODICE RICOSTRUITO:")
print(result.reconstructed_code)

print("\n❌ ERRORE SINTASSI:")
try:
    import ast
    ast.parse(result.reconstructed_code)
    print("✅ Sintassi valida!")
except SyntaxError as e:
    print(f"❌ SyntaxError: {e}")
    print(f"   Linea {e.lineno}: {e.text}")

print("\n📊 METRICHE:")
print(f"   - Fidelity: {result.fidelity_score:.3f}")
print(f"   - Compressione: {result.encoding_result.compression_result.compression_ratio:.1f}%")
print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
