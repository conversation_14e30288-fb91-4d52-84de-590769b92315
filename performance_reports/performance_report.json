{"logic_reasoning": {"total_time": 0.009033918380737305, "avg_time_per_proof": 0.0001806783676147461, "successful_proofs": 50, "success_rate": 1.0, "proofs_per_second": 5534.696893721464}, "symbolic_math": {"total_time": 1.631615161895752, "avg_time_per_operation": 0.016316151618957518, "successful_operations": 100, "success_rate": 1.0, "operations_per_second": 61.2889622108018, "cache_stats": {"operations_count": 76, "cache_hits": 70, "cache_misses": 6, "cache_hit_rate": 0.9210526315789473, "total_computation_time": 1.5411217212677002, "average_computation_time": 0.02027791738510132, "cache_size": 6, "sympy_available": true}}, "code_generation": {"total_time": 0.001316070556640625, "avg_time_per_generation": 4.38690185546875e-05, "successful_generations": 30, "success_rate": 1.0, "generations_per_second": 22795.130434782608, "decoder_stats": {"generations_count": 30, "successful_generations": 30, "success_rate": 1.0, "cache_hits": 0, "available_templates": 8, "template_categories": ["algorithms", "mathematics"]}}, "initialization": {"logic_engine": 4.00543212890625e-05, "math_engine": 7.867813110351562e-06, "kg_builder": 0.006850004196166992, "decoder": 0.001129150390625}}