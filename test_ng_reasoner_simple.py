#!/usr/bin/env python3
"""
Test semplificato per NGReasoner
"""

def test_logical_fact():
    print("🧠 TEST LOGICAL FACT")
    
    try:
        from neuroglyph.cognitive.reasoning_structures import LogicalFact, FactType
        
        # Test creazione LogicalFact
        fact = LogicalFact(
            statement="it is raining",
            predicate="raining",
            fact_type=FactType.PREMISE
        )
        
        print(f"✅ LogicalFact created: {fact.statement}")
        print(f"   ID: {fact.fact_id}")
        print(f"   Predicate: {fact.predicate}")
        print(f"   Type: {fact.fact_type}")
        
        # Test negazione
        neg_fact = fact.negate()
        print(f"✅ Negation: {neg_fact.statement}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logical_operators():
    print("\n🧠 TEST LOGICAL OPERATORS")
    
    try:
        from neuroglyph.cognitive.logical_operators import LogicalOperators
        from neuroglyph.cognitive.reasoning_structures import LogicalFact, FactType
        
        operators = LogicalOperators()
        print("✅ LogicalOperators created")
        
        # Test con fatti semplici
        facts = [
            LogicalFact(
                statement="A",
                predicate="A",
                fact_type=FactType.PREMISE,
                truth_value=True
            ),
            LogicalFact(
                statement="A ⇒ B",
                predicate="implication",
                fact_type=FactType.PREMISE,
                truth_value=True
            )
        ]
        
        print(f"✅ Created {len(facts)} test facts")
        
        # Test modus ponens
        steps = operators.modus_ponens(facts)
        print(f"✅ Modus ponens generated {len(steps)} steps")
        
        if steps:
            step = steps[0]
            print(f"   Conclusion: {step.output_facts[0].statement}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧠 SIMPLE NG_REASONER TEST")
    print("=" * 50)
    
    test1 = test_logical_fact()
    test2 = test_logical_operators()
    
    print("\n" + "=" * 50)
    print("🧠 RISULTATI:")
    print(f"   LogicalFact: {'✅' if test1 else '❌'}")
    print(f"   LogicalOperators: {'✅' if test2 else '❌'}")
    
    if test1 and test2:
        print("✅ BASIC COMPONENTS WORKING!")
    else:
        print("❌ Issues with basic components")
