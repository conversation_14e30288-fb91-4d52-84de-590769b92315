#!/usr/bin/env python3
"""
Test per AST-Equivalenza Perfetta
Verifica che l'AST ricostruito sia identico all'originale usando ast.dump()
"""

import ast
from neuroglyph.core.encoder.encoder import <PERSON><PERSON>ncoder

def test_ast_equivalence_simple():
    """Test AST-equivalenza per codice semplice."""
    print("🎯 TEST AST-EQUIVALENZA: Codice Semplice")
    
    code = """
def simple_function(x):
    if x > 0:
        return x * 2
    else:
        return 0
"""
    
    # Parse AST originale
    original_ast = ast.parse(code)
    original_dump = ast.dump(original_ast, include_attributes=False)
    
    # Test round-trip AST-based
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    # Parse AST ricostruito
    reconstructed_ast = ast.parse(result.decoding_result.reconstructed_code)
    reconstructed_dump = ast.dump(reconstructed_ast, include_attributes=False)
    
    # Confronto AST
    ast_equivalent = (original_dump == reconstructed_dump)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n🔍 AST DUMP ORIGINALE:")
    print(original_dump[:200] + "..." if len(original_dump) > 200 else original_dump)
    print(f"\n🔍 AST DUMP RICOSTRUITO:")
    print(reconstructed_dump[:200] + "..." if len(reconstructed_dump) > 200 else reconstructed_dump)
    print(f"\n📊 RISULTATI:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente (dump): {'✅' if ast_equivalent else '❌'}")
    print(f"   - AST equivalente (built-in): {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return ast_equivalent, result

def test_ast_equivalence_complex():
    """Test AST-equivalenza per codice complesso."""
    print("\n🎯 TEST AST-EQUIVALENZA: Codice Complesso")
    
    code = """
def process_data(items):
    results = []
    
    for item in items:
        try:
            if isinstance(item, dict):
                processed = {k: v * 2 for k, v in item.items() if v > 0}
                results.append(processed)
            elif isinstance(item, list):
                filtered = [x for x in item if x is not None]
                results.extend(filtered)
            else:
                results.append(str(item))
        except Exception as e:
            print(f"Error: {e}")
            continue
    
    return results
"""
    
    # Parse AST originale
    original_ast = ast.parse(code)
    original_dump = ast.dump(original_ast, include_attributes=False)
    
    # Test round-trip AST-based
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    # Parse AST ricostruito
    reconstructed_ast = ast.parse(result.decoding_result.reconstructed_code)
    reconstructed_dump = ast.dump(reconstructed_ast, include_attributes=False)
    
    # Confronto AST
    ast_equivalent = (original_dump == reconstructed_dump)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n🔍 AST DUMP ORIGINALE:")
    print(original_dump[:300] + "..." if len(original_dump) > 300 else original_dump)
    print(f"\n🔍 AST DUMP RICOSTRUITO:")
    print(reconstructed_dump[:300] + "..." if len(reconstructed_dump) > 300 else reconstructed_dump)
    print(f"\n📊 RISULTATI:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente (dump): {'✅' if ast_equivalent else '❌'}")
    print(f"   - AST equivalente (built-in): {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return ast_equivalent, result

def test_ast_equivalence_class():
    """Test AST-equivalenza per class."""
    print("\n🎯 TEST AST-EQUIVALENZA: Class")
    
    code = """
@dataclass
class Person:
    name: str
    age: int
    
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def greet(self):
        return f"Hello, I'm {self.name}"
"""
    
    # Parse AST originale
    original_ast = ast.parse(code)
    original_dump = ast.dump(original_ast, include_attributes=False)
    
    # Test round-trip AST-based
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    # Parse AST ricostruito
    reconstructed_ast = ast.parse(result.decoding_result.reconstructed_code)
    reconstructed_dump = ast.dump(reconstructed_ast, include_attributes=False)
    
    # Confronto AST
    ast_equivalent = (original_dump == reconstructed_dump)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n🔍 AST DUMP ORIGINALE:")
    print(original_dump[:300] + "..." if len(original_dump) > 300 else original_dump)
    print(f"\n🔍 AST DUMP RICOSTRUITO:")
    print(reconstructed_dump[:300] + "..." if len(reconstructed_dump) > 300 else reconstructed_dump)
    print(f"\n📊 RISULTATI:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente (dump): {'✅' if ast_equivalent else '❌'}")
    print(f"   - AST equivalente (built-in): {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return ast_equivalent, result

if __name__ == "__main__":
    print("🎯 AST-EQUIVALENZA PERFETTA TEST")
    print("=" * 80)
    
    # Test 1: Codice Semplice
    ast_eq1, result1 = test_ast_equivalence_simple()
    
    # Test 2: Codice Complesso
    ast_eq2, result2 = test_ast_equivalence_complex()
    
    # Test 3: Class
    ast_eq3, result3 = test_ast_equivalence_class()
    
    print("\n" + "=" * 80)
    print("🎯 RISULTATI FINALI AST-EQUIVALENZA:")
    print(f"   Test 1 (Simple): AST-Equiv {ast_eq1}, Fidelity {result1.fidelity_score:.3f}")
    print(f"   Test 2 (Complex): AST-Equiv {ast_eq2}, Fidelity {result2.fidelity_score:.3f}")
    print(f"   Test 3 (Class): AST-Equiv {ast_eq3}, Fidelity {result3.fidelity_score:.3f}")
    
    results = [result1, result2, result3]
    ast_equivalences = [ast_eq1, ast_eq2, ast_eq3]
    
    avg_fidelity = sum(r.fidelity_score for r in results) / len(results)
    avg_compression = sum(r.compression_ratio for r in results) / len(results)
    ast_equivalent_count = sum(ast_equivalences)
    
    print(f"\n🎯 MEDIA AST-EQUIVALENZA:")
    print(f"   - Fidelity media: {avg_fidelity:.3f}")
    print(f"   - Compressione media: {avg_compression:.1f}%")
    print(f"   - AST equivalenti: {ast_equivalent_count}/{len(results)}")
    
    if ast_equivalent_count >= len(results) and avg_fidelity >= 0.95:
        print("✅ AST-EQUIVALENZA PERFETTA RAGGIUNTA!")
        print("   🎯 Tutti gli AST sono identici (ast.dump)")
        print("   🎯 Fidelity perfetta mantenuta")
        print("   🎯 Sostituzione in-loco funziona perfettamente")
        print("   ✅ NEUROGLYPH ha raggiunto la perfezione AST!")
    else:
        print("❌ AST-EQUIVALENZA NON PERFETTA")
        print(f"   - AST equivalenti: {ast_equivalent_count}/{len(results)} (target: {len(results)}/{len(results)})")
        print(f"   - Fidelity media: {avg_fidelity:.3f} (target: ≥0.95)")
        print("   🔧 Verificare sostituzione in-loco e copy_location")
        print("   📊 Analizzare differenze negli AST dump")
    
    print(f"\n🚀 CONCLUSIONE AST-EQUIVALENZA:")
    if ast_equivalent_count >= len(results):
        print("   🎉 BREAKTHROUGH: AST-equivalenza perfetta raggiunta!")
        print("   🎯 Sostituzione in-loco con copy_location funziona")
        print("   ✅ NEUROGLYPH è ora perfetto a livello AST")
    else:
        print("   ⚠️ AST-equivalenza richiede ulteriori ottimizzazioni")
        print("   🔧 Implementare meglio la sostituzione in-loco")
        print("   📊 Verificare fix_missing_locations e copy_location")
