{"symbols": ["⌦§fstr_0§", "⌦§fstr_1§", "⌦§fstr_2§", "⌦§fstr_3§", "⌦§fstr_4§", "⌦§fstr_5§", "⌦§fstr_6§", "⌦§fstr_7§", "⌦§fstr_8§", "⌦§fstr_9§", "⌦§fstr_10§", "⌦§fstr_11§", "⌦§fstr_12§", "⟨⟩§func_13§", "⌦§fstr_14§", "⌦§fstr_15§", "⌦§fstr_16§", "⌦§fstr_17§", "⌦§fstr_18§", "⌦§fstr_19§", "⌦§fstr_20§", "⌦§fstr_21§", "⌦§fstr_22§", "⌦§fstr_23§", "⌦§fstr_24§", "⌦§fstr_25§", "⌦§fstr_26§", "⟨⟩§func_27§", "⌦§fstr_28§", "⌦§fstr_29§", "⌦§fstr_30§", "⌦§fstr_31§", "⌦§fstr_32§", "⌦§fstr_33§", "⌦§fstr_34§", "⌦§fstr_35§", "⌦§fstr_36§", "⌦§fstr_37§", "⌦§fstr_38§", "⌦§fstr_39§", "⌦§fstr_40§", "⟨⟩§func_41§", "⌦§fstr_42§", "⌦§fstr_43§", "⌦§fstr_44§", "⌦§fstr_45§", "⌦§fstr_46§", "⌦§fstr_47§", "⌦§fstr_48§", "⌦§fstr_49§", "⌦§fstr_50§", "⌦§fstr_51§", "⌦§fstr_52§", "⌦§fstr_53§", "⌦§fstr_54§", "⟨⟩§func_55§", "⌦§fstr_56§", "⌦§fstr_57§", "⌦§fstr_58§", "⌦§fstr_59§", "⌦§fstr_60§", "⌦§fstr_61§", "⌦§fstr_62§", "⌦§fstr_63§", "⌦§fstr_64§", "⌦§fstr_65§", "⌦§fstr_66§", "⌦§fstr_67§", "⌦§fstr_68§", "⟨⟩§func_69§", "⌦§fstr_70§", "⌦§fstr_71§", "⌦§fstr_72§", "⌦§fstr_73§", "⌦§fstr_74§", "⌦§fstr_75§", "⌦§fstr_76§", "⌦§fstr_77§", "⌦§fstr_78§", "⌦§fstr_79§", "⌦§fstr_80§", "⌦§fstr_81§", "⌦§fstr_82§", "⌦§fstr_83§", "⌦§fstr_84§", "⌦§fstr_85§", "⌦§fstr_86§", "⌦§fstr_87§", "⌦§fstr_88§", "⌦§fstr_89§", "⌦§fstr_90§", "⌦§fstr_91§", "⌦§fstr_92§", "⌦§fstr_93§", "⌦§fstr_94§", "⌦§fstr_95§", "⌦§fstr_96§", "⌦§fstr_97§", "◊§if_98§", "⌦§fstr_99§", "◊§if_100§", "◊§if_101§"], "original_length": 10262, "compressed_length": 102, "compression_ratio": 89.16390567140908, "encoding_level": 5, "encoding_timestamp": "2025-06-04T21:39:46.517108", "encoder_version": "2.0_AST", "language": "python", "neuroglyph_version": "2.0_AST"}