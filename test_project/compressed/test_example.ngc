{"symbols": ["⌦§fstr_0§", "⌦§fstr_1§", "⌦§fstr_2§", "⌦§fstr_3§", "⌦§fstr_4§", "⌦§fstr_5§", "⌦§fstr_6§", "⌦§fstr_7§", "⌦§fstr_8§", "⌦§fstr_9§", "⌦§fstr_10§", "⌦§fstr_11§", "⌦§fstr_12§", "⟨⟩§func_13§", "⌦§fstr_14§", "⌦§fstr_15§", "⌦§fstr_16§", "⌦§fstr_17§", "⌦§fstr_18§", "⌦§fstr_19§", "⌦§fstr_20§", "⌦§fstr_21§", "⌦§fstr_22§", "⌦§fstr_23§", "⌦§fstr_24§", "⌦§fstr_25§", "⌦§fstr_26§", "⟨⟩§func_27§", "⌦§fstr_28§", "⌦§fstr_29§", "⌦§fstr_30§", "⌦§fstr_31§", "⌦§fstr_32§", "⌦§fstr_33§", "⌦§fstr_34§", "⌦§fstr_35§", "⌦§fstr_36§", "⌦§fstr_37§", "⌦§fstr_38§", "⌦§fstr_39§", "⌦§fstr_40§", "⟨⟩§func_41§", "⌦§fstr_42§", "⌦§fstr_43§", "⌦§fstr_44§", "⌦§fstr_45§", "⌦§fstr_46§", "⌦§fstr_47§", "⌦§fstr_48§", "⌦§fstr_49§", "⌦§fstr_50§", "⌦§fstr_51§", "⌦§fstr_52§", "⌦§fstr_53§", "⌦§fstr_54§", "⟨⟩§func_55§", "⌦§fstr_56§", "⌦§fstr_57§", "⌦§fstr_58§", "⌦§fstr_59§", "⌦§fstr_60§", "⌦§fstr_61§", "⌦§fstr_62§", "⌦§fstr_63§", "⌦§fstr_64§", "⌦§fstr_65§", "⌦§fstr_66§", "⌦§fstr_67§", "⌦§fstr_68§", "⟨⟩§func_69§", "⌦§fstr_70§", "⌦§fstr_71§", "⌦§fstr_72§", "⌦§fstr_73§", "⌦§fstr_74§", "⌦§fstr_75§", "⌦§fstr_76§", "⌦§fstr_77§", "⌦§fstr_78§", "⌦§fstr_79§", "⌦§fstr_80§", "⌦§fstr_81§", "⌦§fstr_82§", "⌦§fstr_83§", "⌦§fstr_84§", "⌦§fstr_85§", "⌦§fstr_86§", "⌦§fstr_87§", "⌦§fstr_88§", "⌦§fstr_89§", "⌦§fstr_90§", "⌦§fstr_91§", "⌦§fstr_92§", "⌦§fstr_93§", "⌦§fstr_94§", "⌦§fstr_95§", "⌦§fstr_96§", "⌦§fstr_97§", "◊§if_98§", "⌦§fstr_99§", "◊§if_100§", "◊§if_101§", "⌦§fstr_102§", "⌦§fstr_103§", "⌦§fstr_104§", "⌦§fstr_105§", "⌦§fstr_106§", "⌦§fstr_107§", "⌦§fstr_108§", "⌦§fstr_109§", "⌦§fstr_110§", "⌦§fstr_111§", "⌦§fstr_112§", "⌦§fstr_113§", "⌦§fstr_114§", "⌦§fstr_115§", "⟨⟩§func_116§", "⌦§fstr_117§", "⌦§fstr_118§", "⌦§fstr_119§", "⌦§fstr_120§", "⌦§fstr_121§", "⌦§fstr_122§", "⌦§fstr_123§", "⌦§fstr_124§", "⌦§fstr_125§", "⌦§fstr_126§", "⌦§fstr_127§", "⌦§fstr_128§", "⌦§fstr_129§", "⌦§fstr_130§", "⟨⟩§func_131§", "⌦§fstr_132§", "⌦§fstr_133§", "⌦§fstr_134§", "⌦§fstr_135§", "⌦§fstr_136§", "⌦§fstr_137§", "⌦§fstr_138§", "⌦§fstr_139§", "⌦§fstr_140§", "⌦§fstr_141§", "⌦§fstr_142§", "⌦§fstr_143§", "⌦§fstr_144§", "⌦§fstr_145§", "⟨⟩§func_146§", "⌦§fstr_147§", "⌦§fstr_148§", "⌦§fstr_149§", "⌦§fstr_150§", "⌦§fstr_151§", "⌦§fstr_152§", "⌦§fstr_153§", "⌦§fstr_154§", "⌦§fstr_155§", "⌦§fstr_156§", "⌦§fstr_157§", "⌦§fstr_158§", "⌦§fstr_159§", "⌦§fstr_160§", "⌦§fstr_161§", "◊§if_162§", "⌦§fstr_163§", "◊§if_164§", "◊§if_165§", "⎋§with_166§", "◊§if_167§", "⌦§fstr_168§", "◊§if_169§", "◊§if_170§", "◊§if_171§", "⌦§fstr_172§", "⟐§try_173§", "⟲§for_174§", "⟨⟩§func_175§", "⟨⟩§func_176§", "⟨⟩§func_177§", "⟨⟩§func_178§", "⟡§class_179§", "⌦§fstr_180§", "◊§if_181§"], "original_length": 1400, "compressed_length": 182, "compression_ratio": -47.285714285714285, "encoding_level": 5, "encoding_timestamp": "2025-06-04T21:39:46.555219", "encoder_version": "2.0_AST", "language": "python", "neuroglyph_version": "2.0_AST"}