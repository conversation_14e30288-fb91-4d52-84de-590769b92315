#!/usr/bin/env python3
"""
Test per <PERSON><PERSON><PERSON><PERSON> - <PERSON>, <PERSON><PERSON>, Generator, F-strings
Verifica che tutti i nuovi pattern AST-based funzionino perfettamente
"""

from neuroglyph.core.encoder.encoder import NGEncoder

def test_with_statement():
    """Test AST-based per with statement."""
    print("🎯 TEST EXPANDED: With Statement")
    
    code = """
def process_file(filename):
    with open(filename, 'r') as f:
        content = f.read()
        lines = content.split('\\n')
        return len(lines)
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_lambda_expressions():
    """Test AST-based per lambda expressions."""
    print("\n🎯 TEST EXPANDED: Lambda Expressions")
    
    code = """
def process_data(items):
    filtered = filter(lambda x: x > 0, items)
    mapped = map(lambda x: x * 2, filtered)
    sorted_data = sorted(mapped, key=lambda x: -x)
    return list(sorted_data)
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_generator_expressions():
    """Test AST-based per generator expressions."""
    print("\n🎯 TEST EXPANDED: Generator Expressions")
    
    code = """
def analyze_data(data):
    # Generator expression semplice
    squares = (x**2 for x in data if x > 0)
    
    # Generator expression complessa
    processed = (
        item.upper() 
        for sublist in data 
        for item in sublist 
        if isinstance(item, str) and len(item) > 3
    )
    
    return sum(squares), list(processed)
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_f_strings():
    """Test AST-based per f-strings."""
    print("\n🎯 TEST EXPANDED: F-strings")
    
    code = """
def format_report(name, age, score):
    # F-string semplice
    greeting = f"Hello, {name}!"
    
    # F-string con formattazione
    details = f"Age: {age:02d}, Score: {score:.2f}"
    
    # F-string complessa
    summary = f"Report for {name.upper()}: {score/100:.1%} success rate"
    
    return f"{greeting}\\n{details}\\n{summary}"
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_complex_mixed():
    """Test AST-based per codice complesso con tutti i costrutti."""
    print("\n🎯 TEST EXPANDED: Codice Complesso Misto")
    
    code = """
def advanced_processor(data_file, threshold=0.5):
    results = []
    
    with open(data_file, 'r') as f:
        lines = f.readlines()
    
    # Lambda + generator + f-string
    processor = lambda x: float(x.strip()) if x.strip().isdigit() else 0.0
    valid_numbers = (processor(line) for line in lines if line.strip())
    
    for num in valid_numbers:
        if num > threshold:
            # F-string con lambda
            message = f"Processing {num:.2f}: {(lambda x: 'high' if x > 0.8 else 'medium')(num)}"
            results.append(message)
    
    return results
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

if __name__ == "__main__":
    print("🎯 EXPANDED CONSTRUCTS TEST - With, Lambda, Generator, F-strings")
    print("=" * 90)
    
    # Test 1: With Statement
    result1 = test_with_statement()
    
    # Test 2: Lambda Expressions
    result2 = test_lambda_expressions()
    
    # Test 3: Generator Expressions
    result3 = test_generator_expressions()
    
    # Test 4: F-strings
    result4 = test_f_strings()
    
    # Test 5: Codice Complesso Misto
    result5 = test_complex_mixed()
    
    print("\n" + "=" * 90)
    print("🎯 RISULTATI FINALI EXPANDED CONSTRUCTS:")
    print(f"   Test 1 (With): Fidelity {result1.fidelity_score:.3f}, Compressione {result1.compression_ratio:.1f}%")
    print(f"   Test 2 (Lambda): Fidelity {result2.fidelity_score:.3f}, Compressione {result2.compression_ratio:.1f}%")
    print(f"   Test 3 (Generator): Fidelity {result3.fidelity_score:.3f}, Compressione {result3.compression_ratio:.1f}%")
    print(f"   Test 4 (F-strings): Fidelity {result4.fidelity_score:.3f}, Compressione {result4.compression_ratio:.1f}%")
    print(f"   Test 5 (Mixed): Fidelity {result5.fidelity_score:.3f}, Compressione {result5.compression_ratio:.1f}%")
    
    results = [result1, result2, result3, result4, result5]
    avg_fidelity = sum(r.fidelity_score for r in results) / len(results)
    avg_compression = sum(r.compression_ratio for r in results) / len(results)
    
    print(f"\n🎯 MEDIA EXPANDED CONSTRUCTS:")
    print(f"   - Fidelity media: {avg_fidelity:.3f}")
    print(f"   - Compressione media: {avg_compression:.1f}%")
    
    # Verifica target
    ast_equivalent_count = sum([r.ast_equivalent for r in results])
    syntax_valid_count = sum([r.decoding_result.reconstruction_result.syntax_valid for r in results])
    
    print(f"\n🎯 QUALITÀ EXPANDED CONSTRUCTS:")
    print(f"   - AST equivalenti: {ast_equivalent_count}/{len(results)}")
    print(f"   - Sintassi valida: {syntax_valid_count}/{len(results)}")
    
    if avg_fidelity >= 0.95 and avg_compression >= 80 and syntax_valid_count >= len(results):
        print("✅ EXPANDED CONSTRUCTS SUCCESSO: Target raggiunti!")
        print("   🎯 Fidelity ≥0.95 ✅")
        print("   🎯 Compressione ≥80% ✅")
        print("   🎯 Sintassi valida 100% ✅")
        print("   🎯 With, Lambda, Generator, F-strings supportati ✅")
    else:
        print("❌ EXPANDED CONSTRUCTS: Target non raggiunti")
        print(f"   - Fidelity target: ≥0.95 (attuale: {avg_fidelity:.3f})")
        print(f"   - Compressione target: ≥80% (attuale: {avg_compression:.1f}%)")
        print(f"   - Sintassi valida target: 100% (attuale: {syntax_valid_count}/{len(results)})")
    
    print(f"\n🚀 CONCLUSIONE EXPANDED CONSTRUCTS:")
    if avg_fidelity >= 0.95 and syntax_valid_count >= len(results):
        print("   🎉 NEUROGLYPH supporta TUTTI i costrutti Python principali!")
        print("   🎯 With, Lambda, Generator, F-strings funzionano perfettamente")
        print("   ✅ Architettura AST-based scalabile confermata")
        print("   🚀 NEUROGLYPH è ora COMPLETO per Python moderno!")
    else:
        print("   ⚠️ Alcuni costrutti richiedono ottimizzazioni")
        print("   🔧 Verificare implementazione visit_* methods")
        print("   📊 Analizzare casi specifici di fallimento")
