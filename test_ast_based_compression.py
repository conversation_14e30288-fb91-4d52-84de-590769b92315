#!/usr/bin/env python3
"""
Test per AST-Based Compression - PATCH 8
Verifica che l'approccio AST-based risolva i problemi di fidelity.
"""

from neuroglyph.core.encoder.encoder import NGEncoder

def test_ast_simple_while():
    """Test AST-based per while loop semplice."""
    print("🎯 TEST AST-BASED: While Loop Semplice")
    
    code = """
def simple_loop():
    i = 0
    while i < 3:
        i += 1
    return i
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE AST-BASED:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_ast_if_else():
    """Test AST-based per if/else."""
    print("\n🎯 TEST AST-BASED: If/Else")
    
    code = """
def check_positive(x):
    if x > 0:
        return True
    else:
        return False
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE AST-BASED:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_ast_complex_function():
    """Test AST-based per funzione complessa."""
    print("\n🎯 TEST AST-BASED: Funzione Complessa")
    
    code = """
def process_data(items):
    result = []
    for item in items:
        if item > 0:
            result.append(item * 2)
        else:
            result.append(0)
    return result
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE AST-BASED:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

if __name__ == "__main__":
    print("🎯 AST-BASED COMPRESSION TEST - PATCH 8")
    print("=" * 70)
    
    # Test 1: While semplice
    result1 = test_ast_simple_while()
    
    # Test 2: If/Else
    result2 = test_ast_if_else()
    
    # Test 3: Funzione complessa
    result3 = test_ast_complex_function()
    
    print("\n" + "=" * 70)
    print("🎯 RISULTATI FINALI AST-BASED:")
    print(f"   Test 1 (While): Fidelity {result1.fidelity_score:.3f}, Compressione {result1.compression_ratio:.1f}%")
    print(f"   Test 2 (If/Else): Fidelity {result2.fidelity_score:.3f}, Compressione {result2.compression_ratio:.1f}%")
    print(f"   Test 3 (Complex): Fidelity {result3.fidelity_score:.3f}, Compressione {result3.compression_ratio:.1f}%")
    
    avg_fidelity = (result1.fidelity_score + result2.fidelity_score + result3.fidelity_score) / 3
    avg_compression = (result1.compression_ratio + result2.compression_ratio + result3.compression_ratio) / 3
    
    print(f"\n🎯 MEDIA AST-BASED:")
    print(f"   - Fidelity media: {avg_fidelity:.3f}")
    print(f"   - Compressione media: {avg_compression:.1f}%")
    
    # Verifica target
    ast_equivalent_count = sum([result1.ast_equivalent, result2.ast_equivalent, result3.ast_equivalent])
    syntax_valid_count = sum([
        result1.decoding_result.reconstruction_result.syntax_valid,
        result2.decoding_result.reconstruction_result.syntax_valid,
        result3.decoding_result.reconstruction_result.syntax_valid
    ])
    
    print(f"\n🎯 QUALITÀ AST-BASED:")
    print(f"   - AST equivalenti: {ast_equivalent_count}/3")
    print(f"   - Sintassi valida: {syntax_valid_count}/3")
    
    if avg_fidelity >= 0.9 and avg_compression >= 80 and ast_equivalent_count >= 2:
        print("✅ AST-BASED SUCCESSO: Target raggiunti!")
        print("   🎯 Fidelity ≥0.9 ✅")
        print("   🎯 Compressione ≥80% ✅")
        print("   🎯 AST equivalenza ≥2/3 ✅")
    else:
        print("❌ AST-BASED FALLIMENTO: Target non raggiunti")
        print(f"   - Fidelity target: ≥0.9 (attuale: {avg_fidelity:.3f})")
        print(f"   - Compressione target: ≥80% (attuale: {avg_compression:.1f}%)")
        print(f"   - AST equivalenza target: ≥2/3 (attuale: {ast_equivalent_count}/3)")
    
    print(f"\n🚀 CONCLUSIONE:")
    if avg_fidelity >= 0.9:
        print("   🎉 AST-BASED ha risolto il problema di fidelity!")
        print("   🎯 Approccio AST preserva struttura perfettamente")
        print("   ✅ Nessun problema di indentazione o parsing")
    else:
        print("   ⚠️ AST-BASED richiede ulteriori ottimizzazioni")
        print("   🔧 Verificare implementazione NodeTransformer")
        print("   📊 Analizzare payload registry e decompressione")
