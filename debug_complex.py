#!/usr/bin/env python3

from neuroglyph.core.encoder.encoder import NGEncoder

# Codice quicksort originale
quicksort_code = """
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quicksort(left) + middle + quicksort(right)
""".strip()

print("🔍 ANALISI PROBLEMA QUICKSORT")
print("=" * 50)

encoder = NGEncoder()
result = encoder.round_trip_test(quicksort_code, encoding_level=3)

print("\n📝 CODICE ORIGINALE:")
print(quicksort_code)

print("\n🔧 SIMBOLI GENERATI:")
print(result.encoding_result.compressed_symbols)

print("\n🔄 CODICE RICOSTRUITO:")
print(result.reconstructed_code)

print("\n❌ ERRORE SINTASSI:")
try:
    import ast
    ast.parse(result.reconstructed_code)
    print("✅ Sintassi valida!")
except SyntaxError as e:
    print(f"❌ SyntaxError: {e}")
    print(f"   Linea {e.lineno}: {e.text}")

print("\n📊 METRICHE:")
print(f"   - Fidelity: {result.fidelity_score:.3f}")
print(f"   - Compressione: {result.encoding_result.compression_result.compression_ratio:.1f}%")
print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
