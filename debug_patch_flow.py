#!/usr/bin/env python3
"""
Debug script per tracciare il flusso completo del patch engine
"""

import sys
from pathlib import Path

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent))

def test_patch_flow():
    """Testa il flusso completo del patch engine."""
    
    from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
    from neuroglyph.cognitive.validation_structures import ValidationResult, ValidationError, ValidationLevel, ValidationSeverity, ValidationErrorType
    
    print("🔍 Testing complete patch flow...")
    
    # Test diversi tipi di errore
    test_cases = [
        {
            "name": "Import Error",
            "code": """import nonexistent_module_xyz
result = nonexistent_module_xyz.some_function()
print("Hello world")
""",
            "error_message": "ModuleNotFoundError: No module named 'nonexistent_module_xyz'",
            "error_type": ValidationErrorType.MISSING_PREMISES,
            "error_level": ValidationLevel.SEMANTIC
        },
        {
            "name": "Syntax Error - Missing Colon",
            "code": """if True
    print("Hello")
""",
            "error_message": "SyntaxError: invalid syntax",
            "error_type": ValidationErrorType.AST_PARSE_ERROR,
            "error_level": ValidationLevel.SYNTAX
        },
        {
            "name": "Undefined Variable",
            "code": """print(undefined_variable)
result = undefined_variable + 5
""",
            "error_message": "NameError: name 'undefined_variable' is not defined",
            "error_type": ValidationErrorType.MISSING_PREMISES,
            "error_level": ValidationLevel.SEMANTIC
        }
    ]

    for test_case in test_cases:
        print(f"\n{'='*50}")
        print(f"🧪 Testing: {test_case['name']}")
        print(f"{'='*50}")

        test_code = test_case["code"]
        error_message = test_case["error_message"]
        error_type = test_case["error_type"]
        error_level = test_case["error_level"]

        print(f"Test code:\n{test_code}")

        # Inizializza nuovo patcher per ogni test (evita problemi di stato)
        patcher = NGAdaptivePatcher()

        # Crea ValidationResult simulato
        result = ValidationResult()
        val_error = ValidationError(
            error_type=error_type,
            severity=ValidationSeverity.ERROR,
            level=error_level,
            message=error_message,
            line_number=1
        )
        val_error.context = {'code': test_code}
        result.add_error(val_error)
        result.finalize()

        total_errors = len(result.syntax_errors) + len(result.semantic_errors) + len(result.logic_errors)
        print(f"ValidationResult created with {total_errors} total errors")
        print(f"  - Syntax errors: {len(result.syntax_errors)}")
        print(f"  - Semantic errors: {len(result.semantic_errors)}")
        print(f"  - Logic errors: {len(result.logic_errors)}")

        # Applica patch
        print("\n🔧 Applying patches...")
        patch_results = patcher.patch_validation_errors(result)

        print(f"Patch results: {len(patch_results)}")

        for i, patch_result in enumerate(patch_results):
            print(f"\nPatch {i+1}:")
            print(f"  Success: {patch_result.success}")
            print(f"  Status: {patch_result.status}")
            print(f"  Validation passed: {patch_result.validation_passed}")
            print(f"  Error message: {patch_result.error_message}")
            print(f"  Output: {patch_result.output[:100] if patch_result.output else 'None'}...")

            if hasattr(patch_result, 'metadata') and patch_result.metadata:
                print(f"  Metadata: {patch_result.metadata}")

if __name__ == "__main__":
    test_patch_flow()
