# Prometheus Configuration for NEUROGLYPH

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # NEUROGLYPH Core Metrics
  - job_name: 'neuroglyph-core'
    static_configs:
      - targets: ['ng-core:8080']
    scrape_interval: 5s
    metrics_path: '/metrics'
    
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

# Alerting rules (optional)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           # - alertmanager:9093
