#!/usr/bin/env python3
"""
NEUROGLYPH Health Check Script
Verifica stato componenti per Docker health check
"""

import sys
import os
import time
import sqlite3
from pathlib import Path

# Aggiungi path NEUROGLYPH
sys.path.insert(0, '/app')

def check_database():
    """Verifica database SQLite."""
    try:
        db_path = os.environ.get('NEUROGLYPH_DB_PATH', '/app/data/learning.db')
        
        # Verifica file esiste
        if not os.path.exists(db_path):
            print(f"❌ Database non trovato: {db_path}")
            return False
        
        # Verifica connessione
        conn = sqlite3.connect(db_path, timeout=5.0)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        conn.close()
        
        print(f"✅ Database OK: {table_count} tabelle")
        return True
        
    except Exception as e:
        print(f"❌ Errore database: {e}")
        return False

def check_neuroglyph_imports():
    """Verifica import NEUROGLYPH."""
    try:
        from neuroglyph.pipeline.pipeline_engine import PipelineEngine
        from neuroglyph.bus.event_bus import EventBus
        print("✅ Import NEUROGLYPH OK")
        return True
        
    except Exception as e:
        print(f"❌ Errore import NEUROGLYPH: {e}")
        return False

def check_pipeline_config():
    """Verifica configurazione pipeline."""
    try:
        config_path = '/app/pipelines/default.yml'
        
        if not os.path.exists(config_path):
            print(f"❌ Configurazione non trovata: {config_path}")
            return False
        
        from neuroglyph.pipeline.pipeline_engine import PipelineConfig
        config = PipelineConfig.from_yaml(config_path)
        
        print(f"✅ Configurazione OK: {config.name} con {len(config.stages)} stages")
        return True
        
    except Exception as e:
        print(f"❌ Errore configurazione: {e}")
        return False

def main():
    """Health check principale."""
    print("🔍 NEUROGLYPH Health Check")
    print("=" * 40)
    
    checks = [
        ("Database", check_database),
        ("NEUROGLYPH Imports", check_neuroglyph_imports),
        ("Pipeline Config", check_pipeline_config)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n🔄 {check_name}...")
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name} fallito: {e}")
            all_passed = False
    
    print("\n" + "=" * 40)
    
    if all_passed:
        print("✅ Tutti i check passati - HEALTHY")
        return 0
    else:
        print("❌ Alcuni check falliti - UNHEALTHY")
        return 1

if __name__ == '__main__':
    sys.exit(main())
