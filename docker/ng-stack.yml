# NEUROG<PERSON>YPH Docker Stack
# Services: ng-core, ng-db, ng-prometheus

version: '3.8'

services:
  # NEUROGLYPH Core Pipeline
  ng-core:
    build:
      context: ..
      dockerfile: docker/Dockerfile.neuroglyph
    container_name: neuroglyph-core
    ports:
      - "8080:8080"  # Prometheus metrics
      - "8081:8081"  # Health check
    volumes:
      - ng-data:/app/data
      - ../pipelines:/app/pipelines:ro
    environment:
      - NEUROGLYPH_DB_PATH=/app/data/learning.db
      - NEUROGLYPH_LOG_LEVEL=INFO
      - NEUROGLYPH_METRICS_PORT=8080
      - NEUROGLYPH_HEALTH_PORT=8081
    depends_on:
      - ng-db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - neuroglyph-net

  # SQLite Database (via volume)
  ng-db:
    image: alpine:latest
    container_name: neuroglyph-db
    volumes:
      - ng-data:/data
    command: ["sh", "-c", "while true; do sleep 3600; done"]
    restart: unless-stopped
    networks:
      - neuroglyph-net

  # Prometheus Monitoring
  ng-prometheus:
    image: prom/prometheus:latest
    container_name: neuroglyph-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - neuroglyph-net

  # Grafana Dashboard (optional)
  ng-grafana:
    image: grafana/grafana:latest
    container_name: neuroglyph-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=neuroglyph
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - ng-prometheus
    restart: unless-stopped
    networks:
      - neuroglyph-net

volumes:
  ng-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  neuroglyph-net:
    driver: bridge
