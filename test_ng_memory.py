#!/usr/bin/env python3
"""
Test per NEUROGLYPH Memory System
Verifica append, search, decay, eviction, snapshot e performance
"""

import time
import tempfile
import os
from pathlib import Path
from uuid import uuid4

from neuroglyph.cognitive.memory import NGMemory
from neuroglyph.cognitive.memory_models import MemoryConfig, MemorySearchQuery
from neuroglyph.cognitive.data_structures import PriorityVector, DomainType


def create_test_priority_vector(urgency=0.5, risk=0.3, domain=DomainType.GENERAL, confidence=0.8):
    """Crea PriorityVector di test."""
    return PriorityVector(
        urgency=urgency,
        risk=risk,
        domain=domain,
        confidence=confidence,
        reasoning="test priority vector"
    )


def test_memory_initialization():
    """Test inizializzazione NGMemory."""
    print("🎯 TEST MEMORY INITIALIZATION")
    
    # Test con configurazione default
    config = MemoryConfig(db_path=":memory:")
    memory = NGMemory(config)
    
    print(f"   ✅ Memory inizializzata")
    print(f"      - Database: {memory.db_path}")
    print(f"      - Max records: {memory.config.max_records:,}")
    print(f"      - Decay lambda: {memory.config.decay_lambda}")
    
    # Verifica count iniziale
    assert memory.count() == 0, "Memory should start empty"
    print(f"   ✅ Count iniziale: {memory.count()}")
    
    memory.close()
    return True


def test_append_performance():
    """Test performance append (target: ≤0.5ms)."""
    print("\n🎯 TEST APPEND PERFORMANCE")
    
    config = MemoryConfig(db_path=":memory:", max_records=1000)
    memory = NGMemory(config)
    
    # Test append singolo
    prio_vec = create_test_priority_vector(urgency=0.8, risk=0.6)
    
    start_time = time.time()
    record = memory.append(
        prompt="Fix critical bug immediately",
        response="def fix_bug(): pass",
        prio_vec=prio_vec,
        symbols=["⟨⟩§func_0§", "⟐§try_1§"],
        tags=["critical", "bugfix"]
    )
    append_time = time.time() - start_time
    
    print(f"   📊 Single append: {append_time*1000:.2f}ms")
    print(f"      Target: ≤0.5ms {'✅' if append_time <= 0.0005 else '❌'}")
    print(f"      Record ID: {record.id}")
    print(f"      Priority score: {record.priority_score:.3f}")
    
    # Test batch append
    batch_size = 100
    start_time = time.time()
    
    for i in range(batch_size):
        memory.append(
            prompt=f"Test prompt {i}",
            response=f"Test response {i}",
            prio_vec=create_test_priority_vector(urgency=0.1 + (i % 10) * 0.1),
            symbols=[f"⟨⟩§func_{i}§"],
            tags=[f"test_{i % 5}"]
        )
    
    batch_time = time.time() - start_time
    avg_time = batch_time / batch_size
    
    print(f"   📊 Batch append ({batch_size} records):")
    print(f"      Total time: {batch_time*1000:.2f}ms")
    print(f"      Average: {avg_time*1000:.2f}ms per record")
    print(f"      Target: ≤0.5ms {'✅' if avg_time <= 0.0005 else '❌'}")
    print(f"      Total records: {memory.count()}")
    
    memory.close()
    return append_time <= 0.0005 and avg_time <= 0.0005


def test_search_performance():
    """Test performance search (target: ≤5ms per 1k records)."""
    print("\n🎯 TEST SEARCH PERFORMANCE")
    
    config = MemoryConfig(db_path=":memory:", enable_fts=True, enable_rtree=True)
    memory = NGMemory(config)
    
    # Popola con 1000 record
    print("   📊 Populating with 1000 records...")
    domains = [DomainType.CODE, DomainType.DATA, DomainType.SYSTEM, DomainType.SECURITY]
    
    for i in range(1000):
        memory.append(
            prompt=f"Test prompt {i} with keywords bug fix critical",
            response=f"def solution_{i}(): return {i}",
            prio_vec=create_test_priority_vector(
                urgency=0.1 + (i % 10) * 0.1,
                risk=0.1 + (i % 8) * 0.1,
                domain=domains[i % len(domains)]
            ),
            symbols=[f"⟨⟩§func_{i}§", f"◊§if_{i}§"],
            tags=[f"tag_{i % 10}", "test"]
        )
    
    print(f"   ✅ Populated: {memory.count()} records")
    
    # Test ricerca per testo
    query = MemorySearchQuery(
        text="bug fix critical",
        limit=5
    )
    
    start_time = time.time()
    results = memory.search(query)
    search_time = time.time() - start_time
    
    print(f"   📊 Text search:")
    print(f"      Time: {search_time*1000:.2f}ms")
    print(f"      Target: ≤5ms {'✅' if search_time <= 0.005 else '❌'}")
    print(f"      Results: {len(results)}")
    print(f"      Top relevance: {results[0].relevance_score:.3f}" if results else "No results")
    
    # Test ricerca per priority vector
    target_prio = {
        'urgency': 0.8,
        'risk': 0.6,
        'domain': 'code',
        'confidence': 0.9
    }
    
    query = MemorySearchQuery(
        target_prio_vec=target_prio,
        limit=5
    )
    
    start_time = time.time()
    results = memory.search(query)
    prio_search_time = time.time() - start_time
    
    print(f"   📊 Priority vector search:")
    print(f"      Time: {prio_search_time*1000:.2f}ms")
    print(f"      Target: ≤5ms {'✅' if prio_search_time <= 0.005 else '❌'}")
    print(f"      Results: {len(results)}")
    
    # Test ricerca combinata
    query = MemorySearchQuery(
        text="critical",
        target_prio_vec=target_prio,
        domains=["code", "security"],
        tags=["test"],
        limit=3
    )
    
    start_time = time.time()
    results = memory.search(query)
    combined_search_time = time.time() - start_time
    
    print(f"   📊 Combined search:")
    print(f"      Time: {combined_search_time*1000:.2f}ms")
    print(f"      Target: ≤5ms {'✅' if combined_search_time <= 0.005 else '❌'}")
    print(f"      Results: {len(results)}")
    
    if results:
        print(f"      Best match reasons: {results[0].match_reasons}")
    
    memory.close()
    
    # Verifica target performance
    max_time = max(search_time, prio_search_time, combined_search_time)
    return max_time <= 0.005


def test_decay_and_scoring():
    """Test decay esponenziale e scoring."""
    print("\n🎯 TEST DECAY AND SCORING")
    
    config = MemoryConfig(db_path=":memory:", decay_lambda=0.1)  # Decay veloce per test
    memory = NGMemory(config)
    
    # Aggiungi record con score diversi
    record1 = memory.append(
        prompt="High priority task",
        response="Important solution",
        prio_vec=create_test_priority_vector(urgency=1.0, risk=0.2),
        tags=["high_priority"]
    )
    
    time.sleep(0.1)  # Piccola pausa
    
    record2 = memory.append(
        prompt="Low priority task", 
        response="Simple solution",
        prio_vec=create_test_priority_vector(urgency=0.2, risk=0.1),
        tags=["low_priority"]
    )
    
    print(f"   📊 Initial scores:")
    print(f"      Record 1: {record1.score:.3f} (high priority)")
    print(f"      Record 2: {record2.score:.3f} (low priority)")
    
    # Simula utilizzo record 1
    query = MemorySearchQuery(text="High priority", limit=1)
    results = memory.search(query)
    
    print(f"   📊 After search (record 1 used):")
    if results:
        print(f"      Record 1 hits: {results[0].record.hits}")
    
    # Esegui decay update
    time.sleep(0.1)
    memory.decay_update()
    
    # Verifica nuovi score
    stats = memory.get_stats()
    print(f"   📊 After decay update:")
    print(f"      Average score: {stats.avg_score:.3f}")
    print(f"      Total records: {stats.total_records}")
    
    memory.close()
    return True


def test_eviction_policy():
    """Test policy di eviction."""
    print("\n🎯 TEST EVICTION POLICY")
    
    config = MemoryConfig(
        db_path=":memory:", 
        max_records=10,
        eviction_batch_size=5,
        eviction_policy="lru_low_score"
    )
    memory = NGMemory(config)
    
    # Riempi oltre il limite
    print(f"   📊 Adding {config.max_records + 5} records...")
    
    for i in range(config.max_records + 5):
        memory.append(
            prompt=f"Task {i}",
            response=f"Solution {i}",
            prio_vec=create_test_priority_vector(
                urgency=0.1 + (i % 5) * 0.2,  # Score variabili
                risk=0.1
            ),
            tags=[f"task_{i}"]
        )
        
        if i == config.max_records:
            print(f"      Records before eviction: {memory.count()}")
    
    final_count = memory.count()
    print(f"   📊 Final count: {final_count}")
    print(f"      Max allowed: {config.max_records}")
    print(f"      Eviction triggered: {'✅' if final_count <= config.max_records else '❌'}")
    
    memory.close()
    return final_count <= config.max_records


def test_retrieve_memory_context():
    """Test retrieve per MemoryContext."""
    print("\n🎯 TEST RETRIEVE MEMORY CONTEXT")
    
    config = MemoryConfig(db_path=":memory:")
    memory = NGMemory(config)
    
    # Aggiungi record di diversi tipi
    memory.append(
        prompt="Fix security vulnerability",
        response="def fix_security(): pass",
        prio_vec=create_test_priority_vector(urgency=1.0, risk=0.9, domain=DomainType.SECURITY),
        symbols=["⟨⟩§func_0§", "⟐§try_1§"],
        tags=["security", "error", "critical"]
    )
    
    memory.append(
        prompt="Process data efficiently",
        response="def process_data(): pass",
        prio_vec=create_test_priority_vector(urgency=0.5, risk=0.2, domain=DomainType.DATA),
        symbols=["⟨⟩§func_1§", "⟲§for_2§"],
        tags=["data", "optimization"]
    )
    
    memory.append(
        prompt="Create user interface",
        response="def create_ui(): pass",
        prio_vec=create_test_priority_vector(urgency=0.3, risk=0.1, domain=DomainType.CODE),
        symbols=["⟨⟩§func_2§", "⟡§class_3§"],
        tags=["ui", "frontend"]
    )
    
    # Test retrieve
    class MockParsedPrompt:
        def __init__(self, text):
            self.original_text = text
    
    priority_vector = create_test_priority_vector(urgency=0.8, risk=0.7, domain=DomainType.SECURITY)
    parsed_prompt = MockParsedPrompt("Security issue needs fixing")
    
    context = memory.retrieve(priority_vector, parsed_prompt, limit=3)
    
    print(f"   📊 Memory context retrieved:")
    print(f"      Symbols: {len(context.symbols)}")
    print(f"      Episodes: {len(context.episodes)}")
    print(f"      Errors: {len(context.errors)}")
    print(f"      Examples: {len(context.examples)}")
    print(f"      Total items: {context.total_items}")
    
    if context.errors:
        print(f"      Error record found: {context.errors[0]['tags']}")
    
    print(f"      Metadata: {context.metadata}")
    
    memory.close()
    return context.total_items > 0


def test_snapshot_and_load():
    """Test snapshot e load."""
    print("\n🎯 TEST SNAPSHOT AND LOAD")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Crea memoria con dati
        db_path = os.path.join(temp_dir, "test_memory.db")
        config = MemoryConfig(db_path=db_path, snapshot_compression="gzip")
        memory = NGMemory(config)
        
        # Aggiungi alcuni record
        for i in range(5):
            memory.append(
                prompt=f"Test prompt {i}",
                response=f"Test response {i}",
                prio_vec=create_test_priority_vector(urgency=0.5 + i * 0.1),
                tags=[f"test_{i}"]
            )
        
        original_count = memory.count()
        print(f"   📊 Original records: {original_count}")
        
        # Crea snapshot
        snapshot_path = memory.snapshot()
        print(f"   📊 Snapshot created: {snapshot_path}")
        print(f"      File exists: {'✅' if Path(snapshot_path).exists() else '❌'}")
        
        memory.close()
        
        # Crea nuova memoria e carica snapshot
        memory2 = NGMemory(MemoryConfig(db_path=":memory:"))
        memory2.load_snapshot(snapshot_path)
        
        loaded_count = memory2.count()
        print(f"   📊 Loaded records: {loaded_count}")
        print(f"      Match original: {'✅' if loaded_count == original_count else '❌'}")
        
        memory2.close()
        
        return loaded_count == original_count


def test_audit_high_risk():
    """Test audit record ad alto rischio."""
    print("\n🎯 TEST AUDIT HIGH RISK")
    
    config = MemoryConfig(db_path=":memory:")
    memory = NGMemory(config)
    
    # Aggiungi record con rischi diversi
    high_risk_count = 0
    for i in range(10):
        risk = 0.1 + i * 0.1
        memory.append(
            prompt=f"Task with risk {risk:.1f}",
            response=f"Solution {i}",
            prio_vec=create_test_priority_vector(risk=risk),
            tags=[f"risk_{int(risk*10)}"]
        )
        if risk >= 0.6:
            high_risk_count += 1
    
    # Test audit
    high_risk_records = memory.audit_high_risk(min_risk=0.6)
    
    print(f"   📊 Audit results:")
    print(f"      Expected high risk: {high_risk_count}")
    print(f"      Found high risk: {len(high_risk_records)}")
    print(f"      Match: {'✅' if len(high_risk_records) == high_risk_count else '❌'}")
    
    if high_risk_records:
        print(f"      Highest risk: {high_risk_records[0].prio_vec['risk']:.1f}")
    
    memory.close()
    return len(high_risk_records) == high_risk_count


if __name__ == "__main__":
    print("🧠 NEUROGLYPH MEMORY SYSTEM TEST")
    print("=" * 80)
    
    # Esegui tutti i test
    test_results = []
    
    test_results.append(("Initialization", test_memory_initialization()))
    test_results.append(("Append Performance", test_append_performance()))
    test_results.append(("Search Performance", test_search_performance()))
    test_results.append(("Decay and Scoring", test_decay_and_scoring()))
    test_results.append(("Eviction Policy", test_eviction_policy()))
    test_results.append(("Retrieve Context", test_retrieve_memory_context()))
    test_results.append(("Snapshot/Load", test_snapshot_and_load()))
    test_results.append(("Audit High Risk", test_audit_high_risk()))
    
    print("\n" + "=" * 80)
    print("🧠 RISULTATI FINALI NG_MEMORY:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    success_rate = passed / len(test_results)
    print(f"\n🎯 SUCCESS RATE: {success_rate:.1%} ({passed}/{len(test_results)})")
    
    if success_rate >= 0.8:
        print("✅ NG_MEMORY TEST: SUCCESSO!")
        print("   🎯 Performance targets raggiunti")
        print("   📊 Funzionalità core verificate")
        print("   🧠 Secondo modulo cognitivo funzionante!")
        print("   ✅ Prossimo step: NG_REASONER implementation")
    else:
        print("❌ NG_MEMORY TEST: Miglioramenti necessari")
        print(f"   - Success rate: {success_rate:.1%} (target: ≥80%)")
        print("   - Verificare performance e funzionalità")
    
    print(f"\n🚀 CONCLUSIONE NG_MEMORY:")
    if success_rate >= 0.8:
        print("   🎉 NGMemory è PRONTO per la produzione!")
        print("   🎯 Memoria a lungo termine implementata")
        print("   🧠 NEUROGLYPH cognitive pipeline estesa")
        print("   ✅ Parser → Prioritizer → Memory funzionante")
        print("   🚀 Prossimo obiettivo: NG_REASONER per reasoning simbolico")
    else:
        print("   🔧 Ottimizzazioni necessarie prima di procedere")
        print("   📊 Analizzare test falliti")
        print("   ⚡ Verificare performance targets")
