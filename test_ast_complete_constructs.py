#!/usr/bin/env python3
"""
Test Completo per AST-Based Compression - Tutti i Costrutti
Verifica For, AsyncFor, Try/Except/Finally, Match/Case, Class con Decorators
"""

from neuroglyph.core.encoder.encoder import NGEncoder

def test_for_loop():
    """Test AST-based per for loop."""
    print("🎯 TEST AST-BASED: For Loop")
    
    code = """
def process_items(items):
    result = []
    for item in items:
        if item > 0:
            result.append(item * 2)
        else:
            result.append(0)
    return result
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_try_except():
    """Test AST-based per try/except/finally."""
    print("\n🎯 TEST AST-BASED: Try/Except/Finally")
    
    code = """
def safe_divide(a, b):
    try:
        result = a / b
        return result
    except ZeroDivisionError:
        print("Division by zero!")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None
    finally:
        print("Operation completed")
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_class_with_decorators():
    """Test AST-based per class con decorators."""
    print("\n🎯 TEST AST-BASED: Class con Decorators")
    
    code = """
@dataclass
class Person:
    name: str
    age: int
    
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def greet(self):
        return f"Hello, I'm {self.name}"
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_complex_nested():
    """Test AST-based per codice complesso nidificato."""
    print("\n🎯 TEST AST-BASED: Codice Complesso Nidificato")
    
    code = """
def complex_processor(data):
    results = []
    
    for item in data:
        try:
            if isinstance(item, dict):
                processed = {k: v * 2 for k, v in item.items() if v > 0}
                results.append(processed)
            elif isinstance(item, list):
                filtered = [x for x in item if x is not None]
                results.extend(filtered)
            else:
                results.append(str(item))
        except Exception as e:
            print(f"Error processing {item}: {e}")
            continue
    
    return results
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test_ast_based(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

if __name__ == "__main__":
    print("🎯 AST-BASED COMPLETE CONSTRUCTS TEST")
    print("=" * 80)
    
    # Test 1: For Loop
    result1 = test_for_loop()
    
    # Test 2: Try/Except/Finally
    result2 = test_try_except()
    
    # Test 3: Class con Decorators
    result3 = test_class_with_decorators()
    
    # Test 4: Codice Complesso Nidificato
    result4 = test_complex_nested()
    
    print("\n" + "=" * 80)
    print("🎯 RISULTATI FINALI AST-BASED COMPLETO:")
    print(f"   Test 1 (For Loop): Fidelity {result1.fidelity_score:.3f}, Compressione {result1.compression_ratio:.1f}%")
    print(f"   Test 2 (Try/Except): Fidelity {result2.fidelity_score:.3f}, Compressione {result2.compression_ratio:.1f}%")
    print(f"   Test 3 (Class): Fidelity {result3.fidelity_score:.3f}, Compressione {result3.compression_ratio:.1f}%")
    print(f"   Test 4 (Complex): Fidelity {result4.fidelity_score:.3f}, Compressione {result4.compression_ratio:.1f}%")
    
    results = [result1, result2, result3, result4]
    avg_fidelity = sum(r.fidelity_score for r in results) / len(results)
    avg_compression = sum(r.compression_ratio for r in results) / len(results)
    
    print(f"\n🎯 MEDIA AST-BASED COMPLETO:")
    print(f"   - Fidelity media: {avg_fidelity:.3f}")
    print(f"   - Compressione media: {avg_compression:.1f}%")
    
    # Verifica target
    ast_equivalent_count = sum([r.ast_equivalent for r in results])
    syntax_valid_count = sum([r.decoding_result.reconstruction_result.syntax_valid for r in results])
    
    print(f"\n🎯 QUALITÀ AST-BASED COMPLETO:")
    print(f"   - AST equivalenti: {ast_equivalent_count}/{len(results)}")
    print(f"   - Sintassi valida: {syntax_valid_count}/{len(results)}")
    
    if avg_fidelity >= 0.95 and avg_compression >= 80 and syntax_valid_count >= len(results):
        print("✅ AST-BASED COMPLETO SUCCESSO: Target raggiunti!")
        print("   🎯 Fidelity ≥0.95 ✅")
        print("   🎯 Compressione ≥80% ✅")
        print("   🎯 Sintassi valida 100% ✅")
    else:
        print("❌ AST-BASED COMPLETO: Target non raggiunti")
        print(f"   - Fidelity target: ≥0.95 (attuale: {avg_fidelity:.3f})")
        print(f"   - Compressione target: ≥80% (attuale: {avg_compression:.1f}%)")
        print(f"   - Sintassi valida target: 100% (attuale: {syntax_valid_count}/{len(results)})")
    
    print(f"\n🚀 CONCLUSIONE AST-BASED COMPLETO:")
    if avg_fidelity >= 0.95 and syntax_valid_count >= len(results):
        print("   🎉 AST-BASED supporta tutti i costrutti principali!")
        print("   🎯 For, Try/Except, Class, nesting complesso funzionano")
        print("   ✅ Architettura scalabile e robusta confermata")
    else:
        print("   ⚠️ AST-BASED richiede ottimizzazioni per costrutti complessi")
        print("   🔧 Verificare gestione nesting e payload AST")
        print("   📊 Analizzare casi specifici di fallimento")
