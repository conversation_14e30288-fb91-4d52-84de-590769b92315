{"summary": {"total_tests": 6, "patches_applied": 0, "patches_correct": 0, "compilations_success": 0, "patch_application_rate": 0.0, "patch_correctness_rate": 0.0, "compilation_success_rate": 0.0, "avg_patch_time": 0}, "by_error_type": {"import": {"total": 2, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}, "syntax": {"total": 2, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}, "semantic": {"total": 2, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}}, "detailed_results": [{"file_path": "/tmp/patch_test_quick/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: __init__() got an unexpected keyword argument 'has_syntax_errors'"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "__init__() got an unexpected keyword argument 'has_syntax_errors'", "patch_content": null}, {"file_path": "/tmp/patch_test_quick/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "syntax", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: __init__() got an unexpected keyword argument 'has_syntax_errors'"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "__init__() got an unexpected keyword argument 'has_syntax_errors'", "patch_content": null}, {"file_path": "/tmp/patch_test_quick/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "semantic", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: __init__() got an unexpected keyword argument 'has_syntax_errors'"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "__init__() got an unexpected keyword argument 'has_syntax_errors'", "patch_content": null}, {"file_path": "/tmp/patch_test_quick/requests-main/src/requests/auth.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: __init__() got an unexpected keyword argument 'has_syntax_errors'"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "__init__() got an unexpected keyword argument 'has_syntax_errors'", "patch_content": null}, {"file_path": "/tmp/patch_test_quick/requests-main/src/requests/auth.py", "error_injection": {"error_type": "syntax", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: __init__() got an unexpected keyword argument 'column_number'"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "__init__() got an unexpected keyword argument 'column_number'", "patch_content": null}, {"file_path": "/tmp/patch_test_quick/requests-main/src/requests/auth.py", "error_injection": {"error_type": "semantic", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: __init__() got an unexpected keyword argument 'has_syntax_errors'"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "__init__() got an unexpected keyword argument 'has_syntax_errors'", "patch_content": null}]}