#!/usr/bin/env python3
"""
Test per PATCH 6: Branch & Loop Coverage
Testa i nuovi simboli: else (☇), elif (◊☇), while (⟳)
"""

from neuroglyph.core.encoder.encoder import <PERSON>GEncoder

def test_if_elif_else():
    """Test per if/elif/else chain."""
    print("🎯 TEST 1: If/Elif/Else Chain")
    
    code = """
def classify(x):
    if x < 0:
        return "negative"
    elif x == 0:
        return "zero"
    else:
        return "positive"
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_while_loop():
    """Test per while loop."""
    print("\n🎯 TEST 2: While Loop")
    
    code = """
def countdown(n):
    i = n
    while i > 0:
        print(i)
        i -= 1
    return "done"
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

def test_nested_if_while():
    """Test per if/while nidificati."""
    print("\n🎯 TEST 3: Nested If/While")
    
    code = """
def process_list(items):
    result = []
    i = 0
    while i < len(items):
        if items[i] > 0:
            result.append(items[i])
        elif items[i] == 0:
            result.append("zero")
        else:
            result.append("negative")
        i += 1
    return result
"""
    
    encoder = NGEncoder()
    result = encoder.round_trip_test(code, encoding_level=5)
    
    print(f"📝 CODICE ORIGINALE:")
    print(code)
    print(f"\n🔧 SIMBOLI COMPRESSI:")
    print(f"   {result.encoding_result.compressed_symbols}")
    print(f"\n📊 CODICE RICOSTRUITO:")
    print(result.decoding_result.reconstructed_code)
    print(f"\n📊 METRICHE:")
    print(f"   - Fidelity: {result.fidelity_score:.3f}")
    print(f"   - Compressione: {result.compression_ratio:.1f}%")
    print(f"   - Simboli: {len(result.encoding_result.compressed_symbols)}")
    print(f"   - Sintassi valida: {'✅' if result.decoding_result.reconstruction_result.syntax_valid else '❌'}")
    print(f"   - AST equivalente: {'✅' if result.ast_equivalent else '❌'}")
    print(f"   - Round-trip: {'✅' if result.decoding_result.round_trip_successful else '❌'}")
    
    return result

if __name__ == "__main__":
    print("🎯 PATCH 6: Branch & Loop Coverage Test")
    print("=" * 60)
    
    # Test 1: If/Elif/Else
    result1 = test_if_elif_else()
    
    # Test 2: While Loop
    result2 = test_while_loop()
    
    # Test 3: Nested If/While
    result3 = test_nested_if_while()
    
    print("\n" + "=" * 60)
    print("🎯 RISULTATI FINALI PATCH 6:")
    print(f"   Test 1 (If/Elif/Else): Fidelity {result1.fidelity_score:.3f}, Compressione {result1.compression_ratio:.1f}%")
    print(f"   Test 2 (While Loop): Fidelity {result2.fidelity_score:.3f}, Compressione {result2.compression_ratio:.1f}%")
    print(f"   Test 3 (Nested): Fidelity {result3.fidelity_score:.3f}, Compressione {result3.compression_ratio:.1f}%")
    
    avg_fidelity = (result1.fidelity_score + result2.fidelity_score + result3.fidelity_score) / 3
    avg_compression = (result1.compression_ratio + result2.compression_ratio + result3.compression_ratio) / 3
    
    print(f"\n🎯 MEDIA PATCH 6:")
    print(f"   - Fidelity media: {avg_fidelity:.3f}")
    print(f"   - Compressione media: {avg_compression:.1f}%")
    
    if avg_fidelity >= 0.98 and avg_compression >= 80:
        print("✅ PATCH 6 SUCCESSO: Target raggiunti!")
    else:
        print("❌ PATCH 6 FALLIMENTO: Target non raggiunti")
