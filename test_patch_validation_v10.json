{"summary": {"total_tests": 18, "patches_applied": 18, "patches_correct": 0, "compilations_success": 8, "patch_application_rate": 1.0, "patch_correctness_rate": 0.0, "compilation_success_rate": 0.4444444444444444, "avg_patch_time": 0.002618536999999884}, "by_error_type": {"import": {"total": 6, "applied": 6, "correct": 0, "application_rate": 1.0, "correctness_rate": 0.0}, "syntax": {"total": 6, "applied": 6, "correct": 0, "application_rate": 1.0, "correctness_rate": 0.0}, "semantic": {"total": 6, "applied": 6, "correct": 0, "application_rate": 1.0, "correctness_rate": 0.0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.007792832999999888, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.3346062', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 335923, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "syntax", "original_line": "class MockRequest:", "modified_line": "class MockRequest", "line_number": 23, "description": "Removed colon at line 23"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00408816699999992, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "semantic", "original_line": "self._r = request", "modified_line": "self._r = request\nundefined_function_call()", "line_number": 36, "description": "Added undefined function call at line 36"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00020929200000008308, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/auth.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.003519458999999614, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.35168', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 351730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/auth.py", "error_injection": {"error_type": "syntax", "original_line": "def _basic_auth_str(username, password):", "modified_line": "def _basic_auth_str(username, password)", "line_number": 25, "description": "Removed colon at line 25"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00021274999999931765, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/auth.py", "error_injection": {"error_type": "semantic", "original_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"", "modified_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"\nundefined_function_call()", "line_number": 21, "description": "Added undefined function call at line 21"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.014192457999999242, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.37021', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 370252, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.004458957999999846, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.377886', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 377919, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "syntax", "original_line": "if sys.platform == \"win32\":", "modified_line": "if sys.platform == \"win32\"", "line_number": 55, "description": "Removed colon at line 55"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00026112499999975114, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "semantic", "original_line": "if sys.platform == \"win32\":", "modified_line": "if sys.platform == \"win32\":\nundefined_function_call()", "line_number": 55, "description": "Added undefined function call at line 55"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0002213329999998237, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/hooks.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.0002692080000006314, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.386131', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 386165, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/hooks.py", "error_injection": {"error_type": "syntax", "original_line": "def default_hooks():", "modified_line": "def default_hooks()", "line_number": 15, "description": "Removed colon at line 15"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 8.170800000062428e-05, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/hooks.py", "error_injection": {"error_type": "semantic", "original_line": "HOOKS = [\"response\"]", "modified_line": "HOOKS = [\"response\"]\nundefined_function_call()", "line_number": 12, "description": "Added undefined function call at line 12"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.0031359169999998215, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.3869598', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 389869, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/compat.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.000494208000000107, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.3907351', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 390764, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/compat.py", "error_injection": {"error_type": "syntax", "original_line": "def _resolve_char_detection():", "modified_line": "def _resolve_char_detection()", "line_number": 30, "description": "Removed colon at line 30"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00016133299999943063, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/compat.py", "error_injection": {"error_type": "semantic", "original_line": "is_urllib3_1 = int(urllib3_version.split(\".\")[0]) == 1", "modified_line": "is_urllib3_1 = int(urllib3_version.split(\".\")[0]) == 1\nundefined_function_call()", "line_number": 20, "description": "Added undefined function call at line 20"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00010149999999953252, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/models.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": true, "patch_correct": false, "compilation_success": true, "execution_success": true, "patch_time": 0.007193667000000126, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.3992362', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='No patch candidates generated', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 399274, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/models.py", "error_injection": {"error_type": "syntax", "original_line": "class RequestEncodingMixin:", "modified_line": "class RequestEncodingMixin", "line_number": 84, "description": "Removed colon at line 84"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0004252080000002323, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_mh0z1zqh/requests-main/src/requests/models.py", "error_injection": {"error_type": "semantic", "original_line": "REDIRECT_STATI = (", "modified_line": "REDIRECT_STATI = (\nundefined_function_call()", "line_number": 71, "description": "Added undefined function call at line 71"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0003145419999999177, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241390.343482', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 23, 10, 347324, tzinfo=datetime.timezone.utc), metadata={})"}]}