{"summary": {"total_tests": 9, "patches_applied": 0, "patches_correct": 0, "compilations_success": 0, "patch_application_rate": 0.0, "patch_correctness_rate": 0.0, "compilation_success_rate": 0.0, "avg_patch_time": 0}, "by_error_type": {"import": {"total": 3, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}, "syntax": {"total": 3, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}, "semantic": {"total": 3, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: 'bool' object is not callable"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "'bool' object is not callable", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "syntax", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: 'bool' object is not callable"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "'bool' object is not callable", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "semantic", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: 'bool' object is not callable"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "'bool' object is not callable", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/auth.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: 'bool' object is not callable"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "'bool' object is not callable", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/auth.py", "error_injection": {"error_type": "syntax", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: local variable 'ValidationError' referenced before assignment"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "local variable 'ValidationError' referenced before assignment", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/auth.py", "error_injection": {"error_type": "semantic", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: 'bool' object is not callable"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "'bool' object is not callable", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: 'bool' object is not callable"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "'bool' object is not callable", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "syntax", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: 'bool' object is not callable"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "'bool' object is not callable", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_2ba074qa/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "semantic", "original_line": "", "modified_line": "", "line_number": 0, "description": "Exception: local variable 'ValidationError' referenced before assignment"}, "patch_applied": false, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0, "error_message": "local variable 'ValidationError' referenced before assignment", "patch_content": null}]}