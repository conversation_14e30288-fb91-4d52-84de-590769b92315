#!/usr/bin/env python3
"""
NEUROGLYPH Autonomous Evolution Demo

Dimostrazione pratica del sistema di evoluzione autonoma:
- Pattern Learning da proof trees reali
- Symbol Evolution basata su utilizzo
- Cold Start Learning per nuovi domini
- Coordinamento automatico via Controller

Fase 7.0 - Autonomous Evolution
"""

import asyncio
import time
import sys
from pathlib import Path

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))

from neuroglyph.learning.autonomous_evolution_controller import AutonomousEvolutionController, EvolutionConfig
from neuroglyph.learning.cold_start_learning import DomainBootstrap
from neuroglyph.logic.proof_tree import ProofTree, ProofStep
from neuroglyph.logic.formula import Predicate, Variable, Implication
from neuroglyph.cognitive.ng_decoder import NGDecoder


class AutonomousEvolutionDemo:
    """Demo completa del sistema di evoluzione autonoma."""
    
    def __init__(self):
        """Inizializza demo."""
        # Configurazione evoluzione
        self.config = EvolutionConfig(
            enable_pattern_learning=True,
            enable_symbol_evolution=True,
            enable_cold_start_learning=True,
            learning_interval_seconds=30,  # Breve per demo
            evolution_interval_seconds=60,  # Breve per demo
            auto_apply_high_confidence=False,  # Sicurezza
            confidence_threshold_auto_apply=0.9
        )
        
        # Controller principale
        self.controller = AutonomousEvolutionController(config=self.config)
        
        # Decoder per generazione codice
        self.decoder = NGDecoder()
        
        print("🚀 NEUROGLYPH Autonomous Evolution Demo")
        print("=" * 50)
    
    def simulate_real_usage(self):
        """Simula utilizzo reale del sistema NEUROGLYPH."""
        print("\n📊 Simulando utilizzo reale del sistema...")
        
        # 1. Proof trees da reasoning matematico
        math_proofs = self._generate_math_proofs()
        for proof in math_proofs:
            self.controller.add_proof_for_analysis(proof)
        
        # 2. Code samples da generazione codice
        code_samples = self._generate_code_samples()
        for code in code_samples:
            self.controller.add_code_for_analysis(code)
        
        # 3. Simula utilizzo simboli
        self._simulate_symbol_usage()
        
        print(f"✅ Simulazione completata:")
        print(f"   - {len(math_proofs)} proof trees aggiunti")
        print(f"   - {len(code_samples)} code samples aggiunti")
        print(f"   - Utilizzo simboli tracciato")
    
    def _generate_math_proofs(self):
        """Genera proof trees matematici realistici."""
        proofs = []
        
        # Proof 1: Teorema di Euclide (GCD)
        steps1 = [
            ProofStep(
                formula=Predicate('divides', [Variable('d'), Variable('a')]),
                justification='d divides a'
            ),
            ProofStep(
                formula=Predicate('divides', [Variable('d'), Variable('b')]),
                justification='d divides b'
            ),
            ProofStep(
                formula=Predicate('greatest_common_divisor', [Variable('d'), Variable('a'), Variable('b')]),
                justification='d is GCD of a and b'
            )
        ]
        proofs.append(ProofTree(steps=steps1))
        
        # Proof 2: Induzione matematica (Fibonacci)
        steps2 = [
            ProofStep(
                formula=Predicate('fibonacci', [Variable('0')]),
                justification='Base case: F(0) = 0'
            ),
            ProofStep(
                formula=Predicate('fibonacci', [Variable('1')]),
                justification='Base case: F(1) = 1'
            ),
            ProofStep(
                formula=Implication(
                    Predicate('fibonacci', [Variable('n-1')]),
                    Predicate('fibonacci', [Variable('n')])
                ),
                justification='Inductive step: F(n) = F(n-1) + F(n-2)'
            )
        ]
        proofs.append(ProofTree(steps=steps2))
        
        # Proof 3: Proprietà numeri primi
        steps3 = [
            ProofStep(
                formula=Predicate('prime', [Variable('p')]),
                justification='p is prime'
            ),
            ProofStep(
                formula=Predicate('not_divisible', [Variable('p'), Variable('k')]),
                justification='p not divisible by k for 1 < k < p'
            )
        ]
        proofs.append(ProofTree(steps=steps3))
        
        # Proof 4: Pattern ricorrente (per testare frequency)
        for i in range(3):
            steps_repeat = [
                ProofStep(
                    formula=Predicate('recursive_pattern', [Variable(f'x{i}')]),
                    justification=f'Recursive pattern instance {i}'
                )
            ]
            proofs.append(ProofTree(steps=steps_repeat))
        
        return proofs
    
    def _generate_code_samples(self):
        """Genera campioni di codice realistici."""
        return [
            # Algoritmo GCD
            """
def gcd(a, b):
    while b:
        a, b = b, a % b
    return a
            """,
            
            # Fibonacci iterativo
            """
def fibonacci(n):
    if n <= 1:
        return n
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b
            """,
            
            # Verifica numero primo
            """
def is_prime(n):
    if n < 2:
        return False
    for i in range(2, int(n**0.5) + 1):
        if n % i == 0:
            return False
    return True
            """,
            
            # Pattern ricorrenti
            """
for i in range(10):
    if i % 2 == 0:
        print(f"Even: {i}")
    else:
        print(f"Odd: {i}")
            """,
            
            """
def binary_search(arr, target):
    left, right = 0, len(arr) - 1
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    return -1
            """
        ]
    
    def _simulate_symbol_usage(self):
        """Simula utilizzo simboli nel sistema."""
        # Simboli utilizzati frequentemente
        frequent_symbols = ['⟨⟩', '◊', '⟲', '⟳']
        
        # Simboli utilizzati raramente
        rare_symbols = ['⧫', '⧪', '⧬']
        
        # Simula utilizzo frequente
        for symbol in frequent_symbols:
            for _ in range(10):
                self.controller.evolution_system.track_symbol_usage(
                    symbol=symbol,
                    context_type='reasoning',
                    domain='logic',
                    confidence=0.8 + (hash(symbol) % 20) / 100  # Variazione realistica
                )
        
        # Simula utilizzo raro
        for symbol in rare_symbols:
            for _ in range(2):
                self.controller.evolution_system.track_symbol_usage(
                    symbol=symbol,
                    context_type='parsing',
                    domain='math',
                    confidence=0.6
                )
    
    async def run_evolution_cycle(self):
        """Esegue un ciclo completo di evoluzione."""
        print("\n🧠 Eseguendo ciclo di evoluzione autonoma...")
        
        # Forza esecuzione pattern learning
        await self.controller._run_pattern_learning_cycle()
        
        # Forza esecuzione symbol evolution
        await self.controller._run_symbol_evolution_cycle()
        
        print("✅ Ciclo di evoluzione completato")
    
    def demonstrate_pattern_learning(self):
        """Dimostra pattern learning."""
        print("\n🔍 Dimostrazione Pattern Learning:")
        
        if self.controller.pattern_engine:
            stats = self.controller.pattern_engine.get_learning_statistics()
            print(f"   - Pattern analizzati: {stats['patterns_analyzed']}")
            print(f"   - Simboli proposti: {stats['symbols_proposed']}")
            print(f"   - Gap identificati: {stats['gaps_identified']}")
            print(f"   - Registry corrente: {stats['current_registry_size']} simboli")
    
    def demonstrate_symbol_evolution(self):
        """Dimostra symbol evolution."""
        print("\n🧬 Dimostrazione Symbol Evolution:")
        
        if self.controller.evolution_system:
            stats = self.controller.evolution_system.get_evolution_statistics()
            print(f"   - Simboli tracciati: {stats['symbols_tracked']}")
            print(f"   - Proposte generate: {stats['proposals_generated']}")
            print(f"   - Utilizzo medio simboli: {stats['avg_symbol_usage']:.1f}")
            
            # Mostra simboli più utilizzati
            most_used = stats['most_used_symbols'][:3]
            if most_used:
                print("   - Simboli più utilizzati:")
                for symbol, usage_stats in most_used:
                    print(f"     • {symbol}: {usage_stats.total_usage} utilizzi")
    
    def demonstrate_cold_start_learning(self):
        """Dimostra cold start learning."""
        print("\n🚀 Dimostrazione Cold Start Learning:")
        
        if self.controller.cold_start_system:
            # Bootstrap nuovo dominio
            session_id = self.controller.bootstrap_domain(
                domain_name="quantum_computing",
                seed_patterns=[
                    "qubit |0⟩",
                    "qubit |1⟩", 
                    "superposition |+⟩",
                    "entanglement |Φ+⟩"
                ],
                expected_symbols=4
            )
            
            print(f"   - Nuovo dominio 'quantum_computing' avviato")
            print(f"   - Sessione ID: {session_id}")
            
            stats = self.controller.cold_start_system.get_learning_statistics()
            print(f"   - Domini appresi: {stats['domains_learned']}")
            print(f"   - Pattern scoperti: {stats['total_patterns_discovered']}")
            print(f"   - Simboli creati: {stats['total_symbols_created']}")
    
    def demonstrate_code_generation_integration(self):
        """Dimostra integrazione con code generation."""
        print("\n💻 Dimostrazione Integrazione Code Generation:")
        
        # Genera codice da pattern appresi
        from neuroglyph.logic.proof_tree import ProofTree, ProofStep
        from neuroglyph.logic.formula import Predicate, Variable
        
        # Crea proof per algoritmo noto
        steps = [
            ProofStep(
                formula=Predicate('greatest_common_divisor', [Variable('a'), Variable('b')]),
                justification='Evolved pattern for GCD'
            )
        ]
        proof = ProofTree(steps=steps)
        
        # Genera codice
        result = self.decoder.generate_code_from_proof(proof)
        
        if result.success:
            print(f"   - Template utilizzato: {result.template_used}")
            print(f"   - Codice generato: {len(result.generated_code)} caratteri")
            print(f"   - Tempo generazione: {result.generation_time*1000:.1f}ms")
            
            # Test esecuzione
            try:
                namespace = {}
                exec(result.generated_code, namespace)
                if 'gcd' in namespace:
                    test_result = namespace['gcd'](48, 18)
                    print(f"   - Test esecuzione: gcd(48, 18) = {test_result} ✅")
            except Exception as e:
                print(f"   - Errore esecuzione: {e}")
        else:
            print(f"   - Generazione fallita: {result.error_message}")
    
    def show_evolution_status(self):
        """Mostra status completo evoluzione."""
        print("\n📊 Status Evoluzione Autonoma:")
        
        status = self.controller.get_evolution_status()
        
        print(f"   - Sistema attivo: {status['is_running']}")
        print(f"   - Pattern appresi: {status['metrics']['total_patterns_learned']}")
        print(f"   - Simboli evoluti: {status['metrics']['total_symbols_evolved']}")
        print(f"   - Domini bootstrappati: {status['metrics']['total_domains_bootstrapped']}")
        print(f"   - Proof in queue: {status['metrics']['proof_queue_size']}")
        print(f"   - Code samples in queue: {status['metrics']['code_queue_size']}")
    
    async def run_complete_demo(self):
        """Esegue demo completa."""
        print("🎬 Avviando demo completa Autonomous Evolution...")
        
        # 1. Simula utilizzo reale
        self.simulate_real_usage()
        
        # 2. Esegue ciclo evoluzione
        await self.run_evolution_cycle()
        
        # 3. Dimostra componenti individuali
        self.demonstrate_pattern_learning()
        self.demonstrate_symbol_evolution()
        self.demonstrate_cold_start_learning()
        
        # 4. Dimostra integrazione
        self.demonstrate_code_generation_integration()
        
        # 5. Status finale
        self.show_evolution_status()
        
        print("\n🎉 Demo Autonomous Evolution completata!")
        print("Il sistema NEUROGLYPH ora può evolvere autonomamente!")


async def main():
    """Main function."""
    demo = AutonomousEvolutionDemo()
    await demo.run_complete_demo()


if __name__ == "__main__":
    asyncio.run(main())
