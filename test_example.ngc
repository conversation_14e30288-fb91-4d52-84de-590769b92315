{"symbols": ["⎋§with_0§", "◊§if_1§", "⌦§fstr_2§", "◊§if_3§", "◊§if_4§", "◊§if_5§", "⌦§fstr_6§", "⟐§try_7§", "⟲§for_8§", "⟨⟩§func_9§", "⟨⟩§func_10§", "⟨⟩§func_11§", "⟨⟩§func_12§", "⟡§class_13§", "⌦§fstr_14§", "◊§if_15§"], "original_length": 1534, "compressed_length": 16, "compression_ratio": 89.70013037809647, "encoding_level": 5, "encoding_timestamp": "2025-06-04T21:38:50.257813", "encoder_version": "2.0_AST", "language": "python", "neuroglyph_version": "2.0_AST"}