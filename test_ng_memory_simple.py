#!/usr/bin/env python3
"""
Test semplificato per NGMemory
"""

from neuroglyph.cognitive.memory import NGMemory
from neuroglyph.cognitive.memory_models import MemoryConfig
from neuroglyph.cognitive.data_structures import PriorityVector, DomainType

def test_simple():
    print("🧠 TEST SIMPLE NG_MEMORY")
    
    # Crea memoria
    config = MemoryConfig(db_path=":memory:")
    memory = NGMemory(config)
    
    print(f"✅ Memory created, count: {memory.count()}")
    
    # Crea priority vector
    prio_vec = {
        'urgency': 0.8,
        'risk': 0.6,
        'domain': 'security',
        'confidence': 0.9
    }
    
    # Test append
    try:
        record = memory.append(
            prompt="Fix security bug",
            response="def fix(): pass",
            prio_vec=prio_vec,
            symbols=["⟨⟩§func_0§"],
            tags=["security"]
        )
        print(f"✅ Record appended: {record.id}")
        print(f"   Count after append: {memory.count()}")
    except Exception as e:
        print(f"❌ Append failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_simple()
    print(f"Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
