{"summary": {"total_tests": 27, "patches_applied": 15, "patches_correct": 0, "compilations_success": 12, "patch_application_rate": 0.5555555555555556, "patch_correctness_rate": 0.0, "compilation_success_rate": 0.4444444444444444, "avg_patch_time": 0.0004215582666666033}, "by_error_type": {"import": {"total": 9, "applied": 0, "correct": 0, "application_rate": 0.0, "correctness_rate": 0.0}, "syntax": {"total": 9, "applied": 9, "correct": 0, "application_rate": 1.0, "correctness_rate": 0.0}, "semantic": {"total": 9, "applied": 6, "correct": 0, "application_rate": 0.6666666666666666, "correctness_rate": 0.0}}, "detailed_results": [{"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "syntax", "original_line": "class MockRequest:", "modified_line": "class MockRequest", "line_number": 23, "description": "Removed colon at line 23"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0036360000000001946, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/cookies.py", "error_injection": {"error_type": "semantic", "original_line": "self._r = request", "modified_line": "self._r = request\nundefined_function_call()", "line_number": 36, "description": "Added undefined function call at line 36"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00019891699999963208, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/auth.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/auth.py", "error_injection": {"error_type": "syntax", "original_line": "def _basic_auth_str(username, password):", "modified_line": "def _basic_auth_str(username, password)", "line_number": 25, "description": "Removed colon at line 25"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0001222920000003569, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/auth.py", "error_injection": {"error_type": "semantic", "original_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"", "modified_line": "CONTENT_TYPE_FORM_URLENCODED = \"application/x-www-form-urlencoded\"\nundefined_function_call()", "line_number": 21, "description": "Added undefined function call at line 21"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "syntax", "original_line": "if sys.platform == \"win32\":", "modified_line": "if sys.platform == \"win32\"", "line_number": 55, "description": "Removed colon at line 55"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00019220799999963845, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/sessions.py", "error_injection": {"error_type": "semantic", "original_line": "if sys.platform == \"win32\":", "modified_line": "if sys.platform == \"win32\":\nundefined_function_call()", "line_number": 55, "description": "Added undefined function call at line 55"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00016020799999960644, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/hooks.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/hooks.py", "error_injection": {"error_type": "syntax", "original_line": "def default_hooks():", "modified_line": "def default_hooks()", "line_number": 15, "description": "Removed colon at line 15"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 4.466699999916557e-05, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/hooks.py", "error_injection": {"error_type": "semantic", "original_line": "HOOKS = [\"response\"]", "modified_line": "HOOKS = [\"response\"]\nundefined_function_call()", "line_number": 12, "description": "Added undefined function call at line 12"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/compat.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/compat.py", "error_injection": {"error_type": "syntax", "original_line": "def _resolve_char_detection():", "modified_line": "def _resolve_char_detection()", "line_number": 30, "description": "Removed colon at line 30"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 9.604099999993565e-05, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/compat.py", "error_injection": {"error_type": "semantic", "original_line": "is_urllib3_1 = int(urllib3_version.split(\".\")[0]) == 1", "modified_line": "is_urllib3_1 = int(urllib3_version.split(\".\")[0]) == 1\nundefined_function_call()", "line_number": 20, "description": "Added undefined function call at line 20"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 6.89170000001127e-05, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/models.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/models.py", "error_injection": {"error_type": "syntax", "original_line": "class RequestEncodingMixin:", "modified_line": "class RequestEncodingMixin", "line_number": 84, "description": "Removed colon at line 84"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00034466600000016, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/models.py", "error_injection": {"error_type": "semantic", "original_line": "REDIRECT_STATI = (", "modified_line": "REDIRECT_STATI = (\nundefined_function_call()", "line_number": 71, "description": "Added undefined function call at line 71"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0002415830000002117, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/certs.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/certs.py", "error_injection": {"error_type": "syntax", "original_line": "if __name__ == \"__main__\":", "modified_line": "if __name__ == \"__main__\"", "line_number": 16, "description": "Removed colon at line 16"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 7.258300000057005e-05, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/certs.py", "error_injection": {"error_type": "semantic", "original_line": "if __name__ == \"__main__\":", "modified_line": "if __name__ == \"__main__\":\nundefined_function_call()", "line_number": 16, "description": "Added undefined function call at line 16"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 5.27919999999682e-05, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/__init__.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/__init__.py", "error_injection": {"error_type": "syntax", "original_line": "def check_compatibility(urllib3_version, chardet_version, charset_normalizer_version):", "modified_line": "def check_compatibility(urllib3_version, chardet_version, charset_normalizer_version)", "line_number": 58, "description": "Removed colon at line 58"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.00010758400000021595, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/__init__.py", "error_injection": {"error_type": "semantic", "original_line": ">>> r = requests.get('https://www.python.org')", "modified_line": ">>> r = requests.get('https://www.python.org')\nundefined_function_call()", "line_number": 14, "description": "Added undefined function call at line 14"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/status_codes.py", "error_injection": {"error_type": "import", "original_line": "", "modified_line": "import nonexistent_module_xyz\nresult = nonexistent_module_xyz.some_function()", "line_number": 1, "description": "Added non-existent import with usage"}, "patch_applied": false, "patch_correct": false, "compilation_success": true, "execution_success": false, "patch_time": 0.0, "error_message": "Error not detected by validation", "patch_content": null}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/status_codes.py", "error_injection": {"error_type": "syntax", "original_line": "def _init():", "modified_line": "def _init()", "line_number": 109, "description": "Removed colon at line 109"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 0.0009168749999997061, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}, {"file_path": "/var/folders/k7/mt5g0hrn49qdpv_99bv1p13h0000gn/T/ng_patch_test_rwppk98z/requests-main/src/requests/status_codes.py", "error_injection": {"error_type": "semantic", "original_line": "_codes = {", "modified_line": "_codes = {\nundefined_function_call()", "line_number": 23, "description": "Added undefined function call at line 23"}, "patch_applied": true, "patch_correct": false, "compilation_success": false, "execution_success": false, "patch_time": 6.804099999957458e-05, "error_message": null, "patch_content": "PatchResult(patch_id='', error_id='1749241305.936267', success=False, status=<PatchStatus.FAILED: 'failed'>, output=None, error_message='All patch candidates failed', execution_time=0.0, memory_usage=0.0, validation_passed=False, test_results=[], performance_improvement=0.0, quality_improvement=0.0, applied_at=datetime.datetime(2025, 6, 6, 20, 21, 45, 939730, tzinfo=datetime.timezone.utc), metadata={})"}]}