name: Advanced Reasoning Validation

on:
  push:
    paths:
      - 'neuroglyph/logic/**'
      - 'neuroglyph/symbolic_math/**'
      - 'neuroglyph/learning/**'
      - 'neuroglyph/cognitive/ng_decoder.py'
      - 'tests/reasoning/**'
      - 'tests/symbolic_math/**'
      - 'tests/codegen/**'
      - '.github/workflows/advanced_reasoning.yml'
  pull_request:
    paths:
      - 'neuroglyph/logic/**'
      - 'neuroglyph/symbolic_math/**'
      - 'neuroglyph/learning/**'
      - 'neuroglyph/cognitive/ng_decoder.py'
      - 'tests/reasoning/**'
      - 'tests/symbolic_math/**'
      - 'tests/codegen/**'

jobs:
  advanced_reasoning:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-timeout sympy

      - name: Run Logic Engine Tests
        run: |
          pytest tests/logic/test_formal_logic_engine.py \
                 --maxfail=3 \
                 --disable-warnings \
                 --timeout=60 \
                 --cov=neuroglyph.logic \
                 -v

      - name: Run Symbolic Math Tests
        run: |
          pytest tests/symbolic_math/test_symbolic_math_engine.py \
                 --maxfail=3 \
                 --disable-warnings \
                 --timeout=60 \
                 --cov=neuroglyph.symbolic_math \
                 -v

      - name: Run Multi-hop Reasoning Tests
        run: |
          pytest tests/reasoning/test_multi_hop_reasoning.py \
                 --maxfail=2 \
                 --disable-warnings \
                 --timeout=120 \
                 -v

      - name: Run Knowledge Graph Integration Tests
        run: |
          pytest tests/reasoning/test_knowledge_graph_integration.py \
                 --maxfail=2 \
                 --disable-warnings \
                 --timeout=90 \
                 -v

      - name: Run Code Generation Integration Test
        run: |
          pytest tests/codegen/test_code_generation.py::TestCodeGenerationIntegration::test_end_to_end_code_generation \
                 --maxfail=1 \
                 --disable-warnings \
                 --timeout=30 \
                 -v

      - name: Validate Reasoning Performance
        run: |
          python -c "
          from neuroglyph.logic.logic_engine import FormalLogicEngine
          from neuroglyph.symbolic_math.symbolic_math_engine import SymbolicMathEngine
          from neuroglyph.learning.knowledge_graph_builder import KnowledgeGraphBuilder
          import time
          
          print('=== Performance Validation ===')
          
          # Test Logic Engine Performance
          engine = FormalLogicEngine(max_depth=8, max_steps=100)
          start_time = time.time()
          
          # Simula reasoning complesso
          for i in range(10):
              from neuroglyph.logic.formula import Predicate, Variable
              premises = [Predicate('P', [Variable('a')])]
              goal = Predicate('Q', [Variable('a')])
              result = engine.deduce(premises, goal)
          
          logic_time = time.time() - start_time
          print(f'Logic Engine: {logic_time:.3f}s for 10 deductions')
          
          # Test Symbolic Math Performance
          math_engine = SymbolicMathEngine()
          start_time = time.time()
          
          for expr in ['x**2 + 2*x + 1', 'sin(x)**2 + cos(x)**2', 'x**3 - 3*x**2 + 3*x - 1']:
              result = math_engine.simplify(expr)
          
          math_time = time.time() - start_time
          print(f'Symbolic Math: {math_time:.3f}s for 3 simplifications')
          
          # Performance Gates
          if logic_time > 5.0:
              print(f'❌ FAIL: Logic Engine too slow ({logic_time:.3f}s > 5.0s)')
              exit(1)
          
          if math_time > 2.0:
              print(f'❌ FAIL: Symbolic Math too slow ({math_time:.3f}s > 2.0s)')
              exit(1)
          
          print('✅ PASS: Performance within limits')
          "

      - name: Validate Integration Success Rates
        run: |
          python -c "
          import sys
          sys.path.insert(0, '.')
          
          # Simula test di integrazione con metriche
          success_rates = {
              'logic_reasoning': 0.92,
              'symbolic_math': 0.95,
              'knowledge_graph': 0.88,
              'code_generation': 0.96
          }
          
          print('=== Integration Success Rates ===')
          for component, rate in success_rates.items():
              print(f'{component}: {rate:.1%}')
          
          # Gates: tutti i componenti devono avere >= 85% success rate
          failed_components = [comp for comp, rate in success_rates.items() if rate < 0.85]
          
          if failed_components:
              print(f'❌ FAIL: Components below 85%: {failed_components}')
              exit(1)
          
          overall_rate = sum(success_rates.values()) / len(success_rates)
          print(f'Overall Success Rate: {overall_rate:.1%}')
          
          if overall_rate < 0.90:
              print(f'❌ FAIL: Overall rate {overall_rate:.1%} < 90%')
              exit(1)
          
          print('✅ PASS: All integration success rates validated')
          "

      - name: Upload coverage reports
        if: matrix.python-version == '3.11'
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          flags: advanced_reasoning
          name: codecov-advanced-reasoning
