name: NEUROGLYPH External Validation Suite

on:
  push:
    branches: [ main, develop, fix/import-debug-resolution ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Nightly run at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      validation_type:
        description: 'Type of validation to run'
        required: true
        default: 'quick'
        type: choice
        options:
        - quick
        - full
        - reasoning_only
        - humaneval_only

env:
  PYTHONFAULTHANDLER: 1
  NEUROGLYPH_DEBUG: 0

jobs:
  external-validation:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      matrix:
        python-version: [3.9, 3.10]
        validation-suite: [reasoning, humaneval, hellaswag]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 1
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    
    - name: Cache validation datasets
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/neuroglyph
          /tmp/ng_*
        key: ${{ runner.os }}-validation-${{ hashFiles('requirements.txt') }}-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-validation-${{ hashFiles('requirements.txt') }}-
          ${{ runner.os }}-validation-
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install pytest pytest-timeout pytest-cov
        pip install sympy requests
    
    - name: Verify NEUROGLYPH installation
      run: |
        python -c "from neuroglyph.cognitive.reasoner import NGReasoner; print('✅ NGReasoner OK')"
        python -c "from neuroglyph.core.parser.ng_parser import NGParser; print('✅ NGParser OK')"
        python -c "from neuroglyph.core.encoder.encoder import NGEncoder; print('✅ NGEncoder OK')"
    
    - name: Run Quick Import Test
      run: |
        python scripts/test_external_validation_quick.py
    
    - name: Run Reasoning Validation
      if: matrix.validation-suite == 'reasoning' || github.event.inputs.validation_type == 'reasoning_only'
      run: |
        python scripts/validate_reasoning_external.py --output reasoning_ci_report.json
      timeout-minutes: 5
    
    - name: Run HumanEval Validation
      if: matrix.validation-suite == 'humaneval' || github.event.inputs.validation_type == 'humaneval_only'
      run: |
        python scripts/validate_humaneval_extended.py --output humaneval_ci_report.json
      timeout-minutes: 10
    
    - name: Run HellaSwag Validation
      if: matrix.validation-suite == 'hellaswag'
      run: |
        python scripts/validate_hellaswag_extended.py --output hellaswag_ci_report.json
      timeout-minutes: 5
    
    - name: Run Full Validation Suite
      if: github.event.inputs.validation_type == 'full' || github.event_name == 'schedule'
      run: |
        # Skip patch and encoder tests in CI (require external downloads)
        python scripts/validate_reasoning_external.py --output full_reasoning_report.json
        python scripts/validate_humaneval_extended.py --output full_humaneval_report.json
        python scripts/validate_hellaswag_extended.py --output full_hellaswag_report.json
      timeout-minutes: 20
    
    - name: Parse validation results
      if: always()
      run: |
        echo "## 📊 Validation Results" >> $GITHUB_STEP_SUMMARY
        
        # Check if reasoning report exists
        if [ -f "reasoning_ci_report.json" ] || [ -f "full_reasoning_report.json" ]; then
          REASONING_FILE=$(ls *reasoning*report.json | head -1)
          ACCURACY=$(python -c "import json; data=json.load(open('$REASONING_FILE')); print(f\"{data['summary']['overall_accuracy']:.1%}\")" 2>/dev/null || echo "N/A")
          echo "- **Reasoning Accuracy**: $ACCURACY" >> $GITHUB_STEP_SUMMARY
        fi
        
        # Check if humaneval report exists
        if [ -f "humaneval_ci_report.json" ] || [ -f "full_humaneval_report.json" ]; then
          HUMANEVAL_FILE=$(ls *humaneval*report.json | head -1)
          ACCURACY=$(python -c "import json; data=json.load(open('$HUMANEVAL_FILE')); print(f\"{data['summary']['code_correctness_rate']:.1%}\")" 2>/dev/null || echo "N/A")
          echo "- **HumanEval Accuracy**: $ACCURACY" >> $GITHUB_STEP_SUMMARY
        fi
        
        # Check if hellaswag report exists
        if [ -f "hellaswag_ci_report.json" ] || [ -f "full_hellaswag_report.json" ]; then
          HELLASWAG_FILE=$(ls *hellaswag*report.json | head -1)
          ACCURACY=$(python -c "import json; data=json.load(open('$HELLASWAG_FILE')); print(f\"{data['summary']['accuracy']:.1%}\")" 2>/dev/null || echo "N/A")
          echo "- **HellaSwag Accuracy**: $ACCURACY" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "🎯 **Targets**: Reasoning ≥70%, HumanEval ≥80%, HellaSwag ≥60%" >> $GITHUB_STEP_SUMMARY
    
    - name: Upload validation reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: validation-reports-${{ matrix.python-version }}-${{ matrix.validation-suite }}
        path: |
          *_report.json
          *.log
        retention-days: 30
    
    - name: Check validation targets
      if: always()
      run: |
        EXIT_CODE=0
        
        # Check reasoning target (70%)
        if [ -f "reasoning_ci_report.json" ] || [ -f "full_reasoning_report.json" ]; then
          REASONING_FILE=$(ls *reasoning*report.json | head -1)
          ACCURACY=$(python -c "import json; data=json.load(open('$REASONING_FILE')); print(data['summary']['overall_accuracy'])" 2>/dev/null || echo "0")
          if (( $(echo "$ACCURACY < 0.70" | bc -l) )); then
            echo "❌ Reasoning accuracy $ACCURACY < 0.70"
            EXIT_CODE=1
          else
            echo "✅ Reasoning accuracy $ACCURACY ≥ 0.70"
          fi
        fi
        
        # Check humaneval target (80%)
        if [ -f "humaneval_ci_report.json" ] || [ -f "full_humaneval_report.json" ]; then
          HUMANEVAL_FILE=$(ls *humaneval*report.json | head -1)
          ACCURACY=$(python -c "import json; data=json.load(open('$HUMANEVAL_FILE')); print(data['summary']['code_correctness_rate'])" 2>/dev/null || echo "0")
          if (( $(echo "$ACCURACY < 0.80" | bc -l) )); then
            echo "❌ HumanEval accuracy $ACCURACY < 0.80"
            EXIT_CODE=1
          else
            echo "✅ HumanEval accuracy $ACCURACY ≥ 0.80"
          fi
        fi
        
        # HellaSwag is optional for now (target 60% but not failing CI)
        if [ -f "hellaswag_ci_report.json" ] || [ -f "full_hellaswag_report.json" ]; then
          HELLASWAG_FILE=$(ls *hellaswag*report.json | head -1)
          ACCURACY=$(python -c "import json; data=json.load(open('$HELLASWAG_FILE')); print(data['summary']['accuracy'])" 2>/dev/null || echo "0")
          if (( $(echo "$ACCURACY < 0.60" | bc -l) )); then
            echo "⚠️ HellaSwag accuracy $ACCURACY < 0.60 (not failing CI)"
          else
            echo "✅ HellaSwag accuracy $ACCURACY ≥ 0.60"
          fi
        fi
        
        exit $EXIT_CODE

  performance-benchmark:
    runs-on: ubuntu-latest
    needs: external-validation
    if: github.event_name == 'schedule' || github.event.inputs.validation_type == 'full'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python 3.9
      uses: actions/setup-python@v4
      with:
        python-version: 3.9
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install pytest-benchmark
    
    - name: Run performance benchmarks
      run: |
        echo "## 🚀 Performance Benchmarks" >> $GITHUB_STEP_SUMMARY
        
        # Benchmark reasoning speed
        REASONING_TIME=$(python -c "
        import time
        from neuroglyph.cognitive.reasoner import NGReasoner
        from neuroglyph.core.parser.ng_parser import NGParser
        from neuroglyph.cognitive.data_structures import MemoryContext
        
        reasoner = NGReasoner(max_depth=8, max_paths=5)
        parser = NGParser()
        
        start = time.perf_counter()
        for i in range(10):
            parsed = parser.parse('Test reasoning problem')
            memory = MemoryContext(examples=[], errors=[], symbols=[])
            reasoner.reason(memory, parsed)
        end = time.perf_counter()
        
        avg_time = (end - start) / 10 * 1000
        print(f'{avg_time:.1f}')
        ")
        
        echo "- **Avg Reasoning Time**: ${REASONING_TIME}ms" >> $GITHUB_STEP_SUMMARY
        echo "- **Target**: <10ms per problem" >> $GITHUB_STEP_SUMMARY
        
        # Check performance target
        if (( $(echo "$REASONING_TIME > 10" | bc -l) )); then
          echo "⚠️ Performance regression: ${REASONING_TIME}ms > 10ms target"
        else
          echo "✅ Performance target met: ${REASONING_TIME}ms ≤ 10ms"
        fi

  update-readme-badge:
    runs-on: ubuntu-latest
    needs: external-validation
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Download validation reports
      uses: actions/download-artifact@v4
      with:
        pattern: validation-reports-*
        merge-multiple: true
    
    - name: Update README badge
      run: |
        # Calculate overall validation status
        OVERALL_STATUS="passing"
        
        # Check if any critical validation failed
        if ls *reasoning*report.json 1> /dev/null 2>&1; then
          REASONING_FILE=$(ls *reasoning*report.json | head -1)
          ACCURACY=$(python -c "import json; data=json.load(open('$REASONING_FILE')); print(data['summary']['overall_accuracy'])" 2>/dev/null || echo "0")
          if (( $(echo "$ACCURACY < 0.70" | bc -l) )); then
            OVERALL_STATUS="failing"
          fi
        fi
        
        # Update badge in README
        if [ "$OVERALL_STATUS" = "passing" ]; then
          BADGE_COLOR="brightgreen"
          BADGE_TEXT="External%20Validation-passing-brightgreen"
        else
          BADGE_COLOR="red"
          BADGE_TEXT="External%20Validation-failing-red"
        fi
        
        # Replace badge in README.md (if exists)
        if [ -f "README.md" ]; then
          sed -i "s|External%20Validation-[^-]*-[^)]*|$BADGE_TEXT|g" README.md || true
          
          # Commit changes if any
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add README.md
          git diff --staged --quiet || git commit -m "🤖 Update external validation badge [skip ci]"
          git push || true
        fi
