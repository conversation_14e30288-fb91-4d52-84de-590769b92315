name: Code Generation Validation

on:
  push:
    paths:
      - 'neuroglyph/code_templates/**'
      - 'neuroglyph/cognitive/ng_decoder.py'
      - 'tests/codegen/**'
      - '.github/workflows/code_generation.yml'
  pull_request:
    paths:
      - 'neuroglyph/code_templates/**'
      - 'neuroglyph/cognitive/ng_decoder.py'
      - 'tests/codegen/**'

jobs:
  code_generation:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-timeout

      - name: Run Code Generation Tests
        run: |
          pytest tests/codegen/test_code_generation.py \
                 --maxfail=1 \
                 --disable-warnings \
                 --timeout=30 \
                 --cov=neuroglyph.cognitive.ng_decoder \
                 --cov=neuroglyph.code_templates \
                 --cov-report=xml \
                 --cov-report=term-missing \
                 -v

      - name: Check Code Generation Success Rate
        run: |
          python -c "
          from neuroglyph.cognitive.ng_decoder import NGDecoder
          from neuroglyph.logic.proof_tree import ProofTree, ProofStep
          from neuroglyph.logic.formula import Predicate, Variable
          
          decoder = NGDecoder()
          
          # Test batch di generazioni
          test_cases = [
              ('greatest_common_divisor', 'euclid_gcd'),
              ('fibonacci', 'fibonacci_recursive'),
              ('prime', 'prime_check'),
              ('factorial', 'factorial_recursive')
          ]
          
          successful = 0
          total = len(test_cases)
          
          for formula_name, expected_template in test_cases:
              steps = [ProofStep(
                  formula=Predicate(formula_name, [Variable('x')]),
                  justification=f'{formula_name} algorithm test'
              )]
              proof = ProofTree(steps=steps)
              result = decoder.generate_code_from_proof(proof)
              
              if result.success:
                  successful += 1
                  print(f'✅ {formula_name} -> {result.template_used}')
              else:
                  print(f'❌ {formula_name} failed: {result.error_message}')
          
          success_rate = successful / total
          print(f'Success Rate: {success_rate:.2%} ({successful}/{total})')
          
          # Gate: richiede almeno 95% di successo
          if success_rate < 0.95:
              print(f'❌ FAIL: Success rate {success_rate:.2%} < 95%')
              exit(1)
          else:
              print(f'✅ PASS: Success rate {success_rate:.2%} >= 95%')
          "

      - name: Validate Template Completeness
        run: |
          python -c "
          from neuroglyph.code_templates.template_engine import TemplateEngine
          
          engine = TemplateEngine()
          stats = engine.get_statistics()
          
          print(f'Template Statistics: {stats}')
          
          # Gate: richiede almeno 8 template
          if stats['total_templates'] < 8:
              print(f'❌ FAIL: Only {stats[\"total_templates\"]} templates, need >= 8')
              exit(1)
          
          # Gate: richiede almeno 2 categorie
          if len(stats['categories']) < 2:
              print(f'❌ FAIL: Only {len(stats[\"categories\"])} categories, need >= 2')
              exit(1)
          
          print('✅ PASS: Template completeness validated')
          "

      - name: Upload coverage reports to Codecov
        if: matrix.python-version == '3.11'
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          flags: code_generation
          name: codecov-code-generation
