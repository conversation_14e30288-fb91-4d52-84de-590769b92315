#!/usr/bin/env python3
"""
NEUROGLYPH Comprehensive Quality Assurance Audit
Validazione completa del dataset simbolico prima del fine-tuning

Dimensioni di validazione:
1. Dataset Quality Validation
2. Symbol Tokenization Integrity  
3. AST and Structural Validation
4. Excellence Criteria Compliance
5. Benchmark Readiness

Obiettivo: Garantire dataset perfetto per il primo LLM simbolico pensante
"""

import json
import ast
import random
import statistics
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
from collections import Counter, defaultdict
from dataclasses import dataclass, field

# Import moduli NEUROGLYPH
from symbolic_dataset_validator import SymbolicDatasetValidator
from coverage_analyzer import CoverageAnalyzer
from excellence_expansion_config import ExcellenceConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class QAAuditReport:
    """Report completo di audit qualità."""
    
    # Dataset Quality
    total_examples: int = 0
    validation_rate: float = 0.0
    overall_quality: float = 0.0
    quality_distribution: Dict[str, int] = field(default_factory=dict)
    
    # Symbol Coverage
    symbol_coverage_percentage: float = 0.0
    symbols_tested: int = 0
    symbols_passed: int = 0
    coverage_gaps: List[str] = field(default_factory=list)
    
    # Tokenization Integrity
    tokenization_test_count: int = 0
    zero_splitting_rate: float = 0.0
    roundtrip_fidelity: float = 0.0
    tokenization_failures: List[str] = field(default_factory=list)
    
    # AST Validation
    ast_parseable_rate: float = 0.0
    ast_roundtrip_fidelity: float = 0.0
    structural_validity_rate: float = 0.0
    ast_failures: List[str] = field(default_factory=list)
    
    # Excellence Compliance
    excellence_criteria_met: bool = False
    anti_overfitting_score: float = 0.0
    innovation_validation: Dict[str, bool] = field(default_factory=dict)
    split_balance_score: float = 0.0
    
    # Benchmark Readiness
    format_compatibility: bool = False
    metadata_completeness: float = 0.0
    loading_performance_ms: float = 0.0
    training_readiness_score: float = 0.0
    
    # Overall Assessment
    audit_passed: bool = False
    critical_issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    audit_timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


class ComprehensiveQAAuditor:
    """Auditor completo per validazione qualità NEUROGLYPH."""
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """Inizializza auditor completo."""
        self.registry_path = registry_path
        self.validator = SymbolicDatasetValidator(registry_path)
        self.config = ExcellenceConfig()
        
        # Carica registry per validazione simboli
        self.symbol_registry = self._load_symbol_registry()
        
        logger.info(f"🔍 ComprehensiveQAAuditor inizializzato")
        logger.info(f"   Registry symbols: {len(self.symbol_registry)}")
        logger.info(f"   Excellence targets: {len(self.config.get_total_symbol_targets())}")
    
    def _load_symbol_registry(self) -> List[Dict]:
        """Carica registry simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading symbol registry: {e}")
            return []
    
    def conduct_full_audit(self, dataset_path: str) -> QAAuditReport:
        """
        Conduce audit completo del dataset.
        
        Args:
            dataset_path: Percorso al dataset da auditare
            
        Returns:
            Report completo di audit
        """
        logger.info(f"🔍 Starting comprehensive QA audit for: {dataset_path}")
        
        report = QAAuditReport()
        
        # 1. Dataset Quality Validation
        logger.info(f"📊 Phase 1: Dataset Quality Validation")
        self._validate_dataset_quality(dataset_path, report)
        
        # 2. Symbol Tokenization Integrity
        logger.info(f"🔤 Phase 2: Symbol Tokenization Integrity")
        self._validate_tokenization_integrity(dataset_path, report)
        
        # 3. AST and Structural Validation
        logger.info(f"🌳 Phase 3: AST and Structural Validation")
        self._validate_ast_structure(dataset_path, report)
        
        # 4. Excellence Criteria Compliance
        logger.info(f"⭐ Phase 4: Excellence Criteria Compliance")
        self._validate_excellence_compliance(dataset_path, report)
        
        # 5. Benchmark Readiness
        logger.info(f"🚀 Phase 5: Benchmark Readiness")
        self._validate_benchmark_readiness(dataset_path, report)
        
        # Final Assessment
        self._conduct_final_assessment(report)
        
        logger.info(f"✅ Comprehensive QA audit completed")
        return report
    
    def _validate_dataset_quality(self, dataset_path: str, report: QAAuditReport):
        """Valida qualità generale del dataset."""
        logger.info(f"   Validating dataset quality...")
        
        # Usa validator esistente
        validation_result = self.validator.validate_dataset(dataset_path)
        
        report.total_examples = validation_result.total_examples
        report.validation_rate = validation_result.validation_rate
        report.overall_quality = validation_result.quality_scores.get('overall_quality', 0.0)
        
        # Distribuzione qualità per categoria
        quality_by_category = defaultdict(list)
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            example = json.loads(line.strip())
                            category = example.get('category', 'unknown')
                            
                            # Valida esempio singolo
                            example_result = self.validator.validate_example(example)
                            quality_score = example_result.scores.get('overall_quality', 0.0)
                            quality_by_category[category].append(quality_score)
                            
                        except Exception as e:
                            logger.warning(f"Error validating example {line_num}: {e}")
        
        except Exception as e:
            logger.error(f"Error reading dataset: {e}")
        
        # Calcola distribuzione qualità
        for category, scores in quality_by_category.items():
            avg_quality = statistics.mean(scores) if scores else 0.0
            report.quality_distribution[category] = avg_quality
        
        logger.info(f"   ✅ Quality validation completed")
        logger.info(f"      Total examples: {report.total_examples}")
        logger.info(f"      Validation rate: {report.validation_rate:.2%}")
        logger.info(f"      Overall quality: {report.overall_quality:.3f}")
    
    def _validate_tokenization_integrity(self, dataset_path: str, report: QAAuditReport):
        """Valida integrità tokenizzazione simboli."""
        logger.info(f"   Validating tokenization integrity...")
        
        # Estrai tutti i simboli unici dal dataset
        symbols_in_dataset = set()
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        example = json.loads(line.strip())
                        symbols_used = example.get('symbols_used', [])
                        symbols_in_dataset.update(symbols_used)
                        
                        # Estrai simboli anche dal testo simbolico
                        prompt_symbolic = example.get('prompt_symbolic', '')
                        response_symbolic = example.get('response_symbolic', '')
                        
                        # Cerca simboli nel testo (implementazione semplificata)
                        for symbol_data in self.symbol_registry:
                            symbol = symbol_data.get('symbol', '')
                            if symbol and (symbol in prompt_symbolic or symbol in response_symbolic):
                                symbols_in_dataset.add(symbol)
        
        except Exception as e:
            logger.error(f"Error extracting symbols: {e}")
        
        # Test tokenizzazione (simulato - in una implementazione reale useresti il tokenizer)
        report.symbols_tested = len(symbols_in_dataset)
        
        # Simula test zero-splitting
        zero_splitting_failures = []
        roundtrip_failures = []
        
        for symbol in list(symbols_in_dataset)[:100]:  # Test campione
            # Test zero-splitting (simulato)
            if self._test_zero_splitting(symbol):
                report.symbols_passed += 1
            else:
                zero_splitting_failures.append(symbol)
            
            # Test roundtrip fidelity (simulato)
            if not self._test_roundtrip_fidelity(symbol):
                roundtrip_failures.append(symbol)
        
        report.zero_splitting_rate = report.symbols_passed / max(report.symbols_tested, 1)
        report.roundtrip_fidelity = (report.symbols_tested - len(roundtrip_failures)) / max(report.symbols_tested, 1)
        report.tokenization_failures = zero_splitting_failures + roundtrip_failures
        
        logger.info(f"   ✅ Tokenization validation completed")
        logger.info(f"      Symbols tested: {report.symbols_tested}")
        logger.info(f"      Zero-splitting rate: {report.zero_splitting_rate:.2%}")
        logger.info(f"      Roundtrip fidelity: {report.roundtrip_fidelity:.2%}")
    
    def _test_zero_splitting(self, symbol: str) -> bool:
        """Test zero-splitting per simbolo (simulato)."""
        # In una implementazione reale, useresti il tokenizer NEUROGLYPH
        # Per ora, simula il test basandosi su criteri USU
        
        # Criteri USU: Unicode unique, Semantic atomic, ASCII fallback
        if len(symbol) == 1 and ord(symbol) > 127:  # Unicode symbol
            return True
        elif symbol.startswith('<NG_') and symbol.endswith('>'):  # ASCII fallback
            return True
        else:
            return len(symbol) <= 3  # Simboli corti probabilmente atomici
    
    def _test_roundtrip_fidelity(self, symbol: str) -> bool:
        """Test roundtrip fidelity per simbolo (simulato)."""
        # Simula: symbol → token → symbol
        # In implementazione reale: tokenizer.encode(symbol) → tokenizer.decode()
        
        # Per ora, assume che simboli validi abbiano fidelity perfetta
        return self._test_zero_splitting(symbol)
    
    def _validate_ast_structure(self, dataset_path: str, report: QAAuditReport):
        """Valida struttura AST degli esempi."""
        logger.info(f"   Validating AST structure...")
        
        ast_parseable_count = 0
        ast_roundtrip_success = 0
        structural_valid_count = 0
        total_tested = 0
        ast_failures = []
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip() and total_tested < 1000:  # Test campione
                        try:
                            example = json.loads(line.strip())
                            total_tested += 1
                            
                            # Test AST parsing
                            if self._test_ast_parsing(example):
                                ast_parseable_count += 1
                                
                                # Test AST roundtrip
                                if self._test_ast_roundtrip(example):
                                    ast_roundtrip_success += 1
                                
                                # Test structural validity
                                if self._test_structural_validity(example):
                                    structural_valid_count += 1
                            else:
                                ast_failures.append(f"Line {line_num}: AST parsing failed")
                                
                        except Exception as e:
                            ast_failures.append(f"Line {line_num}: {str(e)}")
        
        except Exception as e:
            logger.error(f"Error validating AST structure: {e}")
        
        if total_tested > 0:
            report.ast_parseable_rate = ast_parseable_count / total_tested
            report.ast_roundtrip_fidelity = ast_roundtrip_success / total_tested
            report.structural_validity_rate = structural_valid_count / total_tested
        
        report.ast_failures = ast_failures[:10]  # Top 10 failures
        
        logger.info(f"   ✅ AST validation completed")
        logger.info(f"      AST parseable rate: {report.ast_parseable_rate:.2%}")
        logger.info(f"      AST roundtrip fidelity: {report.ast_roundtrip_fidelity:.2%}")
        logger.info(f"      Structural validity rate: {report.structural_validity_rate:.2%}")
    
    def _test_ast_parsing(self, example: Dict) -> bool:
        """Test parsing AST di un esempio."""
        try:
            # Test parsing del prompt simbolico
            prompt_symbolic = example.get('prompt_symbolic', '')
            response_symbolic = example.get('response_symbolic', '')
            
            # Verifica che contenga simboli validi
            if '⊢' in prompt_symbolic or '⊢' in response_symbolic:
                return True
            
            # Verifica struttura logica base
            logical_symbols = ['∀', '∃', '⇒', '∧', '∨', '¬']
            has_logical_structure = any(sym in prompt_symbolic for sym in logical_symbols)
            
            return has_logical_structure
            
        except Exception:
            return False
    
    def _test_ast_roundtrip(self, example: Dict) -> bool:
        """Test roundtrip AST di un esempio."""
        try:
            # Simula: example → AST → reconstructed example
            # In implementazione reale useresti il parser AST NEUROGLYPH
            
            prompt = example.get('prompt_symbolic', '')
            response = example.get('response_symbolic', '')
            
            # Test semplificato: verifica che simboli critici siano preservati
            critical_symbols = ['⊢', '∀', '∃', '⇒', '∧', '∨']
            
            for symbol in critical_symbols:
                if symbol in prompt or symbol in response:
                    # Simula che il roundtrip preservi il simbolo
                    return True
            
            return True  # Default success per esempi senza simboli critici
            
        except Exception:
            return False
    
    def _test_structural_validity(self, example: Dict) -> bool:
        """Test validità strutturale di un esempio."""
        try:
            # Verifica completezza campi richiesti
            required_fields = ['id', 'category', 'prompt_symbolic', 'response_symbolic', 
                             'symbols_used', 'difficulty', 'reasoning_steps']
            
            for field in required_fields:
                if field not in example:
                    return False
            
            # Verifica coerenza simboli
            symbols_used = example.get('symbols_used', [])
            prompt_symbolic = example.get('prompt_symbolic', '')
            response_symbolic = example.get('response_symbolic', '')
            
            # Almeno un simbolo dichiarato deve essere presente nel testo
            symbols_found = any(symbol in prompt_symbolic or symbol in response_symbolic 
                              for symbol in symbols_used)
            
            return symbols_found
            
        except Exception:
            return False

    def _validate_excellence_compliance(self, dataset_path: str, report: QAAuditReport):
        """Valida compliance con criteri di eccellenza."""
        logger.info(f"   Validating excellence criteria compliance...")

        # Usa CoverageAnalyzer per validare copertura
        analyzer = CoverageAnalyzer(self.config)
        coverage_report = analyzer.analyze_dataset(dataset_path)

        # Verifica target di eccellenza
        target_symbols = self.config.get_total_symbol_targets()
        symbols_meeting_target = 0

        for symbol, target_count in target_symbols.items():
            actual_count = coverage_report.symbols_coverage.get(symbol, 0)
            if actual_count >= target_count * 0.8:  # 80% del target considerato sufficiente
                symbols_meeting_target += 1

        report.excellence_criteria_met = symbols_meeting_target >= len(target_symbols) * 0.9

        # Test anti-overfitting measures
        report.anti_overfitting_score = self._test_anti_overfitting_measures(dataset_path)

        # Valida innovazioni
        report.innovation_validation = self._validate_innovations(dataset_path)

        # Test split balance
        report.split_balance_score = self._test_split_balance(dataset_path)

        logger.info(f"   ✅ Excellence compliance completed")
        logger.info(f"      Excellence criteria met: {report.excellence_criteria_met}")
        logger.info(f"      Anti-overfitting score: {report.anti_overfitting_score:.3f}")
        logger.info(f"      Split balance score: {report.split_balance_score:.3f}")

    def _test_anti_overfitting_measures(self, dataset_path: str) -> float:
        """Test misure anti-overfitting."""
        try:
            # Analizza distribuzione difficoltà
            difficulty_counts = Counter()
            symbol_diversity = set()
            reasoning_steps = []

            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        example = json.loads(line.strip())

                        difficulty = example.get('difficulty', 'unknown')
                        difficulty_counts[difficulty] += 1

                        symbols_used = example.get('symbols_used', [])
                        symbol_diversity.update(symbols_used)

                        steps = example.get('reasoning_steps', 0)
                        reasoning_steps.append(steps)

            total_examples = sum(difficulty_counts.values())

            # Calcola score anti-overfitting
            scores = []

            # 1. Distribuzione difficoltà bilanciata (target: 30/50/20)
            if total_examples > 0:
                easy_ratio = difficulty_counts.get('easy', 0) / total_examples
                medium_ratio = difficulty_counts.get('medium', 0) / total_examples
                hard_ratio = difficulty_counts.get('hard', 0) / total_examples

                # Score basato su quanto è vicino al target
                difficulty_score = 1.0 - (abs(easy_ratio - 0.3) + abs(medium_ratio - 0.5) + abs(hard_ratio - 0.2)) / 2
                scores.append(max(0, difficulty_score))

            # 2. Diversità simbolica
            diversity_score = min(1.0, len(symbol_diversity) / 100)  # Target: 100+ simboli unici
            scores.append(diversity_score)

            # 3. Varianza reasoning steps
            if reasoning_steps:
                steps_variance = statistics.variance(reasoning_steps) if len(reasoning_steps) > 1 else 0
                variance_score = min(1.0, steps_variance / 2.0)  # Normalizza
                scores.append(variance_score)

            return statistics.mean(scores) if scores else 0.0

        except Exception as e:
            logger.error(f"Error testing anti-overfitting measures: {e}")
            return 0.0

    def _validate_innovations(self, dataset_path: str) -> Dict[str, bool]:
        """Valida le 4 innovazioni rivoluzionarie."""
        innovations = {
            'complex_combinations': False,
            'structure_symbols': False,
            'reverse_problems': False,
            'micro_stories': False
        }

        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        example = json.loads(line.strip())

                        category = example.get('category', '')
                        cognitive_tags = example.get('cognitive_tags', [])
                        metadata = example.get('metadata', {})

                        # Test complex combinations
                        if ('complex_reasoning' in category or
                            'complex_combinations' in cognitive_tags or
                            metadata.get('reasoning_type') == 'complex_combination'):
                            innovations['complex_combinations'] = True

                        # Test structure symbols
                        if ('set_theory' in category or
                            'structure_symbols' in cognitive_tags):
                            innovations['structure_symbols'] = True

                        # Test reverse problems
                        if ('reverse_reasoning' in category or
                            'reverse_generation' in cognitive_tags or
                            metadata.get('reasoning_type') == 'reverse_generative'):
                            innovations['reverse_problems'] = True

                        # Test micro-stories
                        if ('narrative_reasoning' in category or
                            'micro_stories' in cognitive_tags or
                            metadata.get('reasoning_type') == 'micro_story'):
                            innovations['micro_stories'] = True

        except Exception as e:
            logger.error(f"Error validating innovations: {e}")

        return innovations

    def _test_split_balance(self, dataset_path: str) -> float:
        """Test bilanciamento split train/val/test."""
        try:
            base_dir = Path(dataset_path).parent

            # Controlla esistenza file split
            train_path = base_dir / "neuroglyph_symbolic_train_final.jsonl"
            val_path = base_dir / "neuroglyph_symbolic_val_final.jsonl"
            test_path = base_dir / "neuroglyph_symbolic_test_final.jsonl"

            if not all(path.exists() for path in [train_path, val_path, test_path]):
                return 0.0

            # Conta esempi in ogni split
            train_count = sum(1 for line in open(train_path, 'r') if line.strip())
            val_count = sum(1 for line in open(val_path, 'r') if line.strip())
            test_count = sum(1 for line in open(test_path, 'r') if line.strip())

            total = train_count + val_count + test_count

            if total == 0:
                return 0.0

            # Calcola ratios
            train_ratio = train_count / total
            val_ratio = val_count / total
            test_ratio = test_count / total

            # Score basato su vicinanza al target 80/10/10
            balance_score = 1.0 - (abs(train_ratio - 0.8) + abs(val_ratio - 0.1) + abs(test_ratio - 0.1)) / 2

            return max(0, balance_score)

        except Exception as e:
            logger.error(f"Error testing split balance: {e}")
            return 0.0

    def _validate_benchmark_readiness(self, dataset_path: str, report: QAAuditReport):
        """Valida readiness per benchmark."""
        logger.info(f"   Validating benchmark readiness...")

        # Test format compatibility
        report.format_compatibility = self._test_format_compatibility(dataset_path)

        # Test metadata completeness
        report.metadata_completeness = self._test_metadata_completeness(dataset_path)

        # Test loading performance
        report.loading_performance_ms = self._test_loading_performance(dataset_path)

        # Calcola training readiness score
        readiness_factors = [
            report.format_compatibility,
            report.metadata_completeness >= 0.95,
            report.loading_performance_ms < 5000,  # < 5 secondi
            report.validation_rate >= 0.98,
            report.overall_quality >= 0.985
        ]

        report.training_readiness_score = sum(readiness_factors) / len(readiness_factors)

        logger.info(f"   ✅ Benchmark readiness completed")
        logger.info(f"      Format compatibility: {report.format_compatibility}")
        logger.info(f"      Metadata completeness: {report.metadata_completeness:.3f}")
        logger.info(f"      Loading performance: {report.loading_performance_ms:.1f}ms")
        logger.info(f"      Training readiness: {report.training_readiness_score:.3f}")

    def _test_format_compatibility(self, dataset_path: str) -> bool:
        """Test compatibilità formato per fine-tuning."""
        try:
            # Test che ogni esempio sia JSON valido con campi richiesti
            required_fields = ['id', 'category', 'prompt_symbolic', 'response_symbolic',
                             'prompt_natural', 'response_natural', 'symbols_used',
                             'difficulty', 'reasoning_steps', 'cognitive_tags']

            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            example = json.loads(line.strip())

                            # Verifica campi richiesti
                            for field in required_fields:
                                if field not in example:
                                    logger.warning(f"Missing field '{field}' in line {line_num}")
                                    return False

                            # Test primi 100 esempi
                            if line_num >= 100:
                                break

                        except json.JSONDecodeError:
                            logger.warning(f"Invalid JSON in line {line_num}")
                            return False

            return True

        except Exception as e:
            logger.error(f"Error testing format compatibility: {e}")
            return False

    def _test_metadata_completeness(self, dataset_path: str) -> float:
        """Test completezza metadata."""
        try:
            total_examples = 0
            complete_examples = 0

            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        total_examples += 1
                        example = json.loads(line.strip())

                        # Verifica completezza metadata
                        metadata = example.get('metadata', {})
                        cognitive_tags = example.get('cognitive_tags', [])

                        completeness_score = 0

                        # Metadata presente
                        if metadata:
                            completeness_score += 0.3

                        # Cognitive tags presenti
                        if cognitive_tags:
                            completeness_score += 0.3

                        # Reasoning steps validi
                        if example.get('reasoning_steps', 0) > 0:
                            completeness_score += 0.2

                        # Difficulty valida
                        if example.get('difficulty') in ['easy', 'medium', 'hard']:
                            completeness_score += 0.2

                        if completeness_score >= 0.8:
                            complete_examples += 1

            return complete_examples / max(total_examples, 1)

        except Exception as e:
            logger.error(f"Error testing metadata completeness: {e}")
            return 0.0

    def _test_loading_performance(self, dataset_path: str) -> float:
        """Test performance di caricamento."""
        try:
            import time

            start_time = time.time()

            # Carica primi 1000 esempi
            count = 0
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        json.loads(line.strip())
                        count += 1
                        if count >= 1000:
                            break

            end_time = time.time()
            loading_time_ms = (end_time - start_time) * 1000

            return loading_time_ms

        except Exception as e:
            logger.error(f"Error testing loading performance: {e}")
            return 10000.0  # Penalty per errore

    def _conduct_final_assessment(self, report: QAAuditReport):
        """Conduce assessment finale e determina se l'audit è passato."""
        logger.info(f"🎯 Conducting final assessment...")

        # Criteri critici per passare l'audit
        critical_criteria = [
            ("Validation Rate", report.validation_rate >= 0.98),
            ("Overall Quality", report.overall_quality >= 0.985),
            ("Symbol Coverage", report.symbol_coverage_percentage >= 99.0),
            ("AST Parseable Rate", report.ast_parseable_rate >= 0.95),
            ("Format Compatibility", report.format_compatibility),
            ("Training Readiness", report.training_readiness_score >= 0.8)
        ]

        # Criteri di eccellenza
        excellence_criteria = [
            ("Excellence Compliance", report.excellence_criteria_met),
            ("Anti-overfitting Score", report.anti_overfitting_score >= 0.8),
            ("Split Balance", report.split_balance_score >= 0.9),
            ("Metadata Completeness", report.metadata_completeness >= 0.95)
        ]

        # Valuta criteri critici
        critical_passed = 0
        for criterion, passed in critical_criteria:
            if passed:
                critical_passed += 1
            else:
                report.critical_issues.append(f"CRITICAL: {criterion} failed")

        # Valuta criteri di eccellenza
        excellence_passed = 0
        for criterion, passed in excellence_criteria:
            if passed:
                excellence_passed += 1
            else:
                report.recommendations.append(f"IMPROVEMENT: {criterion} could be enhanced")

        # Determina se audit è passato
        critical_pass_rate = critical_passed / len(critical_criteria)
        excellence_pass_rate = excellence_passed / len(excellence_criteria)

        # Audit passa se tutti i criteri critici sono soddisfatti
        report.audit_passed = critical_pass_rate >= 1.0

        # Aggiungi raccomandazioni basate sui risultati
        if report.audit_passed:
            if excellence_pass_rate >= 0.8:
                report.recommendations.append("EXCELLENT: Dataset ready for world-class fine-tuning")
            else:
                report.recommendations.append("GOOD: Dataset ready for fine-tuning with minor optimizations")
        else:
            report.recommendations.append("CRITICAL: Dataset requires fixes before fine-tuning")

        # Valida innovazioni
        innovations_passed = sum(report.innovation_validation.values())
        if innovations_passed >= 3:
            report.recommendations.append(f"INNOVATION: {innovations_passed}/4 revolutionary features validated")

        logger.info(f"   ✅ Final assessment completed")
        logger.info(f"      Critical criteria passed: {critical_passed}/{len(critical_criteria)}")
        logger.info(f"      Excellence criteria passed: {excellence_passed}/{len(excellence_criteria)}")
        logger.info(f"      Audit passed: {report.audit_passed}")

    def save_audit_report(self, report: QAAuditReport, output_path: str):
        """Salva report di audit."""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # Converti in dict per serializzazione
        report_dict = {
            'audit_summary': {
                'audit_passed': report.audit_passed,
                'audit_timestamp': report.audit_timestamp,
                'total_examples': report.total_examples
            },
            'dataset_quality': {
                'validation_rate': report.validation_rate,
                'overall_quality': report.overall_quality,
                'quality_distribution': report.quality_distribution
            },
            'symbol_coverage': {
                'coverage_percentage': report.symbol_coverage_percentage,
                'symbols_tested': report.symbols_tested,
                'symbols_passed': report.symbols_passed,
                'coverage_gaps': report.coverage_gaps
            },
            'tokenization_integrity': {
                'test_count': report.tokenization_test_count,
                'zero_splitting_rate': report.zero_splitting_rate,
                'roundtrip_fidelity': report.roundtrip_fidelity,
                'failures': report.tokenization_failures
            },
            'ast_validation': {
                'parseable_rate': report.ast_parseable_rate,
                'roundtrip_fidelity': report.ast_roundtrip_fidelity,
                'structural_validity_rate': report.structural_validity_rate,
                'failures': report.ast_failures
            },
            'excellence_compliance': {
                'criteria_met': report.excellence_criteria_met,
                'anti_overfitting_score': report.anti_overfitting_score,
                'innovation_validation': report.innovation_validation,
                'split_balance_score': report.split_balance_score
            },
            'benchmark_readiness': {
                'format_compatibility': report.format_compatibility,
                'metadata_completeness': report.metadata_completeness,
                'loading_performance_ms': report.loading_performance_ms,
                'training_readiness_score': report.training_readiness_score
            },
            'assessment': {
                'critical_issues': report.critical_issues,
                'recommendations': report.recommendations
            }
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Audit report saved: {output_file}")


def main():
    """Pipeline principale di audit completo."""
    print("🔍 NEUROGLYPH Comprehensive Quality Assurance Audit")
    print("=" * 80)
    print("Validating dataset across 5 critical dimensions:")
    print("1. Dataset Quality Validation")
    print("2. Symbol Tokenization Integrity")
    print("3. AST and Structural Validation")
    print("4. Excellence Criteria Compliance")
    print("5. Benchmark Readiness")

    # Dataset da auditare
    dataset_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"

    if not Path(dataset_path).exists():
        print(f"❌ Dataset not found: {dataset_path}")
        return

    # Conduce audit completo
    auditor = ComprehensiveQAAuditor()
    report = auditor.conduct_full_audit(dataset_path)

    # Salva report
    report_path = "data/audit/comprehensive_qa_audit_report.json"
    auditor.save_audit_report(report, report_path)

    # Stampa risultati finali
    print(f"\n🎯 COMPREHENSIVE QA AUDIT RESULTS")
    print("=" * 80)
    print(f"📊 Dataset: {dataset_path}")
    print(f"📈 Total Examples: {report.total_examples:,}")
    print(f"✅ Audit Passed: {report.audit_passed}")

    print(f"\n📊 QUALITY METRICS:")
    print(f"   Validation Rate: {report.validation_rate:.2%}")
    print(f"   Overall Quality: {report.overall_quality:.3f}")
    print(f"   Symbol Coverage: {report.symbol_coverage_percentage:.1f}%")

    print(f"\n🔤 TOKENIZATION INTEGRITY:")
    print(f"   Zero-splitting Rate: {report.zero_splitting_rate:.2%}")
    print(f"   Roundtrip Fidelity: {report.roundtrip_fidelity:.2%}")

    print(f"\n🌳 AST VALIDATION:")
    print(f"   AST Parseable Rate: {report.ast_parseable_rate:.2%}")
    print(f"   AST Roundtrip Fidelity: {report.ast_roundtrip_fidelity:.2%}")
    print(f"   Structural Validity: {report.structural_validity_rate:.2%}")

    print(f"\n⭐ EXCELLENCE COMPLIANCE:")
    print(f"   Excellence Criteria Met: {report.excellence_criteria_met}")
    print(f"   Anti-overfitting Score: {report.anti_overfitting_score:.3f}")
    print(f"   Split Balance Score: {report.split_balance_score:.3f}")

    print(f"\n🚀 BENCHMARK READINESS:")
    print(f"   Format Compatibility: {report.format_compatibility}")
    print(f"   Metadata Completeness: {report.metadata_completeness:.3f}")
    print(f"   Loading Performance: {report.loading_performance_ms:.1f}ms")
    print(f"   Training Readiness: {report.training_readiness_score:.3f}")

    print(f"\n🔮 INNOVATION VALIDATION:")
    for innovation, validated in report.innovation_validation.items():
        status = "✅" if validated else "❌"
        print(f"   {innovation}: {status}")

    if report.critical_issues:
        print(f"\n❌ CRITICAL ISSUES:")
        for issue in report.critical_issues:
            print(f"   - {issue}")

    if report.recommendations:
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in report.recommendations:
            print(f"   - {rec}")

    print(f"\n📄 Report saved: {report_path}")

    if report.audit_passed:
        print(f"\n🎉 AUDIT PASSED! Dataset ready for fine-tuning!")
    else:
        print(f"\n⚠️ AUDIT FAILED! Please address critical issues before proceeding.")


if __name__ == "__main__":
    main()
