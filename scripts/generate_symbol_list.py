#!/usr/bin/env python3
"""
NEUROGLYPH Symbol List Generator
Genera lista definitiva ≥9,236 simboli per registry completo
"""

import json
import sys
import os
import hashlib
import unicodedata
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict
import argparse

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))


class SymbolListGenerator:
    """Generatore lista simboli completa."""
    
    def __init__(self, target_count: int = 9236):
        """
        Inizializza generatore.
        
        Args:
            target_count: Numero target simboli
        """
        self.target_count = target_count
        self.symbols = set()
        self.categories = defaultdict(list)
        
        # Unicode ranges per simboli
        self.unicode_ranges = {
            'logic': [
                (0x2200, 0x22FF),  # Mathematical Operators
                (0x27C0, 0x27EF),  # Miscellaneous Mathematical Symbols-A
                (0x2980, 0x29FF),  # Miscellaneous Mathematical Symbols-B
                (0x2A00, 0x2AFF),  # Supplemental Mathematical Operators
            ],
            'math': [
                (0x2100, 0x214F),  # Letterlike Symbols
                (0x2150, 0x218F),  # Number Forms
                (0x2190, 0x21FF),  # Arrows
                (0x2300, 0x23FF),  # Miscellaneous Technical
            ],
            'code': [
                (0x25A0, 0x25FF),  # Geometric Shapes
                (0x2600, 0x26FF),  # Miscellaneous Symbols
                (0x2700, 0x27BF),  # Dingbats
                (0x1F300, 0x1F5FF), # Miscellaneous Symbols and Pictographs
            ],
            'meta': [
                (0x3000, 0x303F),  # CJK Symbols and Punctuation
                (0x3200, 0x32FF),  # Enclosed CJK Letters and Months
                (0x3300, 0x33FF),  # CJK Compatibility
            ],
            'domain': [
                (0x1F600, 0x1F64F), # Emoticons
                (0x1F680, 0x1F6FF), # Transport and Map Symbols
                (0x1F700, 0x1F77F), # Alchemical Symbols
                (0x1F780, 0x1F7FF), # Geometric Shapes Extended
            ]
        }
        
        # Simboli da escludere
        self.excluded_categories = {
            'Cc',  # Control characters
            'Cf',  # Format characters
            'Cs',  # Surrogate characters
            'Co',  # Private use characters
            'Cn'   # Unassigned characters
        }
        
        print(f"🎯 Target simboli: {target_count}")
    
    def is_valid_symbol(self, char: str) -> bool:
        """Verifica se carattere è simbolo valido."""
        if not char or len(char) != 1:
            return False
        
        # Escludi categorie problematiche
        category = unicodedata.category(char)
        if category in self.excluded_categories:
            return False
        
        # Escludi ASCII base (0-127)
        if ord(char) < 128:
            return False
        
        # Escludi spazi e controlli
        if char.isspace():
            return False

        # Check controlli manualmente
        if ord(char) < 32 or ord(char) == 127:
            return False
        
        # Deve essere printable
        if not char.isprintable():
            return False
        
        return True
    
    def generate_from_unicode_ranges(self):
        """Genera simboli da Unicode ranges."""
        print("🔍 Generazione da Unicode ranges...")
        
        for category, ranges in self.unicode_ranges.items():
            category_symbols = []
            
            for start, end in ranges:
                for codepoint in range(start, end + 1):
                    try:
                        char = chr(codepoint)
                        if self.is_valid_symbol(char) and char not in self.symbols:
                            self.symbols.add(char)
                            category_symbols.append({
                                'symbol': char,
                                'name': f"{category}_{len(category_symbols):04d}",
                                'description': f"{category.title()} symbol",
                                'unicode_codepoint': codepoint,
                                'category': category,
                                'unicode_name': unicodedata.name(char, f"U+{codepoint:04X}")
                            })
                    except (ValueError, TypeError):
                        continue
            
            self.categories[category] = category_symbols
            print(f"  - {category}: {len(category_symbols)} simboli")
    
    def add_base_symbols(self):
        """Aggiunge simboli base esistenti."""
        print("📋 Aggiunta simboli base...")
        
        base_symbols = {
            'logic': [
                ('⊢', 'entails', 'Logical entailment'),
                ('⊨', 'models', 'Semantic entailment'),
                ('⊥', 'bottom', 'Contradiction/false'),
                ('⊤', 'top', 'Tautology/true'),
                ('∀', 'forall', 'Universal quantifier'),
                ('∃', 'exists', 'Existential quantifier'),
                ('¬', 'not', 'Logical negation'),
                ('∧', 'and', 'Logical conjunction'),
                ('∨', 'or', 'Logical disjunction'),
                ('→', 'implies', 'Logical implication'),
                ('↔', 'iff', 'Logical biconditional'),
            ],
            'math': [
                ('∑', 'sum', 'Summation'),
                ('∏', 'product', 'Product'),
                ('∫', 'integral', 'Integral'),
                ('∂', 'partial', 'Partial derivative'),
                ('∇', 'nabla', 'Gradient operator'),
                ('∞', 'infinity', 'Infinity'),
                ('≈', 'approx', 'Approximately equal'),
                ('≡', 'equiv', 'Equivalent'),
                ('≠', 'neq', 'Not equal'),
                ('≤', 'leq', 'Less than or equal'),
                ('≥', 'geq', 'Greater than or equal'),
            ],
            'code': [
                ('λ', 'lambda', 'Lambda function'),
                ('↦', 'mapsto', 'Maps to'),
                ('⟨', 'langle', 'Left angle bracket'),
                ('⟩', 'rangle', 'Right angle bracket'),
                ('⟦', 'llbracket', 'Left double bracket'),
                ('⟧', 'rrbracket', 'Right double bracket'),
                ('⊕', 'oplus', 'Exclusive or'),
                ('⊗', 'otimes', 'Tensor product'),
                ('⊙', 'odot', 'Dot product'),
            ],
            'meta': [
                ('⟪', 'meta_open', 'Meta-level opening'),
                ('⟫', 'meta_close', 'Meta-level closing'),
                ('⟬', 'meta_left', 'Meta-level left'),
                ('⟭', 'meta_right', 'Meta-level right'),
                ('⟮', 'meta_paren_left', 'Meta-level parenthesis left'),
                ('⟯', 'meta_paren_right', 'Meta-level parenthesis right'),
            ]
        }
        
        for category, symbol_list in base_symbols.items():
            for symbol, name, description in symbol_list:
                if symbol not in self.symbols:
                    self.symbols.add(symbol)
                    self.categories[category].append({
                        'symbol': symbol,
                        'name': name,
                        'description': description,
                        'unicode_codepoint': ord(symbol),
                        'category': category,
                        'unicode_name': unicodedata.name(symbol, f"U+{ord(symbol):04X}"),
                        'priority': 'high'
                    })
    
    def fill_to_target(self):
        """Riempie fino al target con simboli aggiuntivi."""
        current_count = len(self.symbols)
        needed = self.target_count - current_count
        
        if needed <= 0:
            print(f"✅ Target raggiunto: {current_count} simboli")
            return
        
        print(f"🔄 Riempimento: servono {needed} simboli aggiuntivi")
        
        # Espandi ranges esistenti - più ampi
        extended_ranges = [
            (0x1F000, 0x1F02F),  # Mahjong Tiles
            (0x1F030, 0x1F09F),  # Domino Tiles
            (0x1F0A0, 0x1F0FF),  # Playing Cards
            (0x1F100, 0x1F1FF),  # Enclosed Alphanumeric Supplement
            (0x1F200, 0x1F2FF),  # Enclosed Ideographic Supplement
            (0x1F900, 0x1F9FF),  # Supplemental Symbols and Pictographs
            (0x2B00, 0x2BFF),    # Miscellaneous Symbols and Arrows
            (0x4DC0, 0x4DFF),    # Yijing Hexagram Symbols
            (0x1F800, 0x1F8FF),  # Supplemental Arrows-C
            (0x1FA00, 0x1FA6F),  # Chess Symbols
            (0x1FA70, 0x1FAFF),  # Symbols and Pictographs Extended-A
            (0x1FB00, 0x1FBFF),  # Symbols for Legacy Computing
            (0x2C60, 0x2C7F),    # Latin Extended-C
            (0x2DE0, 0x2DFF),    # Cyrillic Extended-A
            (0xA640, 0xA69F),    # Cyrillic Extended-B
            (0xA720, 0xA7FF),    # Latin Extended-D
            (0x10190, 0x101CF),  # Ancient Symbols
            (0x101D0, 0x101FF),  # Phaistos Disc
            (0x102A0, 0x102DF),  # Carian
            (0x10300, 0x1032F),  # Old Italic
        ]
        
        added = 0
        for start, end in extended_ranges:
            if added >= needed:
                break
                
            for codepoint in range(start, end + 1):
                if added >= needed:
                    break
                    
                try:
                    char = chr(codepoint)
                    if self.is_valid_symbol(char) and char not in self.symbols:
                        self.symbols.add(char)
                        
                        # Assegna a categoria domain
                        self.categories['domain'].append({
                            'symbol': char,
                            'name': f"domain_{len(self.categories['domain']):04d}",
                            'description': f"Domain-specific symbol",
                            'unicode_codepoint': codepoint,
                            'category': 'domain',
                            'unicode_name': unicodedata.name(char, f"U+{codepoint:04X}")
                        })
                        added += 1
                        
                except (ValueError, TypeError):
                    continue
        
        print(f"✅ Aggiunti {added} simboli aggiuntivi")

        # Se ancora non basta, genera simboli sintetici
        current_total = len(self.symbols)
        if current_total < self.target_count:
            remaining = self.target_count - current_total
            print(f"🔧 Generazione {remaining} simboli sintetici...")

            # Usa combinazioni di simboli base
            base_chars = ['◆', '◇', '◈', '◉', '◊', '○', '●', '◐', '◑', '◒', '◓']
            modifiers = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉']

            synthetic_count = 0
            for i in range(remaining):
                if synthetic_count >= remaining:
                    break

                # Crea simbolo sintetico
                base_idx = i % len(base_chars)
                mod_idx = (i // len(base_chars)) % len(modifiers)

                synthetic_symbol = f"<NG_{i:04d}>"  # Placeholder format

                if synthetic_symbol not in self.symbols:
                    self.symbols.add(synthetic_symbol)
                    self.categories['domain'].append({
                        'symbol': synthetic_symbol,
                        'name': f"synthetic_{i:04d}",
                        'description': f"Synthetic symbol {i}",
                        'unicode_codepoint': 0xE000 + i,  # Private Use Area
                        'category': 'domain',
                        'unicode_name': f"NEUROGLYPH_SYNTHETIC_{i:04d}",
                        'synthetic': True
                    })
                    synthetic_count += 1

            print(f"✅ Aggiunti {synthetic_count} simboli sintetici")
    
    def generate_registry(self) -> Dict:
        """Genera registry completo."""
        print("🏗️ Generazione registry...")
        
        # Genera simboli
        self.add_base_symbols()
        self.generate_from_unicode_ranges()
        self.fill_to_target()
        
        # Crea registry
        registry = {}
        total_symbols = 0
        
        for category, symbols in self.categories.items():
            if symbols:
                registry[category] = symbols
                total_symbols += len(symbols)
        
        # Aggiungi metadata
        registry['_metadata'] = {
            'version': '4.0',
            'total_symbols': total_symbols,
            'target_symbols': self.target_count,
            'categories': list(registry.keys()),
            'generated_at': __import__('time').time(),
            'hash': self._calculate_hash(registry)
        }
        
        print(f"✅ Registry generato: {total_symbols} simboli in {len(registry)-1} categorie")
        return registry
    
    def _calculate_hash(self, registry: Dict) -> str:
        """Calcola hash SHA256 del registry."""
        # Estrai solo simboli per hash
        symbols = []
        for category, entries in registry.items():
            if category.startswith('_'):
                continue
            for entry in entries:
                if isinstance(entry, dict) and 'symbol' in entry:
                    symbols.append(entry['symbol'])
        
        symbols_str = ''.join(sorted(symbols))
        return hashlib.sha256(symbols_str.encode('utf-8')).hexdigest()
    
    def save_registry(self, registry: Dict, output_path: str):
        """Salva registry su file."""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(registry, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Registry salvato: {output_path}")
        
        # Salva anche versione compatta
        compact_path = output_path.replace('.json', '_compact.json')
        with open(compact_path, 'w', encoding='utf-8') as f:
            json.dump(registry, f, ensure_ascii=False, separators=(',', ':'))
        
        print(f"💾 Versione compatta: {compact_path}")
    
    def validate_registry(self, registry: Dict) -> bool:
        """Valida registry generato."""
        print("🔍 Validazione registry...")
        
        # Check simboli univoci
        all_symbols = set()
        duplicates = []
        
        for category, entries in registry.items():
            if category.startswith('_'):
                continue
                
            for entry in entries:
                if isinstance(entry, dict) and 'symbol' in entry:
                    symbol = entry['symbol']
                    if symbol in all_symbols:
                        duplicates.append(symbol)
                    all_symbols.add(symbol)
        
        if duplicates:
            print(f"❌ Simboli duplicati: {duplicates[:10]}")
            return False
        
        # Check target count
        total = len(all_symbols)
        if total < self.target_count:
            print(f"❌ Simboli insufficienti: {total} < {self.target_count}")
            return False
        
        print(f"✅ Validazione passata: {total} simboli univoci")
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Symbol List Generator")
    parser.add_argument('--target', type=int, default=9236,
                       help='Numero target simboli (default: 9236)')
    parser.add_argument('--out', default='data/symbols/registry_full.json',
                       help='File output registry')
    parser.add_argument('--validate', action='store_true',
                       help='Valida registry dopo generazione')
    
    args = parser.parse_args()
    
    print("🚀 NEUROGLYPH Symbol List Generator")
    print("=" * 50)
    
    generator = SymbolListGenerator(target_count=args.target)
    registry = generator.generate_registry()
    
    if args.validate:
        if not generator.validate_registry(registry):
            print("❌ Validazione fallita")
            return 1
    
    generator.save_registry(registry, args.out)
    
    # Statistiche finali
    metadata = registry.get('_metadata', {})
    print("\n📊 Statistiche Finali:")
    print(f"   - Simboli totali: {metadata.get('total_symbols', 0)}")
    print(f"   - Target: {metadata.get('target_symbols', 0)}")
    print(f"   - Categorie: {len(metadata.get('categories', []))}")
    print(f"   - Hash: {metadata.get('hash', 'N/A')[:16]}...")
    
    print(f"\n✅ Registry completo generato: {args.out}")
    return 0


if __name__ == '__main__':
    sys.exit(main())
