#!/usr/bin/env python3
"""
NEUROGLYPH Practical AST Roundtrip Test
Basato sul tuo approccio rigoroso ma pratico

Test: code → AST → code → AST (confronto normalizzato)
Features: normalizzazione deterministica, fuzzy matching, sampling stratificato
"""

import ast
import json
import argparse
import random
import difflib
from pathlib import Path
from typing import Dict, List, Any

# Try to import Levenshtein, fallback to difflib if not available
try:
    from Levenshtein import ratio as levenshtein_ratio
    HAS_LEVENSHTEIN = True
except ImportError:
    HAS_LEVENSHTEIN = False
    print("⚠️ Levenshtein not available, using difflib fallback")


def normalize_ast(node: ast.AST) -> ast.AST:
    """
    Pulisce AST da attributi non semantici e ordina keyword/args.
    """
    for field, value in ast.iter_fields(node):
        # Rimuovi attributi di posizione (non semantici)
        if field in ('lineno', 'col_offset', 'end_lineno', 'end_col_offset'):
            setattr(node, field, None)
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, ast.AST):
                    normalize_ast(item)
        elif isinstance(value, ast.AST):
            normalize_ast(value)
    
    # Ordina keyword e args per determinismo
    if isinstance(node, ast.Call):
        node.keywords = sorted(node.keywords, key=lambda kw: kw.arg or "")
    elif isinstance(node, ast.FunctionDef):
        # Ordina decorators se presenti
        if hasattr(node, 'decorator_list'):
            node.decorator_list = sorted(node.decorator_list, 
                                       key=lambda d: ast.dump(d) if isinstance(d, ast.AST) else str(d))
    
    return node


def ast_to_normalized_str(code: str) -> str:
    """
    Parse/unparse e dump normalizzato.
    """
    try:
        # Parse → normalize → dump
        tree = ast.parse(code)
        normalize_ast(tree)
        return ast.dump(tree, include_attributes=False)
    except SyntaxError as e:
        raise ValueError(f"Invalid Python syntax: {e}")


def calculate_similarity(str1: str, str2: str) -> float:
    """
    Calcola similarità tra due stringhe.
    Usa Levenshtein se disponibile, altrimenti difflib.
    """
    if HAS_LEVENSHTEIN:
        return levenshtein_ratio(str1, str2)
    else:
        # Fallback usando difflib
        matcher = difflib.SequenceMatcher(None, str1, str2)
        return matcher.ratio()


def convert_neuroglyph_to_python(neuroglyph_code: str) -> str:
    """
    Converte codice NEUROGLYPH in Python valido.
    Mapping semplificato per test pratico.
    """
    # Mapping base per simboli comuni
    symbol_mapping = {
        '⊢': 'proves',
        '∀': 'forall',
        '∃': 'exists',
        '⇒': 'implies',
        '∧': 'and_logic',
        '∨': 'or_logic',
        '¬': 'not_logic',
        '⊥': 'contradiction',
        '≡': 'equivalent',
        '⇔': 'iff',
        '∴': 'therefore',
        '∈': 'element_of',
        '⊂': 'subset',
        '⊇': 'superset',
        '∅': 'emptyset',
        '🧠': 'brain',
        '🔮': 'predict',
        '📖': 'story'
    }
    
    python_code = neuroglyph_code
    
    # Applica mapping
    for symbol, replacement in symbol_mapping.items():
        python_code = python_code.replace(symbol, replacement)
    
    # Pulizia base
    python_code = ''.join(c for c in python_code if c.isprintable())
    python_code = python_code.replace(':', ' COLON ')
    python_code = python_code.replace('?', ' QUESTION ')
    
    # Se vuoto, crea espressione valida
    if not python_code.strip():
        python_code = 'None'
    
    # Test se è Python valido
    try:
        ast.parse(python_code)
        return python_code
    except SyntaxError:
        # Se non valido, wrappa come stringa (ultimo resort)
        return f'"{neuroglyph_code}"'


def test_ast_roundtrip(item: Dict[str, Any], field: str, threshold: float) -> Dict[str, Any]:
    """
    Test AST roundtrip su singolo item.
    """
    result = {
        'id': item.get('id', 'unknown'),
        'passed': False,
        'similarity_score': 0.0,
        'error': None,
        'diff': None
    }
    
    try:
        # Estrai codice
        original_code = item.get(field, '')
        
        # Se è NEUROGLYPH, converti in Python
        if field in ['prompt_symbolic', 'response_symbolic']:
            python_code = convert_neuroglyph_to_python(original_code)
        else:
            python_code = original_code
        
        # Test roundtrip: code → AST → code → AST
        original_ast_str = ast_to_normalized_str(python_code)
        
        # Roundtrip: AST → code → AST
        intermediate_tree = ast.parse(python_code)
        reconstructed_code = ast.unparse(intermediate_tree)
        reconstructed_ast_str = ast_to_normalized_str(reconstructed_code)
        
        # Confronto
        if original_ast_str == reconstructed_ast_str:
            result['passed'] = True
            result['similarity_score'] = 1.0
        else:
            # Fuzzy matching
            similarity = calculate_similarity(original_ast_str, reconstructed_ast_str)
            result['similarity_score'] = similarity
            
            if similarity >= threshold:
                result['passed'] = True
            else:
                # Genera diff per debugging
                diff_lines = list(difflib.unified_diff(
                    original_ast_str.splitlines(),
                    reconstructed_ast_str.splitlines(),
                    lineterm="",
                    fromfile="original_ast",
                    tofile="reconstructed_ast"
                ))
                result['diff'] = '\n'.join(diff_lines)
        
    except Exception as e:
        result['error'] = str(e)
    
    return result


def stratified_sample(data: List[Dict], sample_size: int) -> List[Dict]:
    """
    Sampling stratificato per difficulty.
    """
    if len(data) <= sample_size:
        return data
    
    # Raggruppa per difficulty
    by_difficulty = {}
    for item in data:
        difficulty = item.get('difficulty', 'medium')
        by_difficulty.setdefault(difficulty, []).append(item)
    
    # Campiona proporzionalmente
    sampled = []
    samples_per_difficulty = sample_size // len(by_difficulty)
    remainder = sample_size % len(by_difficulty)
    
    for i, (difficulty, items) in enumerate(by_difficulty.items()):
        # Aggiungi remainder ai primi gruppi
        current_sample_size = samples_per_difficulty + (1 if i < remainder else 0)
        
        if len(items) <= current_sample_size:
            sampled.extend(items)
        else:
            sampled.extend(random.sample(items, current_sample_size))
    
    return sampled


def main():
    """Pipeline principale del test pratico."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Practical AST Roundtrip Test")
    parser.add_argument("--dataset", type=Path, required=True,
                       help="Path to JSONL dataset")
    parser.add_argument("--field", default="response_symbolic",
                       help="Field to test (default: response_symbolic)")
    parser.add_argument("--threshold", type=float, default=0.99,
                       help="Min AST-equivalence ratio (0-1, default: 0.99)")
    parser.add_argument("--sample", type=int, default=None,
                       help="Number of examples to test (default: all)")
    parser.add_argument("--output", type=Path, default="ast_roundtrip_failures.jsonl",
                       help="Output file for failures")
    
    args = parser.parse_args()
    
    print("🔬 NEUROGLYPH Practical AST Roundtrip Test")
    print("=" * 60)
    print(f"Dataset: {args.dataset}")
    print(f"Field: {args.field}")
    print(f"Threshold: {args.threshold}")
    print(f"Levenshtein available: {HAS_LEVENSHTEIN}")
    
    # Carica dataset
    try:
        with open(args.dataset, 'r', encoding='utf-8') as f:
            data = [json.loads(line) for line in f if line.strip()]
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return 1
    
    print(f"Loaded: {len(data)} examples")
    
    # Sampling stratificato se richiesto
    if args.sample and args.sample < len(data):
        data = stratified_sample(data, args.sample)
        print(f"Stratified sample: {len(data)} examples")
    
    # Test roundtrip
    print(f"\n🧪 Testing AST roundtrip...")
    
    total = len(data)
    passed = 0
    failures = []
    
    for i, item in enumerate(data):
        result = test_ast_roundtrip(item, args.field, args.threshold)
        
        if result['passed']:
            passed += 1
        else:
            failures.append(result)
        
        # Progress
        if (i + 1) % 100 == 0:
            print(f"   Processed: {i + 1}/{total}")
    
    # Risultati
    pass_rate = passed / total if total > 0 else 0
    
    print(f"\n🎯 RESULTS")
    print("=" * 60)
    print(f"Total tested: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {len(failures)}")
    print(f"Pass rate: {pass_rate:.2%}")
    print(f"Threshold: {args.threshold}")
    
    # Salva failures se presenti
    if failures:
        with open(args.output, 'w', encoding='utf-8') as f:
            for failure in failures:
                f.write(json.dumps(failure, ensure_ascii=False) + '\n')
        
        print(f"\n❌ FAILURES DETECTED")
        print(f"Failure details saved to: {args.output}")
        
        # Mostra sample failures
        print(f"\nSample failures:")
        for failure in failures[:3]:
            print(f"  ID: {failure['id']}")
            print(f"  Similarity: {failure['similarity_score']:.3f}")
            if failure.get('error'):
                print(f"  Error: {failure['error']}")
            print()
        
        return 1
    else:
        print(f"\n✅ AST ROUNDTRIP VERIFICATION PASSED")
        print(f"All {total} examples passed with threshold {args.threshold}")
        return 0


if __name__ == "__main__":
    exit(main())
