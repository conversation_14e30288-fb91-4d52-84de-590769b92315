#!/usr/bin/env python3
"""
NEUROGLYPH Missing Templates & Contexts Generator
Genera template semantici e contesti prompt per simboli mancanti
"""

import json
import os
import sys
import unicodedata
from pathlib import Path
from typing import Dict, List, Any, Set
import argparse

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))


class MissingTemplatesContextsGenerator:
    """Generatore template e contesti mancanti."""
    
    def __init__(self):
        """Inizializza generatore."""
        self.all_symbols = []
        self.existing_templates = {}
        self.existing_contexts = {}
        
        # Template base per dominio
        self.domain_template_patterns = {
            'logic': 'logical_operator_{code}(left, right)',
            'math': 'mathematical_function_{code}(input)',
            'code': 'code_construct_{code}()',
            'advanced_coding': 'advanced_operation_{code}(args)',
            'ai': 'ai_concept_{code}(context)',
            'cognition': 'cognitive_process_{code}(state)',
            'meta': 'meta_operation_{code}(object)',
            'operator': 'operator_{code}(operands)',
            'structure': 'structural_element_{code}(components)',
            'arrows': 'arrow_relation_{code}(from, to)',
            'geometry': 'geometric_element_{code}(properties)',
            'algebra': 'algebraic_operation_{code}(variables)',
            'analysis': 'analytical_function_{code}(domain)',
            'calculus': 'calculus_operator_{code}(function)',
            'category_theory': 'categorical_morphism_{code}(source, target)',
            'combinatorics': 'combinatorial_function_{code}(set)',
            'concurrency_advanced': 'concurrent_operation_{code}(threads)',
            'control_flow': 'control_structure_{code}(condition)',
            'creative_thinking': 'creative_process_{code}(input)',
            'distributed': 'distributed_operation_{code}(nodes)',
            'distributed_systems': 'distributed_protocol_{code}(network)',
            'extension': 'extension_operator_{code}(base)',
            'formal_verification': 'verification_predicate_{code}(property)',
            'machine_learning': 'ml_algorithm_{code}(data)',
            'mathematical_operators': 'math_operator_{code}(operands)',
            'mathematical_structures': 'math_structure_{code}(elements)',
            'memory': 'memory_operation_{code}(data)',
            'meta_programming': 'metaprog_construct_{code}(code)',
            'neural_architectures': 'neural_component_{code}(inputs)',
            'performance': 'performance_metric_{code}(system)',
            'philosophical_concepts': 'philosophical_concept_{code}(context)',
            'physics_notation': 'physics_quantity_{code}(units)',
            'problem_solving': 'problem_solving_method_{code}(problem)',
            'quantum': 'quantum_operator_{code}(state)',
            'quantum_computing': 'quantum_gate_{code}(qubits)',
            'reasoning': 'reasoning_step_{code}(premises)',
            'security': 'security_protocol_{code}(data)',
            'self': 'self_reference_{code}(object)',
            'set_theory': 'set_operation_{code}(sets)',
            'statistics': 'statistical_measure_{code}(data)',
            'symbolic_ai': 'symbolic_operation_{code}(symbols)',
            'technical_symbols': 'technical_notation_{code}(context)',
            'temporal': 'temporal_relation_{code}(time)',
            'topology': 'topological_property_{code}(space)',
            'type_theory': 'type_constructor_{code}(types)'
        }
        
        # Contesti base per dominio
        self.domain_context_patterns = {
            'logic': 'Operatore logico utilizzato per esprimere relazioni formali e ragionamento deduttivo nel dominio {domain}',
            'math': 'Simbolo matematico per operazioni numeriche, algebriche o analitiche nel calcolo e nell\'analisi matematica',
            'code': 'Simbolo di programmazione per costrutti, operatori e strutture dati nel codice sorgente',
            'advanced_coding': 'Operatore avanzato per programmazione di alto livello, meta-programmazione e ottimizzazioni',
            'ai': 'Simbolo per concetti di intelligenza artificiale, apprendimento automatico e ragionamento computazionale',
            'cognition': 'Rappresentazione di processi cognitivi, modelli mentali e architetture cognitive',
            'meta': 'Operatore meta-linguistico per riferimenti di livello superiore e auto-referenza',
            'operator': 'Operatore generico per trasformazioni, manipolazioni e operazioni su strutture dati',
            'structure': 'Elemento strutturale per organizzazione, composizione e architettura di sistemi complessi',
            'arrows': 'Simbolo di freccia per indicare direzioni, relazioni e trasformazioni tra elementi',
            'geometry': 'Elemento geometrico per forme, spazi e relazioni spaziali nella geometria',
            'algebra': 'Operatore algebrico per manipolazioni simboliche e strutture algebriche',
            'analysis': 'Funzione analitica per studio di limiti, continuità e proprietà analitiche',
            'calculus': 'Operatore del calcolo differenziale e integrale per derivate e integrali',
            'category_theory': 'Morfismo categoriale per relazioni strutturali tra oggetti matematici',
            'combinatorics': 'Funzione combinatoria per conteggi, permutazioni e strutture discrete',
            'concurrency_advanced': 'Operazione di concorrenza avanzata per sistemi paralleli e distribuiti',
            'control_flow': 'Struttura di controllo per flusso di esecuzione e logica condizionale',
            'creative_thinking': 'Processo creativo per generazione di idee e soluzioni innovative',
            'distributed': 'Operazione distribuita per sistemi decentralizzati e reti di calcolo',
            'distributed_systems': 'Protocollo per sistemi distribuiti, comunicazione e coordinamento',
            'extension': 'Operatore di estensione per ampliamento di funzionalità e capacità',
            'formal_verification': 'Predicato di verifica formale per validazione e correttezza',
            'machine_learning': 'Algoritmo di apprendimento automatico per pattern recognition e predizione',
            'mathematical_operators': 'Operatore matematico standard per calcoli e manipolazioni numeriche',
            'mathematical_structures': 'Struttura matematica per organizzazione e relazioni tra elementi',
            'memory': 'Operazione di memoria per storage, retrieval e gestione dati',
            'meta_programming': 'Costrutto di meta-programmazione per generazione e manipolazione codice',
            'neural_architectures': 'Componente di architettura neurale per reti neurali e deep learning',
            'performance': 'Metrica di performance per misurazione e ottimizzazione sistemi',
            'philosophical_concepts': 'Concetto filosofico per ragionamento astratto e riflessione teorica',
            'physics_notation': 'Notazione fisica per quantità, unità e leggi della fisica',
            'problem_solving': 'Metodo di problem solving per risoluzione sistematica di problemi',
            'quantum': 'Operatore quantistico per meccanica quantistica e computazione quantistica',
            'quantum_computing': 'Gate quantistico per circuiti e algoritmi di quantum computing',
            'reasoning': 'Step di ragionamento per inferenza logica e deduzione',
            'security': 'Protocollo di sicurezza per protezione dati e comunicazioni sicure',
            'self': 'Riferimento auto-referenziale per meta-cognizione e auto-consapevolezza',
            'set_theory': 'Operazione su insiemi per teoria degli insiemi e logica matematica',
            'statistics': 'Misura statistica per analisi dati e inferenza statistica',
            'symbolic_ai': 'Operazione simbolica per intelligenza artificiale simbolica e rappresentazione conoscenza',
            'technical_symbols': 'Notazione tecnica specializzata per domini specifici e applicazioni tecniche',
            'temporal': 'Relazione temporale per sequenze, durate e logica temporale',
            'topology': 'Proprietà topologica per spazi, continuità e trasformazioni topologiche',
            'type_theory': 'Costruttore di tipi per sistemi di tipi e teoria dei tipi'
        }
    
    def load_existing_data(self):
        """Carica dati esistenti."""
        print("🔍 Caricamento dati esistenti...")
        
        # Carica simboli
        fixtures_path = Path("tests/fixtures/fixtures_list.json")
        if fixtures_path.exists():
            with open(fixtures_path, 'r', encoding='utf-8') as f:
                fixtures = json.load(f)
            
            for domain, symbols in fixtures['symbols_by_domain'].items():
                for symbol in symbols:
                    self.all_symbols.append({
                        'symbol': symbol,
                        'domain': domain
                    })
        
        # Carica template esistenti
        templates_path = Path("data/symbols/semantic_templates.json")
        if templates_path.exists():
            with open(templates_path, 'r', encoding='utf-8') as f:
                self.existing_templates = json.load(f)
        
        # Carica contesti esistenti
        contexts_path = Path("data/symbols/prompt_contexts.json")
        if contexts_path.exists():
            with open(contexts_path, 'r', encoding='utf-8') as f:
                self.existing_contexts = json.load(f)
        
        print(f"✅ Caricati {len(self.all_symbols)} simboli")
        print(f"✅ Template esistenti: {len(self.existing_templates)}")
        print(f"✅ Contesti esistenti: {len(self.existing_contexts)}")
    
    def generate_missing_templates(self) -> Dict[str, str]:
        """Genera template mancanti."""
        print("🔧 Generazione template mancanti...")
        
        new_templates = {}
        existing_symbols = set(self.existing_templates.keys())
        
        for symbol_data in self.all_symbols:
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            if symbol not in existing_symbols:
                # Genera template per dominio
                pattern = self.domain_template_patterns.get(domain, 'symbol_function_{code}()')
                code = ord(symbol)
                template = pattern.format(code=code)
                
                new_templates[symbol] = template
        
        print(f"✅ Generati {len(new_templates)} template nuovi")
        return new_templates
    
    def generate_missing_contexts(self) -> Dict[str, str]:
        """Genera contesti mancanti."""
        print("📝 Generazione contesti mancanti...")
        
        new_contexts = {}
        existing_symbols = set(self.existing_contexts.keys())
        
        for symbol_data in self.all_symbols:
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            if symbol not in existing_symbols:
                # Genera contesto per dominio
                pattern = self.domain_context_patterns.get(domain, 
                    'Simbolo specializzato per il dominio {domain} utilizzato in contesti specifici')
                
                # Aggiungi info Unicode
                try:
                    unicode_name = unicodedata.name(symbol, f"U+{ord(symbol):04X}")
                    unicode_info = f" (Unicode: {unicode_name})"
                except ValueError:
                    unicode_info = f" (Unicode: U+{ord(symbol):04X})"
                
                context = pattern.format(domain=domain) + unicode_info
                new_contexts[symbol] = context
        
        print(f"✅ Generati {len(new_contexts)} contesti nuovi")
        return new_contexts
    
    def merge_and_save_templates(self, new_templates: Dict[str, str]):
        """Merge e salva template."""
        print("💾 Merge e salvataggio template...")
        
        # Merge con esistenti
        merged_templates = {**self.existing_templates, **new_templates}
        
        # Salva
        templates_path = Path("data/symbols/semantic_templates.json")
        with open(templates_path, 'w', encoding='utf-8') as f:
            json.dump(merged_templates, f, ensure_ascii=False, indent=2, sort_keys=True)
        
        print(f"✅ Salvati {len(merged_templates)} template totali")
        
        # Backup
        backup_path = templates_path.with_suffix('.json.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(self.existing_templates, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Backup originale: {backup_path}")
    
    def merge_and_save_contexts(self, new_contexts: Dict[str, str]):
        """Merge e salva contesti."""
        print("💾 Merge e salvataggio contesti...")
        
        # Merge con esistenti
        merged_contexts = {**self.existing_contexts, **new_contexts}
        
        # Salva
        contexts_path = Path("data/symbols/prompt_contexts.json")
        with open(contexts_path, 'w', encoding='utf-8') as f:
            json.dump(merged_contexts, f, ensure_ascii=False, indent=2, sort_keys=True)
        
        print(f"✅ Salvati {len(merged_contexts)} contesti totali")
        
        # Backup
        backup_path = contexts_path.with_suffix('.json.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(self.existing_contexts, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Backup originale: {backup_path}")
    
    def generate_statistics(self, new_templates: Dict[str, str], new_contexts: Dict[str, str]):
        """Genera statistiche."""
        print("\n📊 Statistiche Generazione:")
        print("=" * 50)
        
        total_symbols = len(self.all_symbols)
        existing_template_count = len(self.existing_templates)
        existing_context_count = len(self.existing_contexts)
        new_template_count = len(new_templates)
        new_context_count = len(new_contexts)
        
        final_template_count = existing_template_count + new_template_count
        final_context_count = existing_context_count + new_context_count
        
        template_coverage = final_template_count / total_symbols
        context_coverage = final_context_count / total_symbols
        
        print(f"Simboli totali: {total_symbols}")
        print(f"")
        print(f"Template:")
        print(f"  - Esistenti: {existing_template_count}")
        print(f"  - Nuovi: {new_template_count}")
        print(f"  - Totali: {final_template_count}")
        print(f"  - Coverage: {template_coverage:.2%}")
        print(f"")
        print(f"Contesti:")
        print(f"  - Esistenti: {existing_context_count}")
        print(f"  - Nuovi: {new_context_count}")
        print(f"  - Totali: {final_context_count}")
        print(f"  - Coverage: {context_coverage:.2%}")
        print(f"")
        
        # Verifica target certificazione
        if template_coverage >= 0.30 and context_coverage >= 0.30:
            print("🎉 TARGET CERTIFICAZIONE RAGGIUNTO!")
        else:
            print("⚠️ Target certificazione non ancora raggiunto")
            print(f"   Template: {template_coverage:.2%} {'✅' if template_coverage >= 0.30 else '❌'} >= 30%")
            print(f"   Context: {context_coverage:.2%} {'✅' if context_coverage >= 0.30 else '❌'} >= 30%")
    
    def run_generation(self):
        """Esegue generazione completa."""
        print("🚀 NEUROGLYPH Missing Templates & Contexts Generator")
        print("=" * 60)
        
        # Carica dati
        self.load_existing_data()
        
        # Genera mancanti
        new_templates = self.generate_missing_templates()
        new_contexts = self.generate_missing_contexts()
        
        # Salva
        self.merge_and_save_templates(new_templates)
        self.merge_and_save_contexts(new_contexts)
        
        # Statistiche
        self.generate_statistics(new_templates, new_contexts)
        
        print(f"\n✅ Generazione completata!")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Missing Templates & Contexts Generator")
    parser.add_argument('--dry-run', action='store_true',
                       help='Esegui senza salvare file')
    
    args = parser.parse_args()
    
    generator = MissingTemplatesContextsGenerator()
    
    if args.dry_run:
        print("🔍 DRY RUN - Nessun file verrà modificato")
        # TODO: Implementa dry run
    else:
        generator.run_generation()
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
