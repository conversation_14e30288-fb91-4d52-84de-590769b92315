#!/usr/bin/env python3
"""
NEUROGLYPH Complete NG-LLM Validation
Test completo che combina AST roundtrip + requisiti specifici NEUROGLYPH

CRITERI VALIDATI:
1. AST Roundtrip + Fuzzy matching
2. Tokenization Integrity (zero-splitting)
3. Registry Compliance
4. Symbol Preservation
5. Code Fidelity Score
6. Annotation Preservation (opzionale)
"""

import ast
import json
import difflib
import argparse
import random
from pathlib import Path
from typing import Dict, List, Set, Any, Optional

# Try imports with fallbacks
try:
    from Levenshtein import ratio as levenshtein_ratio
    HAS_LEVENSHTEIN = True
except ImportError:
    HAS_LEVENSHTEIN = False
    def levenshtein_ratio(a, b):
        return difflib.SequenceMatcher(None, a, b).ratio()


class NGTokenizerMock:
    """Mock tokenizer per test - in implementazione reale usare NGTokenizer."""
    
    def __init__(self):
        # Simboli che dovrebbero essere single-token
        self.single_token_symbols = {
            '⊢', '∀', '∃', '⇒', '∧', '∨', '¬', '⊥', '≡', '⇔', '∴',
            '∈', '∉', '⊂', '⊃', '⊆', '⊇', '∅', '∪', '∩', '∖', '△',
            '∞', '∑', '∏', '∫', '∂', '∇', '±', '∓', '≤', '≥', '≠', '≈',
            '→', '←', '↑', '↓', '↔', '↕', '🧠', '🔮', '📖', '🎯'
        }
    
    def encode(self, text: str) -> List[int]:
        """Mock encoding - simula tokenizzazione."""
        tokens = []
        for char in text:
            if char in self.single_token_symbols:
                tokens.append(hash(char) % 10000)  # Mock token ID
            else:
                tokens.append(ord(char))  # Mock token ID
        return tokens
    
    def decode(self, tokens: List[int]) -> str:
        """Mock decoding."""
        return "".join(chr(min(t, 127)) for t in tokens if t < 128)


def load_neuroglyph_registry() -> Set[str]:
    """Carica registry simboli NEUROGLYPH."""
    return {
        # Simboli logici
        '⊢', '∀', '∃', '⇒', '∧', '∨', '¬', '⊥', '≡', '⇔', '∴',
        # Simboli insiemistici  
        '∈', '∉', '⊂', '⊃', '⊆', '⊇', '∅', '∪', '∩', '∖', '△',
        # Simboli matematici
        '∞', '∑', '∏', '∫', '∂', '∇', '±', '∓', '≤', '≥', '≠', '≈', '≅', '∝',
        # Simboli frecce
        '→', '←', '↑', '↓', '↔', '↕', '⟶', '⟵',
        # Simboli NEUROGLYPH
        '🧠', '🔮', '📖', '🎯', '⚡', '🌟', '🔥', '💎',
        # Simboli greci
        'α', 'β', 'γ', 'δ', 'ε', 'ζ', 'η', 'θ', 'λ', 'μ', 'π', 'ρ', 'σ', 'τ', 'φ', 'χ', 'ψ', 'ω',
        # Altri simboli
        '℘', '℧', '℮', '⊕', '⊗', '⊙', '⊚', '⊛', '⟨', '⟩', '⌊', '⌋', '⌈', '⌉',
        # Simboli aggiuntivi dal dataset
        '²', '³', 'è', 'ò', 'ℝ', '∘', '⊈', '⟦', '⟧', '⤴', '️', '🏛', '🔄', '🔢'
    }


def normalize_ast(node: ast.AST) -> ast.AST:
    """Normalizza AST rimuovendo attributi non-semantici."""
    for field, value in ast.iter_fields(node):
        if field in ('lineno', 'col_offset', 'end_lineno', 'end_col_offset'):
            setattr(node, field, None)
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, ast.AST):
                    normalize_ast(item)
        elif isinstance(value, ast.AST):
            normalize_ast(value)
    
    # Ordina per determinismo
    if isinstance(node, ast.Call):
        node.keywords = sorted(node.keywords, key=lambda kw: kw.arg or "")
    
    return node


def ast_to_normalized_str(code: str) -> str:
    """Parse e dump AST normalizzato."""
    tree = ast.parse(code)
    normalize_ast(tree)
    return ast.dump(tree, include_attributes=False)


def convert_neuroglyph_to_python(neuroglyph_code: str) -> str:
    """Converte NEUROGLYPH in Python per test AST."""
    mapping = {
        '⊢': 'proves', '∀': 'forall', '∃': 'exists', '⇒': 'implies',
        '∧': 'and_logic', '∨': 'or_logic', '¬': 'not_logic', '⊥': 'false',
        '≡': 'equiv', '⇔': 'iff', '∴': 'therefore', '∈': 'in_set',
        '⊂': 'subset', '⊇': 'superset', '∅': 'emptyset',
        '🧠': 'brain', '🔮': 'predict', '📖': 'story'
    }
    
    python_code = neuroglyph_code
    for symbol, replacement in mapping.items():
        python_code = python_code.replace(symbol, replacement)
    
    # Pulizia
    python_code = ''.join(c for c in python_code if c.isprintable())
    python_code = python_code.replace(':', '_COLON_')
    python_code = python_code.replace('?', '_QUESTION_')
    
    if not python_code.strip():
        python_code = 'None'
    
    # Test validità
    try:
        ast.parse(python_code)
        return python_code
    except SyntaxError:
        return f'"{neuroglyph_code}"'


def verify_tokenization_integrity(code: str, symbols: List[str], 
                                tokenizer: NGTokenizerMock) -> Dict[str, Any]:
    """Verifica integrità tokenizzazione."""
    result = {
        'passed': True,
        'zero_splitting_violations': [],
        'missing_symbols': [],
        'total_symbols': len(symbols)
    }
    
    try:
        # Test 1: Zero-splitting per ogni simbolo
        for symbol in symbols:
            tokens = tokenizer.encode(symbol)
            if len(tokens) != 1:
                result['zero_splitting_violations'].append({
                    'symbol': symbol,
                    'token_count': len(tokens)
                })
                result['passed'] = False
        
        # Test 2: Simboli presenti nel token stream
        code_tokens = tokenizer.encode(code)
        code_decoded = tokenizer.decode(code_tokens)
        
        for symbol in symbols:
            if symbol not in code_decoded:
                result['missing_symbols'].append(symbol)
                result['passed'] = False
    
    except Exception as e:
        result['passed'] = False
        result['error'] = str(e)
    
    return result


def verify_registry_compliance(symbols: List[str], 
                              registry: Set[str]) -> Dict[str, Any]:
    """Verifica compliance con registry NEUROGLYPH."""
    result = {
        'passed': True,
        'invalid_symbols': [],
        'total_symbols': len(symbols)
    }
    
    for symbol in symbols:
        if symbol not in registry:
            result['invalid_symbols'].append(symbol)
            result['passed'] = False
    
    return result


def calculate_code_fidelity(original: str, reconstructed: str) -> float:
    """Calcola fidelity score del codice."""
    if original == reconstructed:
        return 1.0
    return levenshtein_ratio(original, reconstructed)


def test_complete_ng_llm_validation(item: Dict[str, Any], 
                                  field: str,
                                  ast_threshold: float,
                                  fidelity_threshold: float,
                                  tokenizer: NGTokenizerMock,
                                  registry: Set[str]) -> Dict[str, Any]:
    """Test completo NG-LLM su singolo item."""
    result = {
        'id': item.get('id', 'unknown'),
        'overall_passed': True,
        'tests': {}
    }
    
    try:
        original_code = item.get(field, '')
        symbols = item.get('symbols_used', [])
        
        # TEST 1: AST Roundtrip + Fuzzy
        python_code = convert_neuroglyph_to_python(original_code)
        
        try:
            orig_ast = ast_to_normalized_str(python_code)
            recon_python = ast.unparse(ast.parse(python_code))
            recon_ast = ast_to_normalized_str(recon_python)
            
            if orig_ast == recon_ast:
                ast_similarity = 1.0
            else:
                ast_similarity = levenshtein_ratio(orig_ast, recon_ast)
            
            ast_passed = ast_similarity >= ast_threshold
            
            result['tests']['ast_roundtrip'] = {
                'passed': ast_passed,
                'similarity': ast_similarity,
                'threshold': ast_threshold
            }
            
            if not ast_passed:
                result['overall_passed'] = False
        
        except Exception as e:
            result['tests']['ast_roundtrip'] = {
                'passed': False,
                'error': str(e)
            }
            result['overall_passed'] = False
        
        # TEST 2: Registry Compliance
        registry_result = verify_registry_compliance(symbols, registry)
        result['tests']['registry_compliance'] = registry_result
        if not registry_result['passed']:
            result['overall_passed'] = False
        
        # TEST 3: Tokenization Integrity
        tokenization_result = verify_tokenization_integrity(original_code, symbols, tokenizer)
        result['tests']['tokenization_integrity'] = tokenization_result
        if not tokenization_result['passed']:
            result['overall_passed'] = False
        
        # TEST 4: Code Fidelity
        if 'recon_python' in locals():
            fidelity_score = calculate_code_fidelity(python_code, recon_python)
            fidelity_passed = fidelity_score >= fidelity_threshold
            
            result['tests']['code_fidelity'] = {
                'passed': fidelity_passed,
                'score': fidelity_score,
                'threshold': fidelity_threshold
            }
            
            if not fidelity_passed:
                result['overall_passed'] = False
    
    except Exception as e:
        result['overall_passed'] = False
        result['error'] = str(e)
    
    return result


def main():
    """Pipeline principale di validazione completa NG-LLM."""
    parser = argparse.ArgumentParser(description="Complete NEUROGLYPH LLM Validation")
    parser.add_argument("--dataset", type=Path, required=True)
    parser.add_argument("--field", default="response_symbolic")
    parser.add_argument("--ast-threshold", type=float, default=0.99)
    parser.add_argument("--fidelity-threshold", type=float, default=0.95)
    parser.add_argument("--sample", type=int, default=None)
    parser.add_argument("--output", type=Path, default="ng_llm_validation_failures.jsonl")
    
    args = parser.parse_args()
    
    print("🔬 NEUROGLYPH Complete NG-LLM Validation")
    print("=" * 70)
    print(f"Dataset: {args.dataset}")
    print(f"Field: {args.field}")
    print(f"AST Threshold: {args.ast_threshold}")
    print(f"Fidelity Threshold: {args.fidelity_threshold}")
    print(f"Levenshtein: {HAS_LEVENSHTEIN}")
    
    # Inizializza componenti
    tokenizer = NGTokenizerMock()
    registry = load_neuroglyph_registry()
    
    print(f"Registry symbols: {len(registry)}")
    
    # Carica dataset
    with open(args.dataset, 'r', encoding='utf-8') as f:
        data = [json.loads(line) for line in f if line.strip()]
    
    if args.sample and args.sample < len(data):
        data = random.sample(data, args.sample)
    
    print(f"Testing: {len(data)} examples")
    
    # Test completo
    print(f"\n🧪 Running complete NG-LLM validation...")
    
    total = len(data)
    passed = 0
    failures = []
    
    test_stats = {
        'ast_roundtrip': 0,
        'registry_compliance': 0,
        'tokenization_integrity': 0,
        'code_fidelity': 0
    }
    
    for i, item in enumerate(data):
        result = test_complete_ng_llm_validation(
            item, args.field, args.ast_threshold, args.fidelity_threshold,
            tokenizer, registry
        )
        
        if result['overall_passed']:
            passed += 1
        else:
            failures.append(result)
        
        # Conta successi per test
        for test_name, test_result in result.get('tests', {}).items():
            if test_result.get('passed', False):
                test_stats[test_name] += 1
        
        if (i + 1) % 100 == 0:
            print(f"   Processed: {i + 1}/{total}")
    
    # Risultati
    print(f"\n🎯 COMPLETE NG-LLM VALIDATION RESULTS")
    print("=" * 70)
    print(f"Total tested: {total}")
    print(f"Overall passed: {passed}")
    print(f"Overall pass rate: {passed/total:.2%}")
    
    print(f"\n📊 Test-specific results:")
    for test_name, count in test_stats.items():
        rate = count / total if total > 0 else 0
        print(f"   {test_name}: {count}/{total} ({rate:.2%})")
    
    # Salva failures
    if failures:
        with open(args.output, 'w', encoding='utf-8') as f:
            for failure in failures:
                f.write(json.dumps(failure, ensure_ascii=False) + '\n')
        
        print(f"\n❌ VALIDATION FAILURES")
        print(f"Failures saved to: {args.output}")
        return 1
    else:
        print(f"\n✅ ALL NG-LLM CRITERIA PASSED")
        return 0


if __name__ == "__main__":
    exit(main())
