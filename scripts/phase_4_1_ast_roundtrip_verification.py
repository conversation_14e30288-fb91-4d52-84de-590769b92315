#!/usr/bin/env python3
"""
NEUROGLYPH Phase 4.1 - AST Roundtrip Verification
Test di integrità AST con fidelity ≥95% e parseability 100%

OBIETTIVO CRITICO: Verificare che code → AST → Neuroglyph → AST → code
mantenga equivalenza semantica perfetta.

Questo è il test che determina se NEUROGLYPH può davvero "pensare" 
invece di solo "generare testo che sembra logico".
"""

import ast
import json
import logging
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ASTRoundtripResult:
    """Risultato di un test roundtrip AST."""
    example_id: str
    original_code: str
    neuroglyph_encoding: str
    reconstructed_code: str
    ast_equivalent: bool
    parse_success: bool
    fidelity_score: float
    error_details: Optional[str] = None


@dataclass
class ASTVerificationReport:
    """Report completo di verifica AST."""
    total_tests: int = 0
    successful_parses: int = 0
    ast_equivalent_count: int = 0
    average_fidelity: float = 0.0
    parseability_rate: float = 0.0
    ast_equivalence_rate: float = 0.0
    
    failed_examples: List[ASTRoundtripResult] = field(default_factory=list)
    success_examples: List[ASTRoundtripResult] = field(default_factory=list)
    
    critical_failures: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    verification_passed: bool = False
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


class ASTRoundtripVerifier:
    """Verificatore di integrità AST per NEUROGLYPH."""
    
    def __init__(self):
        """Inizializza verificatore AST."""
        self.neuroglyph_symbols = self._load_neuroglyph_symbols()
        logger.info(f"🔍 ASTRoundtripVerifier inizializzato")
        logger.info(f"   Simboli NEUROGLYPH: {len(self.neuroglyph_symbols)}")
    
    def _load_neuroglyph_symbols(self) -> Dict[str, str]:
        """Carica mapping simboli NEUROGLYPH."""
        # Mapping simboli logici → rappresentazione Python
        return {
            '⊢': 'entails',
            '∀': 'forall',
            '∃': 'exists', 
            '⇒': 'implies',
            '∧': 'and',
            '∨': 'or',
            '¬': 'not',
            '⊥': 'contradiction',
            '≡': 'equivalent',
            '⇔': 'iff',
            '∈': 'in',
            '⊂': 'subset',
            '⊇': 'superset',
            '∅': 'emptyset',
            '∴': 'therefore'
        }
    
    def verify_dataset_ast_integrity(self, dataset_path: str, 
                                   sample_size: int = 500) -> ASTVerificationReport:
        """
        Verifica integrità AST del dataset.
        
        Args:
            dataset_path: Percorso al dataset
            sample_size: Numero di esempi da testare
            
        Returns:
            Report di verifica completo
        """
        logger.info(f"🔍 Starting AST roundtrip verification...")
        logger.info(f"   Dataset: {dataset_path}")
        logger.info(f"   Sample size: {sample_size}")
        
        report = ASTVerificationReport()
        
        # Carica esempi dal dataset
        examples = self._load_dataset_examples(dataset_path, sample_size)
        report.total_tests = len(examples)
        
        logger.info(f"   Loaded {len(examples)} examples for testing")
        
        # Test roundtrip per ogni esempio
        for i, example in enumerate(examples):
            try:
                result = self._test_ast_roundtrip(example, i)
                
                if result.parse_success:
                    report.successful_parses += 1
                    
                if result.ast_equivalent:
                    report.ast_equivalent_count += 1
                    report.success_examples.append(result)
                else:
                    report.failed_examples.append(result)
                
                # Log progress
                if (i + 1) % 100 == 0:
                    logger.info(f"   Processed {i + 1}/{len(examples)} examples")
                    
            except Exception as e:
                logger.error(f"Error testing example {i}: {e}")
                report.failed_examples.append(ASTRoundtripResult(
                    example_id=f"example_{i}",
                    original_code="",
                    neuroglyph_encoding="",
                    reconstructed_code="",
                    ast_equivalent=False,
                    parse_success=False,
                    fidelity_score=0.0,
                    error_details=str(e)
                ))
        
        # Calcola metriche finali
        self._calculate_final_metrics(report)
        
        # Determina se verifica è passata
        self._assess_verification_result(report)
        
        logger.info(f"✅ AST roundtrip verification completed")
        logger.info(f"   Parseability rate: {report.parseability_rate:.2%}")
        logger.info(f"   AST equivalence rate: {report.ast_equivalence_rate:.2%}")
        logger.info(f"   Average fidelity: {report.average_fidelity:.3f}")
        logger.info(f"   Verification passed: {report.verification_passed}")
        
        return report
    
    def _load_dataset_examples(self, dataset_path: str, sample_size: int) -> List[Dict]:
        """Carica esempi dal dataset."""
        examples = []
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                all_examples = []
                for line in f:
                    if line.strip():
                        example = json.loads(line.strip())
                        all_examples.append(example)
                
                # Campiona esempi casuali
                if len(all_examples) > sample_size:
                    examples = random.sample(all_examples, sample_size)
                else:
                    examples = all_examples
                    
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
        
        return examples
    
    def _test_ast_roundtrip(self, example: Dict, index: int) -> ASTRoundtripResult:
        """Test roundtrip AST per singolo esempio."""
        example_id = example.get('id', f'example_{index}')
        
        # Estrai codice simbolico
        prompt_symbolic = example.get('prompt_symbolic', '')
        response_symbolic = example.get('response_symbolic', '')
        
        # Combina per test completo
        neuroglyph_code = f"{prompt_symbolic} {response_symbolic}"
        
        try:
            # FASE 1: Neuroglyph → Python code
            python_code = self._neuroglyph_to_python(neuroglyph_code)
            
            # FASE 2: Python code → AST
            original_ast = self._parse_to_ast(python_code)
            
            # FASE 3: AST → Neuroglyph encoding
            neuroglyph_encoding = self._ast_to_neuroglyph(original_ast)
            
            # FASE 4: Neuroglyph → Python (roundtrip)
            reconstructed_python = self._neuroglyph_to_python(neuroglyph_encoding)
            
            # FASE 5: Python → AST (roundtrip)
            reconstructed_ast = self._parse_to_ast(reconstructed_python)
            
            # FASE 6: Confronta AST
            ast_equivalent = self._compare_asts(original_ast, reconstructed_ast)
            fidelity_score = self._calculate_fidelity(python_code, reconstructed_python)
            
            return ASTRoundtripResult(
                example_id=example_id,
                original_code=python_code,
                neuroglyph_encoding=neuroglyph_encoding,
                reconstructed_code=reconstructed_python,
                ast_equivalent=ast_equivalent,
                parse_success=True,
                fidelity_score=fidelity_score
            )
            
        except Exception as e:
            return ASTRoundtripResult(
                example_id=example_id,
                original_code=neuroglyph_code,
                neuroglyph_encoding="",
                reconstructed_code="",
                ast_equivalent=False,
                parse_success=False,
                fidelity_score=0.0,
                error_details=str(e)
            )
    
    def _neuroglyph_to_python(self, neuroglyph_code: str) -> str:
        """Converte codice NEUROGLYPH in Python."""
        python_code = neuroglyph_code
        
        # Sostituzioni simboli → Python
        for symbol, python_equiv in self.neuroglyph_symbols.items():
            python_code = python_code.replace(symbol, python_equiv)
        
        # Pulizia e normalizzazione
        python_code = python_code.replace('🧠', '# brain:')
        python_code = python_code.replace('🔮', '# predict:')
        python_code = python_code.replace('📖', '# story:')
        
        # Rimuovi caratteri non-Python
        python_code = ''.join(c for c in python_code if c.isprintable())
        
        # Se non è codice Python valido, crea una rappresentazione
        if not self._is_valid_python_syntax(python_code):
            # Crea rappresentazione come stringa
            python_code = f'"{neuroglyph_code}"'
        
        return python_code
    
    def _parse_to_ast(self, code: str) -> ast.AST:
        """Parse codice Python in AST."""
        try:
            return ast.parse(code)
        except SyntaxError:
            # Se non è Python valido, tratta come espressione stringa
            return ast.parse(f'"{code}"')
    
    def _ast_to_neuroglyph(self, ast_node: ast.AST) -> str:
        """Converte AST in encoding NEUROGLYPH."""
        # Implementazione semplificata - in versione completa
        # dovrebbe ricostruire perfettamente i simboli NEUROGLYPH
        
        if isinstance(ast_node, ast.Module):
            if ast_node.body:
                return self._ast_to_neuroglyph(ast_node.body[0])
        
        elif isinstance(ast_node, ast.Expr):
            return self._ast_to_neuroglyph(ast_node.value)
        
        elif isinstance(ast_node, ast.Constant):
            value = str(ast_node.value)
            # Riconverti Python → NEUROGLYPH
            for symbol, python_equiv in self.neuroglyph_symbols.items():
                value = value.replace(python_equiv, symbol)
            return value
        
        elif isinstance(ast_node, ast.Name):
            name = ast_node.id
            # Riconverti nomi Python → simboli
            for symbol, python_equiv in self.neuroglyph_symbols.items():
                if python_equiv == name:
                    return symbol
            return name
        
        # Default: rappresentazione stringa
        return ast.dump(ast_node)
    
    def _compare_asts(self, ast1: ast.AST, ast2: ast.AST) -> bool:
        """Confronta due AST per equivalenza."""
        try:
            # Confronto strutturale semplificato
            dump1 = ast.dump(ast1, annotate_fields=False)
            dump2 = ast.dump(ast2, annotate_fields=False)
            
            # Normalizza per confronto
            dump1 = self._normalize_ast_dump(dump1)
            dump2 = self._normalize_ast_dump(dump2)
            
            return dump1 == dump2
            
        except Exception:
            return False
    
    def _normalize_ast_dump(self, dump: str) -> str:
        """Normalizza dump AST per confronto."""
        # Rimuovi spazi extra e normalizza
        normalized = ' '.join(dump.split())
        
        # Normalizza rappresentazioni equivalenti
        replacements = {
            'Constant(value=': 'Constant(',
            'Name(id=': 'Name(',
            'arg=': '',
        }
        
        for old, new in replacements.items():
            normalized = normalized.replace(old, new)
        
        return normalized
    
    def _calculate_fidelity(self, original: str, reconstructed: str) -> float:
        """Calcola fidelity score tra originale e ricostruito."""
        if not original and not reconstructed:
            return 1.0
        
        if not original or not reconstructed:
            return 0.0
        
        # Calcola similarità carattere per carattere
        max_len = max(len(original), len(reconstructed))
        matches = sum(1 for i in range(min(len(original), len(reconstructed)))
                     if original[i] == reconstructed[i])
        
        return matches / max_len if max_len > 0 else 0.0
    
    def _is_valid_python_syntax(self, code: str) -> bool:
        """Verifica se il codice ha sintassi Python valida."""
        try:
            ast.parse(code)
            return True
        except SyntaxError:
            return False
    
    def _calculate_final_metrics(self, report: ASTVerificationReport):
        """Calcola metriche finali del report."""
        if report.total_tests > 0:
            report.parseability_rate = report.successful_parses / report.total_tests
            report.ast_equivalence_rate = report.ast_equivalent_count / report.total_tests
        
        # Calcola fidelity media
        if report.success_examples:
            total_fidelity = sum(ex.fidelity_score for ex in report.success_examples)
            report.average_fidelity = total_fidelity / len(report.success_examples)
    
    def _assess_verification_result(self, report: ASTVerificationReport):
        """Valuta se la verifica è passata."""
        # Criteri rigorosi per passare
        criteria = [
            ("Parseability Rate", report.parseability_rate >= 1.0),  # 100% richiesto
            ("AST Equivalence Rate", report.ast_equivalence_rate >= 0.95),  # 95% richiesto
            ("Average Fidelity", report.average_fidelity >= 0.95)  # 95% richiesto
        ]
        
        passed_criteria = 0
        for criterion_name, passed in criteria:
            if passed:
                passed_criteria += 1
            else:
                report.critical_failures.append(f"FAILED: {criterion_name}")
        
        # Verifica passa solo se TUTTI i criteri sono soddisfatti
        report.verification_passed = passed_criteria == len(criteria)
        
        # Aggiungi raccomandazioni
        if not report.verification_passed:
            report.recommendations.append("CRITICAL: AST roundtrip verification failed")
            report.recommendations.append("DO NOT proceed with fine-tuning until fixed")
            report.recommendations.append("Improve symbolic encoding/decoding pipeline")
        else:
            report.recommendations.append("EXCELLENT: AST roundtrip verification passed")
            report.recommendations.append("Safe to proceed with tokenizer integrity testing")
    
    def save_verification_report(self, report: ASTVerificationReport, output_path: str):
        """Salva report di verifica."""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Converti in dict per serializzazione
        report_dict = {
            'verification_summary': {
                'verification_passed': report.verification_passed,
                'timestamp': report.timestamp,
                'total_tests': report.total_tests
            },
            'metrics': {
                'parseability_rate': report.parseability_rate,
                'ast_equivalence_rate': report.ast_equivalence_rate,
                'average_fidelity': report.average_fidelity,
                'successful_parses': report.successful_parses,
                'ast_equivalent_count': report.ast_equivalent_count
            },
            'assessment': {
                'critical_failures': report.critical_failures,
                'recommendations': report.recommendations
            },
            'sample_results': {
                'success_examples': [
                    {
                        'example_id': ex.example_id,
                        'fidelity_score': ex.fidelity_score,
                        'ast_equivalent': ex.ast_equivalent
                    } for ex in report.success_examples[:10]  # Top 10
                ],
                'failed_examples': [
                    {
                        'example_id': ex.example_id,
                        'error_details': ex.error_details,
                        'fidelity_score': ex.fidelity_score
                    } for ex in report.failed_examples[:10]  # Top 10 failures
                ]
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 AST verification report saved: {output_file}")


def main():
    """Pipeline principale di verifica AST."""
    print("🔍 NEUROGLYPH Phase 4.1 - AST Roundtrip Verification")
    print("=" * 80)
    print("CRITICAL TEST: Verifying AST integrity with ≥95% fidelity")
    print("This test determines if NEUROGLYPH can truly 'think' vs 'generate text'")
    
    # Dataset da verificare
    dataset_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"
    
    if not Path(dataset_path).exists():
        print(f"❌ Dataset not found: {dataset_path}")
        return
    
    # Esegui verifica AST
    verifier = ASTRoundtripVerifier()
    report = verifier.verify_dataset_ast_integrity(dataset_path, sample_size=500)
    
    # Salva report
    report_path = "data/verification/ast_roundtrip_verification_report.json"
    verifier.save_verification_report(report, report_path)
    
    # Stampa risultati
    print(f"\n🎯 AST ROUNDTRIP VERIFICATION RESULTS")
    print("=" * 80)
    print(f"📊 Total Tests: {report.total_tests}")
    print(f"✅ Parseability Rate: {report.parseability_rate:.2%}")
    print(f"🔄 AST Equivalence Rate: {report.ast_equivalence_rate:.2%}")
    print(f"📈 Average Fidelity: {report.average_fidelity:.3f}")
    print(f"🎯 Verification Passed: {report.verification_passed}")
    
    if report.critical_failures:
        print(f"\n❌ CRITICAL FAILURES:")
        for failure in report.critical_failures:
            print(f"   - {failure}")
    
    if report.recommendations:
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in report.recommendations:
            print(f"   - {rec}")
    
    print(f"\n📄 Report saved: {report_path}")
    
    if report.verification_passed:
        print(f"\n🎉 AST VERIFICATION PASSED! Ready for Phase 4.2 (Tokenizer Testing)")
    else:
        print(f"\n⚠️ AST VERIFICATION FAILED! Must fix before proceeding.")


if __name__ == "__main__":
    main()
