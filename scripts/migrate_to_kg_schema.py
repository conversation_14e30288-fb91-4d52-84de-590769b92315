#!/usr/bin/env python3
"""
Migrazione Schema Fase 3.0 - Knowledge Graph e Pattern Extraction
Crea tabelle kg_* e pattern_* per learning components
"""

import sqlite3
import sys
import os
from pathlib import Path

# Aggiungi path per import NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))


def migrate_database(db_path: str):
    """
    Migra database esistente al nuovo schema Fase 3.0.
    
    Args:
        db_path: Percorso database da migrare
    """
    print(f"🔄 Migrazione database: {db_path}")
    
    try:
        # Connessione database
        db = sqlite3.connect(db_path, timeout=30.0)
        cur = db.cursor()
        
        # Abilita WAL mode se non già attivo
        cur.execute("PRAGMA journal_mode=WAL")
        cur.execute("PRAGMA synchronous=NORMAL")
        
        print("✅ Connessione database stabilita")
        
        # Schema Knowledge Graph
        print("📊 Creazione tabelle Knowledge Graph...")
        
        cur.executescript("""
            -- Tabella nodi knowledge graph
            CREATE TABLE IF NOT EXISTS kg_nodes(
                node_id TEXT PRIMARY KEY,
                node_type TEXT NOT NULL,
                properties TEXT,
                created_at REAL NOT NULL,
                updated_at REAL NOT NULL
            );
            
            -- Tabella edge knowledge graph
            CREATE TABLE IF NOT EXISTS kg_edges(
                edge_id TEXT PRIMARY KEY,
                source_id TEXT NOT NULL,
                target_id TEXT NOT NULL,
                edge_type TEXT NOT NULL,
                weight REAL DEFAULT 1.0,
                properties TEXT,
                created_at REAL NOT NULL,
                FOREIGN KEY (source_id) REFERENCES kg_nodes(node_id),
                FOREIGN KEY (target_id) REFERENCES kg_nodes(node_id)
            );
            
            -- Tabella statistiche knowledge graph
            CREATE TABLE IF NOT EXISTS kg_stats(
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ts REAL NOT NULL,
                total_nodes INTEGER NOT NULL,
                total_edges INTEGER NOT NULL,
                node_types TEXT,
                edge_types TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        print("✅ Tabelle Knowledge Graph create")
        
        # Schema Pattern Extraction
        print("🔍 Creazione tabelle Pattern Extraction...")
        
        cur.executescript("""
            -- Tabella pattern estratti
            CREATE TABLE IF NOT EXISTS extracted_patterns(
                pattern_id TEXT PRIMARY KEY,
                pattern_name TEXT NOT NULL,
                error_types TEXT NOT NULL,
                patch_strategies TEXT NOT NULL,
                support INTEGER NOT NULL,
                confidence REAL NOT NULL,
                lift REAL DEFAULT 1.0,
                first_seen REAL NOT NULL,
                last_seen REAL NOT NULL,
                version INTEGER DEFAULT 1,
                properties TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Tabella statistiche pattern (già presente in performance_storage)
            CREATE TABLE IF NOT EXISTS pattern_stats(
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ts REAL NOT NULL,
                last_extracted_ts REAL NOT NULL,
                extraction_count INTEGER NOT NULL,
                total_patterns INTEGER NOT NULL,
                new_patterns INTEGER NOT NULL,
                updated_patterns INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        print("✅ Tabelle Pattern Extraction create")
        
        # Indici per performance
        print("⚡ Creazione indici performance...")
        
        cur.executescript("""
            -- Indici Knowledge Graph
            CREATE INDEX IF NOT EXISTS idx_kg_nodes_type
              ON kg_nodes(node_type);
              
            CREATE INDEX IF NOT EXISTS idx_kg_nodes_updated
              ON kg_nodes(updated_at DESC);
              
            CREATE INDEX IF NOT EXISTS idx_kg_edges_source
              ON kg_edges(source_id);
              
            CREATE INDEX IF NOT EXISTS idx_kg_edges_target
              ON kg_edges(target_id);
              
            CREATE INDEX IF NOT EXISTS idx_kg_edges_type
              ON kg_edges(edge_type);
              
            CREATE INDEX IF NOT EXISTS idx_kg_stats_ts
              ON kg_stats(ts DESC);
            
            -- Indici Pattern Extraction
            CREATE INDEX IF NOT EXISTS idx_ep_support
              ON extracted_patterns(support DESC);
              
            CREATE INDEX IF NOT EXISTS idx_ep_confidence
              ON extracted_patterns(confidence DESC);
              
            CREATE INDEX IF NOT EXISTS idx_ep_last_seen
              ON extracted_patterns(last_seen DESC);
              
            CREATE INDEX IF NOT EXISTS idx_ep_version
              ON extracted_patterns(version DESC);
              
            CREATE INDEX IF NOT EXISTS idx_ps_ts
              ON pattern_stats(ts DESC);
              
            CREATE INDEX IF NOT EXISTS idx_ps_extraction_count
              ON pattern_stats(extraction_count DESC);
        """)
        
        print("✅ Indici performance creati")
        
        # Verifica tabelle create
        print("🔍 Verifica schema...")
        
        cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'kg_%'")
        kg_tables = [row[0] for row in cur.fetchall()]
        
        cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%pattern%'")
        pattern_tables = [row[0] for row in cur.fetchall()]
        
        print(f"   - Tabelle KG: {kg_tables}")
        print(f"   - Tabelle Pattern: {pattern_tables}")
        
        # Statistiche finali
        cur.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        total_tables = cur.fetchone()[0]
        
        cur.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index'")
        total_indices = cur.fetchone()[0]
        
        print(f"📊 Schema finale:")
        print(f"   - Tabelle totali: {total_tables}")
        print(f"   - Indici totali: {total_indices}")
        
        # Commit e chiusura
        db.commit()
        db.close()
        
        print(f"✅ Migrazione completata: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ Errore migrazione: {e}")
        return False


def main():
    """Main migration script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrazione schema NEUROGLYPH Fase 3.0")
    parser.add_argument('db_path', nargs='?', default='learning.db', 
                       help='Percorso database da migrare (default: learning.db)')
    parser.add_argument('--force', action='store_true', 
                       help='Forza migrazione anche se file non esiste')
    
    args = parser.parse_args()
    
    # Verifica file esistente
    if not os.path.exists(args.db_path) and not args.force:
        print(f"❌ File database non trovato: {args.db_path}")
        print("   Usa --force per creare nuovo database")
        return 1
    
    # Esegui migrazione
    success = migrate_database(args.db_path)
    
    if success:
        print(f"\n🎉 Migrazione Fase 3.0 completata!")
        print(f"   Database: {args.db_path}")
        print(f"   Pronto per Knowledge Graph e Pattern Extraction")
        return 0
    else:
        print(f"\n❌ Migrazione fallita")
        return 1


if __name__ == '__main__':
    sys.exit(main())
