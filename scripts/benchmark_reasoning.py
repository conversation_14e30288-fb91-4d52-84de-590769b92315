#!/usr/bin/env python3
"""
NEUROGLYPH Reasoning Benchmark Suite
Script per eseguire tutti i benchmark di reasoning e generare report
"""

import sys
import time
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))


def run_test_suite(test_path: str, test_name: str) -> Dict[str, Any]:
    """Esegue una suite di test e raccoglie i risultati."""
    print(f"\n🧪 Eseguendo {test_name}...")
    
    start_time = time.perf_counter()
    
    try:
        # Esegui pytest con output JSON
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            test_path, 
            '-v', 
            '--tb=short',
            '--json-report',
            '--json-report-file=/tmp/pytest_report.json'
        ], capture_output=True, text=True, timeout=300)
        
        duration = time.perf_counter() - start_time
        
        # Leggi report JSON se disponibile
        report_data = {}
        try:
            with open('/tmp/pytest_report.json', 'r') as f:
                report_data = json.load(f)
        except:
            pass
        
        return {
            'test_name': test_name,
            'success': result.returncode == 0,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'report_data': report_data
        }
        
    except subprocess.TimeoutExpired:
        return {
            'test_name': test_name,
            'success': False,
            'duration': 300.0,
            'error': 'Timeout after 300 seconds'
        }
    except Exception as e:
        return {
            'test_name': test_name,
            'success': False,
            'duration': time.perf_counter() - start_time,
            'error': str(e)
        }


def extract_metrics_from_output(output: str) -> Dict[str, Any]:
    """Estrae metriche dai log di output."""
    metrics = {}
    
    lines = output.split('\n')
    for line in lines:
        # Cerca pattern di metriche
        if 'accuracy:' in line.lower():
            try:
                accuracy_str = line.split('accuracy:')[1].strip().split()[0]
                if '%' in accuracy_str:
                    metrics['accuracy'] = float(accuracy_str.replace('%', '')) / 100
            except:
                pass
        
        if 'success rate:' in line.lower():
            try:
                rate_str = line.split('success rate:')[1].strip().split()[0]
                if '%' in rate_str:
                    metrics['success_rate'] = float(rate_str.replace('%', '')) / 100
            except:
                pass
        
        if 'duration:' in line.lower() and 'ms' in line:
            try:
                duration_str = line.split('duration:')[1].strip().split('ms')[0]
                metrics['avg_duration_ms'] = float(duration_str)
            except:
                pass
        
        if 'latency:' in line.lower() and 'ms' in line:
            try:
                latency_str = line.split('latency:')[1].strip().split('ms')[0]
                metrics['avg_latency_ms'] = float(latency_str)
            except:
                pass
        
        if 'throughput:' in line.lower() and 'r/s' in line:
            try:
                throughput_str = line.split('throughput:')[1].strip().split('r/s')[0]
                metrics['throughput_rps'] = float(throughput_str)
            except:
                pass
    
    return metrics


def generate_benchmark_report(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Genera report aggregato dei benchmark."""
    
    total_tests = len(results)
    successful_tests = len([r for r in results if r['success']])
    total_duration = sum(r['duration'] for r in results)
    
    # Estrai metriche aggregate
    all_metrics = {}
    for result in results:
        if result['success'] and 'stdout' in result:
            metrics = extract_metrics_from_output(result['stdout'])
            all_metrics[result['test_name']] = metrics
    
    # Calcola metriche aggregate
    aggregate_metrics = {
        'overall_success_rate': successful_tests / total_tests,
        'total_duration': total_duration,
        'avg_test_duration': total_duration / total_tests,
        'test_results': {}
    }
    
    # Aggiungi risultati per test
    for result in results:
        test_metrics = all_metrics.get(result['test_name'], {})
        aggregate_metrics['test_results'][result['test_name']] = {
            'success': result['success'],
            'duration': result['duration'],
            'metrics': test_metrics
        }
    
    return aggregate_metrics


def print_benchmark_summary(report: Dict[str, Any]):
    """Stampa summary dei benchmark."""
    
    print(f"\n" + "="*80)
    print(f"🏆 NEUROGLYPH REASONING BENCHMARK SUMMARY")
    print(f"="*80)
    
    print(f"📊 Overall Results:")
    print(f"   - Success Rate: {report['overall_success_rate']:.2%}")
    print(f"   - Total Duration: {report['total_duration']:.1f}s")
    print(f"   - Average Test Duration: {report['avg_test_duration']:.1f}s")
    
    print(f"\n📋 Test Results:")
    for test_name, test_result in report['test_results'].items():
        status = "✅" if test_result['success'] else "❌"
        print(f"   {status} {test_name}: {test_result['duration']:.1f}s")
        
        # Stampa metriche chiave
        metrics = test_result['metrics']
        if metrics:
            if 'accuracy' in metrics:
                print(f"      - Accuracy: {metrics['accuracy']:.2%}")
            if 'success_rate' in metrics:
                print(f"      - Success Rate: {metrics['success_rate']:.2%}")
            if 'avg_latency_ms' in metrics:
                print(f"      - Avg Latency: {metrics['avg_latency_ms']:.3f}ms")
            if 'throughput_rps' in metrics:
                print(f"      - Throughput: {metrics['throughput_rps']:.1f} r/s")
    
    # Valutazione complessiva
    print(f"\n🎯 Target Evaluation:")
    
    # Criteri di successo per "truly thinking LLM"
    criteria = {
        'Overall Success Rate': (report['overall_success_rate'], 0.80, '≥80%'),
        'Average Test Duration': (report['avg_test_duration'], 60.0, '≤60s')
    }
    
    passed_criteria = 0
    total_criteria = len(criteria)
    
    for criterion, (actual, target, target_str) in criteria.items():
        if criterion == 'Average Test Duration':
            passed = actual <= target
        else:
            passed = actual >= target
        
        if passed:
            passed_criteria += 1
            status = "✅"
        else:
            status = "❌"
        
        if criterion == 'Overall Success Rate':
            print(f"   {status} {criterion}: {actual:.2%} {target_str}")
        else:
            print(f"   {status} {criterion}: {actual:.1f}s {target_str}")
    
    certification_rate = passed_criteria / total_criteria
    
    print(f"\n🏆 CERTIFICATION RESULT:")
    print(f"   - Criteria Passed: {passed_criteria}/{total_criteria} ({certification_rate:.2%})")
    
    if certification_rate >= 0.80:
        print(f"   🎉 NEUROGLYPH REASONING CERTIFICATION: PASSED!")
        print(f"   🧠 Status: Truly Thinking LLM Candidate")
    else:
        print(f"   ⚠️ NEUROGLYPH REASONING CERTIFICATION: NEEDS IMPROVEMENT")
        print(f"   🔧 Status: Symbolic Infrastructure Complete, Reasoning In Progress")


def main():
    """Main benchmark runner."""
    
    print("🚀 NEUROGLYPH Reasoning Benchmark Suite")
    print("="*80)
    
    # Test suites da eseguire
    test_suites = [
        ('tests/reasoning/test_failure_cases.py', 'Failure Cases & Honesty'),
        ('tests/reasoning/test_external_benchmarks.py', 'External Benchmarks'),
        ('tests/reasoning/test_multi_hop_reasoning.py', 'Multi-Hop Reasoning'),
        ('tests/reasoning/test_performance_benchmarks.py', 'Performance Benchmarks'),
        ('tests/logic/test_formal_logic_engine.py', 'Formal Logic Engine'),
        ('tests/symbols/test_symbolic_reasoning.py', 'Symbolic Reasoning')
    ]
    
    results = []
    
    # Esegui ogni test suite
    for test_path, test_name in test_suites:
        if Path(test_path).exists():
            result = run_test_suite(test_path, test_name)
            results.append(result)
        else:
            print(f"⚠️ Test suite non trovata: {test_path}")
            results.append({
                'test_name': test_name,
                'success': False,
                'duration': 0.0,
                'error': f'Test file not found: {test_path}'
            })
    
    # Genera report
    report = generate_benchmark_report(results)
    
    # Stampa summary
    print_benchmark_summary(report)
    
    # Salva report JSON
    report_path = Path('benchmark_report.json')
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Report completo salvato in: {report_path}")
    
    # Exit code basato su successo
    exit_code = 0 if report['overall_success_rate'] >= 0.80 else 1
    sys.exit(exit_code)


if __name__ == '__main__':
    main()
