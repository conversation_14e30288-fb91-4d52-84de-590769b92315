#!/usr/bin/env python3
"""
NEUROGLYPH Symbol Test Cases Generator
Genera test cases automatici per tutti i 1,854 simboli Unicode
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import argparse

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))


class SymbolTestCaseGenerator:
    """Generatore test cases per simboli Unicode."""
    
    def __init__(self, registry_path: str, output_dir: str = "tests/fixtures"):
        """
        Inizializza generatore.
        
        Args:
            registry_path: Path al registry Unicode
            output_dir: Directory output per fixture
        """
        self.registry_path = registry_path
        self.output_dir = Path(output_dir)
        self.symbols = []
        self.test_cases = []
        
        print(f"🔍 Caricamento registry: {registry_path}")
        self._load_registry()
        
        print(f"📁 Output directory: {output_dir}")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def _load_registry(self):
        """Carica simboli dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            # Estrai simboli dal formato Unicode-only
            if 'unicode_symbols' in registry:
                for domain, entries in registry['unicode_symbols'].items():
                    for entry in entries:
                        if isinstance(entry, dict) and 'symbol' in entry:
                            self.symbols.append({
                                'symbol': entry['symbol'],
                                'domain': domain,
                                'id': entry.get('id', ''),
                                'meaning': entry.get('meaning', ''),
                                'tier': entry.get('tier', ''),
                                'god_mode_certified': entry.get('god_mode_certified', False)
                            })
            
            print(f"✅ Caricati {len(self.symbols)} simboli da {len(registry.get('unicode_symbols', {}))} domini")
            
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            sys.exit(1)
    
    def generate_roundtrip_cases(self) -> List[Dict[str, Any]]:
        """Genera test cases per round-trip AST."""
        print("🔄 Generazione test cases round-trip...")
        
        cases = []
        for i, symbol_data in enumerate(self.symbols):
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            # Genera diversi tipi di test case per ogni simbolo
            test_cases = [
                # Caso 1: Simbolo in variabile
                {
                    'case_id': f"var_{i:04d}",
                    'symbol': symbol,
                    'domain': domain,
                    'code_type': 'variable',
                    'code': f"def test_symbol_{i:04d}():\n    x = '{symbol}'\n    return x",
                    'expected_ast_nodes': ['FunctionDef', 'Assign', 'Constant']
                },
                
                # Caso 2: Simbolo in stringa
                {
                    'case_id': f"str_{i:04d}",
                    'symbol': symbol,
                    'domain': domain,
                    'code_type': 'string',
                    'code': f'message = "Simbolo: {symbol}"',
                    'expected_ast_nodes': ['Assign', 'Constant']
                },
                
                # Caso 3: Simbolo in commento (se supportato)
                {
                    'case_id': f"comment_{i:04d}",
                    'symbol': symbol,
                    'domain': domain,
                    'code_type': 'comment',
                    'code': f"# Simbolo di test: {symbol}\npass",
                    'expected_ast_nodes': ['Expr', 'Constant']
                }
            ]
            
            # Caso speciale per simboli logici/matematici
            if domain in ['logic', 'math']:
                test_cases.append({
                    'case_id': f"logic_{i:04d}",
                    'symbol': symbol,
                    'domain': domain,
                    'code_type': 'logical_expression',
                    'code': f"def logical_test():\n    # {symbol} operator\n    return True",
                    'expected_ast_nodes': ['FunctionDef', 'Return', 'Constant']
                })
            
            cases.extend(test_cases)
        
        print(f"✅ Generati {len(cases)} test cases round-trip")
        return cases
    
    def generate_ast_fidelity_cases(self) -> List[Dict[str, Any]]:
        """Genera test cases per AST fidelity."""
        print("🧬 Generazione test cases AST fidelity...")
        
        cases = []
        for i, symbol_data in enumerate(self.symbols):
            symbol = symbol_data['symbol']
            domain = symbol_data['domain']
            
            # Test case complesso per fidelity
            complex_code = f"""
def complex_function_{i:04d}():
    '''Funzione di test per simbolo {symbol}'''
    
    # Variabile con simbolo
    symbol_var = '{symbol}'
    
    # Lista con simbolo
    symbol_list = ['{symbol}', 'other', '{symbol}']
    
    # Dizionario con simbolo
    symbol_dict = {{
        'symbol': '{symbol}',
        'domain': '{domain}',
        'index': {i}
    }}
    
    # Condizione con simbolo
    if symbol_var == '{symbol}':
        return symbol_dict
    else:
        return symbol_list

# Classe di test
class SymbolTest_{i:04d}:
    def __init__(self):
        self.symbol = '{symbol}'
        self.domain = '{domain}'
    
    def get_symbol(self):
        return self.symbol
"""
            
            cases.append({
                'case_id': f"fidelity_{i:04d}",
                'symbol': symbol,
                'domain': domain,
                'code_type': 'complex',
                'code': complex_code.strip(),
                'expected_symbols': [symbol],
                'expected_domains': [domain]
            })
        
        print(f"✅ Generati {len(cases)} test cases AST fidelity")
        return cases
    
    def save_test_cases(self, cases: List[Dict[str, Any]], filename: str):
        """Salva test cases su file."""
        output_path = self.output_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(cases, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Test cases salvati: {output_path}")
    
    def generate_fixtures_list(self):
        """Genera lista fixture per PyTest."""
        fixtures_list = {
            'total_symbols': len(self.symbols),
            'domains': list(set(s['domain'] for s in self.symbols)),
            'symbols_by_domain': {},
            'god_mode_symbols': [s['symbol'] for s in self.symbols if s['god_mode_certified']],
            'test_files': [
                'roundtrip_cases.json',
                'ast_fidelity_cases.json'
            ]
        }
        
        # Raggruppa per dominio
        for symbol_data in self.symbols:
            domain = symbol_data['domain']
            if domain not in fixtures_list['symbols_by_domain']:
                fixtures_list['symbols_by_domain'][domain] = []
            fixtures_list['symbols_by_domain'][domain].append(symbol_data['symbol'])
        
        # Salva fixtures list
        fixtures_path = self.output_dir / 'fixtures_list.json'
        with open(fixtures_path, 'w', encoding='utf-8') as f:
            json.dump(fixtures_list, f, ensure_ascii=False, indent=2)
        
        print(f"📋 Fixtures list salvata: {fixtures_path}")
        
        # Statistiche
        print(f"\n📊 Statistiche:")
        print(f"   - Simboli totali: {fixtures_list['total_symbols']}")
        print(f"   - Domini: {len(fixtures_list['domains'])}")
        print(f"   - GOD mode certified: {len(fixtures_list['god_mode_symbols'])}")
        
        for domain, symbols in fixtures_list['symbols_by_domain'].items():
            print(f"   - {domain}: {len(symbols)} simboli")
    
    def run_generation(self):
        """Esegue generazione completa."""
        print("🚀 NEUROGLYPH Symbol Test Cases Generator")
        print("=" * 50)
        
        # Genera test cases
        roundtrip_cases = self.generate_roundtrip_cases()
        ast_fidelity_cases = self.generate_ast_fidelity_cases()
        
        # Salva test cases
        self.save_test_cases(roundtrip_cases, 'roundtrip_cases.json')
        self.save_test_cases(ast_fidelity_cases, 'ast_fidelity_cases.json')
        
        # Genera fixtures list
        self.generate_fixtures_list()
        
        print(f"\n✅ Generazione completata!")
        print(f"   - Test cases round-trip: {len(roundtrip_cases)}")
        print(f"   - Test cases AST fidelity: {len(ast_fidelity_cases)}")
        print(f"   - Output directory: {self.output_dir}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Symbol Test Cases Generator")
    parser.add_argument('--registry', default='data/symbols/registry_unicode_only.json',
                       help='Path al registry Unicode')
    parser.add_argument('--output', default='tests/fixtures',
                       help='Directory output per fixture')
    
    args = parser.parse_args()
    
    generator = SymbolTestCaseGenerator(args.registry, args.output)
    generator.run_generation()
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
