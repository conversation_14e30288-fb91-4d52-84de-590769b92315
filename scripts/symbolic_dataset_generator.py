#!/usr/bin/env python3
"""
NEUROGLYPH Symbolic Dataset Generator
Fase 1 - Generazione Dataset Simbolici Nativi

Genera dataset di training basati sul registry simbolico NEUROGLYPH
per creare esempi di reasoning simbolico puro, senza dipendenze da dataset esterni.

Caratteristiche:
- Usa solo simboli dal registry NEUROGLYPH (7,767 simboli)
- Genera prompt e risposte in formato simbolico
- Crea esempi per logical reasoning, mathematical reasoning, code generation
- Garantisce mappatura 1:1 simbolo→token
- Produce dataset AST-aligned e semanticamente coerenti
"""

import json
import random
import uuid
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SymbolicExample:
    """Singolo esempio simbolico per training."""
    id: str
    category: str  # logical_reasoning, mathematical_reasoning, code_generation, etc.
    cognitive_tags: List[str]  # compression, deduction, proof_step, etc.
    prompt_symbolic: str  # Prompt con simboli NEUROGLYPH
    response_symbolic: str  # Risposta con simboli NEUROGLYPH
    prompt_natural: str  # Versione naturale del prompt
    response_natural: str  # Versione naturale della risposta
    symbols_used: List[str]  # Lista simboli utilizzati
    ast_structure: Optional[Dict] = None  # Struttura AST se applicabile
    difficulty: str = "medium"  # easy, medium, hard
    reasoning_steps: int = 1  # Numero di step di reasoning
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SymbolicDatasetConfig:
    """Configurazione per generazione dataset simbolico."""
    total_examples: int = 10000
    categories: Dict[str, int] = field(default_factory=lambda: {
        "logical_reasoning": 3000,
        "mathematical_reasoning": 3000,
        "code_generation": 2000,
        "symbolic_compression": 1000,
        "meta_reasoning": 1000
    })
    difficulty_distribution: Dict[str, float] = field(default_factory=lambda: {
        "easy": 0.3,
        "medium": 0.5,
        "hard": 0.2
    })
    min_symbols_per_example: int = 3
    max_symbols_per_example: int = 15
    min_reasoning_steps: int = 1
    max_reasoning_steps: int = 8


class SymbolicDatasetGenerator:
    """
    Generatore principale per dataset simbolici NEUROGLYPH.
    """
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il generatore.
        
        Args:
            registry_path: Percorso al registry simbolico
        """
        self.registry_path = Path(registry_path)
        self.registry = self._load_registry()
        self.symbols_by_domain = self._organize_symbols_by_domain()
        self.logical_symbols = self._get_logical_symbols()
        self.mathematical_symbols = self._get_mathematical_symbols()
        self.code_symbols = self._get_code_symbols()
        
        logger.info(f"🔮 SymbolicDatasetGenerator inizializzato")
        logger.info(f"   - Registry caricato: {len(self.registry)} simboli")
        logger.info(f"   - Domini disponibili: {len(self.symbols_by_domain)}")
        logger.info(f"   - Simboli logici: {len(self.logical_symbols)}")
        logger.info(f"   - Simboli matematici: {len(self.mathematical_symbols)}")
        logger.info(f"   - Simboli code: {len(self.code_symbols)}")
    
    def _load_registry(self) -> Dict[str, Any]:
        """Carica il registry simbolico."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data.get('approved_symbols', [])
        except Exception as e:
            logger.error(f"Errore caricamento registry: {e}")
            return []
    
    def _organize_symbols_by_domain(self) -> Dict[str, List[Dict]]:
        """Organizza simboli per dominio."""
        domains = {}
        for symbol_data in self.registry:
            domain = symbol_data.get('domain', 'unknown')
            if domain not in domains:
                domains[domain] = []
            domains[domain].append(symbol_data)
        return domains
    
    def _get_logical_symbols(self) -> List[Dict]:
        """Estrae simboli per logical reasoning."""
        logical_domains = ['logic', 'logical_operators', 'formal_verification']
        symbols = []
        for domain in logical_domains:
            symbols.extend(self.symbols_by_domain.get(domain, []))
        
        # Aggiungi simboli logici specifici
        logical_unicode = ['∀', '∃', '⊢', '⊥', '⇒', '∧', '∨', '¬', '⇔', '⊨']
        for symbol_data in self.registry:
            if symbol_data.get('symbol') in logical_unicode:
                symbols.append(symbol_data)
        
        return symbols
    
    def _get_mathematical_symbols(self) -> List[Dict]:
        """Estrae simboli per mathematical reasoning."""
        math_domains = ['math', 'mathematical_operators', 'calculus', 'algebra', 'geometry']
        symbols = []
        for domain in math_domains:
            symbols.extend(self.symbols_by_domain.get(domain, []))
        
        # Aggiungi simboli matematici specifici
        math_unicode = ['∫', '∑', '∏', '∂', '∇', '≈', '≠', '≤', '≥', '±', '∞']
        for symbol_data in self.registry:
            if symbol_data.get('symbol') in math_unicode:
                symbols.append(symbol_data)
        
        return symbols
    
    def _get_code_symbols(self) -> List[Dict]:
        """Estrae simboli per code generation."""
        code_domains = ['code', 'advanced_coding', 'control_flow', 'concurrency_advanced']
        symbols = []
        for domain in code_domains:
            symbols.extend(self.symbols_by_domain.get(domain, []))
        return symbols
    
    def generate_logical_reasoning_example(self, difficulty: str = "medium") -> SymbolicExample:
        """
        Genera esempio di logical reasoning simbolico.
        
        Args:
            difficulty: Livello di difficoltà
            
        Returns:
            Esempio simbolico di logical reasoning
        """
        # Seleziona simboli logici
        num_symbols = random.randint(3, 8 if difficulty == "easy" else 12)
        selected_symbols = random.sample(self.logical_symbols, min(num_symbols, len(self.logical_symbols)))
        
        # Crea proposizioni simboliche
        max_props = min(5, max(3, num_symbols//2 + 1))
        propositions = ['P', 'Q', 'R', 'S', 'T'][:max_props]
        
        # Genera regole logiche
        rules = []
        conclusions = []
        
        if difficulty == "easy":
            # Modus ponens semplice: P, P⇒Q ⊢ Q
            p1, p2 = random.sample(propositions, 2)
            rules.append(f"{p1}")
            rules.append(f"{p1} ⇒ {p2}")
            conclusions.append(f"⊢ {p2}")
            reasoning_steps = 1
            
        elif difficulty == "medium":
            # Chain reasoning: P⇒Q, Q⇒R, P ⊢ R
            if len(propositions) >= 3:
                p1, p2, p3 = random.sample(propositions, 3)
            else:
                p1, p2, p3 = propositions[0], propositions[1], propositions[0] + "'"
            rules.append(f"{p1} ⇒ {p2}")
            rules.append(f"{p2} ⇒ {p3}")
            rules.append(f"{p1}")
            conclusions.append(f"⊢ {p2}")  # Step intermedio
            conclusions.append(f"⊢ {p3}")  # Conclusione finale
            reasoning_steps = 2
            
        else:  # hard
            # Complex reasoning con quantificatori
            if len(propositions) >= 3:
                p1, p2, p3 = random.sample(propositions, 3)
            else:
                p1, p2, p3 = propositions[0], propositions[1], propositions[0] + "'"
            rules.append(f"∀x ({p1}(x) ⇒ {p2}(x))")
            rules.append(f"∃x {p1}(x)")
            rules.append(f"∀x ({p2}(x) ⇒ {p3}(x))")
            conclusions.append(f"⊢ ∃x {p2}(x)")
            conclusions.append(f"⊢ ∃x {p3}(x)")
            reasoning_steps = 3
        
        # Costruisci prompt simbolico
        prompt_symbolic = "🧠 " + " ∧ ".join(rules) + " ⊢ ?"
        response_symbolic = " ∧ ".join(conclusions)
        
        # Versioni naturali
        prompt_natural = f"Given the logical premises: {', '.join(rules)}, what can we conclude?"
        response_natural = f"We can conclude: {', '.join(conclusions)}"
        
        # Simboli utilizzati
        symbols_used = [s['symbol'] for s in selected_symbols if s['symbol'] in prompt_symbolic + response_symbolic]
        symbols_used.extend(['⊢', '∧', '⇒', '∀', '∃'])
        symbols_used = list(set(symbols_used))
        
        return SymbolicExample(
            id=f"logical_{uuid.uuid4().hex[:8]}",
            category="logical_reasoning",
            cognitive_tags=["deduction", "logical_inference", "symbolic_reasoning"],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=symbols_used,
            difficulty=difficulty,
            reasoning_steps=reasoning_steps,
            metadata={
                "reasoning_type": "deductive",
                "logical_operators": ["⊢", "⇒", "∧"],
                "propositions": propositions[:reasoning_steps+1]
            }
        )
    
    def generate_mathematical_reasoning_example(self, difficulty: str = "medium") -> SymbolicExample:
        """
        Genera esempio di mathematical reasoning simbolico.
        
        Args:
            difficulty: Livello di difficoltà
            
        Returns:
            Esempio simbolico di mathematical reasoning
        """
        # Seleziona simboli matematici
        selected_symbols = random.sample(self.mathematical_symbols, min(5, len(self.mathematical_symbols)))
        
        if difficulty == "easy":
            # Equazione lineare semplice: x + 3 = 7 ⊢ x = 4
            a, b = random.randint(1, 10), random.randint(1, 10)
            result = b - a
            prompt_symbolic = f"🔢 x + {a} = {b} ⊢ x = ?"
            response_symbolic = f"⊢ x = {result}"
            reasoning_steps = 1
            
        elif difficulty == "medium":
            # Sistema di equazioni: 2x + y = 7, x - y = 2 ⊢ x = 3, y = 1
            prompt_symbolic = "🔢 2x + y = 7 ∧ x - y = 2 ⊢ (x, y) = ?"
            response_symbolic = "⊢ x = 3 ∧ y = 1"
            reasoning_steps = 2
            
        else:  # hard
            # Calcolo integrale: ∫ x² dx ⊢ x³/3 + C
            prompt_symbolic = "🔢 ∫ x² dx ⊢ ?"
            response_symbolic = "⊢ x³/3 + C"
            reasoning_steps = 3
        
        # Versioni naturali
        prompt_natural = f"Solve the mathematical problem: {prompt_symbolic.replace('🔢 ', '').replace(' ⊢ ?', '')}"
        response_natural = f"Solution: {response_symbolic.replace('⊢ ', '')}"
        
        # Simboli utilizzati
        symbols_used = [s['symbol'] for s in selected_symbols if s['symbol'] in prompt_symbolic + response_symbolic]
        symbols_used.extend(['⊢', '∧', '∫'])
        symbols_used = list(set(symbols_used))
        
        return SymbolicExample(
            id=f"math_{uuid.uuid4().hex[:8]}",
            category="mathematical_reasoning",
            cognitive_tags=["calculation", "algebraic_manipulation", "symbolic_math"],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=symbols_used,
            difficulty=difficulty,
            reasoning_steps=reasoning_steps,
            metadata={
                "math_type": "algebra" if difficulty != "hard" else "calculus",
                "operations": ["addition", "subtraction"] if difficulty == "easy" else ["integration"]
            }
        )
    
    def generate_code_generation_example(self, difficulty: str = "medium") -> SymbolicExample:
        """
        Genera esempio di code generation simbolico.
        
        Args:
            difficulty: Livello di difficoltà
            
        Returns:
            Esempio simbolico di code generation
        """
        # Seleziona simboli code
        selected_symbols = random.sample(self.code_symbols, min(6, len(self.code_symbols)))
        
        if difficulty == "easy":
            # Funzione semplice
            prompt_symbolic = "⚡ def add(a, b): ⤴ a + b"
            response_symbolic = "⚡ ⟦a, b⟧ → ⤴ a + b"
            code_natural = "def add(a, b):\n    return a + b"
            reasoning_steps = 1
            
        elif difficulty == "medium":
            # Funzione con loop
            prompt_symbolic = "⚡ def sum_list(lst): 🔄 item in lst → ⤴ sum"
            response_symbolic = "⚡ ⟦lst⟧ → 🔄 ⟦sum = 0⟧ → ⟦sum += item⟧ → ⤴ sum"
            code_natural = "def sum_list(lst):\n    sum = 0\n    for item in lst:\n        sum += item\n    return sum"
            reasoning_steps = 2
            
        else:  # hard
            # Classe con metodi
            prompt_symbolic = "🏛️ Calculator: ⚡ add(a,b) ∧ ⚡ multiply(a,b)"
            response_symbolic = "🏛️ ⟦Calculator⟧ → ⚡ add ⟦a,b⟧ → ⤴ a+b ∧ ⚡ multiply ⟦a,b⟧ → ⤴ a*b"
            code_natural = "class Calculator:\n    def add(self, a, b):\n        return a + b\n    def multiply(self, a, b):\n        return a * b"
            reasoning_steps = 3
        
        # Versioni naturali
        prompt_natural = f"Generate code for: {code_natural.split(':')[0] if ':' in code_natural else code_natural[:50]}..."
        response_natural = f"Code:\n{code_natural}"
        
        # Simboli utilizzati
        symbols_used = [s['symbol'] for s in selected_symbols if s['symbol'] in prompt_symbolic + response_symbolic]
        symbols_used.extend(['⚡', '🔄', '🏛️', '⤴', '⟦', '⟧'])
        symbols_used = list(set(symbols_used))
        
        return SymbolicExample(
            id=f"code_{uuid.uuid4().hex[:8]}",
            category="code_generation",
            cognitive_tags=["code_synthesis", "algorithmic_thinking", "symbolic_programming"],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=symbols_used,
            difficulty=difficulty,
            reasoning_steps=reasoning_steps,
            metadata={
                "code_type": "function" if difficulty != "hard" else "class",
                "constructs": ["function"] if difficulty == "easy" else ["function", "loop"] if difficulty == "medium" else ["class", "method"]
            }
        )
    
    def generate_dataset(self, config: SymbolicDatasetConfig) -> List[SymbolicExample]:
        """
        Genera dataset completo secondo configurazione.
        
        Args:
            config: Configurazione del dataset
            
        Returns:
            Lista di esempi simbolici
        """
        dataset = []
        
        logger.info(f"🔮 Generando dataset simbolico...")
        logger.info(f"   - Esempi totali: {config.total_examples}")
        logger.info(f"   - Categorie: {config.categories}")
        
        for category, count in config.categories.items():
            logger.info(f"📊 Generando {count} esempi per {category}")
            
            for i in range(count):
                # Determina difficoltà
                rand = random.random()
                if rand < config.difficulty_distribution["easy"]:
                    difficulty = "easy"
                elif rand < config.difficulty_distribution["easy"] + config.difficulty_distribution["medium"]:
                    difficulty = "medium"
                else:
                    difficulty = "hard"
                
                # Genera esempio basato su categoria
                if category == "logical_reasoning":
                    example = self.generate_logical_reasoning_example(difficulty)
                elif category == "mathematical_reasoning":
                    example = self.generate_mathematical_reasoning_example(difficulty)
                elif category == "code_generation":
                    example = self.generate_code_generation_example(difficulty)
                elif category == "symbolic_compression":
                    # TODO: Implementare symbolic compression
                    example = self.generate_logical_reasoning_example(difficulty)
                    example.category = "symbolic_compression"
                    example.cognitive_tags = ["compression", "symbolic_encoding"]
                elif category == "meta_reasoning":
                    # TODO: Implementare meta reasoning
                    example = self.generate_logical_reasoning_example(difficulty)
                    example.category = "meta_reasoning"
                    example.cognitive_tags = ["meta_cognition", "reasoning_about_reasoning"]
                else:
                    continue
                
                dataset.append(example)
                
                if (i + 1) % 100 == 0:
                    logger.info(f"   - Generati {i + 1}/{count} esempi per {category}")
        
        logger.info(f"✅ Dataset simbolico generato: {len(dataset)} esempi")
        return dataset
    
    def save_dataset(self, dataset: List[SymbolicExample], output_path: str):
        """
        Salva dataset in formato JSONL.
        
        Args:
            dataset: Lista di esempi simbolici
            output_path: Percorso file di output
        """
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for example in dataset:
                # Converti in dict per serializzazione
                example_dict = {
                    'id': example.id,
                    'category': example.category,
                    'cognitive_tags': example.cognitive_tags,
                    'prompt_symbolic': example.prompt_symbolic,
                    'response_symbolic': example.response_symbolic,
                    'prompt_natural': example.prompt_natural,
                    'response_natural': example.response_natural,
                    'symbols_used': example.symbols_used,
                    'difficulty': example.difficulty,
                    'reasoning_steps': example.reasoning_steps,
                    'metadata': example.metadata,
                    'generated_at': datetime.now().isoformat()
                }
                f.write(json.dumps(example_dict, ensure_ascii=False) + '\n')
        
        logger.info(f"💾 Dataset salvato: {output_file} ({len(dataset)} esempi)")


def main():
    """Pipeline principale di generazione dataset simbolico."""
    print("🔮 NEUROGLYPH Symbolic Dataset Generator")
    print("=" * 60)
    
    # Configurazione
    config = SymbolicDatasetConfig(
        total_examples=1000,  # Start small per testing
        categories={
            "logical_reasoning": 300,
            "mathematical_reasoning": 300,
            "code_generation": 200,
            "symbolic_compression": 100,
            "meta_reasoning": 100
        }
    )
    
    # Genera dataset
    generator = SymbolicDatasetGenerator()
    dataset = generator.generate_dataset(config)
    
    # Salva dataset
    output_path = "data/datasets/symbolic/neuroglyph_symbolic_dataset.jsonl"
    generator.save_dataset(dataset, output_path)
    
    # Statistiche finali
    print(f"\n📊 STATISTICHE DATASET SIMBOLICO")
    print("=" * 60)
    
    categories = {}
    difficulties = {}
    total_symbols = set()
    
    for example in dataset:
        categories[example.category] = categories.get(example.category, 0) + 1
        difficulties[example.difficulty] = difficulties.get(example.difficulty, 0) + 1
        total_symbols.update(example.symbols_used)
    
    print(f"📈 Esempi per categoria:")
    for cat, count in categories.items():
        print(f"   - {cat}: {count}")
    
    print(f"\n📊 Distribuzione difficoltà:")
    for diff, count in difficulties.items():
        print(f"   - {diff}: {count}")
    
    print(f"\n🔮 Simboli unici utilizzati: {len(total_symbols)}")
    print(f"📁 Dataset salvato in: {output_path}")
    print(f"\n🎉 Generazione dataset simbolico completata!")


if __name__ == "__main__":
    main()
