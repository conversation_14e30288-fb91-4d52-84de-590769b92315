#!/usr/bin/env python3
"""
Script di migrazione da adaptive_learning_state.json a SQLite PerformanceStorage.
Esegue migrazione una-tantum dei dati esistenti.
"""

import json
import time
import sys
from pathlib import Path

# Aggiungi path per import neuroglyph
sys.path.insert(0, str(Path(__file__).parent.parent))

from neuroglyph.cognitive.performance_storage import PerformanceStorage


def migrate_json_to_sqlite(json_path: str = "adaptive_learning_state.json", 
                          db_path: str = "learning.db"):
    """
    Migra dati da JSON a SQLite PerformanceStorage.
    
    Args:
        json_path: Percorso file JSON esistente
        db_path: Percorso database SQLite di destinazione
    """
    print(f"🔄 Migrazione {json_path} → {db_path}")
    
    # Controlla se file JSON esiste
    json_file = Path(json_path)
    if not json_file.exists():
        print(f"⚠️ File JSON non trovato: {json_path}")
        print("   Creazione database SQLite vuoto...")
        storage = PerformanceStorage(db_path)
        storage.shutdown()
        print("✅ Database SQLite vuoto creato")
        return
    
    # Carica dati JSON
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Caricato JSON: {len(data)} chiavi")
    except Exception as e:
        print(f"❌ Errore caricamento JSON: {e}")
        return
    
    # Inizializza storage SQLite
    storage = PerformanceStorage(db_path)
    
    # Migra statistiche come patch history
    migrated_patches = 0
    migrated_patterns = 0
    
    try:
        # Migra contatori come patch history sintetici
        if 'total_errors_seen' in data:
            for i in range(data['total_errors_seen']):
                storage.append_patch_history(
                    error_type="migrated_error",
                    status="seen",
                    metadata={"source": "json_migration", "index": i}
                )
                migrated_patches += 1
        
        if 'total_patches_successful' in data:
            for i in range(data['total_patches_successful']):
                storage.append_patch_history(
                    error_type="migrated_patch",
                    status="successful",
                    metadata={"source": "json_migration", "index": i}
                )
                migrated_patches += 1
        
        # Migra learned patterns se presenti
        if 'learned_patterns' in data and isinstance(data['learned_patterns'], list):
            for i, pattern in enumerate(data['learned_patterns']):
                if isinstance(pattern, dict):
                    storage.append_learned_pattern(
                        pattern=pattern.get('pattern_name', f'migrated_pattern_{i}'),
                        metadata={
                            "source": "json_migration",
                            "original_data": pattern
                        },
                        effectiveness=pattern.get('effectiveness_score', 0.5)
                    )
                    migrated_patterns += 1
        
        # Flush finale
        storage.force_flush()
        
        print(f"✅ Migrazione completata:")
        print(f"   - Patch history: {migrated_patches} record")
        print(f"   - Learned patterns: {migrated_patterns} record")
        
        # Mostra statistiche finali
        stats = storage.get_statistics()
        print(f"📊 Statistiche finali: {stats}")
        
        # Mostra info database
        db_info = storage.get_database_info()
        print(f"💾 Database info: {db_info}")
        
    except Exception as e:
        print(f"❌ Errore migrazione: {e}")
        return
    
    finally:
        storage.shutdown()
    
    # Backup file JSON originale
    backup_path = f"{json_path}.backup"
    try:
        json_file.rename(backup_path)
        print(f"✅ Backup JSON creato: {backup_path}")
    except Exception as e:
        print(f"⚠️ Errore backup JSON: {e}")
    
    print("🎉 Migrazione completata con successo!")


def test_performance(db_path: str = "learning.db", num_records: int = 1000):
    """
    Test performance del nuovo storage engine.
    
    Args:
        db_path: Percorso database
        num_records: Numero record per test
    """
    print(f"🧪 Test performance con {num_records} record...")
    
    storage = PerformanceStorage(db_path)
    
    try:
        # Test append performance
        start_time = time.perf_counter()
        
        for i in range(num_records):
            storage.append_patch_history(
                error_type=f"test_error_{i % 10}",
                status="successful" if i % 2 == 0 else "failed",
                metadata={"test_index": i, "batch": "performance_test"}
            )
        
        append_time = time.perf_counter() - start_time
        avg_append_ms = (append_time / num_records) * 1000
        
        print(f"✅ Append performance:")
        print(f"   - Total time: {append_time:.3f}s")
        print(f"   - Avg per record: {avg_append_ms:.3f}ms")
        print(f"   - Target ≤1ms: {'✅ PASS' if avg_append_ms <= 1.0 else '❌ FAIL'}")
        
        # Flush per assicurare persistenza
        storage.force_flush()
        
        # Test retrieve performance
        start_time = time.perf_counter()
        
        for i in range(100):  # 100 query random
            error_type = f"test_error_{i % 10}"
            results = storage.get_patch_history(error_type=error_type, limit=50)
        
        retrieve_time = time.perf_counter() - start_time
        avg_retrieve_ms = (retrieve_time / 100) * 1000
        
        print(f"✅ Retrieve performance:")
        print(f"   - Total time: {retrieve_time:.3f}s")
        print(f"   - Avg per query: {avg_retrieve_ms:.3f}ms")
        print(f"   - Target ≤5ms: {'✅ PASS' if avg_retrieve_ms <= 5.0 else '❌ FAIL'}")
        
        # Test statistiche hot-path
        start_time = time.perf_counter()
        
        for i in range(1000):
            stats = storage.get_statistics()
        
        stats_time = time.perf_counter() - start_time
        avg_stats_ms = (stats_time / 1000) * 1000
        
        print(f"✅ Statistics performance:")
        print(f"   - Total time: {stats_time:.3f}s")
        print(f"   - Avg per call: {avg_stats_ms:.3f}ms")
        print(f"   - Target ≤0.1ms: {'✅ PASS' if avg_stats_ms <= 0.1 else '❌ FAIL'}")
        
        # Mostra database info finale
        db_info = storage.get_database_info()
        print(f"💾 Database finale: {db_info['db_size_mb']}MB, {db_info['patch_history_count']} records")
        
    except Exception as e:
        print(f"❌ Errore test performance: {e}")
    
    finally:
        storage.shutdown()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrazione JSON → SQLite per NEUROGLYPH")
    parser.add_argument("--json", default="adaptive_learning_state.json", 
                       help="Percorso file JSON (default: adaptive_learning_state.json)")
    parser.add_argument("--db", default="learning.db", 
                       help="Percorso database SQLite (default: learning.db)")
    parser.add_argument("--test", action="store_true", 
                       help="Esegui test performance dopo migrazione")
    parser.add_argument("--test-records", type=int, default=1000,
                       help="Numero record per test performance (default: 1000)")
    
    args = parser.parse_args()
    
    # Esegui migrazione
    migrate_json_to_sqlite(args.json, args.db)
    
    # Esegui test performance se richiesto
    if args.test:
        test_performance(args.db, args.test_records)
    
    print("🚀 Operazione completata!")
