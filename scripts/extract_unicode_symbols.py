#!/usr/bin/env python3
"""
NEUROGLYPH Unicode Symbol Extractor
Estrae solo simboli Unicode reali dal registry ULTIMATE
"""

import json
import sys
import os
import hashlib
import unicodedata
from pathlib import Path
from typing import Dict, List, Set
import argparse

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))


class UnicodeSymbolExtractor:
    """Estrattore simboli Unicode dal registry ULTIMATE."""
    
    def __init__(self, ultimate_registry_path: str):
        """
        Inizializza estrattore.
        
        Args:
            ultimate_registry_path: Path al registry ULTIMATE
        """
        self.ultimate_registry_path = ultimate_registry_path
        self.unicode_symbols = []
        self.identifier_symbols = []
        
        print(f"🔍 Caricamento registry ULTIMATE: {ultimate_registry_path}")
    
    def is_unicode_symbol(self, symbol: str) -> bool:
        """Verifica se è un simbolo Unicode reale."""
        if not symbol or len(symbol) == 0:
            return False
        
        # Escludi identificatori ng:domain:name
        if symbol.startswith('ng:'):
            return False
        
        # Deve essere Unicode printable
        try:
            for char in symbol:
                if not char.isprintable():
                    return False
                
                # Escludi ASCII base (lettere/numeri normali)
                if ord(char) < 128 and char.isalnum():
                    return False
            
            # Deve avere lunghezza ragionevole per simbolo Unicode
            if len(symbol) > 4:
                return False
            
            return True
            
        except (ValueError, TypeError):
            return False
    
    def extract_symbols(self) -> Dict:
        """Estrae simboli Unicode dal registry ULTIMATE."""
        print("🔍 Estrazione simboli Unicode...")
        
        # Carica registry ULTIMATE
        try:
            with open(self.ultimate_registry_path, 'r', encoding='utf-8') as f:
                ultimate_data = json.load(f)
        except Exception as e:
            print(f"❌ Errore caricamento registry: {e}")
            return {}
        
        if 'approved_symbols' not in ultimate_data:
            print("❌ Registry ULTIMATE non valido - manca 'approved_symbols'")
            return {}
        
        # Estrai simboli
        unicode_count = 0
        identifier_count = 0
        domain_groups = {}
        
        for entry in ultimate_data['approved_symbols']:
            if not isinstance(entry, dict) or 'symbol' not in entry:
                continue
            
            symbol = entry['symbol']
            domain = entry.get('domain', 'unknown')
            
            if self.is_unicode_symbol(symbol):
                # Simbolo Unicode reale
                unicode_count += 1
                
                if domain not in domain_groups:
                    domain_groups[domain] = []
                
                unicode_entry = {
                    'symbol': symbol,
                    'id': entry.get('id', ''),
                    'domain': domain,
                    'fallback': entry.get('fallback', ''),
                    'unicode_point': entry.get('unicode_point', ''),
                    'meaning': entry.get('meaning', ''),
                    'score': entry.get('score', 0),
                    'tier': entry.get('tier', ''),
                    'god_mode_certified': entry.get('god_mode_certified', False)
                }
                
                domain_groups[domain].append(unicode_entry)
                self.unicode_symbols.append(unicode_entry)
                
            else:
                # Identificatore simbolico
                identifier_count += 1
                self.identifier_symbols.append({
                    'symbol': symbol,
                    'id': entry.get('id', ''),
                    'domain': domain
                })
        
        print(f"✅ Estratti {unicode_count} simboli Unicode reali")
        print(f"⚠️ Ignorati {identifier_count} identificatori simbolici")
        
        # Crea registry pulito
        clean_registry = {
            'version': '4.0_UNICODE_ONLY',
            'creation_date': __import__('datetime').datetime.now().isoformat(),
            'description': 'NEUROGLYPH Unicode Symbols Only - Extracted from ULTIMATE registry',
            'source': 'neuroglyph_ULTIMATE_registry.json',
            'stats': {
                'total_unicode_symbols': unicode_count,
                'total_identifier_symbols': identifier_count,
                'domains': len(domain_groups),
                'domain_distribution': {
                    domain: len(symbols) for domain, symbols in domain_groups.items()
                }
            },
            'unicode_symbols': domain_groups,
            '_metadata': {
                'extraction_timestamp': __import__('time').time(),
                'hash': self._calculate_hash(self.unicode_symbols)
            }
        }
        
        return clean_registry
    
    def _calculate_hash(self, symbols: List[Dict]) -> str:
        """Calcola hash SHA256 dei simboli."""
        symbol_strings = [entry['symbol'] for entry in symbols]
        symbols_str = ''.join(sorted(symbol_strings))
        return hashlib.sha256(symbols_str.encode('utf-8')).hexdigest()
    
    def save_clean_registry(self, clean_registry: Dict, output_path: str):
        """Salva registry pulito."""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(clean_registry, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Registry Unicode salvato: {output_path}")
        
        # Salva anche lista simboli semplice
        simple_path = output_path.replace('.json', '_symbols_only.txt')
        with open(simple_path, 'w', encoding='utf-8') as f:
            for entry in self.unicode_symbols:
                f.write(f"{entry['symbol']}\n")
        
        print(f"💾 Lista simboli semplice: {simple_path}")
    
    def generate_statistics(self, clean_registry: Dict):
        """Genera statistiche dettagliate."""
        print("\n📊 Statistiche Estrazione:")
        print("=" * 50)
        
        stats = clean_registry['stats']
        print(f"Simboli Unicode totali: {stats['total_unicode_symbols']}")
        print(f"Identificatori ignorati: {stats['total_identifier_symbols']}")
        print(f"Domini: {stats['domains']}")
        
        print(f"\n🏆 Top 10 domini per simboli:")
        domain_dist = stats['domain_distribution']
        sorted_domains = sorted(domain_dist.items(), key=lambda x: x[1], reverse=True)
        
        for domain, count in sorted_domains[:10]:
            print(f"  - {domain}: {count} simboli")
        
        # Analisi tier
        tier_counts = {}
        god_mode_count = 0
        
        for entry in self.unicode_symbols:
            tier = entry.get('tier', 'unknown')
            tier_counts[tier] = tier_counts.get(tier, 0) + 1
            
            if entry.get('god_mode_certified', False):
                god_mode_count += 1
        
        print(f"\n🎯 Distribuzione tier:")
        for tier, count in sorted(tier_counts.items()):
            print(f"  - {tier}: {count} simboli")
        
        print(f"\n🔥 GOD mode certified: {god_mode_count} simboli")
        
        # Hash per verifica integrità
        hash_value = clean_registry['_metadata']['hash']
        print(f"\n🔒 Hash integrità: {hash_value[:16]}...")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="NEUROGLYPH Unicode Symbol Extractor")
    parser.add_argument('--input', default='neuroglyph_ULTIMATE_registry.json',
                       help='Registry ULTIMATE di input')
    parser.add_argument('--output', default='data/symbols/registry_unicode_only.json',
                       help='Registry Unicode di output')
    parser.add_argument('--stats', action='store_true',
                       help='Mostra statistiche dettagliate')
    
    args = parser.parse_args()
    
    print("🚀 NEUROGLYPH Unicode Symbol Extractor")
    print("=" * 50)
    
    if not os.path.exists(args.input):
        print(f"❌ File input non trovato: {args.input}")
        return 1
    
    extractor = UnicodeSymbolExtractor(args.input)
    clean_registry = extractor.extract_symbols()
    
    if not clean_registry:
        print("❌ Estrazione fallita")
        return 1
    
    extractor.save_clean_registry(clean_registry, args.output)
    
    if args.stats:
        extractor.generate_statistics(clean_registry)
    
    print(f"\n✅ Estrazione completata: {args.output}")
    return 0


if __name__ == '__main__':
    sys.exit(main())
