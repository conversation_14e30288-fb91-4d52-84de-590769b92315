#!/usr/bin/env python3
"""
NEUROGLYPH End-to-End Benchmark

Benchmark completo per validare performance dell'intera pipeline
NEUROGLYPH: Logic → Math → Code Generation.
"""

import time
import sys
import os
import statistics
from pathlib import Path
from typing import Dict, List, Any
import json
import psutil

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))

from neuroglyph.logic.logic_engine import FormalLogicEngine
from neuroglyph.symbolic_math.symbolic_math_engine import SymbolicMathEngine
from neuroglyph.cognitive.ng_decoder import NGDecoder
from neuroglyph.logic.proof_tree import ProofTree, ProofStep
from neuroglyph.logic.formula import Predicate, Variable, Implication


class EndToEndBenchmark:
    """Benchmark end-to-end per NEUROGLYPH."""
    
    def __init__(self):
        """Inizializza benchmark."""
        self.logic_engine = None
        self.math_engine = None
        self.decoder = None
        
        # Metriche
        self.results = {
            "initialization": {},
            "single_request": {},
            "batch_processing": {},
            "memory_usage": {},
            "stress_test": {}
        }
    
    def setup_engines(self):
        """Inizializza tutti i motori."""
        print("🔧 Initializing NEUROGLYPH engines...")
        
        start_time = time.time()
        self.logic_engine = FormalLogicEngine(max_depth=12, max_steps=200, enable_memoization=True)
        logic_init_time = time.time() - start_time
        
        start_time = time.time()
        self.math_engine = SymbolicMathEngine(enable_cache=True, cache_size=1000)
        math_init_time = time.time() - start_time
        
        start_time = time.time()
        self.decoder = NGDecoder()
        decoder_init_time = time.time() - start_time
        
        self.results["initialization"] = {
            "logic_engine": logic_init_time,
            "math_engine": math_init_time,
            "decoder": decoder_init_time,
            "total": logic_init_time + math_init_time + decoder_init_time
        }
        
        print(f"✅ Engines initialized in {self.results['initialization']['total']:.3f}s")
    
    def benchmark_single_request(self, iterations: int = 100) -> Dict[str, Any]:
        """Benchmark singole richieste."""
        print(f"⚡ Benchmarking single requests ({iterations} iterations)...")
        
        # Test scenarios
        scenarios = [
            {
                "name": "simple_logic",
                "type": "logic",
                "data": {
                    "premises": [Predicate("P", [Variable("a")])],
                    "goal": Predicate("P", [Variable("a")])
                }
            },
            {
                "name": "math_simplify",
                "type": "math",
                "data": {"expression": "x**2 + 2*x + 1"}
            },
            {
                "name": "code_generation",
                "type": "code",
                "data": {"formula": "greatest_common_divisor"}
            }
        ]
        
        results = {}
        
        for scenario in scenarios:
            print(f"  Testing {scenario['name']}...")
            times = []
            successful = 0
            
            for i in range(iterations):
                start_time = time.time()
                
                try:
                    if scenario["type"] == "logic":
                        result = self.logic_engine.deduce(
                            scenario["data"]["premises"],
                            scenario["data"]["goal"]
                        )
                        if result.success:
                            successful += 1
                    
                    elif scenario["type"] == "math":
                        result = self.math_engine.simplify(scenario["data"]["expression"])
                        if result.success:
                            successful += 1
                    
                    elif scenario["type"] == "code":
                        steps = [ProofStep(
                            formula=Predicate(scenario["data"]["formula"], [Variable("x")]),
                            justification=f"Benchmark test {i}"
                        )]
                        proof = ProofTree(steps=steps)
                        result = self.decoder.generate_code_from_proof(proof)
                        if result.success:
                            successful += 1
                    
                    elapsed = time.time() - start_time
                    times.append(elapsed)
                    
                except Exception as e:
                    print(f"    ⚠️ Error in iteration {i}: {e}")
                    times.append(float('inf'))
            
            # Filtra tempi infiniti
            valid_times = [t for t in times if t != float('inf')]
            
            if valid_times:
                results[scenario["name"]] = {
                    "avg_time": statistics.mean(valid_times),
                    "median_time": statistics.median(valid_times),
                    "min_time": min(valid_times),
                    "max_time": max(valid_times),
                    "p95_time": statistics.quantiles(valid_times, n=20)[18] if len(valid_times) > 20 else max(valid_times),
                    "success_rate": successful / iterations,
                    "requests_per_second": 1 / statistics.mean(valid_times) if valid_times else 0
                }
                
                print(f"    ✅ {scenario['name']}: {results[scenario['name']]['avg_time']*1000:.1f}ms avg, "
                      f"{results[scenario['name']]['success_rate']:.1%} success")
            else:
                results[scenario["name"]] = {"error": "All requests failed"}
        
        return results
    
    def benchmark_batch_processing(self, batch_sizes: List[int] = [10, 50, 100, 200]) -> Dict[str, Any]:
        """Benchmark elaborazione batch."""
        print("📦 Benchmarking batch processing...")
        
        results = {}
        
        for batch_size in batch_sizes:
            print(f"  Testing batch size {batch_size}...")
            
            # Prepara batch di richieste code generation
            proofs = []
            formulas = ["greatest_common_divisor", "fibonacci", "factorial", "prime"]
            
            for i in range(batch_size):
                formula = formulas[i % len(formulas)]
                steps = [ProofStep(
                    formula=Predicate(formula, [Variable("x")]),
                    justification=f"Batch test {i}"
                )]
                proofs.append(ProofTree(steps=steps))
            
            # Misura tempo batch
            start_time = time.time()
            successful = 0
            
            for proof in proofs:
                try:
                    result = self.decoder.generate_code_from_proof(proof)
                    if result.success:
                        successful += 1
                except Exception:
                    pass
            
            total_time = time.time() - start_time
            
            results[f"batch_{batch_size}"] = {
                "total_time": total_time,
                "avg_time_per_request": total_time / batch_size,
                "throughput": batch_size / total_time,
                "success_rate": successful / batch_size
            }
            
            print(f"    ✅ Batch {batch_size}: {results[f'batch_{batch_size}']['throughput']:.1f} req/s, "
                  f"{results[f'batch_{batch_size}']['success_rate']:.1%} success")
        
        return results
    
    def benchmark_memory_usage(self) -> Dict[str, Any]:
        """Benchmark utilizzo memoria."""
        print("💾 Benchmarking memory usage...")
        
        process = psutil.Process()
        
        # Memoria baseline
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Stress test memoria
        proofs = []
        for i in range(1000):
            steps = [ProofStep(
                formula=Predicate(f"test_predicate_{i}", [Variable("x")]),
                justification=f"Memory test {i}"
            )]
            proofs.append(ProofTree(steps=steps))
        
        # Memoria dopo creazione oggetti
        after_creation_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Elabora tutti i proof
        for proof in proofs:
            try:
                self.decoder.generate_code_from_proof(proof)
            except Exception:
                pass
        
        # Memoria dopo elaborazione
        after_processing_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Cleanup
        del proofs
        
        # Memoria dopo cleanup
        after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            "baseline_mb": baseline_memory,
            "after_creation_mb": after_creation_memory,
            "after_processing_mb": after_processing_memory,
            "after_cleanup_mb": after_cleanup_memory,
            "peak_usage_mb": after_processing_memory,
            "memory_growth_mb": after_processing_memory - baseline_memory
        }
    
    def benchmark_stress_test(self, duration_seconds: int = 30) -> Dict[str, Any]:
        """Stress test per durata specificata."""
        print(f"🔥 Running stress test for {duration_seconds}s...")
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        total_requests = 0
        successful_requests = 0
        times = []
        
        formulas = ["greatest_common_divisor", "fibonacci", "factorial", "prime"]
        
        while time.time() < end_time:
            formula = formulas[total_requests % len(formulas)]
            
            request_start = time.time()
            
            try:
                steps = [ProofStep(
                    formula=Predicate(formula, [Variable("x")]),
                    justification=f"Stress test {total_requests}"
                )]
                proof = ProofTree(steps=steps)
                result = self.decoder.generate_code_from_proof(proof)
                
                if result.success:
                    successful_requests += 1
                
                request_time = time.time() - request_start
                times.append(request_time)
                
            except Exception:
                pass
            
            total_requests += 1
        
        actual_duration = time.time() - start_time
        
        return {
            "duration_seconds": actual_duration,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "success_rate": successful_requests / total_requests if total_requests > 0 else 0,
            "requests_per_second": total_requests / actual_duration,
            "avg_response_time": statistics.mean(times) if times else 0,
            "p95_response_time": statistics.quantiles(times, n=20)[18] if len(times) > 20 else max(times) if times else 0
        }
    
    def run_full_benchmark(self) -> Dict[str, Any]:
        """Esegue benchmark completo."""
        print("🚀 Starting NEUROGLYPH End-to-End Benchmark")
        print("=" * 60)
        
        # Setup
        self.setup_engines()
        
        # Benchmark individuali
        print("\n" + "=" * 60)
        self.results["single_request"] = self.benchmark_single_request()
        
        print("\n" + "=" * 60)
        self.results["batch_processing"] = self.benchmark_batch_processing()
        
        print("\n" + "=" * 60)
        self.results["memory_usage"] = self.benchmark_memory_usage()
        
        print("\n" + "=" * 60)
        self.results["stress_test"] = self.benchmark_stress_test()
        
        # Salva risultati
        output_file = Path("benchmark_results.json")
        with open(output_file, "w") as f:
            json.dump(self.results, f, indent=2, default=str)
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 BENCHMARK SUMMARY")
        print("=" * 60)
        
        if "code_generation" in self.results["single_request"]:
            code_gen = self.results["single_request"]["code_generation"]
            print(f"Code Generation: {code_gen['avg_time']*1000:.1f}ms avg, {code_gen['success_rate']:.1%} success")
        
        if "batch_100" in self.results["batch_processing"]:
            batch = self.results["batch_processing"]["batch_100"]
            print(f"Batch Processing: {batch['throughput']:.1f} req/s")
        
        memory = self.results["memory_usage"]
        print(f"Memory Usage: {memory['peak_usage_mb']:.1f}MB peak")
        
        stress = self.results["stress_test"]
        print(f"Stress Test: {stress['requests_per_second']:.1f} req/s sustained")
        
        print(f"\nResults saved to: {output_file}")
        
        return self.results


def main():
    """Main function."""
    benchmark = EndToEndBenchmark()
    
    try:
        results = benchmark.run_full_benchmark()
        
        # Performance gates
        print("\n🎯 PERFORMANCE GATES")
        print("=" * 30)
        
        passed = True
        
        # Gate 1: Code generation < 200ms avg
        if "code_generation" in results["single_request"]:
            code_avg = results["single_request"]["code_generation"]["avg_time"]
            if code_avg > 0.2:
                print(f"❌ Code generation too slow: {code_avg*1000:.1f}ms > 200ms")
                passed = False
            else:
                print(f"✅ Code generation: {code_avg*1000:.1f}ms ≤ 200ms")
        
        # Gate 2: Memory usage < 500MB
        memory_peak = results["memory_usage"]["peak_usage_mb"]
        if memory_peak > 500:
            print(f"❌ Memory usage too high: {memory_peak:.1f}MB > 500MB")
            passed = False
        else:
            print(f"✅ Memory usage: {memory_peak:.1f}MB ≤ 500MB")
        
        # Gate 3: Throughput > 50 req/s
        if "batch_100" in results["batch_processing"]:
            throughput = results["batch_processing"]["batch_100"]["throughput"]
            if throughput < 50:
                print(f"❌ Throughput too low: {throughput:.1f} req/s < 50 req/s")
                passed = False
            else:
                print(f"✅ Throughput: {throughput:.1f} req/s ≥ 50 req/s")
        
        if passed:
            print("\n🎉 ALL PERFORMANCE GATES PASSED!")
            return 0
        else:
            print("\n❌ SOME PERFORMANCE GATES FAILED!")
            return 1
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
