#!/usr/bin/env python3
"""
NEUROGLYPH True AST Roundtrip Test
Test AST veramente rigoroso che testa il vero roundtrip NEUROGLYPH completo

VERO ROUNDTRIP:
NEUROGLYPH → Python → AST → Python → NEUROGLYPH → Python → AST
Confronto: AST_originale vs AST_finale

CRITERI RIGOROSI:
- Mapping completo di TUTTI i simboli del dataset
- Zero fallback a stringhe
- Confronto AST esatto (zero tolleranza)
- Test su simboli reali dal dataset
"""

import ast
import json
import logging
import random
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, field
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TrueRoundtripResult:
    """Risultato di un vero test roundtrip."""
    example_id: str
    original_neuroglyph: str
    step1_python: str
    step2_ast_dump: str
    step3_python_reconstructed: str
    step4_neuroglyph_reconstructed: str
    step5_python_final: str
    step6_ast_final_dump: str
    
    ast_identical: bool
    semantic_preserved: bool
    symbols_preserved: bool
    roundtrip_successful: bool
    
    failure_reason: Optional[str] = None


@dataclass
class TrueASTRoundtripReport:
    """Report del vero test AST roundtrip."""
    total_tests: int = 0
    successful_roundtrips: int = 0
    ast_identical_count: int = 0
    semantic_preserved_count: int = 0
    symbols_preserved_count: int = 0
    
    true_ast_fidelity: float = 0.0
    semantic_preservation_rate: float = 0.0
    symbol_preservation_rate: float = 0.0
    overall_roundtrip_rate: float = 0.0
    
    unique_symbols_found: Set[str] = field(default_factory=set)
    symbols_with_mapping: Set[str] = field(default_factory=set)
    symbols_without_mapping: Set[str] = field(default_factory=set)
    
    sample_results: List[TrueRoundtripResult] = field(default_factory=list)
    critical_issues: List[str] = field(default_factory=list)
    
    test_passed: bool = False
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


class TrueNeuroglyphASTTester:
    """Tester per vero roundtrip AST NEUROGLYPH."""
    
    def __init__(self):
        """Inizializza tester con mapping completo."""
        # MAPPING COMPLETO - deve coprire TUTTI i simboli del dataset
        self.complete_symbol_mapping = self._build_complete_mapping()
        self.reverse_mapping = {v: k for k, v in self.complete_symbol_mapping.items()}
        
        logger.info(f"🔬 TrueNeuroglyphASTTester inizializzato")
        logger.info(f"   Simboli mappati: {len(self.complete_symbol_mapping)}")
    
    def _build_complete_mapping(self) -> Dict[str, str]:
        """Costruisce mapping completo per TUTTI i simboli NEUROGLYPH."""
        # MAPPING COMPLETO basato su standard matematici/logici
        return {
            # Simboli logici base
            '⊢': 'PROVES',
            '∀': 'FORALL', 
            '∃': 'EXISTS',
            '⇒': 'IMPLIES',
            '∧': 'AND_LOGIC',
            '∨': 'OR_LOGIC', 
            '¬': 'NOT_LOGIC',
            '⊥': 'CONTRADICTION',
            '≡': 'EQUIVALENT',
            '⇔': 'IFF',
            '∴': 'THEREFORE',
            
            # Simboli insiemistici
            '∈': 'ELEMENT_OF',
            '∉': 'NOT_ELEMENT_OF',
            '⊂': 'SUBSET',
            '⊃': 'SUPERSET',
            '⊆': 'SUBSET_EQ',
            '⊇': 'SUPERSET_EQ',
            '∅': 'EMPTYSET',
            '∪': 'UNION',
            '∩': 'INTERSECTION',
            '∖': 'SET_MINUS',
            '△': 'SYMMETRIC_DIFF',
            
            # Simboli matematici
            '∞': 'INFINITY',
            '∑': 'SUM',
            '∏': 'PRODUCT',
            '∫': 'INTEGRAL',
            '∂': 'PARTIAL',
            '∇': 'NABLA',
            '±': 'PLUS_MINUS',
            '∓': 'MINUS_PLUS',
            '≤': 'LEQ',
            '≥': 'GEQ',
            '≠': 'NEQ',
            '≈': 'APPROX',
            '≅': 'CONGRUENT',
            '∝': 'PROPORTIONAL',
            
            # Simboli frecce
            '→': 'ARROW_RIGHT',
            '←': 'ARROW_LEFT',
            '↑': 'ARROW_UP',
            '↓': 'ARROW_DOWN',
            '↔': 'ARROW_BOTH',
            '↕': 'ARROW_VERTICAL',
            '⟶': 'LONG_ARROW_RIGHT',
            '⟵': 'LONG_ARROW_LEFT',
            
            # Simboli NEUROGLYPH speciali
            '🧠': 'BRAIN_MARKER',
            '🔮': 'PREDICT_MARKER',
            '📖': 'STORY_MARKER',
            '🎯': 'TARGET_MARKER',
            '⚡': 'ENERGY_MARKER',
            '🌟': 'STAR_MARKER',
            '🔥': 'FIRE_MARKER',
            '💎': 'DIAMOND_MARKER',
            
            # Simboli greci comuni
            'α': 'ALPHA',
            'β': 'BETA', 
            'γ': 'GAMMA',
            'δ': 'DELTA',
            'ε': 'EPSILON',
            'ζ': 'ZETA',
            'η': 'ETA',
            'θ': 'THETA',
            'λ': 'LAMBDA',
            'μ': 'MU',
            'π': 'PI',
            'ρ': 'RHO',
            'σ': 'SIGMA',
            'τ': 'TAU',
            'φ': 'PHI',
            'χ': 'CHI',
            'ψ': 'PSI',
            'ω': 'OMEGA',
            
            # Simboli aggiuntivi
            '℘': 'POWERSET',
            '℧': 'OHM',
            '℮': 'ESTIMATED',
            '⊕': 'XOR',
            '⊗': 'TENSOR',
            '⊙': 'DOT_PRODUCT',
            '⊚': 'CIRCLED_DOT',
            '⊛': 'CIRCLED_STAR',
            '⟨': 'LANGLE',
            '⟩': 'RANGLE',
            '⌊': 'FLOOR_LEFT',
            '⌋': 'FLOOR_RIGHT',
            '⌈': 'CEIL_LEFT',
            '⌉': 'CEIL_RIGHT'
        }
    
    def discover_dataset_symbols(self, dataset_path: str) -> Set[str]:
        """Scopre tutti i simboli unici nel dataset."""
        logger.info(f"🔍 Discovering all symbols in dataset...")
        
        unique_symbols = set()
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            example = json.loads(line.strip())
                            
                            # Estrai tutto il contenuto simbolico
                            symbolic_content = (
                                example.get('prompt_symbolic', '') + ' ' +
                                example.get('response_symbolic', '')
                            )
                            
                            # Trova simboli non-ASCII (simboli speciali)
                            for char in symbolic_content:
                                if ord(char) > 127:  # Non-ASCII
                                    unique_symbols.add(char)
                            
                            if line_num % 5000 == 0:
                                logger.info(f"   Processed {line_num} examples, found {len(unique_symbols)} unique symbols")
                                
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Error discovering symbols: {e}")
        
        logger.info(f"✅ Symbol discovery completed")
        logger.info(f"   Total unique symbols found: {len(unique_symbols)}")
        
        return unique_symbols
    
    def test_true_ast_roundtrip(self, dataset_path: str, 
                               sample_size: int = 500) -> TrueASTRoundtripReport:
        """
        Esegue vero test AST roundtrip su dataset.
        
        Args:
            dataset_path: Percorso al dataset
            sample_size: Numero di esempi da testare
            
        Returns:
            Report completo del test
        """
        logger.info(f"🔬 Starting TRUE AST roundtrip test...")
        logger.info(f"   Dataset: {dataset_path}")
        logger.info(f"   Sample size: {sample_size}")
        
        report = TrueASTRoundtripReport()
        
        # FASE 1: Scopri tutti i simboli nel dataset
        dataset_symbols = self.discover_dataset_symbols(dataset_path)
        report.unique_symbols_found = dataset_symbols
        
        # FASE 2: Verifica copertura mapping
        self._analyze_symbol_coverage(report)
        
        # FASE 3: Test roundtrip su campione
        samples = self._load_samples(dataset_path, sample_size)
        report.total_tests = len(samples)
        
        logger.info(f"🧪 Testing {len(samples)} examples with TRUE roundtrip...")
        
        for i, sample in enumerate(samples):
            try:
                result = self._test_single_true_roundtrip(sample, i)
                
                if result.roundtrip_successful:
                    report.successful_roundtrips += 1
                
                if result.ast_identical:
                    report.ast_identical_count += 1
                
                if result.semantic_preserved:
                    report.semantic_preserved_count += 1
                
                if result.symbols_preserved:
                    report.symbols_preserved_count += 1
                
                # Salva campioni per analisi
                if len(report.sample_results) < 20:
                    report.sample_results.append(result)
                
                if (i + 1) % 100 == 0:
                    logger.info(f"   Processed {i + 1}/{len(samples)} examples")
                    
            except Exception as e:
                logger.error(f"Error testing example {i}: {e}")
        
        # FASE 4: Calcola metriche finali
        self._calculate_true_metrics(report)
        
        # FASE 5: Assessment rigoroso
        self._assess_true_roundtrip(report)
        
        logger.info(f"✅ TRUE AST roundtrip test completed")
        logger.info(f"   True AST fidelity: {report.true_ast_fidelity:.2%}")
        logger.info(f"   Semantic preservation: {report.semantic_preservation_rate:.2%}")
        logger.info(f"   Symbol preservation: {report.symbol_preservation_rate:.2%}")
        logger.info(f"   Overall roundtrip: {report.overall_roundtrip_rate:.2%}")
        logger.info(f"   Test passed: {report.test_passed}")
        
        return report
    
    def _analyze_symbol_coverage(self, report: TrueASTRoundtripReport):
        """Analizza copertura del mapping simboli."""
        logger.info(f"📊 Analyzing symbol mapping coverage...")
        
        for symbol in report.unique_symbols_found:
            if symbol in self.complete_symbol_mapping:
                report.symbols_with_mapping.add(symbol)
            else:
                report.symbols_without_mapping.add(symbol)
        
        coverage_rate = len(report.symbols_with_mapping) / len(report.unique_symbols_found) if report.unique_symbols_found else 0
        
        logger.info(f"   Symbols with mapping: {len(report.symbols_with_mapping)}")
        logger.info(f"   Symbols without mapping: {len(report.symbols_without_mapping)}")
        logger.info(f"   Coverage rate: {coverage_rate:.2%}")
        
        if report.symbols_without_mapping:
            logger.warning(f"   Missing mappings for: {sorted(list(report.symbols_without_mapping))}")
            report.critical_issues.append(f"Missing mappings for {len(report.symbols_without_mapping)} symbols")
    
    def _test_single_true_roundtrip(self, sample: Dict, index: int) -> TrueRoundtripResult:
        """Test vero roundtrip su singolo esempio."""
        example_id = sample.get('id', f'example_{index}')
        
        # Estrai codice NEUROGLYPH originale
        original_neuroglyph = (
            sample.get('prompt_symbolic', '') + ' ' + 
            sample.get('response_symbolic', '')
        ).strip()
        
        try:
            # STEP 1: NEUROGLYPH → Python
            step1_python = self._neuroglyph_to_python_complete(original_neuroglyph)
            
            # STEP 2: Python → AST
            step2_ast = ast.parse(step1_python)
            step2_ast_dump = ast.dump(step2_ast, annotate_fields=False, include_attributes=False)
            
            # STEP 3: AST → Python (ricostruito)
            step3_python_reconstructed = ast.unparse(step2_ast)
            
            # STEP 4: Python → NEUROGLYPH (ricostruito)
            step4_neuroglyph_reconstructed = self._python_to_neuroglyph_complete(step3_python_reconstructed)
            
            # STEP 5: NEUROGLYPH → Python (finale)
            step5_python_final = self._neuroglyph_to_python_complete(step4_neuroglyph_reconstructed)
            
            # STEP 6: Python → AST (finale)
            step6_ast_final = ast.parse(step5_python_final)
            step6_ast_final_dump = ast.dump(step6_ast_final, annotate_fields=False, include_attributes=False)
            
            # CONFRONTI RIGOROSI
            ast_identical = step2_ast_dump == step6_ast_final_dump
            semantic_preserved = self._check_semantic_preservation(original_neuroglyph, step4_neuroglyph_reconstructed)
            symbols_preserved = self._check_symbol_preservation(original_neuroglyph, step4_neuroglyph_reconstructed)
            
            roundtrip_successful = ast_identical and semantic_preserved and symbols_preserved
            
            return TrueRoundtripResult(
                example_id=example_id,
                original_neuroglyph=original_neuroglyph,
                step1_python=step1_python,
                step2_ast_dump=step2_ast_dump,
                step3_python_reconstructed=step3_python_reconstructed,
                step4_neuroglyph_reconstructed=step4_neuroglyph_reconstructed,
                step5_python_final=step5_python_final,
                step6_ast_final_dump=step6_ast_final_dump,
                ast_identical=ast_identical,
                semantic_preserved=semantic_preserved,
                symbols_preserved=symbols_preserved,
                roundtrip_successful=roundtrip_successful
            )
            
        except Exception as e:
            return TrueRoundtripResult(
                example_id=example_id,
                original_neuroglyph=original_neuroglyph,
                step1_python="",
                step2_ast_dump="",
                step3_python_reconstructed="",
                step4_neuroglyph_reconstructed="",
                step5_python_final="",
                step6_ast_final_dump="",
                ast_identical=False,
                semantic_preserved=False,
                symbols_preserved=False,
                roundtrip_successful=False,
                failure_reason=str(e)
            )
    
    def _neuroglyph_to_python_complete(self, neuroglyph_code: str) -> str:
        """Conversione NEUROGLYPH → Python con mapping completo."""
        python_code = neuroglyph_code
        
        # Applica mapping completo
        for symbol, python_name in self.complete_symbol_mapping.items():
            if symbol in python_code:
                python_code = python_code.replace(symbol, python_name)
        
        # Pulizia per Python valido
        python_code = self._clean_for_python(python_code)
        
        # ZERO FALLBACK - se non è Python valido, FALLISCE
        try:
            ast.parse(python_code)
            return python_code
        except SyntaxError:
            # NO FALLBACK A STRINGA - deve essere Python valido
            raise ValueError(f"Cannot convert to valid Python: {neuroglyph_code}")
    
    def _python_to_neuroglyph_complete(self, python_code: str) -> str:
        """Conversione Python → NEUROGLYPH con mapping completo."""
        neuroglyph_code = python_code
        
        # Applica mapping inverso
        for python_name, symbol in self.reverse_mapping.items():
            if python_name in neuroglyph_code:
                neuroglyph_code = neuroglyph_code.replace(python_name, symbol)
        
        return neuroglyph_code
    
    def _clean_for_python(self, code: str) -> str:
        """Pulisce codice per Python valido senza fallback."""
        # Rimuovi caratteri non-printable
        cleaned = ''.join(c for c in code if c.isprintable())
        
        # Sostituzioni base per Python
        cleaned = cleaned.replace(':', ' COLON ')
        cleaned = cleaned.replace('?', ' QUESTION ')
        cleaned = cleaned.replace('!', ' EXCLAMATION ')
        
        # Se vuoto, crea espressione minima
        if not cleaned.strip():
            cleaned = 'EMPTY_EXPRESSION'
        
        return cleaned
    
    def _check_semantic_preservation(self, original: str, reconstructed: str) -> bool:
        """Verifica preservazione semantica."""
        # Estrai simboli logici da entrambi
        original_symbols = self._extract_logical_symbols(original)
        reconstructed_symbols = self._extract_logical_symbols(reconstructed)
        
        return original_symbols == reconstructed_symbols
    
    def _check_symbol_preservation(self, original: str, reconstructed: str) -> bool:
        """Verifica preservazione simboli."""
        original_special = set(c for c in original if ord(c) > 127)
        reconstructed_special = set(c for c in reconstructed if ord(c) > 127)
        
        return original_special == reconstructed_special
    
    def _extract_logical_symbols(self, text: str) -> Set[str]:
        """Estrae simboli logici dal testo."""
        logical_symbols = {'⊢', '∀', '∃', '⇒', '∧', '∨', '¬', '⊥', '≡', '⇔', '∴'}
        return set(c for c in text if c in logical_symbols)
    
    def _load_samples(self, dataset_path: str, sample_size: int) -> List[Dict]:
        """Carica campione dal dataset."""
        samples = []
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                all_samples = []
                for line in f:
                    if line.strip():
                        sample = json.loads(line.strip())
                        all_samples.append(sample)
                
                if len(all_samples) > sample_size:
                    samples = random.sample(all_samples, sample_size)
                else:
                    samples = all_samples
                    
        except Exception as e:
            logger.error(f"Error loading samples: {e}")
        
        return samples
    
    def _calculate_true_metrics(self, report: TrueASTRoundtripReport):
        """Calcola metriche vere."""
        if report.total_tests > 0:
            report.true_ast_fidelity = report.ast_identical_count / report.total_tests
            report.semantic_preservation_rate = report.semantic_preserved_count / report.total_tests
            report.symbol_preservation_rate = report.symbols_preserved_count / report.total_tests
            report.overall_roundtrip_rate = report.successful_roundtrips / report.total_tests
    
    def _assess_true_roundtrip(self, report: TrueASTRoundtripReport):
        """Assessment rigoroso del vero roundtrip."""
        # CRITERI RIGOROSI per vero roundtrip
        criteria = [
            ("True AST Fidelity", report.true_ast_fidelity >= 0.95),
            ("Semantic Preservation", report.semantic_preservation_rate >= 0.95),
            ("Symbol Preservation", report.symbol_preservation_rate >= 0.95),
            ("Symbol Mapping Coverage", len(report.symbols_without_mapping) == 0)
        ]
        
        passed_criteria = sum(1 for _, passed in criteria if passed)
        
        # Test passa solo se TUTTI i criteri sono soddisfatti
        report.test_passed = passed_criteria == len(criteria)
        
        # Aggiungi issues critici
        for criterion_name, passed in criteria:
            if not passed:
                report.critical_issues.append(f"FAILED: {criterion_name}")


def main():
    """Pipeline principale del vero test AST."""
    print("🔬 NEUROGLYPH True AST Roundtrip Test")
    print("=" * 80)
    print("TESTING TRUE ROUNDTRIP:")
    print("NEUROGLYPH → Python → AST → Python → NEUROGLYPH → Python → AST")
    print("CRITERIA: AST_original == AST_final (EXACT MATCH)")
    
    # Dataset da testare
    dataset_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"
    
    if not Path(dataset_path).exists():
        print(f"❌ Dataset not found: {dataset_path}")
        return
    
    # Esegui vero test AST
    tester = TrueNeuroglyphASTTester()
    report = tester.test_true_ast_roundtrip(dataset_path, sample_size=500)
    
    # Salva report
    report_path = "data/verification/true_ast_roundtrip_report.json"
    Path(report_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump({
            'test_passed': report.test_passed,
            'timestamp': report.timestamp,
            'metrics': {
                'true_ast_fidelity': report.true_ast_fidelity,
                'semantic_preservation_rate': report.semantic_preservation_rate,
                'symbol_preservation_rate': report.symbol_preservation_rate,
                'overall_roundtrip_rate': report.overall_roundtrip_rate
            },
            'symbol_analysis': {
                'unique_symbols_found': len(report.unique_symbols_found),
                'symbols_with_mapping': len(report.symbols_with_mapping),
                'symbols_without_mapping': len(report.symbols_without_mapping),
                'missing_symbols': sorted(list(report.symbols_without_mapping))
            },
            'critical_issues': report.critical_issues
        }, f, indent=2)
    
    # Stampa risultati VERI
    print(f"\n🎯 TRUE AST ROUNDTRIP RESULTS")
    print("=" * 80)
    print(f"🔬 True AST Fidelity: {report.true_ast_fidelity:.2%} (≥95% required)")
    print(f"🧠 Semantic Preservation: {report.semantic_preservation_rate:.2%} (≥95% required)")
    print(f"🔤 Symbol Preservation: {report.symbol_preservation_rate:.2%} (≥95% required)")
    print(f"📊 Overall Roundtrip: {report.overall_roundtrip_rate:.2%}")
    
    print(f"\n📋 SYMBOL ANALYSIS:")
    print(f"   Unique symbols found: {len(report.unique_symbols_found)}")
    print(f"   Symbols with mapping: {len(report.symbols_with_mapping)}")
    print(f"   Symbols without mapping: {len(report.symbols_without_mapping)}")
    
    if report.symbols_without_mapping:
        print(f"   Missing mappings: {sorted(list(report.symbols_without_mapping))}")
    
    print(f"\n🎯 TRUE TEST RESULT: {report.test_passed}")
    
    if report.critical_issues:
        print(f"\n❌ CRITICAL ISSUES:")
        for issue in report.critical_issues:
            print(f"   - {issue}")
    
    print(f"\n📄 Report saved: {report_path}")
    
    if report.test_passed:
        print(f"\n🎉 TRUE AST ROUNDTRIP PASSED!")
        print("NEUROGLYPH symbolic system is truly robust")
    else:
        print(f"\n⚠️ TRUE AST ROUNDTRIP FAILED!")
        print("NEUROGLYPH symbolic system needs improvements")


if __name__ == "__main__":
    main()
