#!/usr/bin/env python3
"""
NEUROGLYPH Symbolic Dataset Expander
FASE 2A - Espansione Sistematica per Eccellenza

Espande il dataset simbolico da 1,000 a 30,000-35,000 esempi
con copertura completa del registry simbolico NEUROGLYPH.

Target:
- 300-500 esempi per simbolo critico (⊢, ∀, ∃, ⇒, ∧, ∨, ¬, ⊥)
- 200-400 esempi per costrutto matematico (∫, ∂, equazioni, sistemi)
- 300-500 esempi per pattern di codice (λ, ⚡, 🔄, 🏛️, ⤴)
- Distribuzione 80/10/10 train/val/test
- Quality gates automatici per ogni batch

Obiettivo: MASSIMA QUALITÀ, ZERO OVERFITTING
"""

import json
import random
import logging
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict, Counter

# Import del generatore base
from symbolic_dataset_generator import (
    SymbolicDatasetGenerator, 
    SymbolicExample, 
    SymbolicDatasetConfig
)

# Import del validatore
from symbolic_dataset_validator import SymbolicDatasetValidator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ExpansionConfig:
    """Configurazione per espansione dataset simbolico."""
    target_total_examples: int = 35000
    target_examples_per_critical_symbol: int = 500
    target_examples_per_math_construct: int = 400
    target_examples_per_code_pattern: int = 500
    
    # Simboli critici che devono avere copertura massima
    critical_symbols: List[str] = field(default_factory=lambda: [
        '⊢', '∀', '∃', '⇒', '∧', '∨', '¬', '⊥', '≡', '⇔',  # Logic
        '∫', '∂', '∑', '∏', '√', '∞', '≈', '≠', '≤', '≥',  # Math
        '⚡', '🔄', '🏛️', '⤴', '⟦', '⟧', '→', 'λ'          # Code
    ])
    
    # Distribuzione train/val/test
    train_ratio: float = 0.8
    val_ratio: float = 0.1
    test_ratio: float = 0.1
    
    # Quality gates
    min_validation_rate: float = 0.95
    min_overall_quality: float = 0.90
    
    # Batch processing
    batch_size: int = 1000
    validate_every_batch: bool = True


@dataclass
class ExpansionReport:
    """Report di espansione dataset."""
    initial_examples: int
    target_examples: int
    generated_examples: int
    final_examples: int
    symbol_coverage: Dict[str, int] = field(default_factory=dict)
    quality_scores: Dict[str, float] = field(default_factory=dict)
    validation_results: List[Dict] = field(default_factory=list)
    generated_at: str = field(default_factory=lambda: datetime.now().isoformat())


class SymbolicDatasetExpander:
    """
    Espansore principale per dataset simbolici NEUROGLYPH.
    Gestisce espansione sistematica con quality gates.
    """
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza l'espansore.
        
        Args:
            registry_path: Percorso al registry simbolico
        """
        self.registry_path = registry_path
        self.generator = SymbolicDatasetGenerator(registry_path)
        self.validator = SymbolicDatasetValidator(registry_path)
        
        # Analizza registry per simboli disponibili
        self.available_symbols = self._analyze_registry()
        self.symbol_categories = self._categorize_symbols()
        
        logger.info(f"🚀 SymbolicDatasetExpander inizializzato")
        logger.info(f"   - Simboli disponibili: {len(self.available_symbols)}")
        logger.info(f"   - Categorie simboli: {len(self.symbol_categories)}")
    
    def _analyze_registry(self) -> Set[str]:
        """Analizza registry per estrarre tutti i simboli disponibili."""
        symbols = set()
        if hasattr(self.generator, 'registry'):
            for symbol_data in self.generator.registry:
                if 'symbol' in symbol_data:
                    symbols.add(symbol_data['symbol'])
        return symbols
    
    def _categorize_symbols(self) -> Dict[str, List[str]]:
        """Categorizza simboli per dominio."""
        categories = {
            'logical': [],
            'mathematical': [],
            'code': [],
            'meta': [],
            'other': []
        }
        
        # Simboli logici
        logical_symbols = ['⊢', '∀', '∃', '⇒', '∧', '∨', '¬', '⊥', '≡', '⇔', '⊨', '⊭']
        
        # Simboli matematici
        math_symbols = ['∫', '∂', '∑', '∏', '√', '∞', '≈', '≠', '≤', '≥', '±', '∇', '∆']
        
        # Simboli di codice
        code_symbols = ['⚡', '🔄', '🏛️', '⤴', '⟦', '⟧', '→', 'λ', '⟨', '⟩', '⇄', '⇆']
        
        # Simboli meta
        meta_symbols = ['🧠', '🔮', '⚖', '✪', '⛓', '🎯', '📊', '🔍']
        
        for symbol in self.available_symbols:
            if symbol in logical_symbols:
                categories['logical'].append(symbol)
            elif symbol in math_symbols:
                categories['mathematical'].append(symbol)
            elif symbol in code_symbols:
                categories['code'].append(symbol)
            elif symbol in meta_symbols:
                categories['meta'].append(symbol)
            else:
                categories['other'].append(symbol)
        
        return categories
    
    def analyze_current_coverage(self, dataset_path: str) -> Dict[str, int]:
        """
        Analizza copertura simbolica del dataset esistente.
        
        Args:
            dataset_path: Percorso al dataset esistente
            
        Returns:
            Dizionario con conteggio per simbolo
        """
        symbol_counts = Counter()
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        example = json.loads(line.strip())
                        symbols_used = example.get('symbols_used', [])
                        for symbol in symbols_used:
                            symbol_counts[symbol] += 1
        
        except Exception as e:
            logger.error(f"Error analyzing coverage: {e}")
        
        return dict(symbol_counts)
    
    def calculate_expansion_needs(self, current_coverage: Dict[str, int], 
                                config: ExpansionConfig) -> Dict[str, int]:
        """
        Calcola quanti esempi servono per ogni simbolo.
        
        Args:
            current_coverage: Copertura attuale
            config: Configurazione espansione
            
        Returns:
            Dizionario con esempi necessari per simbolo
        """
        needs = {}
        
        for symbol in config.critical_symbols:
            current_count = current_coverage.get(symbol, 0)
            target_count = config.target_examples_per_critical_symbol
            
            if current_count < target_count:
                needs[symbol] = target_count - current_count
            
        logger.info(f"📊 Expansion needs calculated:")
        for symbol, need in sorted(needs.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"   - {symbol}: need {need} more examples")
        
        return needs
    
    def generate_targeted_examples(self, symbol: str, count: int, 
                                 config: ExpansionConfig) -> List[SymbolicExample]:
        """
        Genera esempi mirati per un simbolo specifico.
        
        Args:
            symbol: Simbolo target
            count: Numero di esempi da generare
            config: Configurazione
            
        Returns:
            Lista di esempi generati
        """
        examples = []
        
        # Determina categoria del simbolo
        category = self._get_symbol_category(symbol)
        
        # Distribuzione difficoltà
        difficulties = ['easy'] * int(count * 0.3) + \
                      ['medium'] * int(count * 0.5) + \
                      ['hard'] * int(count * 0.2)
        
        # Aggiungi esempi rimanenti come medium
        while len(difficulties) < count:
            difficulties.append('medium')
        
        random.shuffle(difficulties)
        
        for i, difficulty in enumerate(difficulties):
            try:
                if category == 'logical':
                    example = self.generator.generate_logical_reasoning_example(difficulty)
                elif category == 'mathematical':
                    example = self.generator.generate_mathematical_reasoning_example(difficulty)
                elif category == 'code':
                    example = self.generator.generate_code_generation_example(difficulty)
                else:
                    # Default to logical reasoning
                    example = self.generator.generate_logical_reasoning_example(difficulty)
                
                # Assicurati che il simbolo target sia presente
                if symbol not in example.symbols_used:
                    example.symbols_used.append(symbol)
                    # Modifica prompt/response per includere il simbolo se necessario
                    example = self._inject_symbol_if_needed(example, symbol)
                
                examples.append(example)
                
            except Exception as e:
                logger.warning(f"Error generating example for {symbol}: {e}")
        
        logger.info(f"✅ Generated {len(examples)} examples for symbol '{symbol}'")
        return examples
    
    def _get_symbol_category(self, symbol: str) -> str:
        """Determina categoria di un simbolo."""
        for category, symbols in self.symbol_categories.items():
            if symbol in symbols:
                return category
        return 'logical'  # Default
    
    def _inject_symbol_if_needed(self, example: SymbolicExample, symbol: str) -> SymbolicExample:
        """Inietta simbolo nell'esempio se non presente."""
        # Implementazione semplificata - in una versione completa
        # dovremmo modificare intelligentemente prompt e response
        
        if symbol not in example.prompt_symbolic:
            # Aggiungi simbolo al prompt in modo semanticamente corretto
            if symbol == '⊢':
                # Già presente nella maggior parte dei casi
                pass
            elif symbol in ['∀', '∃']:
                # Aggiungi quantificatore
                example.prompt_symbolic = f"{symbol}x " + example.prompt_symbolic
            elif symbol in ['∧', '∨']:
                # Già presente nella maggior parte dei casi logici
                pass
            # ... altri casi specifici
        
        return example
    
    def expand_dataset(self, input_path: str, output_path: str, 
                      config: ExpansionConfig) -> ExpansionReport:
        """
        Espande dataset esistente secondo configurazione.
        
        Args:
            input_path: Percorso dataset esistente
            output_path: Percorso dataset espanso
            config: Configurazione espansione
            
        Returns:
            Report di espansione
        """
        logger.info(f"🚀 Starting dataset expansion...")
        logger.info(f"   - Input: {input_path}")
        logger.info(f"   - Output: {output_path}")
        logger.info(f"   - Target: {config.target_total_examples} examples")
        
        # Inizializza report
        report = ExpansionReport(
            initial_examples=0,
            target_examples=config.target_total_examples,
            generated_examples=0,
            final_examples=0
        )
        
        # Carica dataset esistente
        existing_examples = self._load_existing_dataset(input_path)
        report.initial_examples = len(existing_examples)
        
        # Analizza copertura attuale
        current_coverage = self.analyze_current_coverage(input_path)
        report.symbol_coverage = current_coverage.copy()
        
        # Calcola necessità di espansione
        expansion_needs = self.calculate_expansion_needs(current_coverage, config)
        
        # Genera esempi aggiuntivi
        all_new_examples = []
        
        for symbol, needed_count in expansion_needs.items():
            if needed_count > 0:
                logger.info(f"📝 Generating {needed_count} examples for symbol '{symbol}'")
                
                # Genera in batch per gestire memoria
                batch_count = 0
                while batch_count < needed_count:
                    current_batch_size = min(config.batch_size, needed_count - batch_count)
                    
                    batch_examples = self.generate_targeted_examples(
                        symbol, current_batch_size, config
                    )
                    
                    # Valida batch se richiesto
                    if config.validate_every_batch:
                        batch_valid = self._validate_batch(batch_examples, config)
                        if not batch_valid:
                            logger.warning(f"Batch validation failed for {symbol}, retrying...")
                            continue
                    
                    all_new_examples.extend(batch_examples)
                    batch_count += current_batch_size
                    
                    logger.info(f"   - Generated {batch_count}/{needed_count} for {symbol}")
        
        report.generated_examples = len(all_new_examples)
        
        # Combina esempi esistenti e nuovi
        all_examples = existing_examples + all_new_examples
        
        # Shuffle per distribuzione casuale
        random.shuffle(all_examples)
        
        # Limita al target se necessario
        if len(all_examples) > config.target_total_examples:
            all_examples = all_examples[:config.target_total_examples]
        
        report.final_examples = len(all_examples)
        
        # Salva dataset espanso
        self._save_expanded_dataset(all_examples, output_path)
        
        # Crea split train/val/test
        self._create_train_val_test_splits(all_examples, output_path, config)
        
        # Validazione finale
        final_validation = self.validator.validate_dataset(output_path)
        report.validation_results.append({
            'type': 'final',
            'validation_rate': final_validation.validation_rate,
            'quality_scores': final_validation.quality_scores
        })
        
        logger.info(f"✅ Dataset expansion completed!")
        logger.info(f"   - Initial: {report.initial_examples}")
        logger.info(f"   - Generated: {report.generated_examples}")
        logger.info(f"   - Final: {report.final_examples}")
        logger.info(f"   - Validation rate: {final_validation.validation_rate:.2%}")
        
        return report
    
    def _load_existing_dataset(self, path: str) -> List[SymbolicExample]:
        """Carica dataset esistente."""
        examples = []
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        data = json.loads(line.strip())
                        example = SymbolicExample(
                            id=data['id'],
                            category=data['category'],
                            cognitive_tags=data['cognitive_tags'],
                            prompt_symbolic=data['prompt_symbolic'],
                            response_symbolic=data['response_symbolic'],
                            prompt_natural=data['prompt_natural'],
                            response_natural=data['response_natural'],
                            symbols_used=data['symbols_used'],
                            difficulty=data['difficulty'],
                            reasoning_steps=data['reasoning_steps'],
                            metadata=data.get('metadata', {})
                        )
                        examples.append(example)
        
        except Exception as e:
            logger.error(f"Error loading existing dataset: {e}")
        
        return examples
    
    def _validate_batch(self, examples: List[SymbolicExample], 
                       config: ExpansionConfig) -> bool:
        """Valida un batch di esempi."""
        # Implementazione semplificata - valida solo alcuni esempi campione
        sample_size = min(10, len(examples))
        sample_examples = random.sample(examples, sample_size)
        
        valid_count = 0
        for example in sample_examples:
            # Converti in dict per validazione
            example_dict = {
                'id': example.id,
                'category': example.category,
                'cognitive_tags': example.cognitive_tags,
                'prompt_symbolic': example.prompt_symbolic,
                'response_symbolic': example.response_symbolic,
                'prompt_natural': example.prompt_natural,
                'response_natural': example.response_natural,
                'symbols_used': example.symbols_used,
                'difficulty': example.difficulty,
                'reasoning_steps': example.reasoning_steps,
                'metadata': example.metadata
            }
            
            result = self.validator.validate_example(example_dict)
            if result.is_valid:
                valid_count += 1
        
        validation_rate = valid_count / sample_size
        return validation_rate >= config.min_validation_rate
    
    def _save_expanded_dataset(self, examples: List[SymbolicExample], output_path: str):
        """Salva dataset espanso."""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for example in examples:
                example_dict = {
                    'id': example.id,
                    'category': example.category,
                    'cognitive_tags': example.cognitive_tags,
                    'prompt_symbolic': example.prompt_symbolic,
                    'response_symbolic': example.response_symbolic,
                    'prompt_natural': example.prompt_natural,
                    'response_natural': example.response_natural,
                    'symbols_used': example.symbols_used,
                    'difficulty': example.difficulty,
                    'reasoning_steps': example.reasoning_steps,
                    'metadata': example.metadata,
                    'generated_at': datetime.now().isoformat()
                }
                f.write(json.dumps(example_dict, ensure_ascii=False) + '\n')
        
        logger.info(f"💾 Expanded dataset saved: {output_file}")
    
    def _create_train_val_test_splits(self, examples: List[SymbolicExample], 
                                    base_path: str, config: ExpansionConfig):
        """Crea split train/validation/test."""
        total = len(examples)
        train_size = int(total * config.train_ratio)
        val_size = int(total * config.val_ratio)
        
        train_examples = examples[:train_size]
        val_examples = examples[train_size:train_size + val_size]
        test_examples = examples[train_size + val_size:]
        
        # Salva split
        base_dir = Path(base_path).parent
        
        splits = {
            'train': train_examples,
            'val': val_examples,
            'test': test_examples
        }
        
        for split_name, split_examples in splits.items():
            split_path = base_dir / f"neuroglyph_symbolic_{split_name}.jsonl"
            self._save_expanded_dataset(split_examples, str(split_path))
            logger.info(f"📁 {split_name.upper()} split: {len(split_examples)} examples")


def main():
    """Pipeline principale di espansione dataset simbolico."""
    print("🚀 NEUROGLYPH Symbolic Dataset Expander")
    print("=" * 60)
    
    # Configurazione
    config = ExpansionConfig(
        target_total_examples=5000,  # Start con target più piccolo per testing
        target_examples_per_critical_symbol=200,
        validate_every_batch=True
    )
    
    # Percorsi
    input_path = "data/datasets/symbolic/neuroglyph_symbolic_dataset.jsonl"
    output_path = "data/datasets/symbolic/neuroglyph_symbolic_expanded.jsonl"
    
    # Espandi dataset
    expander = SymbolicDatasetExpander()
    report = expander.expand_dataset(input_path, output_path, config)
    
    # Salva report
    report_path = "data/expansion/expansion_report.json"
    Path(report_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump({
            'initial_examples': report.initial_examples,
            'target_examples': report.target_examples,
            'generated_examples': report.generated_examples,
            'final_examples': report.final_examples,
            'symbol_coverage': report.symbol_coverage,
            'validation_results': report.validation_results,
            'generated_at': report.generated_at
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 EXPANSION SUMMARY")
    print("=" * 60)
    print(f"📈 Initial examples: {report.initial_examples}")
    print(f"🎯 Target examples: {report.target_examples}")
    print(f"✨ Generated examples: {report.generated_examples}")
    print(f"📁 Final examples: {report.final_examples}")
    print(f"💾 Report saved: {report_path}")
    print(f"\n🎉 Dataset expansion completed!")


if __name__ == "__main__":
    main()
