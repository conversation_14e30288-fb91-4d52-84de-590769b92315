#!/usr/bin/env python3
"""
NEUROGLYPH Registry Consistency Checker
Verifica overlap simboli/token e congela vocabolario
"""

import json
import sys
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict
import hashlib

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from transformers import AutoTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("⚠️ transformers non disponibile - solo check registry")


class RegistryConsistencyChecker:
    """Checker per consistenza registry simboli."""
    
    def __init__(self, registry_path: str = "data/symbols/registry_v3.json"):
        """
        Inizializza checker.
        
        Args:
            registry_path: Path al registry simboli
        """
        self.registry_path = registry_path
        self.registry = self._load_registry()
        self.issues = []
        
    def _load_registry(self) -> Dict:
        """Carica registry simboli."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # Detect ULTIMATE registry format
                if 'approved_symbols' in data:
                    print("🔍 Rilevato formato ULTIMATE registry")
                    return data

                return data
        except FileNotFoundError:
            print(f"❌ Registry non trovato: {self.registry_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ Errore parsing JSON: {e}")
            return {}
    
    def check_symbol_uniqueness(self) -> bool:
        """Verifica unicità simboli."""
        print("🔍 Controllo unicità simboli...")

        symbols = []
        symbol_to_entries = defaultdict(list)

        # Handle ULTIMATE registry format
        if 'approved_symbols' in self.registry:
            for entry in self.registry['approved_symbols']:
                if isinstance(entry, dict) and 'symbol' in entry:
                    symbol = entry['symbol']
                    symbols.append(symbol)
                    symbol_to_entries[symbol].append(f"{entry.get('domain', 'unknown')}:{entry.get('id', 'unknown')}")
        # Handle Unicode-only registry format
        elif 'unicode_symbols' in self.registry:
            for domain, entries in self.registry['unicode_symbols'].items():
                if isinstance(entries, list):
                    for entry in entries:
                        if isinstance(entry, dict) and 'symbol' in entry:
                            symbol = entry['symbol']
                            symbols.append(symbol)
                            symbol_to_entries[symbol].append(f"{domain}:{entry.get('id', 'unknown')}")
        else:
            # Handle standard registry format
            for category, entries in self.registry.items():
                if not isinstance(entries, list):
                    continue

                for entry in entries:
                    if isinstance(entry, dict) and 'symbol' in entry:
                        symbol = entry['symbol']
                        symbols.append(symbol)
                        symbol_to_entries[symbol].append(f"{category}:{entry.get('name', 'unknown')}")

        # Check duplicati
        duplicates = {s: entries for s, entries in symbol_to_entries.items() if len(entries) > 1}

        if duplicates:
            print(f"❌ Trovati {len(duplicates)} simboli duplicati:")
            for symbol, entries in duplicates.items():
                print(f"   '{symbol}' in: {', '.join(entries)}")
                self.issues.append(f"Duplicate symbol: {symbol}")
            return False

        print(f"✅ Tutti {len(symbols)} simboli sono univoci")
        return True
    
    def check_unicode_validity(self) -> bool:
        """Verifica validità Unicode simboli."""
        print("🔍 Controllo validità Unicode...")
        
        invalid_symbols = []
        
        # Handle ULTIMATE registry format
        if 'approved_symbols' in self.registry:
            for entry in self.registry['approved_symbols']:
                if isinstance(entry, dict) and 'symbol' in entry:
                    symbol = entry['symbol']

                    try:
                        # Test encoding/decoding
                        symbol.encode('utf-8').decode('utf-8')

                        # Test lunghezza ragionevole (simboli Unicode singoli)
                        if len(symbol) > 4:  # Permetti emoji multi-byte
                            invalid_symbols.append((symbol, "Too long"))

                        # Test caratteri di controllo per ogni char
                        for char in symbol:
                            if ord(char) < 32 or ord(char) == 127:
                                invalid_symbols.append((symbol, "Control characters"))
                                break

                    except (UnicodeError, ValueError):
                        invalid_symbols.append((symbol, "Unicode error"))
        # Handle Unicode-only registry format
        elif 'unicode_symbols' in self.registry:
            for domain, entries in self.registry['unicode_symbols'].items():
                if isinstance(entries, list):
                    for entry in entries:
                        if isinstance(entry, dict) and 'symbol' in entry:
                            symbol = entry['symbol']

                            try:
                                # Test encoding/decoding
                                symbol.encode('utf-8').decode('utf-8')

                                # Test lunghezza ragionevole
                                if len(symbol) > 4:
                                    invalid_symbols.append((symbol, "Too long"))

                                # Test caratteri di controllo
                                for char in symbol:
                                    if ord(char) < 32 or ord(char) == 127:
                                        invalid_symbols.append((symbol, "Control characters"))
                                        break

                            except (UnicodeError, ValueError):
                                invalid_symbols.append((symbol, "Unicode error"))
        else:
            # Handle standard registry format
            for category, entries in self.registry.items():
                if not isinstance(entries, list):
                    continue

                for entry in entries:
                    if isinstance(entry, dict) and 'symbol' in entry:
                        symbol = entry['symbol']

                        try:
                            # Test encoding/decoding
                            symbol.encode('utf-8').decode('utf-8')

                            # Test lunghezza ragionevole
                            if len(symbol) > 4:
                                invalid_symbols.append((symbol, "Too long"))

                            # Test caratteri di controllo
                            if any(ord(c) < 32 for c in symbol if c not in '\t\n\r'):
                                invalid_symbols.append((symbol, "Control characters"))

                        except (UnicodeError, ValueError):
                            invalid_symbols.append((symbol, "Unicode error"))
        
        if invalid_symbols:
            print(f"❌ Trovati {len(invalid_symbols)} simboli invalidi:")
            for symbol, reason in invalid_symbols[:10]:  # Mostra primi 10
                print(f"   '{symbol}': {reason}")
                self.issues.append(f"Invalid symbol: {symbol} ({reason})")
            return False
        
        print("✅ Tutti i simboli sono Unicode validi")
        return True
    
    def check_tokenizer_compatibility(self, tokenizer_path: str = "Qwen/Qwen2.5-Coder-1.5B-Instruct") -> bool:
        """Verifica compatibilità con tokenizer."""
        if not TOKENIZER_AVAILABLE:
            print("⚠️ Tokenizer check skipped - transformers non disponibile")
            return True
            
        print(f"🔍 Controllo compatibilità tokenizer: {tokenizer_path}")
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        except Exception as e:
            print(f"❌ Errore caricamento tokenizer: {e}")
            self.issues.append(f"Tokenizer load error: {e}")
            return False
        
        # Estrai simboli
        symbols = []
        if 'approved_symbols' in self.registry:
            for entry in self.registry['approved_symbols']:
                if isinstance(entry, dict) and 'symbol' in entry:
                    symbols.append(entry['symbol'])
        elif 'unicode_symbols' in self.registry:
            for domain, entries in self.registry['unicode_symbols'].items():
                if isinstance(entries, list):
                    for entry in entries:
                        if isinstance(entry, dict) and 'symbol' in entry:
                            symbols.append(entry['symbol'])
        else:
            for category, entries in self.registry.items():
                if not isinstance(entries, list):
                    continue
                for entry in entries:
                    if isinstance(entry, dict) and 'symbol' in entry:
                        symbols.append(entry['symbol'])
        
        # Test tokenization
        problematic_symbols = []
        
        for symbol in symbols:
            try:
                tokens = tokenizer.encode(symbol, add_special_tokens=False)
                decoded = tokenizer.decode(tokens, skip_special_tokens=True)
                
                # Check atomicity (1 token per symbol)
                if len(tokens) != 1:
                    problematic_symbols.append((symbol, f"Split into {len(tokens)} tokens"))
                
                # Check round-trip fidelity
                elif decoded != symbol:
                    problematic_symbols.append((symbol, f"Round-trip failed: '{decoded}'"))
                    
            except Exception as e:
                problematic_symbols.append((symbol, f"Tokenization error: {e}"))
        
        if problematic_symbols:
            print(f"❌ Trovati {len(problematic_symbols)} simboli problematici:")
            for symbol, issue in problematic_symbols[:10]:  # Mostra primi 10
                print(f"   '{symbol}': {issue}")
                self.issues.append(f"Tokenizer issue: {symbol} ({issue})")
            return False
        
        print(f"✅ Tutti {len(symbols)} simboli sono tokenizer-compatible")
        return True
    
    def generate_frozen_vocab(self, output_path: str = "data/symbols/frozen_vocab_v4.json") -> bool:
        """Genera vocabolario congelato."""
        print("🔒 Generazione vocabolario congelato...")
        
        # Estrai simboli con metadati
        frozen_vocab = {
            'version': '4.0',
            'timestamp': __import__('time').time(),
            'total_symbols': 0,
            'categories': {},
            'symbol_hash': '',
            'symbols': []
        }
        
        all_symbols = []

        # Handle ULTIMATE registry format
        if 'approved_symbols' in self.registry:
            category_groups = {}

            for entry in self.registry['approved_symbols']:
                if isinstance(entry, dict) and 'symbol' in entry:
                    domain = entry.get('domain', 'unknown')

                    if domain not in category_groups:
                        category_groups[domain] = []

                    symbol_data = {
                        'symbol': entry['symbol'],
                        'name': entry.get('id', ''),
                        'category': domain,
                        'unicode_codepoint': ord(entry['symbol'][0]) if entry['symbol'] else 0,
                        'description': entry.get('description', '')
                    }
                    category_groups[domain].append(symbol_data)
                    all_symbols.append(entry['symbol'])

            for category, symbols in category_groups.items():
                frozen_vocab['categories'][category] = {
                    'count': len(symbols),
                    'symbols': symbols
                }
        # Handle Unicode-only registry format
        elif 'unicode_symbols' in self.registry:
            for domain, entries in self.registry['unicode_symbols'].items():
                if isinstance(entries, list):
                    category_symbols = []
                    for entry in entries:
                        if isinstance(entry, dict) and 'symbol' in entry:
                            symbol_data = {
                                'symbol': entry['symbol'],
                                'name': entry.get('id', ''),
                                'category': domain,
                                'unicode_codepoint': ord(entry['symbol'][0]) if entry['symbol'] else 0,
                                'description': entry.get('meaning', ''),
                                'tier': entry.get('tier', ''),
                                'god_mode_certified': entry.get('god_mode_certified', False)
                            }
                            category_symbols.append(symbol_data)
                            all_symbols.append(entry['symbol'])

                    frozen_vocab['categories'][domain] = {
                        'count': len(category_symbols),
                        'symbols': category_symbols
                    }
        else:
            # Handle standard registry format
            for category, entries in self.registry.items():
                if not isinstance(entries, list):
                    continue

                category_symbols = []
                for entry in entries:
                    if isinstance(entry, dict) and 'symbol' in entry:
                        symbol_data = {
                            'symbol': entry['symbol'],
                            'name': entry.get('name', ''),
                            'category': category,
                            'unicode_codepoint': ord(entry['symbol'][0]) if entry['symbol'] else 0,
                            'description': entry.get('description', '')
                        }
                        category_symbols.append(symbol_data)
                        all_symbols.append(entry['symbol'])

                frozen_vocab['categories'][category] = {
                    'count': len(category_symbols),
                    'symbols': category_symbols
                }
        
        # Calcola hash per verifica integrità
        symbols_str = ''.join(sorted(all_symbols))
        frozen_vocab['symbol_hash'] = hashlib.sha256(symbols_str.encode('utf-8')).hexdigest()
        frozen_vocab['total_symbols'] = len(all_symbols)
        frozen_vocab['symbols'] = sorted(all_symbols)
        
        # Salva vocabolario congelato
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(frozen_vocab, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Vocabolario congelato salvato: {output_path}")
            print(f"   - Simboli totali: {frozen_vocab['total_symbols']}")
            print(f"   - Categorie: {len(frozen_vocab['categories'])}")
            print(f"   - Hash integrità: {frozen_vocab['symbol_hash'][:16]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ Errore salvataggio vocabolario: {e}")
            self.issues.append(f"Vocab save error: {e}")
            return False
    
    def run_full_check(self, tokenizer_path: str = None) -> bool:
        """Esegue check completo."""
        print("🚀 NEUROGLYPH Registry Consistency Check")
        print("=" * 50)
        
        if not self.registry:
            print("❌ Registry vuoto o non caricabile")
            return False
        
        checks = [
            ("Unicità simboli", self.check_symbol_uniqueness),
            ("Validità Unicode", self.check_unicode_validity),
        ]
        
        if tokenizer_path and TOKENIZER_AVAILABLE:
            checks.append(("Compatibilità tokenizer", lambda: self.check_tokenizer_compatibility(tokenizer_path)))
        
        all_passed = True
        
        for check_name, check_func in checks:
            try:
                if not check_func():
                    all_passed = False
            except Exception as e:
                print(f"❌ {check_name} fallito: {e}")
                self.issues.append(f"{check_name} error: {e}")
                all_passed = False
        
        print("\n" + "=" * 50)
        
        if all_passed:
            print("✅ Tutti i check passati - Registry consistente")
            
            # Genera vocabolario congelato
            if self.generate_frozen_vocab():
                print("🔒 Vocabolario congelato generato con successo")
            
            return True
        else:
            print(f"❌ {len(self.issues)} problemi trovati:")
            for issue in self.issues:
                print(f"   - {issue}")
            return False


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="NEUROGLYPH Registry Consistency Checker")
    parser.add_argument('--registry', default='data/symbols/registry_v3.json',
                       help='Path al registry simboli')
    parser.add_argument('--tokenizer', default='Qwen/Qwen2.5-Coder-1.5B-Instruct',
                       help='Tokenizer da testare')
    parser.add_argument('--skip-tokenizer', action='store_true',
                       help='Salta check tokenizer')
    
    args = parser.parse_args()
    
    checker = RegistryConsistencyChecker(args.registry)
    
    tokenizer_path = None if args.skip_tokenizer else args.tokenizer
    success = checker.run_full_check(tokenizer_path)
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
