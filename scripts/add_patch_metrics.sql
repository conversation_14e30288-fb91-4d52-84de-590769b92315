-- Migrazione Fase 2.4: Aggiunta tabella patch_metrics
-- Per telemetria has_errors flow

-- Crea tabella patch_metrics se non esiste
CREATE TABLE IF NOT EXISTS patch_metrics(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ts REAL NOT NULL,
    latency_ms REAL NOT NULL,
    cache_hit INTEGER NOT NULL,
    error_count INTEGER DEFAULT 0,
    success_rate REAL DEFAULT 0.0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance
CREATE INDEX IF NOT EXISTS idx_pm_ts
  ON patch_metrics(ts DESC);
  
CREATE INDEX IF NOT EXISTS idx_pm_cache_hit
  ON patch_metrics(cache_hit, ts DESC);

CREATE INDEX IF NOT EXISTS idx_pm_latency
  ON patch_metrics(latency_ms, ts DESC);

-- Verifica tabella creata
SELECT name FROM sqlite_master WHERE type='table' AND name='patch_metrics';

-- Verifica indici creati
SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='patch_metrics';

-- Query di esempio per telemetria
-- SELECT 
--   COUNT(*) as total_calls,
--   AVG(latency_ms) as avg_latency,
--   MAX(latency_ms) as max_latency,
--   SUM(cache_hit) as cache_hits,
--   AVG(success_rate) as avg_success_rate
-- FROM patch_metrics 
-- WHERE ts > (strftime('%s', 'now') - 3600); -- Ultima ora
