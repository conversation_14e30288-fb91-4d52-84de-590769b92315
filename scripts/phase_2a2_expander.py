#!/usr/bin/env python3
"""
NEUROGLYPH Phase 2A.2 Expander
Espansione strategica da 10,000 → 20,000 esempi

Piano di espansione:
1. Gap residui: 5 simboli matematici (≤, ≥, ≠, ≈, ∞) → +300 esempi
2. Nuovi simboli avanzati: 20 simboli → +4,000-6,000 esempi  
3. Multi-step reasoning: esempi mixed/multi-hop → +1,000 esempi
4. Bilanciamento e qualità: shuffle stratificato + validation continua

Target: 20,000 esempi con coverage 99%+ su tutti i simboli critici
"""

import json
import random
import logging
from pathlib import Path
from typing import Dict, List, Set
from datetime import datetime
from collections import Counter

from excellence_expansion_config import ExcellenceConfig
from coverage_analyzer import CoverageAnalyzer
from symbolic_dataset_generator import SymbolicDatasetGenerator, SymbolicExample
from symbolic_dataset_validator import SymbolicDatasetValidator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Phase2A2Expander:
    """Espansore specifico per Phase 2A.2 - Mathematical operators focus."""
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """Inizializza espansore Phase 2A.2."""
        self.registry_path = registry_path
        self.generator = SymbolicDatasetGenerator(registry_path)
        self.validator = SymbolicDatasetValidator(registry_path)
        self.config = ExcellenceConfig()
        
        # Simboli target per Phase 2A.2
        self.gap_symbols = ['≤', '≥', '≠', '≈', '∞']  # Gap residui
        self.advanced_symbols = self._get_advanced_symbols()  # Nuovi simboli avanzati
        
        logger.info(f"🚀 Phase2A2Expander inizializzato")
        logger.info(f"   Gap symbols: {len(self.gap_symbols)}")
        logger.info(f"   Advanced symbols: {len(self.advanced_symbols)}")
    
    def _get_advanced_symbols(self) -> List[str]:
        """Identifica simboli avanzati per espansione."""
        # Simboli logici avanzati
        logical_advanced = ['⊨', '⊭', '⊤', '⊥', '≡', '⇔', '↔', '↕', '⟷']
        
        # Simboli matematici avanzati  
        math_advanced = ['±', '∇', '∆', '∮', '∯', '∰', '∱', '∲', '∳']
        
        # Simboli di codice avanzati
        code_advanced = ['⟨', '⟩', '⇄', '⇆', '⇈', '⇊', '⇌', '⇎', '⇐', '⇑', '⇒', '⇓']
        
        # Simboli meta-cognitivi
        meta_advanced = ['🧠', '🔮', '⚖', '✪', '⛓', '🎯', '📊', '🔍']
        
        return logical_advanced + math_advanced + code_advanced + meta_advanced
    
    def expand_to_20k(self, input_path: str, output_path: str) -> Dict:
        """
        Espande dataset da ~10k a 20k esempi.
        
        Args:
            input_path: Dataset attuale (~10k esempi)
            output_path: Dataset espanso (20k esempi)
            
        Returns:
            Report di espansione dettagliato
        """
        logger.info(f"🚀 Starting Phase 2A.2 expansion to 20,000 examples")
        
        # Analizza stato attuale
        analyzer = CoverageAnalyzer(self.config)
        current_report = analyzer.analyze_dataset(input_path)
        
        logger.info(f"📊 Current state:")
        logger.info(f"   Examples: {current_report.total_examples:,}")
        logger.info(f"   Coverage: {current_report.calculate_coverage_percentage():.1%}")
        
        # Carica esempi esistenti
        existing_examples = self._load_existing_dataset(input_path)
        
        # Piano di espansione Phase 2A.2
        expansion_plan = self._create_phase_2a2_plan(current_report)
        
        # Esegui espansione in fasi
        all_new_examples = []
        
        # FASE 1: Riempi gap residui (≤, ≥, ≠, ≈, ∞)
        logger.info(f"📝 FASE 1: Filling residual gaps")
        gap_examples = self._fill_residual_gaps(current_report)
        all_new_examples.extend(gap_examples)
        logger.info(f"   ✅ Generated {len(gap_examples)} gap-filling examples")
        
        # FASE 2: Espandi simboli avanzati
        logger.info(f"📝 FASE 2: Expanding advanced symbols")
        advanced_examples = self._expand_advanced_symbols(expansion_plan)
        all_new_examples.extend(advanced_examples)
        logger.info(f"   ✅ Generated {len(advanced_examples)} advanced symbol examples")
        
        # FASE 3: Multi-step reasoning
        logger.info(f"📝 FASE 3: Multi-step reasoning examples")
        multistep_examples = self._generate_multistep_examples(1000)
        all_new_examples.extend(multistep_examples)
        logger.info(f"   ✅ Generated {len(multistep_examples)} multi-step examples")
        
        # Combina e bilancia
        all_examples = existing_examples + all_new_examples
        
        # Shuffle stratificato per simbolo e difficoltà
        all_examples = self._stratified_shuffle(all_examples)
        
        # Limita a 20,000 se necessario
        if len(all_examples) > 20000:
            all_examples = all_examples[:20000]
        
        # Salva dataset espanso
        self._save_dataset(all_examples, output_path)
        self._create_splits(all_examples, output_path)
        
        # Validazione finale
        final_validation = self.validator.validate_dataset(output_path)
        
        # Report finale
        report = {
            'phase': '2A.2',
            'initial_examples': len(existing_examples),
            'generated_examples': len(all_new_examples),
            'final_examples': len(all_examples),
            'gap_examples': len(gap_examples),
            'advanced_examples': len(advanced_examples),
            'multistep_examples': len(multistep_examples),
            'validation_rate': final_validation.validation_rate,
            'quality_scores': final_validation.quality_scores,
            'expansion_plan': expansion_plan
        }
        
        logger.info(f"✅ Phase 2A.2 expansion completed!")
        logger.info(f"   Final examples: {len(all_examples):,}")
        logger.info(f"   Validation rate: {final_validation.validation_rate:.2%}")
        
        return report
    
    def _create_phase_2a2_plan(self, current_report) -> Dict[str, int]:
        """Crea piano di espansione per Phase 2A.2."""
        plan = {}
        
        # Gap residui: portare a 300 esempi ciascuno
        for symbol in self.gap_symbols:
            current_count = current_report.symbols_coverage.get(symbol, 0)
            target = 300
            gap = max(0, target - current_count)
            if gap > 0:
                plan[symbol] = gap
        
        # Simboli avanzati: 200-300 esempi ciascuno
        available_budget = 8000  # Budget per simboli avanzati
        symbols_to_expand = self.advanced_symbols[:20]  # Top 20 simboli
        
        per_symbol = available_budget // len(symbols_to_expand)
        for symbol in symbols_to_expand:
            current_count = current_report.symbols_coverage.get(symbol, 0)
            target = min(300, per_symbol)
            needed = max(0, target - current_count)
            if needed > 0:
                plan[symbol] = needed
        
        logger.info(f"📋 Phase 2A.2 expansion plan:")
        logger.info(f"   Gap symbols: {sum(plan.get(s, 0) for s in self.gap_symbols)} examples")
        logger.info(f"   Advanced symbols: {sum(plan.get(s, 0) for s in symbols_to_expand)} examples")
        
        return plan
    
    def _fill_residual_gaps(self, current_report) -> List[SymbolicExample]:
        """Riempi gap residui sui 5 simboli matematici."""
        examples = []
        
        for symbol in self.gap_symbols:
            current_count = current_report.symbols_coverage.get(symbol, 0)
            target = 300
            needed = max(0, target - current_count)
            
            if needed > 0:
                logger.info(f"   Filling gap for {symbol}: +{needed} examples")
                symbol_examples = self._generate_symbol_examples(symbol, needed, 'mathematical')
                examples.extend(symbol_examples)
        
        return examples
    
    def _expand_advanced_symbols(self, expansion_plan) -> List[SymbolicExample]:
        """Espandi simboli avanzati secondo piano."""
        examples = []
        
        for symbol, count in expansion_plan.items():
            if symbol not in self.gap_symbols and count > 0:
                logger.info(f"   Expanding {symbol}: +{count} examples")
                
                # Determina categoria del simbolo
                if symbol in ['⊨', '⊭', '⊤', '⊥', '≡', '⇔', '↔', '↕', '⟷']:
                    category = 'logical'
                elif symbol in ['±', '∇', '∆', '∮', '∯', '∰', '∱', '∲', '∳']:
                    category = 'mathematical'
                elif symbol in ['⟨', '⟩', '⇄', '⇆', '⇈', '⇊', '⇌', '⇎', '⇐', '⇑', '⇒', '⇓']:
                    category = 'code'
                else:
                    category = 'meta'
                
                symbol_examples = self._generate_symbol_examples(symbol, count, category)
                examples.extend(symbol_examples)
        
        return examples
    
    def _generate_multistep_examples(self, count: int) -> List[SymbolicExample]:
        """Genera esempi multi-step reasoning."""
        examples = []
        
        logger.info(f"   Generating {count} multi-step reasoning examples")
        
        # Distribuzione difficoltà per multi-step
        difficulties = (['medium'] * int(count * 0.4) + 
                       ['hard'] * int(count * 0.6))
        
        random.shuffle(difficulties)
        
        for i, difficulty in enumerate(difficulties):
            try:
                # Genera esempio multi-step combinando simboli
                example = self._generate_multistep_example(difficulty)
                examples.append(example)
                
                if (i + 1) % 100 == 0:
                    logger.info(f"     Generated {i + 1}/{count} multi-step examples")
                    
            except Exception as e:
                logger.warning(f"Error generating multi-step example: {e}")
        
        return examples
    
    def _generate_multistep_example(self, difficulty: str) -> SymbolicExample:
        """Genera singolo esempio multi-step."""
        # Seleziona 2-4 simboli per combinazione
        num_symbols = 2 if difficulty == 'medium' else random.randint(3, 4)
        
        # Mix di simboli logici e matematici
        logical_pool = ['⊢', '∀', '∃', '⇒', '∧', '∨', '¬']
        math_pool = ['∫', '∂', '∑', '≤', '≥', '≠']
        
        selected_symbols = (random.sample(logical_pool, num_symbols // 2) + 
                          random.sample(math_pool, num_symbols - num_symbols // 2))
        
        # Crea prompt multi-step
        if difficulty == 'medium':
            # 2-step: P ∧ Q, P⇒R ⊢ Q ∧ R
            p1, p2, p3 = 'P', 'Q', 'R'
            prompt_symbolic = f"🧠 {p1} ∧ {p2} ∧ ({p1} ⇒ {p3}) ⊢ ?"
            response_symbolic = f"⊢ {p2} ∧ {p3}"
            reasoning_steps = 2
        else:
            # 3-4 step: Complex reasoning chain
            p1, p2, p3, p4 = 'P', 'Q', 'R', 'S'
            prompt_symbolic = f"🧠 ∀x ({p1}(x) ⇒ {p2}(x)) ∧ ∃x {p1}(x) ∧ ∀x ({p2}(x) ⇒ {p3}(x)) ⊢ ?"
            response_symbolic = f"⊢ ∃x {p2}(x) ∧ ∃x {p3}(x)"
            reasoning_steps = 3
        
        # Versioni naturali
        prompt_natural = f"Given the complex logical premises, what can we conclude through multi-step reasoning?"
        response_natural = f"Through multi-step reasoning, we conclude: {response_symbolic.replace('⊢ ', '')}"
        
        return SymbolicExample(
            id=f"multistep_{random.randint(10000, 99999)}",
            category="meta_reasoning",
            cognitive_tags=["multi_step_reasoning", "complex_inference", "symbolic_chaining"],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=selected_symbols + ['⊢', '∧'],
            difficulty=difficulty,
            reasoning_steps=reasoning_steps,
            metadata={
                "reasoning_type": "multi_step",
                "symbol_combination": selected_symbols,
                "complexity_level": difficulty
            }
        )
    
    def _generate_symbol_examples(self, symbol: str, count: int, category: str) -> List[SymbolicExample]:
        """Genera esempi per simbolo specifico."""
        examples = []
        
        # Distribuzione difficoltà
        difficulties = (['easy'] * int(count * 0.3) + 
                       ['medium'] * int(count * 0.5) + 
                       ['hard'] * int(count * 0.2))
        
        while len(difficulties) < count:
            difficulties.append('medium')
        
        random.shuffle(difficulties)
        
        # Genera esempi
        for difficulty in difficulties:
            try:
                if category == 'logical':
                    example = self.generator.generate_logical_reasoning_example(difficulty)
                elif category == 'mathematical':
                    example = self.generator.generate_mathematical_reasoning_example(difficulty)
                elif category == 'code':
                    example = self.generator.generate_code_generation_example(difficulty)
                else:  # meta
                    example = self.generator.generate_logical_reasoning_example(difficulty)
                    example.category = "meta_reasoning"
                    example.cognitive_tags = ["meta_cognition", "symbolic_reasoning"]
                
                # Assicura presenza del simbolo target
                if symbol not in example.symbols_used:
                    example.symbols_used.append(symbol)
                
                examples.append(example)
                
            except Exception as e:
                logger.warning(f"Error generating example for {symbol}: {e}")
        
        return examples
    
    def _stratified_shuffle(self, examples: List[SymbolicExample]) -> List[SymbolicExample]:
        """Shuffle stratificato per simbolo e difficoltà."""
        # Raggruppa per difficoltà
        by_difficulty = {'easy': [], 'medium': [], 'hard': []}
        
        for example in examples:
            difficulty = example.difficulty
            by_difficulty[difficulty].append(example)
        
        # Shuffle ogni gruppo
        for difficulty_examples in by_difficulty.values():
            random.shuffle(difficulty_examples)
        
        # Ricombina mantenendo distribuzione
        shuffled = []
        max_len = max(len(group) for group in by_difficulty.values())
        
        for i in range(max_len):
            for difficulty in ['easy', 'medium', 'hard']:
                if i < len(by_difficulty[difficulty]):
                    shuffled.append(by_difficulty[difficulty][i])
        
        return shuffled
    
    def _load_existing_dataset(self, path: str) -> List[SymbolicExample]:
        """Carica dataset esistente."""
        examples = []
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        data = json.loads(line.strip())
                        example = SymbolicExample(
                            id=data['id'],
                            category=data['category'],
                            cognitive_tags=data['cognitive_tags'],
                            prompt_symbolic=data['prompt_symbolic'],
                            response_symbolic=data['response_symbolic'],
                            prompt_natural=data['prompt_natural'],
                            response_natural=data['response_natural'],
                            symbols_used=data['symbols_used'],
                            difficulty=data['difficulty'],
                            reasoning_steps=data['reasoning_steps'],
                            metadata=data.get('metadata', {})
                        )
                        examples.append(example)
        
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
        
        return examples
    
    def _save_dataset(self, examples: List[SymbolicExample], output_path: str):
        """Salva dataset."""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for example in examples:
                example_dict = {
                    'id': example.id,
                    'category': example.category,
                    'cognitive_tags': example.cognitive_tags,
                    'prompt_symbolic': example.prompt_symbolic,
                    'response_symbolic': example.response_symbolic,
                    'prompt_natural': example.prompt_natural,
                    'response_natural': example.response_natural,
                    'symbols_used': example.symbols_used,
                    'difficulty': example.difficulty,
                    'reasoning_steps': example.reasoning_steps,
                    'metadata': example.metadata,
                    'generated_at': datetime.now().isoformat()
                }
                f.write(json.dumps(example_dict, ensure_ascii=False) + '\n')
        
        logger.info(f"💾 Dataset saved: {output_file}")
    
    def _create_splits(self, examples: List[SymbolicExample], base_path: str):
        """Crea split train/val/test."""
        total = len(examples)
        train_size = int(total * 0.8)
        val_size = int(total * 0.1)
        
        train_examples = examples[:train_size]
        val_examples = examples[train_size:train_size + val_size]
        test_examples = examples[train_size + val_size:]
        
        base_dir = Path(base_path).parent
        
        splits = {
            'train': train_examples,
            'val': val_examples,
            'test': test_examples
        }
        
        for split_name, split_examples in splits.items():
            split_path = base_dir / f"neuroglyph_symbolic_{split_name}_20k.jsonl"
            self._save_dataset(split_examples, str(split_path))
            logger.info(f"📁 {split_name.upper()}: {len(split_examples)} examples")


def main():
    """Pipeline principale Phase 2A.2."""
    print("🚀 NEUROGLYPH Phase 2A.2 Expander")
    print("=" * 60)
    print("Target: 10,000 → 20,000 esempi")
    print("Focus: Mathematical operators + Advanced symbols")
    
    # Percorsi
    input_path = "data/datasets/symbolic/neuroglyph_symbolic_excellence.jsonl"
    output_path = "data/datasets/symbolic/neuroglyph_symbolic_20k.jsonl"
    
    # Espandi a 20k
    expander = Phase2A2Expander()
    report = expander.expand_to_20k(input_path, output_path)
    
    # Stampa risultati
    print(f"\n📊 PHASE 2A.2 RESULTS")
    print("=" * 60)
    print(f"✅ Phase: {report['phase']}")
    print(f"📈 Initial examples: {report['initial_examples']:,}")
    print(f"✨ Generated examples: {report['generated_examples']:,}")
    print(f"📁 Final examples: {report['final_examples']:,}")
    print(f"🔧 Gap examples: {report['gap_examples']:,}")
    print(f"🔮 Advanced examples: {report['advanced_examples']:,}")
    print(f"🧠 Multi-step examples: {report['multistep_examples']:,}")
    print(f"🎯 Validation rate: {report['validation_rate']:.2%}")
    
    if 'quality_scores' in report:
        overall_quality = report['quality_scores'].get('overall_quality', 0)
        print(f"⭐ Overall quality: {overall_quality:.3f}")
    
    print(f"\n🎉 Phase 2A.2 expansion completed!")
    print(f"🎯 Next: Phase 2A.3 (20,000 → 30,000 examples)")


if __name__ == "__main__":
    main()
