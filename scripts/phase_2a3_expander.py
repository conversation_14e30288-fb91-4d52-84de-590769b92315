#!/usr/bin/env python3
"""
NEUROGLYPH Phase 2A.3 Expander
Espansione finale innovativa da 14,460 → 20,000 esempi

Innovazioni strategiche:
1. Combinazioni 3-4 simboli complesse (∀x ∃y ∈ ℝ: x ≠ y ⇒ f(x) ⊥ f(y))
2. Simbo<PERSON> di struttura (⊂, ⊇, ∅, ∴, ⇔)
3. Problemi generativi a ritroso ("quale formula può derivare da questa inferenza?")
4. Micro-storie logiche ("se Maria ha ∞ risorse e divide in ↕ modo...")

Target: 20,000 esempi con innovazione narrativa e complessità simbolica
"""

import json
import random
import logging
from pathlib import Path
from typing import Dict, List, Set, Tuple
from datetime import datetime
from collections import Counter

from excellence_expansion_config import ExcellenceConfig
from coverage_analyzer import CoverageAnalyzer
from symbolic_dataset_generator import SymbolicDatasetGenerator, SymbolicExample
from symbolic_dataset_validator import SymbolicDatasetValidator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Phase2A3Expander:
    """Espansore innovativo per Phase 2A.3 - Combinazioni complesse e micro-storie."""

    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """Inizializza espansore Phase 2A.3."""
        self.registry_path = registry_path
        self.generator = SymbolicDatasetGenerator(registry_path)
        self.validator = SymbolicDatasetValidator(registry_path)
        self.config = ExcellenceConfig()

        # Simboli di struttura per Phase 2A.3
        self.structure_symbols = ['⊂', '⊇', '∅', '∴', '⇔', '∈', '∉', '⊆', '⊈', '∪', '∩']

        # Nomi per micro-storie
        self.story_names = ['Maria', 'Luca', 'Anna', 'Marco', 'Sofia', 'Paolo', 'Elena', 'Andrea']
        self.story_objects = ['risorse', 'libri', 'monete', 'carte', 'punti', 'energie', 'dati', 'elementi']

        logger.info(f"🚀 Phase2A3Expander inizializzato")
        logger.info(f"   Structure symbols: {len(self.structure_symbols)}")
        logger.info(f"   Story elements: {len(self.story_names)} names, {len(self.story_objects)} objects")

    def expand_to_20k(self, input_path: str, output_path: str) -> Dict:
        """
        Espande dataset da ~14.5k a 20k esempi con innovazioni.

        Args:
            input_path: Dataset attuale (~14.5k esempi)
            output_path: Dataset finale (20k esempi)

        Returns:
            Report di espansione dettagliato
        """
        logger.info(f"🚀 Starting Phase 2A.3 innovative expansion to 20,000 examples")

        # Analizza stato attuale
        analyzer = CoverageAnalyzer(self.config)
        current_report = analyzer.analyze_dataset(input_path)

        current_examples = current_report.total_examples
        target_examples = 20000
        needed_examples = target_examples - current_examples

        logger.info(f"📊 Current state:")
        logger.info(f"   Examples: {current_examples:,}")
        logger.info(f"   Target: {target_examples:,}")
        logger.info(f"   Needed: {needed_examples:,}")

        # Carica esempi esistenti
        existing_examples = self._load_existing_dataset(input_path)

        # Piano di espansione innovativo
        all_new_examples = []

        # FASE 1: Combinazioni 3-4 simboli complesse (40% del budget)
        complex_count = int(needed_examples * 0.40)
        logger.info(f"📝 FASE 1: Complex 3-4 symbol combinations ({complex_count} examples)")
        complex_examples = self._generate_complex_combinations(complex_count)
        all_new_examples.extend(complex_examples)
        logger.info(f"   ✅ Generated {len(complex_examples)} complex combination examples")

        # FASE 2: Simboli di struttura (25% del budget)
        structure_count = int(needed_examples * 0.25)
        logger.info(f"📝 FASE 2: Structure symbols expansion ({structure_count} examples)")
        structure_examples = self._generate_structure_examples(structure_count)
        all_new_examples.extend(structure_examples)
        logger.info(f"   ✅ Generated {len(structure_examples)} structure symbol examples")

        # FASE 3: Problemi generativi a ritroso (20% del budget)
        reverse_count = int(needed_examples * 0.20)
        logger.info(f"📝 FASE 3: Reverse generative problems ({reverse_count} examples)")
        reverse_examples = self._generate_reverse_problems(reverse_count)
        all_new_examples.extend(reverse_examples)
        logger.info(f"   ✅ Generated {len(reverse_examples)} reverse problem examples")

        # FASE 4: Micro-storie logiche (15% del budget)
        story_count = needed_examples - len(all_new_examples)  # Resto del budget
        logger.info(f"📝 FASE 4: Logical micro-stories ({story_count} examples)")
        story_examples = self._generate_micro_stories(story_count)
        all_new_examples.extend(story_examples)
        logger.info(f"   ✅ Generated {len(story_examples)} micro-story examples")

        # Combina e bilancia
        all_examples = existing_examples + all_new_examples

        # Shuffle innovativo per diversità massima
        all_examples = self._innovative_shuffle(all_examples)

        # Limita esattamente a 20,000
        if len(all_examples) > 20000:
            all_examples = all_examples[:20000]

        # Salva dataset finale
        self._save_dataset(all_examples, output_path)
        self._create_splits(all_examples, output_path)

        # Validazione finale
        final_validation = self.validator.validate_dataset(output_path)

        # Report finale
        report = {
            'phase': '2A.3',
            'initial_examples': len(existing_examples),
            'generated_examples': len(all_new_examples),
            'final_examples': len(all_examples),
            'complex_combinations': len(complex_examples),
            'structure_symbols': len(structure_examples),
            'reverse_problems': len(reverse_examples),
            'micro_stories': len(story_examples),
            'validation_rate': final_validation.validation_rate,
            'quality_scores': final_validation.quality_scores
        }

        logger.info(f"✅ Phase 2A.3 innovative expansion completed!")
        logger.info(f"   Final examples: {len(all_examples):,}")
        logger.info(f"   Validation rate: {final_validation.validation_rate:.2%}")

        return report

    def _generate_complex_combinations(self, count: int) -> List:
        """Genera combinazioni complesse 3-4 simboli."""
        examples = []

        logger.info(f"   Generating {count} complex 3-4 symbol combinations")

        # Template per combinazioni complesse
        templates = [
            # Template matematico-logico
            {
                'pattern': '∀x ∃y ∈ ℝ: x ≠ y ⇒ f(x) ⊥ f(y)',
                'symbols': ['∀', '∃', '∈', '≠', '⇒', '⊥'],
                'category': 'mathematical_logic',
                'complexity': 'hard'
            },
            # Template insiemistico
            {
                'pattern': '∀A ⊂ B ∧ B ⊆ C ⇒ A ⊂ C ∧ A ≠ ∅',
                'symbols': ['∀', '⊂', '∧', '⊆', '⇒', '≠', '∅'],
                'category': 'set_theory',
                'complexity': 'hard'
            },
            # Template logico avanzato
            {
                'pattern': '(P ∧ Q) ⇔ ¬(¬P ∨ ¬Q) ∴ (P ⇒ R) ∧ (Q ⇒ S)',
                'symbols': ['∧', '⇔', '¬', '∨', '∴', '⇒'],
                'category': 'propositional_logic',
                'complexity': 'hard'
            },
            # Template funzionale
            {
                'pattern': '∀f: A → B, ∃g: B → A | g ∘ f = id_A ⇒ f ∈ Inj(A,B)',
                'symbols': ['∀', '→', '∃', '∘', '⇒', '∈'],
                'category': 'function_theory',
                'complexity': 'hard'
            }
        ]

        for i in range(count):
            try:
                template = random.choice(templates)
                example = self._create_complex_example(template, i)
                examples.append(example)

                if (i + 1) % 200 == 0:
                    logger.info(f"     Generated {i + 1}/{count} complex examples")

            except Exception as e:
                logger.warning(f"Error generating complex example: {e}")

        return examples

    def _create_complex_example(self, template: Dict, index: int):
        """Crea esempio complesso da template."""
        pattern = template['pattern']
        symbols = template['symbols']
        category = template['category']
        complexity = template['complexity']

        # Genera variazioni del pattern
        variables = ['x', 'y', 'z', 'w', 'u', 'v']
        sets = ['A', 'B', 'C', 'D', 'X', 'Y']
        functions = ['f', 'g', 'h', 'φ', 'ψ', 'τ']

        # Sostituzioni casuali per variabilità
        varied_pattern = pattern
        if 'x' in pattern:
            var_map = dict(zip(['x', 'y', 'z'], random.sample(variables, 3)))
            for old, new in var_map.items():
                varied_pattern = varied_pattern.replace(old, new)

        # Crea prompt e response
        prompt_symbolic = f"🧠 {varied_pattern} ⊢ ?"

        # Response basata sulla categoria
        if category == 'mathematical_logic':
            response_symbolic = "⊢ ∃x,y: f(x) ⊥ f(y) ∧ x ≠ y"
        elif category == 'set_theory':
            response_symbolic = "⊢ A ⊂ C ∧ A ≠ ∅"
        elif category == 'propositional_logic':
            response_symbolic = "⊢ (P ⇒ R) ∧ (Q ⇒ S)"
        else:
            response_symbolic = "⊢ f ∈ Inj(A,B)"

        # Versioni naturali
        prompt_natural = f"Given the complex logical-mathematical statement: {varied_pattern}, what can we conclude?"
        response_natural = f"From the complex reasoning, we conclude: {response_symbolic.replace('⊢ ', '')}"

        return SymbolicExample(
            id=f"complex_{category}_{index}",
            category="complex_reasoning",
            cognitive_tags=["complex_combinations", "multi_symbol_reasoning", category],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=symbols + ['⊢'],
            difficulty=complexity,
            reasoning_steps=random.randint(3, 5),
            metadata={
                "reasoning_type": "complex_combination",
                "template_category": category,
                "symbol_count": len(symbols),
                "pattern": pattern
            }
        )

    def _generate_structure_examples(self, count: int) -> List:
        """Genera esempi con simboli di struttura."""
        examples = []

        logger.info(f"   Generating {count} structure symbol examples")

        # Distribuzione simboli di struttura
        symbol_distribution = {
            '⊂': count // 4,      # Subset
            '⊇': count // 4,      # Superset
            '∅': count // 6,      # Empty set
            '∴': count // 6,      # Therefore
            '⇔': count // 6,      # If and only if
            '∈': count // 8,      # Element of
            '∉': count // 8,      # Not element of
            '⊆': count // 10,     # Subset or equal
            '⊈': count // 10,     # Not subset
            '∪': count // 12,     # Union
            '∩': count // 12      # Intersection
        }

        # Riempi il resto
        remaining = count - sum(symbol_distribution.values())
        symbol_distribution['⊂'] += remaining

        for symbol, symbol_count in symbol_distribution.items():
            for i in range(symbol_count):
                try:
                    example = self._create_structure_example(symbol, i)
                    examples.append(example)
                except Exception as e:
                    logger.warning(f"Error generating structure example for {symbol}: {e}")

        return examples

    def _create_structure_example(self, symbol: str, index: int):
        """Crea esempio con simbolo di struttura."""
        sets = ['A', 'B', 'C', 'D', 'X', 'Y', 'Z']
        elements = ['x', 'y', 'z', 'a', 'b', 'c']

        if symbol == '⊂':  # Subset
            set1, set2, set3 = random.sample(sets, 3)
            prompt_symbolic = f"🧠 {set1} ⊂ {set2} ∧ {set2} ⊂ {set3} ⊢ ?"
            response_symbolic = f"⊢ {set1} ⊂ {set3}"
            natural_desc = f"subset transitivity: if {set1} is a subset of {set2} and {set2} is a subset of {set3}"

        elif symbol == '⊇':  # Superset
            set1, set2 = random.sample(sets, 2)
            prompt_symbolic = f"🧠 {set1} ⊇ {set2} ∧ {set2} ≠ ∅ ⊢ ?"
            response_symbolic = f"⊢ {set1} ≠ ∅"
            natural_desc = f"superset property: if {set1} contains {set2} and {set2} is non-empty"

        elif symbol == '∅':  # Empty set
            set1, set2 = random.sample(sets, 2)
            prompt_symbolic = f"🧠 {set1} ∩ {set2} = ∅ ∧ x ∈ {set1} ⊢ ?"
            response_symbolic = f"⊢ x ∉ {set2}"
            natural_desc = f"empty intersection: if {set1} and {set2} have no common elements"

        elif symbol == '∴':  # Therefore
            prop1, prop2, prop3 = 'P', 'Q', 'R'
            prompt_symbolic = f"🧠 {prop1} ⇒ {prop2} ∧ {prop2} ⇒ {prop3} ∴ ?"
            response_symbolic = f"⊢ {prop1} ⇒ {prop3}"
            natural_desc = f"logical transitivity: if P implies Q and Q implies R, therefore"

        elif symbol == '⇔':  # If and only if
            prop1, prop2 = 'P', 'Q'
            prompt_symbolic = f"🧠 {prop1} ⇔ {prop2} ∧ {prop1} ⊢ ?"
            response_symbolic = f"⊢ {prop2}"
            natural_desc = f"biconditional: P if and only if Q, and P is true"

        elif symbol == '∈':  # Element of
            elem = random.choice(elements)
            set1, set2 = random.sample(sets, 2)
            prompt_symbolic = f"🧠 {elem} ∈ {set1} ∧ {set1} ⊂ {set2} ⊢ ?"
            response_symbolic = f"⊢ {elem} ∈ {set2}"
            natural_desc = f"element membership: if {elem} belongs to {set1} and {set1} is subset of {set2}"

        elif symbol == '∉':  # Not element of
            elem = random.choice(elements)
            set1 = random.choice(sets)
            prompt_symbolic = f"🧠 {elem} ∉ {set1} ∧ {set1} ≠ ∅ ⊢ ?"
            response_symbolic = f"⊢ ∃y ∈ {set1}: y ≠ {elem}"
            natural_desc = f"non-membership: if {elem} does not belong to non-empty {set1}"

        else:  # Default case for other symbols
            set1, set2 = random.sample(sets, 2)
            prompt_symbolic = f"🧠 {set1} {symbol} {set2} ⊢ ?"
            response_symbolic = f"⊢ {set1} ≠ ∅ ∨ {set2} ≠ ∅"
            natural_desc = f"set operation with {symbol}"

        prompt_natural = f"Given the set-theoretic statement about {natural_desc}, what can we conclude?"
        response_natural = f"We can conclude: {response_symbolic.replace('⊢ ', '')}"

        return SymbolicExample(
            id=f"structure_{symbol}_{index}",
            category="set_theory",
            cognitive_tags=["structure_symbols", "set_operations", "mathematical_reasoning"],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=[symbol, '⊢', '∧'],
            difficulty='medium',
            reasoning_steps=2,
            metadata={
                "reasoning_type": "structure_symbol",
                "primary_symbol": symbol,
                "domain": "set_theory"
            }
        )

    def _generate_reverse_problems(self, count: int) -> List:
        """Genera problemi generativi a ritroso."""
        examples = []

        logger.info(f"   Generating {count} reverse generative problems")

        # Template per problemi a ritroso
        reverse_templates = [
            {
                'conclusion': '⊢ P ∧ Q',
                'possible_premises': ['P ∧ Q', 'P ∧ Q ∧ R', '(P ∨ R) ∧ (Q ∨ S) ∧ ¬R ∧ ¬S'],
                'category': 'logical_reverse'
            },
            {
                'conclusion': '⊢ ∃x P(x)',
                'possible_premises': ['∀x (Q(x) ⇒ P(x)) ∧ ∃x Q(x)', 'P(a) ∧ a ∈ Domain'],
                'category': 'quantifier_reverse'
            },
            {
                'conclusion': '⊢ A ⊂ C',
                'possible_premises': ['A ⊂ B ∧ B ⊂ C', 'A ⊆ B ∧ B ⊂ C ∧ A ≠ B'],
                'category': 'set_reverse'
            },
            {
                'conclusion': '⊢ f(x) = y',
                'possible_premises': ['f: X → Y ∧ x ∈ X ∧ f injective ∧ f(x) defined'],
                'category': 'function_reverse'
            }
        ]

        for i in range(count):
            try:
                template = random.choice(reverse_templates)
                example = self._create_reverse_example(template, i)
                examples.append(example)

                if (i + 1) % 200 == 0:
                    logger.info(f"     Generated {i + 1}/{count} reverse examples")

            except Exception as e:
                logger.warning(f"Error generating reverse example: {e}")

        return examples

    def _create_reverse_example(self, template: Dict, index: int):
        """Crea esempio di problema a ritroso."""
        conclusion = template['conclusion']
        premises = template['possible_premises']
        category = template['category']

        # Seleziona premessa casuale
        selected_premise = random.choice(premises)

        # Crea prompt a ritroso
        prompt_symbolic = f"🔮 Data la conclusione: {conclusion}, quale formula può derivare questa inferenza?"
        response_symbolic = f"⊢ Possibile premessa: {selected_premise}"

        # Versioni naturali
        prompt_natural = f"Given the conclusion '{conclusion.replace('⊢ ', '')}', what logical premise could lead to this inference?"
        response_natural = f"A possible premise that leads to this conclusion is: {selected_premise}"

        # Estrai simboli utilizzati
        symbols_used = []
        for char in conclusion + selected_premise:
            if char in ['⊢', '∧', '∨', '⇒', '∀', '∃', '⊂', '⊆', '≠', '∈']:
                if char not in symbols_used:
                    symbols_used.append(char)

        return SymbolicExample(
            id=f"reverse_{category}_{index}",
            category="reverse_reasoning",
            cognitive_tags=["reverse_generation", "premise_construction", "backward_reasoning"],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=symbols_used,
            difficulty='hard',
            reasoning_steps=3,
            metadata={
                "reasoning_type": "reverse_generative",
                "template_category": category,
                "conclusion": conclusion,
                "premise": selected_premise
            }
        )

    def _generate_micro_stories(self, count: int) -> List:
        """Genera micro-storie logiche innovative."""
        examples = []

        logger.info(f"   Generating {count} logical micro-stories")

        # Template per micro-storie
        story_templates = [
            {
                'pattern': '{name} ha ∞ {objects} e divide in ↕ modo',
                'symbols': ['∞', '↕'],
                'logic': 'infinite_division',
                'conclusion': 'ogni parte rimane ∞'
            },
            {
                'pattern': '{name} colleziona {objects} dove ∀ elemento ≠ precedente',
                'symbols': ['∀', '≠'],
                'logic': 'universal_uniqueness',
                'conclusion': 'collezione senza duplicati'
            },
            {
                'pattern': 'Se {name} ∃ {objects} ∈ insieme A ⇒ successo',
                'symbols': ['∃', '∈', '⇒'],
                'logic': 'existential_condition',
                'conclusion': 'successo garantito se condizione soddisfatta'
            },
            {
                'pattern': '{name} organizza {objects} in ⊂ gruppi dove A ⊂ B ⊂ C',
                'symbols': ['⊂'],
                'logic': 'hierarchical_organization',
                'conclusion': 'struttura gerarchica transitiva'
            },
            {
                'pattern': '{name} applica f: A → B su {objects} ∧ f è iniettiva',
                'symbols': ['→', '∧'],
                'logic': 'function_application',
                'conclusion': 'mappatura uno-a-uno preservata'
            }
        ]

        for i in range(count):
            try:
                template = random.choice(story_templates)
                example = self._create_micro_story_example(template, i)
                examples.append(example)

                if (i + 1) % 100 == 0:
                    logger.info(f"     Generated {i + 1}/{count} micro-story examples")

            except Exception as e:
                logger.warning(f"Error generating micro-story: {e}")

        return examples

    def _create_micro_story_example(self, template: Dict, index: int):
        """Crea esempio di micro-storia logica."""
        pattern = template['pattern']
        symbols = template['symbols']
        logic_type = template['logic']
        conclusion = template['conclusion']

        # Seleziona elementi casuali
        name = random.choice(self.story_names)
        objects = random.choice(self.story_objects)

        # Crea storia specifica
        story = pattern.format(name=name, objects=objects)

        # Crea prompt simbolico
        prompt_symbolic = f"📖 {story} ⊢ ?"

        # Crea response basata sulla logica
        if logic_type == 'infinite_division':
            response_symbolic = f"⊢ ∀ parte: parte = ∞"
        elif logic_type == 'universal_uniqueness':
            response_symbolic = f"⊢ ∀x,y ∈ collezione: x ≠ y"
        elif logic_type == 'existential_condition':
            response_symbolic = f"⊢ ∃x ∈ A ⇒ successo({name})"
        elif logic_type == 'hierarchical_organization':
            response_symbolic = f"⊢ A ⊂ B ⊂ C ⇒ A ⊂ C"
        else:  # function_application
            response_symbolic = f"⊢ ∀x,y ∈ A: f(x) = f(y) ⇒ x = y"

        # Versioni naturali
        prompt_natural = f"In this logical micro-story: '{story}', what can we conclude using symbolic reasoning?"
        response_natural = f"From the story's logical structure, we conclude: {conclusion}"

        return SymbolicExample(
            id=f"story_{logic_type}_{index}",
            category="narrative_reasoning",
            cognitive_tags=["micro_stories", "narrative_logic", "symbolic_storytelling"],
            prompt_symbolic=prompt_symbolic,
            response_symbolic=response_symbolic,
            prompt_natural=prompt_natural,
            response_natural=response_natural,
            symbols_used=symbols + ['⊢'],
            difficulty='medium',
            reasoning_steps=2,
            metadata={
                "reasoning_type": "micro_story",
                "story_character": name,
                "story_objects": objects,
                "logic_pattern": logic_type,
                "narrative_conclusion": conclusion
            }
        )

    def _innovative_shuffle(self, examples: List) -> List:
        """Shuffle innovativo per massima diversità."""
        # Raggruppa per categoria
        by_category = {}
        for example in examples:
            category = example.category
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(example)

        # Shuffle ogni categoria
        for category_examples in by_category.values():
            random.shuffle(category_examples)

        # Interlaccia categorie per diversità
        shuffled = []
        max_len = max(len(group) for group in by_category.values()) if by_category else 0

        categories = list(by_category.keys())
        for i in range(max_len):
            for category in categories:
                if i < len(by_category[category]):
                    shuffled.append(by_category[category][i])

        return shuffled

    def _load_existing_dataset(self, path: str) -> List:
        """Carica dataset esistente."""
        examples = []

        try:
            with open(path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        data = json.loads(line.strip())
                        example = SymbolicExample(
                            id=data['id'],
                            category=data['category'],
                            cognitive_tags=data['cognitive_tags'],
                            prompt_symbolic=data['prompt_symbolic'],
                            response_symbolic=data['response_symbolic'],
                            prompt_natural=data['prompt_natural'],
                            response_natural=data['response_natural'],
                            symbols_used=data['symbols_used'],
                            difficulty=data['difficulty'],
                            reasoning_steps=data['reasoning_steps'],
                            metadata=data.get('metadata', {})
                        )
                        examples.append(example)

        except Exception as e:
            logger.error(f"Error loading dataset: {e}")

        return examples

    def _save_dataset(self, examples: List, output_path: str):
        """Salva dataset."""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            for example in examples:
                example_dict = {
                    'id': example.id,
                    'category': example.category,
                    'cognitive_tags': example.cognitive_tags,
                    'prompt_symbolic': example.prompt_symbolic,
                    'response_symbolic': example.response_symbolic,
                    'prompt_natural': example.prompt_natural,
                    'response_natural': example.response_natural,
                    'symbols_used': example.symbols_used,
                    'difficulty': example.difficulty,
                    'reasoning_steps': example.reasoning_steps,
                    'metadata': example.metadata,
                    'generated_at': datetime.now().isoformat()
                }
                f.write(json.dumps(example_dict, ensure_ascii=False) + '\n')

        logger.info(f"💾 Dataset saved: {output_file}")

    def _create_splits(self, examples: List, base_path: str):
        """Crea split train/val/test."""
        total = len(examples)
        train_size = int(total * 0.8)
        val_size = int(total * 0.1)

        train_examples = examples[:train_size]
        val_examples = examples[train_size:train_size + val_size]
        test_examples = examples[train_size + val_size:]

        base_dir = Path(base_path).parent

        splits = {
            'train': train_examples,
            'val': val_examples,
            'test': test_examples
        }

        for split_name, split_examples in splits.items():
            split_path = base_dir / f"neuroglyph_symbolic_{split_name}_final.jsonl"
            self._save_dataset(split_examples, str(split_path))
            logger.info(f"📁 {split_name.upper()}: {len(split_examples)} examples")


def main():
    """Pipeline principale Phase 2A.3."""
    print("🚀 NEUROGLYPH Phase 2A.3 Innovative Expander")
    print("=" * 60)
    print("Target: 14,460 → 20,000 esempi")
    print("Innovations: Complex combinations + Structure symbols + Reverse problems + Micro-stories")

    # Percorsi
    input_path = "data/datasets/symbolic/neuroglyph_symbolic_20k.jsonl"
    output_path = "data/datasets/symbolic/neuroglyph_symbolic_final.jsonl"

    # Espandi a 20k con innovazioni
    expander = Phase2A3Expander()
    report = expander.expand_to_20k(input_path, output_path)

    # Stampa risultati
    print(f"\n📊 PHASE 2A.3 INNOVATIVE RESULTS")
    print("=" * 60)
    print(f"✅ Phase: {report['phase']}")
    print(f"📈 Initial examples: {report['initial_examples']:,}")
    print(f"✨ Generated examples: {report['generated_examples']:,}")
    print(f"📁 Final examples: {report['final_examples']:,}")
    print(f"🔮 Complex combinations: {report['complex_combinations']:,}")
    print(f"🏗️ Structure symbols: {report['structure_symbols']:,}")
    print(f"🔄 Reverse problems: {report['reverse_problems']:,}")
    print(f"📖 Micro-stories: {report['micro_stories']:,}")
    print(f"🎯 Validation rate: {report['validation_rate']:.2%}")

    if 'quality_scores' in report:
        overall_quality = report['quality_scores'].get('overall_quality', 0)
        print(f"⭐ Overall quality: {overall_quality:.3f}")

    print(f"\n🎉 Phase 2A.3 innovative expansion completed!")
    print(f"🎯 Ready for Phase 2C: Tokenizer Integration!")


if __name__ == "__main__":
    main()